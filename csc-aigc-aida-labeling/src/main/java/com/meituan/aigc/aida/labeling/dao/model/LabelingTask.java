package com.meituan.aigc.aida.labeling.dao.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 标注任务表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelingTask {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 数据集ID
     */
    private Long dataSetId;
    
    /**
     * 数据集版本
     */
    private String dataSetVersion;
    
    /**
     * 任务描述
     */
    private String taskDesc;
    
    /**
     * 标注项配置
     */
    private String labelingConfig;
    
    /**
     * 任务标注状态 1:创建中 2:待分配 3:分配中 4:标注中 5:标注完成 7:失败
     * {@link com.meituan.aigc.aida.labeling.common.enums.LabelingTaskStatus}
     */
    private Integer labelingStatus;
    
    /**
     * 任务质检状态 1:创建中 2:质检中 3:已质检 4:失败
     * {@link com.meituan.aigc.aida.labeling.common.enums.QualityCheckTaskStatus}
     */
    private Integer qualityCheckStatus;
    
    /**
     * 标注负责人
     */
    private String labelingManager;

    /**
     * 标注负责人名称
     */
    private String labelingManagerName;
    
    /**
     * 标注任务类型 1:评测集标注 2:训练集标注 3:线上会话标注
     */
    private Integer taskType;
    
    /**
     * 数据类型 1:模型评测 2:Agent评测 3:有监督微调(SFT) 4:偏好对齐(DPO) 5:Session维度 6:Query维度
     */
    private Integer dataType;
    
    /**
     * 任务来源 1:数据集创建 2:文件导入
     */
    private Integer taskSource;
    
    /**
     * 上传文件的S3存储路径
     */
    private String uploadFilePath;
    
    /**
     * 扩展字段，存储错误信息及操作记录，全量质检
     */
    private String extraInfo;
    
    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 业务类型
     */
    private Long bizType;

    /**
     * 是否删除: 0-否, 1-是
     */
    private Boolean isDeleted;
    
    /**
     * 创建人Mis号
     */
    private String creatorMis;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 标注完成时间  仅标注完成时候更新，其他场景不要动这个时间
     */
    private Date checkFinishTime;

    /**
     * 质检完成时间  仅质检完成时候更新，其他场景不要动这个时间
     */
    private Date labelingFinishTime;

    /**
     * 分配类型，1-session 2-query
     */
    private Integer assignType;
}