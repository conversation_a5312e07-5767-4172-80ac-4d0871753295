package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import com.meituan.aigc.aida.labeling.job.dashboard.po.TaskStatisticsPO;

import java.util.Date;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:10
 * @Version: 1.0
 */
public interface LabelingTaskRepository {

    /**
     * 根据ID获取详情
     *
     * @param taskId
     * @return
     */
    LabelingTask getById(Long taskId);

    /**
     * 根据任务ID更新任务状态
     *
     * @param taskId
     * @param status
     * @param labelingFinishTime 标注完成时间，仅状态从标注中到标注完成时赋值
     * @return
     */
    int updateTaskStatusById(Long taskId, Integer status, Date labelingFinishTime);

    /**
     * 更新任务质检状态
     *
     * @param taskId             任务ID
     * @param qualityCheckStatus 质检状态
     * @param checkFinishTime    质检完成时间，仅状态从质检中到质检完成时赋值
     * @return 更新结果
     */
    int updateTaskQualityCheckStatusById(Long taskId, Integer qualityCheckStatus, Date checkFinishTime);

    /**
     * 更新任务
     *
     * @param labelingTask 标注任务要更新的数据
     * @return 更新数量
     */
    int updateByPrimaryKeySelective(LabelingTask labelingTask);

    /**
     * 插入一条记录
     *
     * @param labelingTask 标注任务
     * @return 插入数量
     */
    int insert(LabelingTask labelingTask);

    /**
     * 插入一条记录
     *
     * @param labelingTask 标注任务
     * @return 插入数量
     */
    int insertSelective(LabelingTask labelingTask);

    /**
     * 根据任务名称和标注人查询任务列表
     *
     * @param name    任务名称
     * @param taskId  任务ID
     * @param mis     标注人mis
     * @param isAdmin 是否是管理员
     * @return 任务列表
     */
    List<LabelingTask> listByIdNameAndMis(String name, Long taskId, String mis, boolean isAdmin);

    /**
     * 根据任务名称查询已经质检的任务列表
     *
     * @param name 任务名称
     * @return 任务列表
     */
    List<LabelingTask> listCheckDataByName(String name);

    /**
     * 根据任务ID列表查询任务列表
     *
     * @param taskIdList 任务ID列表
     * @return 任务列表
     */
    List<LabelingTask> listByIdList(List<Long> taskIdList);

    /**
     * 统计任务数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    List<TaskStatisticsPO> statisticsTaskData(Date startTime, Date endTime);

    /**
     * 根据任务创建时间和状态查询列表
     *
     * @param startTime      查询开始时间
     * @param endTime        查询结束时间
     * @param labelingStatus 标注状态集合
     * @return 结果
     */
    List<LabelingTask> listTaskByCreateTimeAndStatus(Date startTime, Date endTime, List<Integer> labelingStatus);
}
