package com.meituan.aigc.aida.benchmark.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Benchmark版本项VO
 * 
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BenchmarkVersionItemVO {

    /**
     * benchmark版本id
     */
    private Long id;

    /**
     * benchmark版本名称
     */
    private String name;

    /**
     * 是否为默认版本
     */
    private Boolean isDefault = Boolean.FALSE;
} 