package com.meituan.aigc.aida.labeling.strategy.task.create.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategy;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 17:16
 * @Version: 1.0
 * 在线会话标注结果导出
 */
@Slf4j
@Component
public class OnlineSessionCreateTask extends CommonCreateTask implements CreateTaskStrategy {

    private static final String LOG_FLAG = LabelTaskDataType.SESSION_ANNOTATION.getValue();

    @Override
    public Integer getTaskType() {
        return LabelTaskDataType.SESSION_ANNOTATION.getCode();
    }

    @Override
    public void checkHeader(MultipartFile file) {
        checkHeader(file, null);
    }

    @Override
    public List<LabelingTaskRawData> processData(List<List<String>> rowData, List<String> headerList) {
        return processData(rowData, headerList, null);
    }

    /**
     * 覆盖父类方法，实现在线会话场景下的sessionId重复检查逻辑
     */
    @Override
    protected boolean checkDuplicateSessionId(String sessionId, Set<String> duplicates, int rowIndex) {
        if (duplicates.contains(sessionId)) {
            log.info("数据中第【{}】行sessionId重复", rowIndex);
            return true;
        }
        duplicates.add(sessionId);
        return false;
    }

    /**
     * 覆盖父类方法，表头只存在sessionId
     *
     * @param mustFields 必填字段
     */
    @Override
    protected void headerProcessing(List<String> mustFields) {
        // 保留第一个sessionId，移除其他元素
        mustFields.subList(1, mustFields.size()).clear();
    }

    @Override
    public void export(String taskName, List<Long> subTaskIds, Integer labelDetailStatus, String mis) {
        commonExport(taskName, subTaskIds, labelDetailStatus, LOG_FLAG, "会话数据", mis);
    }

    @Override
    protected int[] createHeaderAndFillData(SXSSFWorkbook workbook, Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData,
                                            List<Long> subTaskIds, Integer labelDetailStatus, String taskName) {
        // 创建表头行
        int colIndex = createHeaderRows(sheet, headerStyle, fieldData);

        // 设置列宽
        setupColumnWidths(sheet, colIndex);

        // 处理数据
        int totalCount = processSessionData(sheet, subTaskIds, labelDetailStatus,
                fieldData, taskName);

        return new int[]{colIndex, totalCount};
    }

    /**
     * 创建表头行
     *
     * @param sheet       工作表
     * @param headerStyle 表头样式
     * @return 最终列索引
     */
    private int createHeaderRows(Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData) {
        // 创建第一行表头（主分类）
        int colIndex = createFirstHeaderRow(sheet, headerStyle, fieldData);

        // 创建第二行表头（具体字段）
        createSecondHeaderRow(sheet, headerStyle, fieldData);

        return colIndex;
    }

    /**
     * 创建第一行表头（主分类）
     *
     * @param sheet       工作表
     * @param headerStyle 表头样式
     * @return 最终列索引
     */
    private int createFirstHeaderRow(Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData) {
        List<InspectionItemData> labelingFields = fieldData.getLabelingFields();
        List<InspectionItemData> qualityCheckFields = fieldData.getQualityCheckFields();
        List<InspectionItemData> qualityCheckModifiedLabelingItems = fieldData.getQualityCheckModifiedLabelingItems();

        Row firstHeaderRow = sheet.createRow(0);
        int colIndex = 0;

        // 会话信息列
        colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "会话信息", 2);

        // 标注结果列
        colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "标注结果", labelingFields.size());

        // 质检结果列
        colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "质检结果",
                qualityCheckFields.size());

        // 标注结果列-质检员修改后内容
        if (CollectionUtils.isNotEmpty(qualityCheckModifiedLabelingItems)) {
            colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "标注结果-质检员修改后", qualityCheckModifiedLabelingItems.size());
        }

        return colIndex;
    }

    /**
     * 设置列宽
     *
     * @param sheet    工作表
     * @param colIndex 列数
     */
    private void setupColumnWidths(Sheet sheet, int colIndex) {
        for (int i = 0; i < colIndex; i++) {
            // 约25个字符宽
            sheet.setColumnWidth(i, 5000);
        }
    }

    /**
     * 处理会话数据
     *
     * @param sheet             工作表
     * @param subTaskIds        子任务ID集合
     * @param labelDetailStatus 标注详情状态
     * @param taskName          任务名称
     * @return 处理的数据总数
     */
    private int processSessionData(Sheet sheet, List<Long> subTaskIds, Integer labelDetailStatus, ExportFieldData fieldData, String taskName) {
        // 创建数据处理器
        SessionDataProcessor processor = new SessionDataProcessor(fieldData.getLabelingFields(), fieldData.getQualityCheckFields(), fieldData.getQualityCheckModifiedLabelingItems());

        // 批量处理数据
        return processDataInBatches(sheet, 2, subTaskIds, labelDetailStatus, LOG_FLAG, taskName, processor);
    }

    /**
     * 会话数据处理器
     */
    private static class SessionDataProcessor implements ExportDataProcessor {
        private final List<InspectionItemData> labelingFields;
        private final List<InspectionItemData> qualityCheckFields;
        private final List<InspectionItemData> qualityCheckModifiedLabelingItems;

        public SessionDataProcessor(List<InspectionItemData> labelingFields, List<InspectionItemData> qualityCheckFields, List<InspectionItemData> qualityCheckModifiedLabelingItems) {
            this.labelingFields = labelingFields;
            this.qualityCheckFields = qualityCheckFields;
            this.qualityCheckModifiedLabelingItems = qualityCheckModifiedLabelingItems;
        }

        @Override
        public void processRow(Row row, LabelTaskDataExportVO data) {
            int cellIndex = 0;

            // 写入sessionId
            row.createCell(cellIndex++).setCellValue(data.getSessionId());

            String time = data.getSessionTime() != null
                    ? DateUtil.formatDate(data.getSessionTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD)
                    : "";
            // 写入会话时间
            row.createCell(cellIndex++).setCellValue(time);

            // 使用公共方法处理标注结果和质检结果的写入
            CommonCreateTask.writeLabelingAndQualityCheckResults(row, data, cellIndex, labelingFields,
                    qualityCheckFields, qualityCheckModifiedLabelingItems);
        }
    }
}