package com.meituan.aigc.aida.data.management.fetch.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LlmSignalTrackingRulesExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LlmSignalTrackingRulesExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andChannelIsNull() {
            addCriterion("channel is null");
            return (Criteria) this;
        }

        public Criteria andChannelIsNotNull() {
            addCriterion("channel is not null");
            return (Criteria) this;
        }

        public Criteria andChannelEqualTo(Byte value) {
            addCriterion("channel =", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualTo(Byte value) {
            addCriterion("channel <>", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThan(Byte value) {
            addCriterion("channel >", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualTo(Byte value) {
            addCriterion("channel >=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThan(Byte value) {
            addCriterion("channel <", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualTo(Byte value) {
            addCriterion("channel <=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelIn(List<Byte> values) {
            addCriterion("channel in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotIn(List<Byte> values) {
            addCriterion("channel not in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelBetween(Byte value1, Byte value2) {
            addCriterion("channel between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotBetween(Byte value1, Byte value2) {
            addCriterion("channel not between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsIsNull() {
            addCriterion("typical_question_ids is null");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsIsNotNull() {
            addCriterion("typical_question_ids is not null");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsEqualTo(String value) {
            addCriterion("typical_question_ids =", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsNotEqualTo(String value) {
            addCriterion("typical_question_ids <>", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsGreaterThan(String value) {
            addCriterion("typical_question_ids >", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsGreaterThanOrEqualTo(String value) {
            addCriterion("typical_question_ids >=", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsLessThan(String value) {
            addCriterion("typical_question_ids <", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsLessThanOrEqualTo(String value) {
            addCriterion("typical_question_ids <=", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsLike(String value) {
            addCriterion("typical_question_ids like", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsNotLike(String value) {
            addCriterion("typical_question_ids not like", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsIn(List<String> values) {
            addCriterion("typical_question_ids in", values, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsNotIn(List<String> values) {
            addCriterion("typical_question_ids not in", values, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsBetween(String value1, String value2) {
            addCriterion("typical_question_ids between", value1, value2, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsNotBetween(String value1, String value2) {
            addCriterion("typical_question_ids not between", value1, value2, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsIsNull() {
            addCriterion("mis_ids is null");
            return (Criteria) this;
        }

        public Criteria andMisIdsIsNotNull() {
            addCriterion("mis_ids is not null");
            return (Criteria) this;
        }

        public Criteria andMisIdsEqualTo(String value) {
            addCriterion("mis_ids =", value, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsNotEqualTo(String value) {
            addCriterion("mis_ids <>", value, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsGreaterThan(String value) {
            addCriterion("mis_ids >", value, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsGreaterThanOrEqualTo(String value) {
            addCriterion("mis_ids >=", value, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsLessThan(String value) {
            addCriterion("mis_ids <", value, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsLessThanOrEqualTo(String value) {
            addCriterion("mis_ids <=", value, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsLike(String value) {
            addCriterion("mis_ids like", value, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsNotLike(String value) {
            addCriterion("mis_ids not like", value, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsIn(List<String> values) {
            addCriterion("mis_ids in", values, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsNotIn(List<String> values) {
            addCriterion("mis_ids not in", values, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsBetween(String value1, String value2) {
            addCriterion("mis_ids between", value1, value2, "misIds");
            return (Criteria) this;
        }

        public Criteria andMisIdsNotBetween(String value1, String value2) {
            addCriterion("mis_ids not between", value1, value2, "misIds");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceIsNull() {
            addCriterion("ac_interface is null");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceIsNotNull() {
            addCriterion("ac_interface is not null");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceEqualTo(String value) {
            addCriterion("ac_interface =", value, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceNotEqualTo(String value) {
            addCriterion("ac_interface <>", value, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceGreaterThan(String value) {
            addCriterion("ac_interface >", value, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceGreaterThanOrEqualTo(String value) {
            addCriterion("ac_interface >=", value, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceLessThan(String value) {
            addCriterion("ac_interface <", value, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceLessThanOrEqualTo(String value) {
            addCriterion("ac_interface <=", value, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceLike(String value) {
            addCriterion("ac_interface like", value, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceNotLike(String value) {
            addCriterion("ac_interface not like", value, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceIn(List<String> values) {
            addCriterion("ac_interface in", values, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceNotIn(List<String> values) {
            addCriterion("ac_interface not in", values, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceBetween(String value1, String value2) {
            addCriterion("ac_interface between", value1, value2, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAcInterfaceNotBetween(String value1, String value2) {
            addCriterion("ac_interface not between", value1, value2, "acInterface");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgIsNull() {
            addCriterion("aida_app_cfg is null");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgIsNotNull() {
            addCriterion("aida_app_cfg is not null");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgEqualTo(String value) {
            addCriterion("aida_app_cfg =", value, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgNotEqualTo(String value) {
            addCriterion("aida_app_cfg <>", value, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgGreaterThan(String value) {
            addCriterion("aida_app_cfg >", value, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgGreaterThanOrEqualTo(String value) {
            addCriterion("aida_app_cfg >=", value, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgLessThan(String value) {
            addCriterion("aida_app_cfg <", value, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgLessThanOrEqualTo(String value) {
            addCriterion("aida_app_cfg <=", value, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgLike(String value) {
            addCriterion("aida_app_cfg like", value, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgNotLike(String value) {
            addCriterion("aida_app_cfg not like", value, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgIn(List<String> values) {
            addCriterion("aida_app_cfg in", values, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgNotIn(List<String> values) {
            addCriterion("aida_app_cfg not in", values, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgBetween(String value1, String value2) {
            addCriterion("aida_app_cfg between", value1, value2, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andAidaAppCfgNotBetween(String value1, String value2) {
            addCriterion("aida_app_cfg not between", value1, value2, "aidaAppCfg");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNull() {
            addCriterion("creator_mis is null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNotNull() {
            addCriterion("creator_mis is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisEqualTo(String value) {
            addCriterion("creator_mis =", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotEqualTo(String value) {
            addCriterion("creator_mis <>", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThan(String value) {
            addCriterion("creator_mis >", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThanOrEqualTo(String value) {
            addCriterion("creator_mis >=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThan(String value) {
            addCriterion("creator_mis <", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThanOrEqualTo(String value) {
            addCriterion("creator_mis <=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLike(String value) {
            addCriterion("creator_mis like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotLike(String value) {
            addCriterion("creator_mis not like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIn(List<String> values) {
            addCriterion("creator_mis in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotIn(List<String> values) {
            addCriterion("creator_mis not in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisBetween(String value1, String value2) {
            addCriterion("creator_mis between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotBetween(String value1, String value2) {
            addCriterion("creator_mis not between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNull() {
            addCriterion("creator_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNotNull() {
            addCriterion("creator_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameEqualTo(String value) {
            addCriterion("creator_name =", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotEqualTo(String value) {
            addCriterion("creator_name <>", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThan(String value) {
            addCriterion("creator_name >", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("creator_name >=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThan(String value) {
            addCriterion("creator_name <", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanOrEqualTo(String value) {
            addCriterion("creator_name <=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLike(String value) {
            addCriterion("creator_name like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotLike(String value) {
            addCriterion("creator_name not like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIn(List<String> values) {
            addCriterion("creator_name in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotIn(List<String> values) {
            addCriterion("creator_name not in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameBetween(String value1, String value2) {
            addCriterion("creator_name between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotBetween(String value1, String value2) {
            addCriterion("creator_name not between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Boolean value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Boolean value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Boolean value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Boolean value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Boolean value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Boolean> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Boolean> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Boolean value1, Boolean value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}