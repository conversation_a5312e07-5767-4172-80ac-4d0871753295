package com.meituan.aigc.aida.labeling.job.dashboard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.cat.Cat;
import com.meituan.aigc.aida.labeling.common.enums.LabelingTaskStatus;
import com.meituan.aigc.aida.labeling.common.enums.QualityCheckResultEnum;
import com.meituan.aigc.aida.labeling.dao.model.*;
import com.meituan.aigc.aida.labeling.dao.repository.*;
import com.meituan.aigc.aida.labeling.exception.AidaStatisticsException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.job.dashboard.param.StatisticalTaskAndLabelingAndCheckParam;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingNumCountPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingStatisticsPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonQualityCheckStatisticsPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.TaskStatisticsPO;
import com.meituan.aigc.aida.labeling.param.task.LabelingTaskExtraInfo;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingQualityCheckInfoVO;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: guowenhui
 * @Create: 2025/5/21 10:22
 * @Version: 1.0
 * 标注大盘数据统计定时任务
 */
@Slf4j
@CraneConfiguration
public class DashboardDataStatisticalJob {

    private static final String LOG_PRE = "【标注大盘每日数据统计】";

    @Resource
    private PersonLabelingStatisticsRepository personLabelingStatisticsRepository;

    @Resource
    private QualityCheckStatisticsRepository qualityCheckStatisticsRepository;

    @Resource
    private TaskStatisticsRepository taskStatisticsRepository;

    @Resource
    private LabelingDetailRepository labelingDetailRepository;

    @Resource
    private LabelingQualityCheckItemRepository labelingQualityCheckItemRepository;

    @Resource
    private LabelingTaskRepository labelingTaskRepository;

    @Resource
    private LabelingSubTaskRepository labelingSubTaskRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 统计每日标注数据定时任务 包括任务、人员标注数量、人员质检数量三个维度
     * @param param 可选填的参数，填入值时按填入的时间统计数据，未填入值时默认前一天的时间统计数据
     */
    @Crane("labeling.dashboard.dataStatisticalJob")
    public void statisticalTaskAndLabelingAndCheckData(StatisticalTaskAndLabelingAndCheckParam param){
        log.info("{}开始执行，入参：{}", LOG_PRE, JSON.toJSONString(param));

        // 使用编程式事务
        transactionTemplate.execute(status -> {
            try {
                DashboardDataStatisticalJob dashboardDataStatisticalJob = applicationContext.getBean(DashboardDataStatisticalJob.class);
                dashboardDataStatisticalJob.statisticDashboardData(param);
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 统计大盘数据
     * @param param 参数
     */
    public void statisticDashboardData(StatisticalTaskAndLabelingAndCheckParam param) {
        try {
            // 确定统计时间区间
            Date startTime = null;
            Date endTime = null;
            Date statisticalTime = null;
            if (Objects.nonNull(param)
                    && DateUtil.isValidDateTime(param.getStartTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD)
                    && DateUtil.isValidDateTime(param.getEndTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD)){
                startTime = DateUtil.parseToDate(param.getStartTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
                endTime = DateUtil.parseToDate(param.getEndTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
                statisticalTime =startTime;
                if (DateUtil.daysBetween(startTime, endTime) != 1){
                    log.warn("{}起止日期间隔大于1天不可以执行", LOG_PRE);
                    return;
                }
            }
            // 入参没传默认取前一天的时间统计
            startTime = Objects.isNull(startTime) ? DateUtil.getYesterdayStartDate() : startTime;
            endTime = Objects.isNull(endTime) ? DateUtil.getYesterdayEndDate() : endTime;
            statisticalTime = Objects.isNull(statisticalTime) ? DateUtil.getYesterdayStartDate() : statisticalTime;
            log.info("{}开始统计，startTime={}, endTime={}, statisticalTime={}", LOG_PRE,  DateUtil.formatDate(startTime, DateUtil.DATE_PATTERN_YYYY_MM_DD),
                    DateUtil.formatDate(endTime, DateUtil.DATE_PATTERN_YYYY_MM_DD), DateUtil.formatDate(statisticalTime, DateUtil.DATE_PATTERN));
            // 复标任务数据回刷
            reLabelingTaskDataRefresh(startTime, endTime);
            // 标注数据统计
            statisticsLabelingData(startTime, endTime, statisticalTime);
            // 质检数据统计
            statisticsQualityCheckData(startTime, endTime, statisticalTime);
            // 任务数据统计
            statisticsTaskData(startTime, endTime, statisticalTime);
            // 标注数据统计回刷质检总量和质检正确量
            statisticsLabelingDataRefresh(startTime, endTime);
            log.info("{}执行结束", LOG_PRE);
        } catch (Exception e) {
            Cat.logErrorWithCategory("labeling.dashboard.dataStatisticalJob", e);
            log.error("{}执行异常", LOG_PRE, e);
            throw e;
        }
    }

    public void statisticsTaskData(Date startTime, Date endTime, Date statisticalTime) {
        List<TaskStatisticsPO> statisticsTaskRes = labelingTaskRepository.statisticsTaskData(startTime, endTime);
        List<TaskStatistics> taskStatisticsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(statisticsTaskRes)) {
            statisticsTaskRes.forEach(task -> {
                TaskStatistics taskStatistics = new TaskStatistics();
                taskStatistics.setStatDate(statisticalTime);
                taskStatistics.setBizType(task.getBizType());
                taskStatistics.setTaskType(task.getTaskType());
                taskStatistics.setDataType(task.getDataType());
                taskStatistics.setTaskId(task.getTaskId());
                taskStatistics.setTaskName(task.getTaskName());
                taskStatistics.setLabelingStatus(task.getLabelingStatus());
                taskStatistics.setQualityCheckStatus(task.getQualityCheckStatus());
                taskStatistics.setTotalSampleCount(task.getTotalSampleCount());
                taskStatistics.setCreateTime(new Date());
                taskStatistics.setUpdateTime(new Date());
                taskStatisticsList.add(taskStatistics);
            });
        }
        taskStatisticsRepository.batchInsert(taskStatisticsList);
    }

    public void statisticsQualityCheckData(Date startTime, Date endTime, Date statisticalTime) {
        List<PersonQualityCheckStatisticsPO> personQualityCheckStatistics = labelingQualityCheckItemRepository.statisticsPersonQualityCheckData(startTime, endTime);
        List<QualityCheckStatistics> qualityCheckStatisticsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(personQualityCheckStatistics)) {
            personQualityCheckStatistics.forEach(check -> {
                QualityCheckStatistics qualityCheckStatistics = new QualityCheckStatistics();
                qualityCheckStatistics.setStatDate(statisticalTime);
                qualityCheckStatistics.setBizType(check.getBizType());
                qualityCheckStatistics.setTaskType(check.getTaskType());
                qualityCheckStatistics.setDataType(check.getDataType());
                qualityCheckStatistics.setQualityCheckerName(check.getQualityCheckerName());
                qualityCheckStatistics.setQualityCheckerMis(check.getQualityCheckerMis());
                qualityCheckStatistics.setQualityCheckCount(check.getQualityCheckCount());
                qualityCheckStatistics.setQualityCheckCorrectCount(check.getQualityCheckCorrectCount());
                qualityCheckStatistics.setCreateTime(new Date());
                qualityCheckStatistics.setUpdateTime(new Date());
                qualityCheckStatisticsList.add(qualityCheckStatistics);
            });
        }
        qualityCheckStatisticsRepository.batchInsert(qualityCheckStatisticsList);
    }

    public void statisticsLabelingData(Date startTime, Date endTime, Date statisticalTime) {
        List<PersonLabelingStatisticsPO> statisticsPersonLabelingRes = labelingDetailRepository.statisticsPersonLabelingData(startTime, endTime);
        List<PersonLabelingStatistics> personLabelingStatisticsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(statisticsPersonLabelingRes)) {
            statisticsPersonLabelingRes.forEach(labeling -> {
                PersonLabelingStatistics personLabelingStatistics = new PersonLabelingStatistics();
                personLabelingStatistics.setStatDate(statisticalTime);
                personLabelingStatistics.setBizType(labeling.getBizType());
                personLabelingStatistics.setTaskType(labeling.getTaskType());
                personLabelingStatistics.setDataType(labeling.getDataType());
                personLabelingStatistics.setLabelerName(labeling.getLabelerName());
                personLabelingStatistics.setLabelerMis(labeling.getLabelerMis());
                personLabelingStatistics.setLabelingCount(labeling.getLabelingCount());
                personLabelingStatistics.setQcInspectedCount(labeling.getQcInspectedCount());
                personLabelingStatistics.setQcInspectedCorrectCount(labeling.getQcInspectedCorrectCount());
                personLabelingStatistics.setCreateTime(new Date());
                personLabelingStatistics.setUpdateTime(new Date());
                personLabelingStatisticsList.add(personLabelingStatistics);
            });
        }
        personLabelingStatisticsRepository.batchInsert(personLabelingStatisticsList);
    }

    /**
     * 复标任务数据回刷
     * @param startTime 开始日期
     * @param endTime   结束日期
     */
    public void reLabelingTaskDataRefresh(Date startTime, Date endTime){
        // 查询规定日期内复标的任务
        List<LabelingTask> reLabelingTasks = labelingTaskRepository.listTaskByCreateTimeAndStatus(startTime, endTime, LabelingTaskStatus.getReLabelingStatus());
        // 无复标任务不需要处理
        if (CollectionUtils.isEmpty(reLabelingTasks)) {
            return;
        }
        // 解析extra_info 拿到复标前的任务ID，这些数据是要刷新的
        reLabelingTasks.forEach(reTask -> {
            String extraInfo = reTask.getExtraInfo();
            if (StringUtils.isBlank(extraInfo)){
                // 这种数据是有问题的，复标的数据extraInfo不应该为空
                log.error("{}复标任务缺失extraInfo,任务ID：{}", LOG_PRE, reTask.getId());
                throw new AidaTrainingCheckException(LOG_PRE + "复标任务缺失extraInfo,任务ID：" + reTask.getId());
            }
            LabelingTaskExtraInfo labelingTaskExtraInfo = JSON.parseObject(extraInfo, new TypeReference<LabelingTaskExtraInfo>() {
            });
            if (Objects.isNull(labelingTaskExtraInfo) || Objects.isNull(labelingTaskExtraInfo.getReLabelingTaskId())){
                log.error("{}复标任务缺失复标前任务ID,任务ID：{}", LOG_PRE, reTask.getId());
                throw new AidaTrainingCheckException(LOG_PRE + "复标任务缺失复标前任务ID,任务ID：" + reTask.getId());
            }
            List<TaskStatistics> taskStatisticsList = taskStatisticsRepository.listByTaskId(labelingTaskExtraInfo.getReLabelingTaskId());
            if (CollectionUtils.isEmpty(taskStatisticsList)){
                log.warn("{}复标前任务未统计到无需处理", LOG_PRE);
                return;
            }
            // 查询任务有没有质检信息，有质检信息的留下不需要删除
            taskStatisticsList.forEach(taskStatistics -> {
                Date statDate = taskStatistics.getStatDate();
                Date dayStartTime = DateUtil.getDayStartTime(statDate);
                Date dayEndTime = DateUtil.getDayEndTime(statDate);
                Integer checkItemCount = labelingQualityCheckItemRepository.countByTaskIdAndFirstCheckTime(taskStatistics.getTaskId(), dayStartTime, dayEndTime);
                // 没有质检信息的任务要删掉
                if (checkItemCount < 1 && Objects.nonNull(taskStatistics.getTaskId()) && Objects.nonNull(statDate)) {
                    taskStatisticsRepository.deleteByTaskIdAndStatDate(taskStatistics.getTaskId(), statDate);
                }
                // 开始回刷标注员统计数据
                reLabelingTaskPersonDataRefresh(taskStatistics, dayStartTime, dayEndTime, statDate);
            });
        });

    }

    /**
     * 标注员数据回刷
     * @param taskStatistics 统计任务
     * @param dayStartTime  一天的开始时间
     * @param dayEndTime    一天的结束时间
     * @param statDate      统计时间
     */
    public void reLabelingTaskPersonDataRefresh(TaskStatistics taskStatistics, Date dayStartTime, Date dayEndTime, Date statDate) {
        // 查询这个任务每个标注员在这一天标注的数量（未复标的子任务不需要回刷）
        List<PersonLabelingNumCountPO> personLabelingNumCountList = labelingDetailRepository.listPersonLabelingCount(taskStatistics.getTaskId(), dayStartTime, dayEndTime);
        if (CollectionUtils.isEmpty(personLabelingNumCountList)){
            log.warn("{}标注员未标注，无需处理, 日期:{}, 任务ID:{}", LOG_PRE, DateUtil.formatDate(statDate), taskStatistics.getTaskId());
            return;
        }
        Map<String, PersonLabelingNumCountPO> personLabelingNumCountMap = personLabelingNumCountList.stream().collect(Collectors.toMap(PersonLabelingNumCountPO::getLabelerMis, Function.identity()));
        List<String> userMisList = personLabelingNumCountList.stream().map(PersonLabelingNumCountPO::getLabelerMis).collect(Collectors.toList());
        // 查询标注员标注统计信息
        List<PersonLabelingStatistics> personLabelingStatisticsList = personLabelingStatisticsRepository.listPersonStatsByType(userMisList, statDate, taskStatistics.getBizType(), taskStatistics.getTaskType(), taskStatistics.getDataType());
        if (CollectionUtils.isEmpty(personLabelingStatisticsList)) {
            log.warn("{}未查询到标注员统计数据, 日期:{}, 任务ID:{}", LOG_PRE, DateUtil.formatDate(statDate), taskStatistics.getTaskId());
            return;
        }
        // 需要删除的统计是数据
        List<Long> deletePersonLabelingStatistics = new ArrayList<>();
        // 需要更新的统计数据
        List<PersonLabelingStatistics> updatePersonLabelingStatistics = new ArrayList<>();

        personLabelingStatisticsList.forEach(personLabelingStatistics -> {
            PersonLabelingNumCountPO personLabelingNumCount = personLabelingNumCountMap.get(personLabelingStatistics.getLabelerMis());
            // 没有匹配到标注信息不处理
            if (Objects.isNull(personLabelingNumCount)) {
                log.warn("{}未匹配到标注数据, 日期:{}, 任务ID:{}, userMis:{}", LOG_PRE, DateUtil.formatDate(statDate), taskStatistics.getTaskId(), personLabelingStatistics.getLabelerMis());
                return;
            }
            Integer labelingCount = personLabelingStatistics.getLabelingCount();
            Integer qcInspectedCount = personLabelingStatistics.getQcInspectedCount();
            Integer qcInspectedCorrectCount = personLabelingStatistics.getQcInspectedCorrectCount();
            int newLabelingCount = labelingCount - personLabelingNumCount.getLabelingCount();
            Integer newQcInspectedCount = qcInspectedCount - personLabelingNumCount.getQcInspectedCount();
            Integer newQcInspectedCorrectCount = qcInspectedCorrectCount - personLabelingNumCount.getQcInspectedCorrectCount();
            if (newLabelingCount > 0) {
                personLabelingStatistics.setLabelingCount(newLabelingCount);
                personLabelingStatistics.setQcInspectedCount(newQcInspectedCount);
                personLabelingStatistics.setQcInspectedCorrectCount(newQcInspectedCorrectCount);
                personLabelingStatistics.setUpdateTime(new Date());
                updatePersonLabelingStatistics.add(personLabelingStatistics);
            } else {
                deletePersonLabelingStatistics.add(personLabelingStatistics.getId());
            }
        });
        if (CollectionUtils.isNotEmpty(updatePersonLabelingStatistics)){
             personLabelingStatisticsRepository.batchUpdate(updatePersonLabelingStatistics);
        }
        if (CollectionUtils.isNotEmpty(deletePersonLabelingStatistics)) {
            personLabelingStatisticsRepository.batchDelete(deletePersonLabelingStatistics);
        }
    }

    /**
     * 标注数据统计回刷
     * @param startTime 开始日期
     * @param endTime 结束日期
     */
    public void statisticsLabelingDataRefresh(Date startTime, Date endTime) {
        // 查找改时间段内位于标注当天以后的质检信息
        List<LabelingQualityCheckInfoVO> labelingQualityCheckInfoVOList = getLabelingQualityCheckInfoVO(startTime, endTime);
        if (CollectionUtils.isEmpty(labelingQualityCheckInfoVOList)) {
            // 没有需要回刷的标注数据统计
            return;
        }
        List<PersonLabelingStatistics> updatePersonLabelingStatistics = new ArrayList<>();
        // 遍历需要回刷的标注数据统计
        labelingQualityCheckInfoVOList.forEach(labelingQualityCheckInfoVO -> {
            // 查询标注员统计信息
            List<PersonLabelingStatistics> personLabelingStatisticsList = personLabelingStatisticsRepository.listPersonStatsByType(Collections.singletonList(labelingQualityCheckInfoVO.getLabelerMis()), labelingQualityCheckInfoVO.getStatDate(), labelingQualityCheckInfoVO.getBizType(), labelingQualityCheckInfoVO.getTaskType(), labelingQualityCheckInfoVO.getDataType());
            if (CollectionUtils.isEmpty(personLabelingStatisticsList)) {
                log.warn("{}未查询到标注员统计数据, 日期:{}", LOG_PRE, DateUtil.formatDate(labelingQualityCheckInfoVO.getStatDate()));
                Cat.logEvent("Labeling.dashboard.dataStatisticalJob", "FAIL", "EmptyLabelingStatistics", "缺少标注统计数据");
                return;
            }
            PersonLabelingStatistics personLabelingStatistics = personLabelingStatisticsList.get(0);
            // 更新标注员统计数据
            personLabelingStatistics.setQcInspectedCount(personLabelingStatistics.getQcInspectedCount() + labelingQualityCheckInfoVO.getQcInspectedCount());
            personLabelingStatistics.setQcInspectedCorrectCount(personLabelingStatistics.getQcInspectedCorrectCount() + labelingQualityCheckInfoVO.getQcInspectedCorrectCount());
            personLabelingStatistics.setUpdateTime(new Date());
            updatePersonLabelingStatistics.add(personLabelingStatistics);
        });
        if (CollectionUtils.isNotEmpty(updatePersonLabelingStatistics)) {
            personLabelingStatisticsRepository.batchUpdate(updatePersonLabelingStatistics);
        }
    }

    /**
     * 获取需要回刷的质检信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 需要回刷的质检信息列表
     */
    public List<LabelingQualityCheckInfoVO> getLabelingQualityCheckInfoVO(Date startTime, Date endTime) {
        // 查询时间范围内的已质检的质检详情
        List<LabelingQualityCheckItem> qualityCheckedItems = labelingQualityCheckItemRepository.listQualityCheckedItemByDateRange(startTime, endTime);
        if (CollectionUtils.isEmpty(qualityCheckedItems)) {
            log.warn("{}:{}-{}未查询到质检信息",LOG_PRE, DateUtil.formatDate(startTime), DateUtil.formatDate(endTime));
            return Collections.emptyList();
        }
        // 提取标注详情id和质检结果的map
        List<Long> labelingDetailIds = qualityCheckedItems.stream().map(LabelingQualityCheckItem::getLabelingDataId).filter(Objects::nonNull).collect(Collectors.toList());
        // 根据质检详情id查询质检之前的标注详情(用时间过滤了)
        List<LabelingDetail> labelingDetails = labelingDetailRepository.listLabelingDetailByIdsAndDate(labelingDetailIds, startTime);
        if (CollectionUtils.isEmpty(labelingDetails)) {
            log.warn("{}:{}之前未查询到质检之前的标注详情", LOG_PRE, DateUtil.formatDate(startTime));
            return Collections.emptyList();
        }
        // 过滤的qualityCheckedItems
        List<Long> labelingDetailIdsFilter = labelingDetails.stream().map(LabelingDetail::getId).collect(Collectors.toList());
        qualityCheckedItems = qualityCheckedItems.stream().filter(item -> labelingDetailIdsFilter.contains(item.getLabelingDataId())).collect(Collectors.toList());
        // 详情id和标注详情的映射，子任务id列表
        Map<Long, LabelingDetail> labelingDetailIdAndDetailMap = labelingDetails.stream().collect(Collectors.toMap(LabelingDetail::getId, Function.identity()));
        List<Long> subTaskIds = labelingDetails.stream().map(LabelingDetail::getSubTaskId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 通过子任务id找到子任务列表,子任务id和子任务映射
        List<LabelingSubTask> labelingSubTasks = labelingSubTaskRepository.listByIds(subTaskIds);
        if (CollectionUtils.isEmpty(labelingSubTasks)) {
            log.warn("{}:subTaskIds-{}未查询到子任务列表", LOG_PRE, subTaskIds);
            throw new AidaStatisticsException(LOG_PRE + "质检信息存在但相关联的子任务不存在");
        }
        Map<Long, LabelingSubTask> labelingSubTaskIdAndSubTaskMap = labelingSubTasks.stream().collect(Collectors.toMap(LabelingSubTask::getId, Function.identity()));
        // 主任务列表,主任务id和主任务的映射
        List<Long> taskIds = labelingSubTasks.stream().map(LabelingSubTask::getTaskId).distinct().collect(Collectors.toList());
        List<LabelingTask> labelingTasks = labelingTaskRepository.listByIdList(taskIds);
        if (CollectionUtils.isEmpty(labelingTasks)) {
            log.warn("{}:taskIds-{}未查询到主任务列表", LOG_PRE, taskIds);
            throw new AidaStatisticsException(LOG_PRE + "质检信息存在但相关联的主任务不存在");
        }
        Map<Long, LabelingTask> labelingTaskIdAndTaskMap = labelingTasks.stream().collect(Collectors.toMap(LabelingTask::getId, Function.identity()));
        Map<String, LabelingQualityCheckInfoVO> resultMap = getStringLabelingQualityCheckInfoVOMap(qualityCheckedItems, labelingDetailIdAndDetailMap, labelingSubTaskIdAndSubTaskMap, labelingTaskIdAndTaskMap);
        return new ArrayList<>(resultMap.values());
    }

    /**
     * 获取质检信息映射
     * @param qualityCheckedItems 质检详情列表
     * @param labelingDetailIdAndDetailMap 标注详情id和标注详情的映射
     * @param labelingSubTaskIdAndSubTaskMap 子任务id和子任务列表的映射
     * @param labelingTaskIdAndTaskMap 主任务id和主任务列表的映射
     * @return 新增的非今日标注的质检信息映射
     */
    public Map<String, LabelingQualityCheckInfoVO> getStringLabelingQualityCheckInfoVOMap(List<LabelingQualityCheckItem> qualityCheckedItems, Map<Long, LabelingDetail> labelingDetailIdAndDetailMap, Map<Long, LabelingSubTask> labelingSubTaskIdAndSubTaskMap, Map<Long, LabelingTask> labelingTaskIdAndTaskMap) {
        // 按照标注员、业务类型、任务类型、数据类型、统计日期分组统计质检数量和质检正确数量
        Map<String, LabelingQualityCheckInfoVO> resultMap = new HashMap<>();
        for(LabelingQualityCheckItem qualityCheckedItem : qualityCheckedItems) {
            Long labelingDetailId = qualityCheckedItem.getLabelingDataId();
            if(Objects.isNull(labelingDetailId) || Objects.isNull(labelingDetailIdAndDetailMap.get(labelingDetailId))){
                continue;
            }
            LabelingDetail labelingDetail = labelingDetailIdAndDetailMap.get(labelingDetailId);
            if(Objects.isNull(labelingDetail) || Objects.isNull(labelingDetail.getSubTaskId())){
                continue;
            }
            Long subTaskId = labelingDetail.getSubTaskId();
            LabelingSubTask labelingSubTask = labelingSubTaskIdAndSubTaskMap.get(subTaskId);
            if(Objects.isNull(labelingSubTask) || Objects.isNull(labelingSubTask.getTaskId())){
                continue;
            }
            Long taskId = labelingSubTask.getTaskId();
            if(Objects.isNull(labelingTaskIdAndTaskMap.get(taskId))){
                continue;
            }
            LabelingTask labelingTask = labelingTaskIdAndTaskMap.get(taskId);
            if(Objects.isNull(labelingTask)){
                continue;
            }
            // 分组键
            String groupKey = labelingDetail.getLabelerMis() + "_" + labelingTask.getBizType() + "_" + labelingTask.getTaskType() + "_" + labelingTask.getDataType() + "_" + DateUtil.formatDate(labelingDetail.getFirstLabelingTime());
            LabelingQualityCheckInfoVO labelingQualityCheckInfoVO = resultMap.computeIfAbsent(groupKey, k -> {
                LabelingQualityCheckInfoVO vo = new LabelingQualityCheckInfoVO();
                vo.setLabelerMis(labelingDetail.getLabelerMis());
                vo.setBizType(labelingTask.getBizType());
                vo.setTaskType(labelingTask.getTaskType());
                vo.setDataType(labelingTask.getDataType());
                vo.setQcInspectedCount(0);
                vo.setQcInspectedCorrectCount(0);
                vo.setStatDate(DateUtil.parseToDate(DateUtil.formatDate(labelingDetail.getFirstLabelingTime())));
                return vo;
            });
            // 质检量和质检正确数量的的更新逻辑
            labelingQualityCheckInfoVO.setQcInspectedCount(labelingQualityCheckInfoVO.getQcInspectedCount() + 1);
            labelingQualityCheckInfoVO.setQcInspectedCorrectCount(labelingQualityCheckInfoVO.getQcInspectedCorrectCount() + (Objects.equals(qualityCheckedItem.getQualityCheckResult(), QualityCheckResultEnum.CORRECT.getCode()) ? 1 : 0));
        }
        return resultMap;
    }

}
