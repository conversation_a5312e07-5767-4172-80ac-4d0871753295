package com.meituan.aigc.aida.data.management.dataset.param;

import com.meituan.aigc.aida.data.management.dataset.dto.DataSourceField;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-05-23 16:00
 * @description
 */
@Data
public class DataSetCreateParam implements Serializable {
    /**
     * 数据集ID 首次为空
     */
    private Long dataSetId;
    /**
     * 数据传输锁，首次为空，获取后需要传入
     */
    private String lockId;
    /**
     * 是否最后一批，为true会释放锁，后续再无法使用
     */
    private Boolean isEnd;
    /**
     * 创建人mis
     */
    private String creatorMis;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 文件
     */
    private MultipartFile file;
    /**
     * 数据集名称
     */
    private String dataSetName;
    /**
     * 数据集描述
     */
    private String description;
    /**
     * 数据用途 1:模型训练/2:会话分析/3:自定义
     */
    private Integer usageCategory;
    /**
     * 模型训练用途类型1:文本生成/2:图像生成/3:图像理解
     */
    private Integer usageType;
    /**
     * 训练类型 1:sft/2:dpo/3:session维度
     */
    private Integer trainingType;
    /**
     * 自定义用途描述
     */
    private String taskDescription;
    /**
     * 数据来源 1:数据获取导入 2:文件上传 3: case分析
     */
    private Integer dataSource;
    /**
     * 数据获取任务id
     */
    private Long fetchId;
    /**
     * 数据源字段列表
     */
    private List<DataFieldParam> headList;
    /**
     * 数据列表
     */
    private List<DataSetRecordParam> dataList;
    /**
     * 目标字段
     */
    private Map<String, String> targetField;
    /**
     * 会话分组字段
     */
    private Map<String, String> sessionGroupingField;
}
