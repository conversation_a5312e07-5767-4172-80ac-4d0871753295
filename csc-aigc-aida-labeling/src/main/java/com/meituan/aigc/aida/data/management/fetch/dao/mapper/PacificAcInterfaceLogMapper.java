package com.meituan.aigc.aida.data.management.fetch.dao.mapper;

import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLog;
import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogExample;
import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PacificAcInterfaceLogMapper {
    long countByExample(PacificAcInterfaceLogExample example);

    int deleteByExample(PacificAcInterfaceLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PacificAcInterfaceLogWithBlobs record);

    int insertSelective(PacificAcInterfaceLogWithBlobs record);

    List<PacificAcInterfaceLogWithBlobs> selectByExampleWithBlobs(PacificAcInterfaceLogExample example);

    List<PacificAcInterfaceLog> selectByExample(PacificAcInterfaceLogExample example);

    PacificAcInterfaceLogWithBlobs selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") PacificAcInterfaceLogWithBlobs record, @Param("example") PacificAcInterfaceLogExample example);

    int updateByExampleWithBlobs(@Param("record") PacificAcInterfaceLogWithBlobs record, @Param("example") PacificAcInterfaceLogExample example);

    int updateByExample(@Param("record") PacificAcInterfaceLog record, @Param("example") PacificAcInterfaceLogExample example);

    int updateByPrimaryKeySelective(PacificAcInterfaceLogWithBlobs record);

    int updateByPrimaryKeyWithBlobs(PacificAcInterfaceLogWithBlobs record);

    int updateByPrimaryKey(PacificAcInterfaceLog record);

    List<PacificAcInterfaceLogWithBlobs> listByContactIdContactTypeAndStaffMis(@Param("contactId") String contactId, @Param("contactType") String contactType, @Param("staffMis") String staffMis);
}