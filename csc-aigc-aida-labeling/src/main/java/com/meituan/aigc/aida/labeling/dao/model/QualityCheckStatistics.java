package com.meituan.aigc.aida.labeling.dao.model;

import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.common.enums.LabelingTaskType;
import lombok.Data;

import java.util.Date;

@Data
public class QualityCheckStatistics {

    /**
     * 主键
     */
    private Long id;
    /**
     * 添加日期
     */
    private Date statDate;
    /**
     * 业务类型
     */
    private Long bizType;
    /**
     * 任务类型 {@link LabelingTaskType}
     */
    private Integer taskType;
    /**
     * 数据类型 {@link LabelTaskDataType}
     */
    private Integer dataType;
    /**
     * 质检员姓名
     */
    private String qualityCheckerName;
    /**
     * 质检员mis
     */
    private String qualityCheckerMis;
    /**
     * 质检数量
     */
    private Integer qualityCheckCount;
    /**
     * 质检正确数量
     */
    private Integer qualityCheckCorrectCount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}