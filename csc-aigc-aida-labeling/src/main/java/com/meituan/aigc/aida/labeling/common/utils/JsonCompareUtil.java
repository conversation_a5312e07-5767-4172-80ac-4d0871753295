package com.meituan.aigc.aida.labeling.common.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.*;

/**
 * @Author: guowenhui
 * @Create: 2025/3/4 20:18
 * @Version: 1.0
 * JSON 比较工具类
 * 用于比较两个 JSON 字符串或对象是否一致
 */
@Slf4j
public class JsonCompareUtil {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 私有化构造方法
     */
    private JsonCompareUtil(){}

    /**
     * 比较两个 JSON 字符串是否一致
     *
     * @param json1 第一个 JSON 字符串
     * @param json2 第二个 JSON 字符串
     * @return 如果一致返回 true，否则返回 false
     */
    public static boolean areEqual(String json1, String json2) {
        try {
            JsonNode tree1 = OBJECT_MAPPER.readTree(json1);
            JsonNode tree2 = OBJECT_MAPPER.readTree(json2);
            return areEqual(tree1, tree2);
        } catch (IOException e) {
            log.error("解析 JSON 字符串时出错", e);
            return false;
        }
    }

    /**
     * 比较两个 JSON 对象是否一致
     *
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @return 如果一致返回 true，否则返回 false
     */
    public static boolean areEqual(Object obj1, Object obj2) {
        try {
            JsonNode tree1 = OBJECT_MAPPER.valueToTree(obj1);
            JsonNode tree2 = OBJECT_MAPPER.valueToTree(obj2);
            return areEqual(tree1, tree2);
        } catch (Exception e) {
            log.error("转换对象为 JSON 时出错", e);
            return false;
        }
    }

    /**
     * 比较两个 JsonNode 是否一致
     *
     * @param node1 第一个 JsonNode
     * @param node2 第二个 JsonNode
     * @return 如果一致返回 true，否则返回 false
     */
    public static boolean areEqual(JsonNode node1, JsonNode node2) {
        // 如果两个节点类型不同，则不相等
        if (node1.getNodeType() != node2.getNodeType()) {
            return false;
        }

        // 根据节点类型进行比较
        switch (node1.getNodeType()) {
            case OBJECT:
                return compareObjects((ObjectNode) node1, (ObjectNode) node2);
            case ARRAY:
                return compareArrays((ArrayNode) node1, (ArrayNode) node2);
            default:
                // 对于基本类型节点，直接比较值
                return node1.equals(node2);
        }
    }

    /**
     * 比较两个 JSON 对象节点是否一致
     *
     * @param obj1 第一个对象节点
     * @param obj2 第二个对象节点
     * @return 如果一致返回 true，否则返回 false
     */
    private static boolean compareObjects(ObjectNode obj1, ObjectNode obj2) {
        // 如果字段数不同，则不相等
        if (obj1.size() != obj2.size()) {
            return false;
        }

        // 获取所有字段名
        Iterator<String> fieldNames = obj1.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();

            // 如果第二个对象不包含该字段，则不相等
            if (!obj2.has(fieldName)) {
                return false;
            }

            // 递归比较字段值
            if (!areEqual(obj1.get(fieldName), obj2.get(fieldName))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 比较两个 JSON 数组节点是否一致
     *
     * @param arr1 第一个数组节点
     * @param arr2 第二个数组节点
     * @return 如果一致返回 true，否则返回 false
     */
    private static boolean compareArrays(ArrayNode arr1, ArrayNode arr2) {
        // 如果数组长度不同，则不相等
        if (arr1.size() != arr2.size()) {
            return false;
        }

        // 对于简单类型的数组，可以排序后比较
        if (!arr1.isEmpty() && isSimpleValueArray(arr1)) {
            return compareSimpleArrays(arr1, arr2);
        }

        // 对于复杂类型的数组，需要考虑顺序
        for (int i = 0; i < arr1.size(); i++) {
            if (!areEqual(arr1.get(i), arr2.get(i))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 比较两个简单类型的数组是否包含相同的元素（忽略顺序）
     *
     * @param arr1 第一个数组节点
     * @param arr2 第二个数组节点
     * @return 如果包含相同元素返回 true，否则返回 false
     */
    private static boolean compareSimpleArrays(ArrayNode arr1, ArrayNode arr2) {
        // 将数组元素转换为列表
        List<String> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();

        for (JsonNode node : arr1) {
            list1.add(node.toString());
        }

        for (JsonNode node : arr2) {
            list2.add(node.toString());
        }

        // 排序后比较
        Collections.sort(list1);
        Collections.sort(list2);

        return list1.equals(list2);
    }

    /**
     * 判断数组是否只包含简单值（数字、字符串、布尔值、null）
     *
     * @param arr 数组节点
     * @return 如果只包含简单值返回 true，否则返回 false
     */
    private static boolean isSimpleValueArray(ArrayNode arr) {
        for (JsonNode node : arr) {
            if (node.isObject() || node.isArray()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 比较两个 JSON 字符串并返回差异
     *
     * @param json1 第一个 JSON 字符串
     * @param json2 第二个 JSON 字符串
     * @return 差异列表
     */
    public static List<String> getDifferences(String json1, String json2) {
        try {
            JsonNode tree1 = OBJECT_MAPPER.readTree(json1);
            JsonNode tree2 = OBJECT_MAPPER.readTree(json2);
            List<String> differences = new ArrayList<>();
            findDifferences(tree1, tree2, "", differences);
            return differences;
        } catch (IOException e) {
            log.error("解析 JSON 字符串时出错", e);
            List<String> errors = new ArrayList<>();
            errors.add("解析 JSON 出错: " + e.getMessage());
            return errors;
        }
    }

    /**
     * 查找两个 JsonNode 之间的差异
     *
     * @param node1 第一个 JsonNode
     * @param node2 第二个 JsonNode
     * @param path 当前路径
     * @param differences 差异列表
     */
    private static void findDifferences(JsonNode node1, JsonNode node2, String path, List<String> differences) {
        // 如果节点类型不同
        if (node1.getNodeType() != node2.getNodeType()) {
            differences.add(String.format("路径 '%s' 的类型不同: %s vs %s",
                    path, node1.getNodeType(), node2.getNodeType()));
            return;
        }

        // 根据节点类型查找差异
        switch (node1.getNodeType()) {
            case OBJECT:
                findObjectDifferences((ObjectNode) node1, (ObjectNode) node2, path, differences);
                break;
            case ARRAY:
                findArrayDifferences((ArrayNode) node1, (ArrayNode) node2, path, differences);
                break;
            default:
                // 对于基本类型节点，直接比较值
                if (!node1.equals(node2)) {
                    differences.add(String.format("路径 '%s' 的值不同: %s vs %s",
                            path, node1, node2));
                }
                break;
        }
    }

    /**
     * 查找两个对象节点之间的差异
     *
     * @param obj1 第一个对象节点
     * @param obj2 第二个对象节点
     * @param path 当前路径
     * @param differences 差异列表
     */
    private static void findObjectDifferences(ObjectNode obj1, ObjectNode obj2, String path, List<String> differences) {
        // 检查 obj1 中存在但 obj2 中不存在的字段
        Iterator<String> fieldNames1 = obj1.fieldNames();
        while (fieldNames1.hasNext()) {
            String fieldName = fieldNames1.next();
            String fieldPath = path.isEmpty() ? fieldName : path + "." + fieldName;

            if (!obj2.has(fieldName)) {
                differences.add(String.format("字段 '%s' 在第二个 JSON 中不存在", fieldPath));
            } else {
                // 递归比较字段值
                findDifferences(obj1.get(fieldName), obj2.get(fieldName), fieldPath, differences);
            }
        }

        // 检查 obj2 中存在但 obj1 中不存在的字段
        Iterator<String> fieldNames2 = obj2.fieldNames();
        while (fieldNames2.hasNext()) {
            String fieldName = fieldNames2.next();
            String fieldPath = path.isEmpty() ? fieldName : path + "." + fieldName;

            if (!obj1.has(fieldName)) {
                differences.add(String.format("字段 '%s' 在第一个 JSON 中不存在", fieldPath));
            }
        }
    }

    /**
     * 查找两个数组节点之间的差异
     *
     * @param arr1 第一个数组节点
     * @param arr2 第二个数组节点
     * @param path 当前路径
     * @param differences 差异列表
     */
    private static void findArrayDifferences(ArrayNode arr1, ArrayNode arr2, String path, List<String> differences) {
        // 如果数组长度不同
        if (arr1.size() != arr2.size()) {
            differences.add(String.format("数组 '%s' 的长度不同: %d vs %d",
                    path, arr1.size(), arr2.size()));
        }

        // 对于简单类型的数组，可以比较元素集合
        if (!arr1.isEmpty() && isSimpleValueArray(arr1) && isSimpleValueArray(arr2)) {
            findSimpleArrayDifferences(arr1, arr2, path, differences);
            return;
        }

        // 对于复杂类型的数组，逐个比较元素
        int minSize = Math.min(arr1.size(), arr2.size());
        for (int i = 0; i < minSize; i++) {
            String indexPath = path + "[" + i + "]";
            findDifferences(arr1.get(i), arr2.get(i), indexPath, differences);
        }
    }

    /**
     * 查找两个简单类型数组之间的差异
     *
     * @param arr1 第一个数组节点
     * @param arr2 第二个数组节点
     * @param path 当前路径
     * @param differences 差异列表
     */
    private static void findSimpleArrayDifferences(ArrayNode arr1, ArrayNode arr2, String path, List<String> differences) {
        // 将数组元素转换为集合
        Set<String> set1 = new HashSet<>();
        Set<String> set2 = new HashSet<>();

        for (JsonNode node : arr1) {
            set1.add(node.toString());
        }

        for (JsonNode node : arr2) {
            set2.add(node.toString());
        }

        // 查找 set1 中有但 set2 中没有的元素
        Set<String> onlyInFirst = new HashSet<>(set1);
        onlyInFirst.removeAll(set2);
        if (!onlyInFirst.isEmpty()) {
            differences.add(String.format("数组 '%s' 中，第一个 JSON 独有的元素: %s",
                    path, onlyInFirst));
        }

        // 查找 set2 中有但 set1 中没有的元素
        Set<String> onlyInSecond = new HashSet<>(set2);
        onlyInSecond.removeAll(set1);
        if (!onlyInSecond.isEmpty()) {
            differences.add(String.format("数组 '%s' 中，第二个 JSON 独有的元素: %s",
                    path, onlyInSecond));
        }
    }
}