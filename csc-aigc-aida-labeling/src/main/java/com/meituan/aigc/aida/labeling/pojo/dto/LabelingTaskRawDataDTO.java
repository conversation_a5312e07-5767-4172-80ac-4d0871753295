package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2025-03-10 16:57
 * @description
 */
@Data
public class LabelingTaskRawDataDTO {

    /**
     * 主键ID
     */
    private Long dataId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 会话时间
     */
    private Date sessionTime;

    /**
     * 大模型消息ID
     */
    private String messageId;

    /**
     * 原始数据内容
     */
    private String rawDataContent;

    /**
     * 原始数据内容映射内容
     */
    private String rawDataMappedContent;

}
