package com.meituan.aigc.aida.data.management.dataset.dao.model;

import java.util.Date;

public class DataProcessTask {
    private Long id;

    private String taskName;

    private Long datasetId;

    private Long processDatasetVersionId;

    private Long originalDatasetVersionId;

    private Byte status;

    private Integer totalCount;

    private String creatorMis;

    private String creatorName;

    private Date createTime;

    private Date updateTime;

    private String extraInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName == null ? null : taskName.trim();
    }

    public Long getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(Long datasetId) {
        this.datasetId = datasetId;
    }

    public Long getProcessDatasetVersionId() {
        return processDatasetVersionId;
    }

    public void setProcessDatasetVersionId(Long processDatasetVersionId) {
        this.processDatasetVersionId = processDatasetVersionId;
    }

    public Long getOriginalDatasetVersionId() {
        return originalDatasetVersionId;
    }

    public void setOriginalDatasetVersionId(Long originalDatasetVersionId) {
        this.originalDatasetVersionId = originalDatasetVersionId;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getCreatorMis() {
        return creatorMis;
    }

    public void setCreatorMis(String creatorMis) {
        this.creatorMis = creatorMis == null ? null : creatorMis.trim();
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo == null ? null : extraInfo.trim();
    }
}