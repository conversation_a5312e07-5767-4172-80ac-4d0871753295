package com.meituan.aigc.aida.labeling.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: guowenhui
 * @Create: 2025/3/7 14:41
 * @Version: 1.0
 */
@Data
public class LabelingDetailBySessionQueryParam implements Serializable {

    /**
     * 子任务ID
     */
    @NotNull(message = "请选择任务")
    private Long subTaskId;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 标注状态
     * {@link com.meituan.aigc.aida.labeling.common.enums.LabelingDetailStatus}
     */
    private Integer labelStatus;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;

}
