<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.OperationLogsMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.OperationLogs">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operate_type" jdbcType="TINYINT" property="operateType" />
    <result column="operate_data_id" jdbcType="BIGINT" property="operateDataId" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="before_data" jdbcType="CHAR" property="beforeData" />
    <result column="after_data" jdbcType="CHAR" property="afterData" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.meituan.aigc.aida.labeling.dao.model.OperationLogs">
    <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, operator, operate_type, operate_data_id, biz_type, before_data, after_data, tenant_id, 
    create_time
  </sql>
  <sql id="Blob_Column_List">
    extra_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogsExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from operation_logs
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from operation_logs
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from operation_logs
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from operation_logs
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogsExample">
    delete from operation_logs
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogs">
    insert into operation_logs (id, operator, operate_type, 
      operate_data_id, biz_type, before_data, 
      after_data, tenant_id, create_time, 
      extra_info)
    values (#{id,jdbcType=BIGINT}, #{operator,jdbcType=VARCHAR}, #{operateType,jdbcType=TINYINT}, 
      #{operateDataId,jdbcType=BIGINT}, #{bizType,jdbcType=TINYINT}, #{beforeData,jdbcType=CHAR}, 
      #{afterData,jdbcType=CHAR}, #{tenantId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{extraInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogs">
    insert into operation_logs
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operateDataId != null">
        operate_data_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="beforeData != null">
        before_data,
      </if>
      <if test="afterData != null">
        after_data,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operateDataId != null">
        #{operateDataId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="beforeData != null">
        #{beforeData,jdbcType=CHAR},
      </if>
      <if test="afterData != null">
        #{afterData,jdbcType=CHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogsExample" resultType="java.lang.Long">
    select count(*) from operation_logs
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update operation_logs
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=TINYINT},
      </if>
      <if test="record.operateDataId != null">
        operate_data_id = #{record.operateDataId,jdbcType=BIGINT},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=TINYINT},
      </if>
      <if test="record.beforeData != null">
        before_data = #{record.beforeData,jdbcType=CHAR},
      </if>
      <if test="record.afterData != null">
        after_data = #{record.afterData,jdbcType=CHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update operation_logs
    set id = #{record.id,jdbcType=BIGINT},
      operator = #{record.operator,jdbcType=VARCHAR},
      operate_type = #{record.operateType,jdbcType=TINYINT},
      operate_data_id = #{record.operateDataId,jdbcType=BIGINT},
      biz_type = #{record.bizType,jdbcType=TINYINT},
      before_data = #{record.beforeData,jdbcType=CHAR},
      after_data = #{record.afterData,jdbcType=CHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update operation_logs
    set id = #{record.id,jdbcType=BIGINT},
      operator = #{record.operator,jdbcType=VARCHAR},
      operate_type = #{record.operateType,jdbcType=TINYINT},
      operate_data_id = #{record.operateDataId,jdbcType=BIGINT},
      biz_type = #{record.bizType,jdbcType=TINYINT},
      before_data = #{record.beforeData,jdbcType=CHAR},
      after_data = #{record.afterData,jdbcType=CHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogs">
    update operation_logs
    <set>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operateDataId != null">
        operate_data_id = #{operateDataId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="beforeData != null">
        before_data = #{beforeData,jdbcType=CHAR},
      </if>
      <if test="afterData != null">
        after_data = #{afterData,jdbcType=CHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogs">
    update operation_logs
    set operator = #{operator,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=TINYINT},
      operate_data_id = #{operateDataId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=TINYINT},
      before_data = #{beforeData,jdbcType=CHAR},
      after_data = #{afterData,jdbcType=CHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      extra_info = #{extraInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.OperationLogs">
    update operation_logs
    set operator = #{operator,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=TINYINT},
      operate_data_id = #{operateDataId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=TINYINT},
      before_data = #{beforeData,jdbcType=CHAR},
      after_data = #{afterData,jdbcType=CHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>