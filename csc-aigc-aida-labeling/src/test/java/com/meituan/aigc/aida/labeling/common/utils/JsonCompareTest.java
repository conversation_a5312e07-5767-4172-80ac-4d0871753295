package com.meituan.aigc.aida.labeling.common.utils;

import java.util.List;

/**
 * @Author: guo<PERSON><PERSON>
 * @Create: 2025/3/5 14:14
 * @Version: 1.0
 */
public class JsonCompareTest {

    public static void main(String[] args) {
        // 示例1：完全相同的JSON
        String json1 = "{\"name\":\"张三\",\"age\":30,\"skills\":[\"Java\",\"Python\"]}";
        String json2 = "{\"age\":30,\"name\":\"张三\",\"skills\":[\"Python\",\"Java\"]}";

        boolean areEqual = JsonCompareUtil.areEqual(json1, json2);
        System.out.println("示例1 - JSON是否相等: " + areEqual);

        // 示例2：有差异的JSON
        String json3 = "{\"name\":\"张三\",\"age\":30,\"skills\":[\"Java\",\"Python\"],\"address\":\"北京\"}";
        String json4 = "{\"name\":\"张三\",\"age\":31,\"skills\":[\"Java\",\"C++\"],\"email\":\"<EMAIL>\"}";

        boolean areEqual2 = JsonCompareUtil.areEqual(json3, json4);
        System.out.println("示例2 - JSON是否相等: " + areEqual2);

        // 获取差异
        List<String> differences = JsonCompareUtil.getDifferences(json3, json4);
        System.out.println("差异列表:");
        for (String diff : differences) {
            System.out.println("- " + diff);
        }

        // 示例3：嵌套对象比较
        String json5 = "{\"person\":{\"name\":\"李四\",\"contacts\":{\"phone\":\"123456\",\"email\":\"<EMAIL>\"}}}";
        String json6 = "{\"person\":{\"name\":\"李四\",\"contacts\":{\"email\":\"<EMAIL>\",\"phone\":\"123456\"}}}";

        boolean areEqual3 = JsonCompareUtil.areEqual(json5, json6);
        System.out.println("示例3 - JSON是否相等: " + areEqual3);

        // 示例4：数组顺序不同
        String json7 = "{\"data\":[{\"id\":1,\"value\":\"A\"},{\"id\":2,\"value\":\"B\"}]}";
        String json8 = "{\"data\":[{\"id\":2,\"value\":\"B\"},{\"id\":1,\"value\":\"A\"}]}";

        boolean areEqual4 = JsonCompareUtil.areEqual(json7, json8);
        System.out.println("示例4 - JSON是否相等: " + areEqual4);

        // 获取差异
        List<String> differences2 = JsonCompareUtil.getDifferences(json7, json8);
        System.out.println("差异列表:");
        for (String diff : differences2) {
            System.out.println("- " + diff);
        }
    }
}