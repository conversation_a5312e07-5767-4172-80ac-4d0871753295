package com.meituan.aigc.aida.labeling.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabelingQualityCheckTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LabelingQualityCheckTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andSampleSizeIsNull() {
            addCriterion("sample_size is null");
            return (Criteria) this;
        }

        public Criteria andSampleSizeIsNotNull() {
            addCriterion("sample_size is not null");
            return (Criteria) this;
        }

        public Criteria andSampleSizeEqualTo(Integer value) {
            addCriterion("sample_size =", value, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeNotEqualTo(Integer value) {
            addCriterion("sample_size <>", value, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeGreaterThan(Integer value) {
            addCriterion("sample_size >", value, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sample_size >=", value, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeLessThan(Integer value) {
            addCriterion("sample_size <", value, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeLessThanOrEqualTo(Integer value) {
            addCriterion("sample_size <=", value, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeIn(List<Integer> values) {
            addCriterion("sample_size in", values, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeNotIn(List<Integer> values) {
            addCriterion("sample_size not in", values, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeBetween(Integer value1, Integer value2) {
            addCriterion("sample_size between", value1, value2, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andSampleSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("sample_size not between", value1, value2, "sampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeIsNull() {
            addCriterion("raw_data_sample_size is null");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeIsNotNull() {
            addCriterion("raw_data_sample_size is not null");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeEqualTo(Integer value) {
            addCriterion("raw_data_sample_size =", value, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeNotEqualTo(Integer value) {
            addCriterion("raw_data_sample_size <>", value, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeGreaterThan(Integer value) {
            addCriterion("raw_data_sample_size >", value, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("raw_data_sample_size >=", value, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeLessThan(Integer value) {
            addCriterion("raw_data_sample_size <", value, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeLessThanOrEqualTo(Integer value) {
            addCriterion("raw_data_sample_size <=", value, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeIn(List<Integer> values) {
            addCriterion("raw_data_sample_size in", values, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeNotIn(List<Integer> values) {
            addCriterion("raw_data_sample_size not in", values, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeBetween(Integer value1, Integer value2) {
            addCriterion("raw_data_sample_size between", value1, value2, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andRawDataSampleSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("raw_data_sample_size not between", value1, value2, "rawDataSampleSize");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigIsNull() {
            addCriterion("quality_check_config is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigIsNotNull() {
            addCriterion("quality_check_config is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigEqualTo(String value) {
            addCriterion("quality_check_config =", value, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigNotEqualTo(String value) {
            addCriterion("quality_check_config <>", value, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigGreaterThan(String value) {
            addCriterion("quality_check_config >", value, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigGreaterThanOrEqualTo(String value) {
            addCriterion("quality_check_config >=", value, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigLessThan(String value) {
            addCriterion("quality_check_config <", value, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigLessThanOrEqualTo(String value) {
            addCriterion("quality_check_config <=", value, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigLike(String value) {
            addCriterion("quality_check_config like", value, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigNotLike(String value) {
            addCriterion("quality_check_config not like", value, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigIn(List<String> values) {
            addCriterion("quality_check_config in", values, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigNotIn(List<String> values) {
            addCriterion("quality_check_config not in", values, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigBetween(String value1, String value2) {
            addCriterion("quality_check_config between", value1, value2, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andQualityCheckConfigNotBetween(String value1, String value2) {
            addCriterion("quality_check_config not between", value1, value2, "qualityCheckConfig");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisIsNull() {
            addCriterion("quality_check_mis is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisIsNotNull() {
            addCriterion("quality_check_mis is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisEqualTo(String value) {
            addCriterion("quality_check_mis =", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisNotEqualTo(String value) {
            addCriterion("quality_check_mis <>", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisGreaterThan(String value) {
            addCriterion("quality_check_mis >", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisGreaterThanOrEqualTo(String value) {
            addCriterion("quality_check_mis >=", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisLessThan(String value) {
            addCriterion("quality_check_mis <", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisLessThanOrEqualTo(String value) {
            addCriterion("quality_check_mis <=", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisLike(String value) {
            addCriterion("quality_check_mis like", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisNotLike(String value) {
            addCriterion("quality_check_mis not like", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisIn(List<String> values) {
            addCriterion("quality_check_mis in", values, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisNotIn(List<String> values) {
            addCriterion("quality_check_mis not in", values, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisBetween(String value1, String value2) {
            addCriterion("quality_check_mis between", value1, value2, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisNotBetween(String value1, String value2) {
            addCriterion("quality_check_mis not between", value1, value2, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameIsNull() {
            addCriterion("quality_check_name is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameIsNotNull() {
            addCriterion("quality_check_name is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameEqualTo(String value) {
            addCriterion("quality_check_name =", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameNotEqualTo(String value) {
            addCriterion("quality_check_name <>", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameGreaterThan(String value) {
            addCriterion("quality_check_name >", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameGreaterThanOrEqualTo(String value) {
            addCriterion("quality_check_name >=", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameLessThan(String value) {
            addCriterion("quality_check_name <", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameLessThanOrEqualTo(String value) {
            addCriterion("quality_check_name <=", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameLike(String value) {
            addCriterion("quality_check_name like", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameNotLike(String value) {
            addCriterion("quality_check_name not like", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameIn(List<String> values) {
            addCriterion("quality_check_name in", values, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameNotIn(List<String> values) {
            addCriterion("quality_check_name not in", values, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameBetween(String value1, String value2) {
            addCriterion("quality_check_name between", value1, value2, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameNotBetween(String value1, String value2) {
            addCriterion("quality_check_name not between", value1, value2, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andCreateMisIsNull() {
            addCriterion("create_mis is null");
            return (Criteria) this;
        }

        public Criteria andCreateMisIsNotNull() {
            addCriterion("create_mis is not null");
            return (Criteria) this;
        }

        public Criteria andCreateMisEqualTo(String value) {
            addCriterion("create_mis =", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisNotEqualTo(String value) {
            addCriterion("create_mis <>", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisGreaterThan(String value) {
            addCriterion("create_mis >", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisGreaterThanOrEqualTo(String value) {
            addCriterion("create_mis >=", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisLessThan(String value) {
            addCriterion("create_mis <", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisLessThanOrEqualTo(String value) {
            addCriterion("create_mis <=", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisLike(String value) {
            addCriterion("create_mis like", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisNotLike(String value) {
            addCriterion("create_mis not like", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisIn(List<String> values) {
            addCriterion("create_mis in", values, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisNotIn(List<String> values) {
            addCriterion("create_mis not in", values, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisBetween(String value1, String value2) {
            addCriterion("create_mis between", value1, value2, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisNotBetween(String value1, String value2) {
            addCriterion("create_mis not between", value1, value2, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andAssignTypeIsNull() {
            addCriterion("assign_type is null");
            return (Criteria) this;
        }

        public Criteria andAssignTypeIsNotNull() {
            addCriterion("assign_type is not null");
            return (Criteria) this;
        }

        public Criteria andAssignTypeEqualTo(Integer value) {
            addCriterion("assign_type =", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeNotEqualTo(Integer value) {
            addCriterion("assign_type <>", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeGreaterThan(Integer value) {
            addCriterion("assign_type >", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("assign_type >=", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeLessThan(Integer value) {
            addCriterion("assign_type <", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeLessThanOrEqualTo(Integer value) {
            addCriterion("assign_type <=", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeIn(List<Integer> values) {
            addCriterion("assign_type in", values, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeNotIn(List<Integer> values) {
            addCriterion("assign_type not in", values, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeBetween(Integer value1, Integer value2) {
            addCriterion("assign_type between", value1, value2, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("assign_type not between", value1, value2, "assignType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}