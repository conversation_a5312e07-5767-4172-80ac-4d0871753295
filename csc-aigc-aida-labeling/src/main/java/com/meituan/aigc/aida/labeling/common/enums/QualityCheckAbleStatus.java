package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON>
 * @Create: 2025/3/31 14:16
 * @Version: 1.0
 * 质检数据详情可检状态
 */
@Getter
public enum QualityCheckAbleStatus {
    CHECK_ABLE(1, "可检"),
    UNDETECTABLE(2, "不可检");

    private final int code;
    private final String value;

    QualityCheckAbleStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static QualityCheckAbleStatus getByCode(int code) {
        for (QualityCheckAbleStatus status : QualityCheckAbleStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
