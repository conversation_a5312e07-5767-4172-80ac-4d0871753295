package com.meituan.aigc.aida.data.management.fetch.common.enums;


import lombok.Getter;

/**
 * 渠道枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ChannelEnum {
    ONLINE("online", "在线渠道"),
    CALL("call", "电话渠道");

    private final String code;
    private final String name;

    ChannelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ChannelEnum findByCode(String code) {
        for (ChannelEnum skillType : ChannelEnum.values()) {
            if (skillType.getCode().equals(code)) {
                return skillType;
            }
        }
        return null;
    }
}
