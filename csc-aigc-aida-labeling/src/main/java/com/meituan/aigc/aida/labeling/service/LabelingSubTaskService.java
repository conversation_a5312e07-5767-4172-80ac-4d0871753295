package com.meituan.aigc.aida.labeling.service;


import com.meituan.aigc.aida.labeling.param.*;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingDetailDataDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingSubTaskListDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.quality.check.TaskConditionDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelDetailNumCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingDetailBySessionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-03 15:54
 * @description 数据标注服务
 */
public interface LabelingSubTaskService {
    /**
     * 转交标注任务
     *
     * @param param
     */
    void transfer(LabelingSubTaskTransferParam param);

    /**
     * 分页查询标注详情数据
     *
     * @param param
     * @return
     */
    LabelingDetailDataDTO pageDetailData(LabelingDetailParam param);

    /**
     * 保存标注结果
     *
     * @param param 参数
     */
    void saveLabelingResult(LabelingResultSaveParam param);

    /**
     * 查询子任务列表
     *
     * @param param 此任务列表参数
     * @return 子任务列表
     */
    PageData<LabelingSubTaskListDTO> subTasks(LabelingListParam param);

    PageData<LabelingDetailBySessionVO> pageBySession(LabelingDetailBySessionQueryParam param);

    LabelDetailNumCountVO countNumBySubTask(Long subTaskId);

    /**
     * 获取标注子任务下可筛选的条件
     *
     * @param labelingSubTaskId 标注子任务ID
     * @return 可筛选条件
     */
    List<TaskConditionDTO> getLabelingCondition(Long labelingSubTaskId);

    /**
     * 保存上下文
     *
     * @param param 保存参数
     */
    void saveContext(LabelingTaskContextParam param);
}
