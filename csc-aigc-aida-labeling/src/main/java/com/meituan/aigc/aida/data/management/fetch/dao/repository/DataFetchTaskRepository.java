package com.meituan.aigc.aida.data.management.fetch.dao.repository;

import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/7 15:51
 * @Version: 1.0
 */
public interface DataFetchTaskRepository {

    /**
     * 新增数据拉取任务
     *
     * @param dataFetchTask 任务详情
     */
    void addDataFetchTask(DataFetchTask dataFetchTask);

    /**
     * 查询任务列表
     *
     * @param taskName 任务名称
     * @return 任务列表
     */
    List<DataFetchTask> listTask(String taskName);

    /**
     * 根据任务id查询任务信息
     *
     * @param taskId 任务id
     * @return 任务信息
     */
    DataFetchTask getTaskById(Long taskId);

    /**
     * 根据主键更新非空字段
     *
     * @param dataFetchTask 更新对象
     */
    void updateByIdSelective(DataFetchTask dataFetchTask);

}
