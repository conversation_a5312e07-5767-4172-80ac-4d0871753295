package com.meituan.aigc.aida.data.management.dataset.controller;

import com.meituan.aigc.aida.data.management.dataset.dto.DataSetVersionDTO;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetVersionParam;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetVersionService;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-04 16:54
 * @description 数据集版本控制类
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/DataSet/version")
public class DataSetVersionController {

    @Autowired
    private DataSetVersionService dataSetVersionService;

    /**
     * 新增数据集版本
     *
     * @param param 版本参数
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Void> addVersion(@RequestBody DataSetVersionParam param) {
        log.info("新增数据集版本, 参数: {}", param);
        dataSetVersionService.addVersion(param);
        return Result.create();
    }

    /**
     * 删除数据集版本
     *
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    public Result<Void> deleteVersion(@RequestParam("dataSetId") Long dataSetId, @RequestParam("versionId") Long versionId) {
        log.info("删除数据集版本, dataSetId: {}, versionId: {}", dataSetId, versionId);
        dataSetVersionService.deleteVersion(dataSetId, versionId);
        return Result.create();
    }

    /**
     * 修改版本名称
     *
     * @param dataSetId   数据集ID
     * @param versionId   版本ID
     * @param versionName 版本名称
     * @return 操作结果
     */
    @PutMapping("/update/name")
    public Result<Void> updateVersionName(@RequestParam("dataSetId") Long dataSetId, 
                                         @RequestParam("versionId") Long versionId, 
                                         @RequestParam("versionName") String versionName) {
        log.info("修改版本名称, dataSetId: {}, versionId: {}, versionName: {}", dataSetId, versionId, versionName);
        dataSetVersionService.updateVersionName(dataSetId, versionId, versionName);
        return Result.create();
    }

    /**
     * 获取数据集版本列表
     *
     * @param dataSetId 数据集ID
     * @return 版本列表
     */
    @GetMapping("/list")
    public Result<List<DataSetVersionDTO>> listVersion(@RequestParam("dataSetId") Long dataSetId) {
        log.info("获取数据集版本列表, dataSetId: {}", dataSetId);
        List<DataSetVersionDTO> versionList = dataSetVersionService.listVersion(dataSetId);
        return Result.ok(versionList);
    }

    /**
     * 获取数据集版本详情
     *
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @return 版本详情
     */
    @GetMapping("/detail")
    public Result<DataSetVersionDTO> getVersionDetail(@RequestParam("dataSetId") Long dataSetId, 
                                                    @RequestParam("versionId") Long versionId) {
        log.info("获取数据集版本详情, dataSetId: {}, versionId: {}", dataSetId, versionId);
        DataSetVersionDTO versionDTO = dataSetVersionService.getByDataSetIdAndVersionId(dataSetId, versionId);
        return Result.ok(versionDTO);
    }
}
