package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTaskExample;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskPO;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubtaskCountPo;
import com.meituan.aigc.aida.labeling.param.LabelingListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LabelingSubTaskMapper {
    long countByExample(LabelingSubTaskExample example);

    int deleteByExample(LabelingSubTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LabelingSubTask record);

    int insertSelective(LabelingSubTask record);

    List<LabelingSubTask> selectByExample(LabelingSubTaskExample example);

    LabelingSubTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LabelingSubTask record, @Param("example") LabelingSubTaskExample example);

    int updateByExample(@Param("record") LabelingSubTask record, @Param("example") LabelingSubTaskExample example);

    int updateByPrimaryKeySelective(LabelingSubTask record);

    int updateByPrimaryKey(LabelingSubTask record);

    List<LabelingSubTaskPO> listByTaskIdAndNameAndLabelerMis(LabelingListParam param);

    void batchInsertSubTasks(List<LabelingSubTask> subTaskList);

    /**
     * 子任务统计信息
     *
     * @param taskId 任务id
     * @return 子任务统计信息
     */
    List<LabelingSubtaskCountPo> countSubTask(@Param("taskId") Long taskId);

    Integer countGroupNumByQualityCheckItemIds(@Param("checkItemIds") List<Long> checkItemIds);

    List<LabelingSubTask> listByIds(List<Long> subTaskIds);
}