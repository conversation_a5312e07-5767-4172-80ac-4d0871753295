package com.meituan.aigc.aida.labeling.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AdvancedFilter implements Serializable {
    /**
     * 操作符
     *
     * @see com.meituan.aigc.aida.labeling.common.enums.ConditionOperateEnum
     */
    private String operator;
    /**
     * 字段值
     */
    private String value;

    /**
     * 多选的匹配字段
     */
    private List<Field> fieldList;

    @Data
    public static class Field implements Serializable {
        /**
         * 字段名称
         *
         * @see com.meituan.aigc.aida.labeling.common.enums.ConditionFieldEnum
         */
        private String field;
        /**
         * 字段类型
         *
         * @see com.meituan.aigc.aida.labeling.common.enums.ConditionFieldTypeEnum
         */
        private Integer fieldType;
    }
}
