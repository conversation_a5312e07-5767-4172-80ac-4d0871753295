package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * 质检概览VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityCheckOverviewVO {

    /**
     * 抽检数量
     */
    private Integer sampleData;

    /**
     * 已完成数量
     */
    private Integer completeData;

    /**
     * 质检分组详情
     */
    private List<QualityCheckGroupItemsVO> qualityCheckGroupItems;
}
