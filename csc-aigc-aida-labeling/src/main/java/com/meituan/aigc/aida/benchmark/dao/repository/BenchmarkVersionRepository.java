package com.meituan.aigc.aida.benchmark.dao.repository;

import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion;

import java.util.List;

/**
 * Benchmark版本Repository
 * 
 * <AUTHOR>
 * @description Benchmark版本数据访问层接口
 * @date 2025/6/24
 */
public interface BenchmarkVersionRepository {

    /**
     * 查询所有Benchmark版本信息
     * 按创建时间倒序排列
     * 
     * @return Benchmark版本列表
     */
    List<BenchmarkVersion> listAllBenchmarkVersions();

    /**
     * 根据状态查询Benchmark版本信息
     * 
     * @param status 版本状态 (0-未发布, 1-已发布)
     * @return Benchmark版本列表
     */
    List<BenchmarkVersion> listBenchmarkVersionsByStatus(Integer status);

    /**
     * 根据版本名称模糊查询Benchmark版本信息
     * 
     * @param versionName 版本名称(支持模糊查询)
     * @return Benchmark版本列表
     */
    List<BenchmarkVersion> listBenchmarkVersionsByName(String versionName);

    /**
     * 根据ID查询Benchmark版本信息
     * 
     * @param id 版本ID
     * @return Benchmark版本信息
     */
    BenchmarkVersion getBenchmarkVersionById(Long id);
}
