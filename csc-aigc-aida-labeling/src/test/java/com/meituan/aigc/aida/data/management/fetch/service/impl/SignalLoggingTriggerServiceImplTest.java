package com.meituan.aigc.aida.data.management.fetch.service.impl;

import com.meituan.aigc.aida.labeling.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.meituan.aigc.aida.data.management.fetch.service.SignalLoggingTriggerService;

import lombok.extern.log4j.Log4j;

/**
 *
 * <AUTHOR> wangyibo
 * @date : 2025/4/10
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Log4j
public class SignalLoggingTriggerServiceImplTest {

    @Autowired
    private SignalLoggingTriggerService signalLoggingTriggerService;

    @Test
    public void testProcessSignalLoggingTriggerByMessageString() {
        // 准备测试数据
        String messageString = "{\n" +
                "    \"eventType\": \"staffMessageSendEvent\",\n" +
                "    \"event\": \"{\\\"serviceId\\\":457655430,\\\"staffId\\\":304291,\\\"customerId\\\":466486925,\\\"dialogId\\\":4407475368,\\\"skillId\\\":309,\\\"isSystem\\\":false,\\\"messageId\\\":9646862224,\\\"cscMessageId\\\":\\\"1816032202116251747\\\",\\\"extParam\\\":\\\"{\\\\\\\"smartBrokenLineTag\\\\\\\":\\\\\\\"pacific\\\\\\\"}\\\",\\\"msgContent\\\":\\\"您看是否可以先更换一下位置\\\",\\\"eventId\\\":\\\"7254a583-f47a-418c-a981-d0a4231cfc24\\\",\\\"occurredOn\\\":1721810792008}\"\n"
                +
                "}";

        // 记录测试开始
        log.info("开始测试 processSignalLoggingTriggerByMessageString 方法");

        try {
            // 调用测试方法
            signalLoggingTriggerService.processSignalLoggingTriggerByMessageString(messageString);

            // 测试成功
            log.info("测试 processSignalLoggingTriggerByMessageString 方法成功");
        } catch (Exception e) {
            // 记录异常
            log.error("测试 processSignalLoggingTriggerByMessageString 方法失败", e);
            throw e;
        }
    }
}