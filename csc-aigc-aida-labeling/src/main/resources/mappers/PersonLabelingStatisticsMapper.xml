<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.PersonLabelingStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="stat_date" jdbcType="DATE" property="statDate" />
    <result column="biz_type" jdbcType="BIGINT" property="bizType" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="labeler_name" jdbcType="VARCHAR" property="labelerName" />
    <result column="labeler_mis" jdbcType="VARCHAR" property="labelerMis" />
    <result column="labeling_count" jdbcType="INTEGER" property="labelingCount" />
    <result column="qc_inspected_count" jdbcType="INTEGER" property="qcInspectedCount" />
    <result column="qc_inspected_correct_count" jdbcType="INTEGER" property="qcInspectedCorrectCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, stat_date, biz_type, task_type, data_type, labeler_name, labeler_mis, labeling_count, 
    qc_inspected_count, qc_inspected_correct_count, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatisticsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from person_labeling_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from person_labeling_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from person_labeling_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatisticsExample">
    delete from person_labeling_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics">
    insert into person_labeling_statistics (id, stat_date, biz_type, 
      task_type, data_type, labeler_name, 
      labeler_mis, labeling_count, qc_inspected_count, 
      qc_inspected_correct_count, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{statDate,jdbcType=DATE}, #{bizType,jdbcType=BIGINT},
      #{taskType,jdbcType=TINYINT}, #{dataType,jdbcType=TINYINT}, #{labelerName,jdbcType=VARCHAR}, 
      #{labelerMis,jdbcType=VARCHAR}, #{labelingCount,jdbcType=INTEGER}, #{qcInspectedCount,jdbcType=INTEGER}, 
      #{qcInspectedCorrectCount,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics">
    insert into person_labeling_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="statDate != null">
        stat_date,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="labelerName != null">
        labeler_name,
      </if>
      <if test="labelerMis != null">
        labeler_mis,
      </if>
      <if test="labelingCount != null">
        labeling_count,
      </if>
      <if test="qcInspectedCount != null">
        qc_inspected_count,
      </if>
      <if test="qcInspectedCorrectCount != null">
        qc_inspected_correct_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="statDate != null">
        #{statDate,jdbcType=DATE},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="labelerName != null">
        #{labelerName,jdbcType=VARCHAR},
      </if>
      <if test="labelerMis != null">
        #{labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="labelingCount != null">
        #{labelingCount,jdbcType=INTEGER},
      </if>
      <if test="qcInspectedCount != null">
        #{qcInspectedCount,jdbcType=INTEGER},
      </if>
      <if test="qcInspectedCorrectCount != null">
        #{qcInspectedCorrectCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatisticsExample" resultType="java.lang.Long">
    select count(*) from person_labeling_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update person_labeling_statistics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.statDate != null">
        stat_date = #{record.statDate,jdbcType=DATE},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=BIGINT},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=TINYINT},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.labelerName != null">
        labeler_name = #{record.labelerName,jdbcType=VARCHAR},
      </if>
      <if test="record.labelerMis != null">
        labeler_mis = #{record.labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="record.labelingCount != null">
        labeling_count = #{record.labelingCount,jdbcType=INTEGER},
      </if>
      <if test="record.qcInspectedCount != null">
        qc_inspected_count = #{record.qcInspectedCount,jdbcType=INTEGER},
      </if>
      <if test="record.qcInspectedCorrectCount != null">
        qc_inspected_correct_count = #{record.qcInspectedCorrectCount,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update person_labeling_statistics
    set id = #{record.id,jdbcType=BIGINT},
      stat_date = #{record.statDate,jdbcType=DATE},
      biz_type = #{record.bizType,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=TINYINT},
      data_type = #{record.dataType,jdbcType=TINYINT},
      labeler_name = #{record.labelerName,jdbcType=VARCHAR},
      labeler_mis = #{record.labelerMis,jdbcType=VARCHAR},
      labeling_count = #{record.labelingCount,jdbcType=INTEGER},
      qc_inspected_count = #{record.qcInspectedCount,jdbcType=INTEGER},
      qc_inspected_correct_count = #{record.qcInspectedCorrectCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics">
    update person_labeling_statistics
    <set>
      <if test="statDate != null">
        stat_date = #{statDate,jdbcType=DATE},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="labelerName != null">
        labeler_name = #{labelerName,jdbcType=VARCHAR},
      </if>
      <if test="labelerMis != null">
        labeler_mis = #{labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="labelingCount != null">
        labeling_count = #{labelingCount,jdbcType=INTEGER},
      </if>
      <if test="qcInspectedCount != null">
        qc_inspected_count = #{qcInspectedCount,jdbcType=INTEGER},
      </if>
      <if test="qcInspectedCorrectCount != null">
        qc_inspected_correct_count = #{qcInspectedCorrectCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics">
    update person_labeling_statistics
    set stat_date = #{statDate,jdbcType=DATE},
      biz_type = #{bizType,jdbcType=BIGINT},
      task_type = #{taskType,jdbcType=TINYINT},
      data_type = #{dataType,jdbcType=TINYINT},
      labeler_name = #{labelerName,jdbcType=VARCHAR},
      labeler_mis = #{labelerMis,jdbcType=VARCHAR},
      labeling_count = #{labelingCount,jdbcType=INTEGER},
      qc_inspected_count = #{qcInspectedCount,jdbcType=INTEGER},
      qc_inspected_correct_count = #{qcInspectedCorrectCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="queryPersonLabelingStats" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonLabelingQueryVO" parameterType="com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonLabelingStatisticsDTO">
    SELECT
        labeler_name AS labelerName,
        labeler_mis AS labelerMis,
        COALESCE(SUM(qc_inspected_count), 0) AS qcInspectedCount,
        COALESCE(SUM(qc_inspected_correct_count), 0) AS qcInspectedCorrectCount
    FROM
        person_labeling_statistics
    <where>
        <if test="param.startDate != null">
            AND stat_date &gt;= #{param.startDate,jdbcType=DATE}
        </if>
        <if test="param.endDate != null">
            AND stat_date &lt;= #{param.endDate,jdbcType=DATE}
        </if>
        <if test="param.taskType != null">
            AND task_type = #{param.taskType,jdbcType=TINYINT}
        </if>
        <if test="param.dataType != null">
            AND data_type = #{param.dataType,jdbcType=TINYINT}
        </if>
        <if test="param.bizTypes != null and param.bizTypes.size() > 0">
            AND biz_type IN
            <foreach item="bizType" collection="param.bizTypes" open="(" separator="," close=")">
                #{bizType}
            </foreach>
        </if>
    </where>
    GROUP BY
        labeler_name, labeler_mis
    HAVING qcInspectedCount > 0
  </select>
  <select id="queryDailyTrends" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DailyBizStatisticsVO" parameterType="com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonLabelingStatisticsDTO">
    SELECT
      biz_type as bizTypeId,
      DATE_FORMAT(stat_date, '%m/%d') as date,
      COALESCE(SUM(labeling_count), 0) as dailyInspectedCount
    FROM
      person_labeling_statistics
    <where>
      <if test="param.startDate != null">
        AND stat_date >= #{param.startDate,jdbcType=DATE}
      </if>
      <if test="param.endDate != null">
        AND stat_date &lt;= #{param.endDate,jdbcType=DATE}
      </if>
      <if test="param.taskType != null">
        AND task_type = #{param.taskType,jdbcType=TINYINT}
      </if>
      <if test="param.dataType != null">
        AND data_type = #{param.dataType,jdbcType=TINYINT}
      </if>
      <if test="param.bizTypes != null and param.bizTypes.size() > 0">
        AND biz_type IN
        <foreach item="bizType" collection="param.bizTypes" open="(" separator="," close=")">
          #{bizType}
        </foreach>
      </if>
    </where>
    GROUP BY
      stat_date, biz_type
  </select>
  <select id="statisticalLabelingData" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingAndCheckCountVO">
      SELECT
        COUNT(DISTINCT statistics.labeler_mis) AS peopleNum,
        COALESCE(SUM(statistics.labeling_count), 0) AS quantity
      FROM
        person_labeling_statistics AS statistics
      <where>
        <if test="query.bizTypes != null">
          statistics.biz_type IN
          <foreach collection="query.bizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
          </foreach>
        </if>
        <if test="query.taskType != null">
          AND statistics.task_type = #{query.taskType}
        </if>
        <if test="query.dataType != null">
          AND statistics.data_type = #{query.dataType}
        </if>
        <if test="query.startDate != null">
          AND statistics.stat_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null">
          AND statistics.stat_date &lt;= #{query.endDate}
        </if>
      </where>
  </select>
  <select id="queryPersonnelDetail" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO" parameterType="com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonnelDetailQuery">
    SELECT
      COUNT(DISTINCT stat_date) AS workingDays,
      labeler_name as name,
      labeler_mis as userMis,
      biz_type as bizType,
      COALESCE(SUM(labeling_count), 0) as totalCount,
      CASE
        WHEN COALESCE(SUM(qc_inspected_count), 0) > 0
          THEN 100.0 * COALESCE(SUM(qc_inspected_correct_count), 0) / SUM(qc_inspected_count)
        ELSE 0
      END AS qualityCheckRate
    FROM
      person_labeling_statistics
    <where>
      <if test="query.name != null">
        labeler_name = #{query.name}
      </if>
      <if test="query.userMis != null">
        labeler_mis = #{query.userMis}
      </if>
      <if test="query.userMisList != null and query.userMisList.size() > 0">
        labeler_mis IN
        <foreach item="item" collection="query.userMisList" open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="query.startDate != null">
        AND stat_date &gt;= #{query.startDate,jdbcType=DATE}
      </if>
      <if test="query.endDate != null">
        AND stat_date &lt;= #{query.endDate,jdbcType=DATE}
      </if>
      <if test="query.taskType != null">
        AND task_type = #{query.taskType,jdbcType=TINYINT}
      </if>
      <if test="query.dataType != null">
        AND data_type = #{query.dataType,jdbcType=TINYINT}
      </if>
      <if test="query.bizTypes != null and query.bizTypes.size() > 0">
        AND biz_type IN
        <foreach item="bizType" collection="query.bizTypes" open="(" separator="," close=")">
          #{bizType}
        </foreach>
      </if>
    </where>
    GROUP BY
      labeler_name, labeler_mis, biz_type
    <if test="query.sortBy != null and query.sortOrder != null">
      ORDER BY
      <choose>
        <when test="query.sortBy == 'totalCount'">totalCount</when>
        <when test="query.sortBy == 'workingDays'">workingDays</when>
        <when test="query.sortBy == 'qualityCheckRate'">qualityCheckRate</when> <!-- quality_check_rate 需要在SELECT中计算或存在 -->
        <!-- 其他允许的排序列 -->
        <otherwise>
          total_count <!-- 默认排序列 -->
        </otherwise>
      </choose>
      <choose>
        <when test="query.sortOrder == 'ASC'">ASC</when>
        <when test="query.sortOrder == 'DESC'">DESC</when>
        <otherwise>DESC</otherwise> <!-- 默认排序方向 -->
      </choose>
    </if>
  </select>
  <!-- 批量插入人员标注统计数据 -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO person_labeling_statistics
    (
    stat_date,
    biz_type,
    task_type,
    data_type,
    labeler_name,
    labeler_mis,
    labeling_count,
    qc_inspected_count,
    qc_inspected_correct_count,
    create_time,
    update_time
    )
    VALUES
    <foreach collection="personLabelingStatisticsList" item="item" separator=",">
      (
      #{item.statDate},
      #{item.bizType},
      #{item.taskType},
      #{item.dataType},
      #{item.labelerName},
      #{item.labelerMis},
      #{item.labelingCount},
      #{item.qcInspectedCount},
      #{item.qcInspectedCorrectCount},
      #{item.createTime},
      #{item.updateTime}
      )
    </foreach>
  </insert>

</mapper>