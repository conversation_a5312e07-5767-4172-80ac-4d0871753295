package com.meituan.aigc.aida.data.management.fetch.dao.model;

import lombok.Data;

import java.util.Date;

@Data
public class PacificAcInterfaceLog {

    /**
     * ID
     */
    private Long id;
    /**
     * 联络类型
     */
    private String contactType;
    /**
     * 联络ID
     */
    private String contactId;
    /**
     * 客服MIS
     */
    private String staffMis;
    /**
     * AC接口ID
     */
    private Integer interfaceId;
    /**
     * 请求时间
     */
    private String sendTime;
    /**
     * 解析类型
     */
    private String parseType;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 删除状态(0-未删除,1-删除)
     */
    private Boolean isDelete;

}