package com.meituan.aigc.aida.data.management.fetch.dao.repository;

import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/3 16:12
 * @Version: 1.0
 */
public interface PacificAcInterfaceLogRepository {

    /**
     * 添加太平洋AC接口调用记录
     * @param pacificAcInterfaceLogWithBlobs 记录
     */
    void addSelective(PacificAcInterfaceLogWithBlobs pacificAcInterfaceLogWithBlobs);

    /**
     * 查询太平洋AC接口调用记录
     * @param contactId 联络ID
     * @param contactType 联络类型
     * @param staffMis 客服MIS
     * @return 查询结果
     */
    List<PacificAcInterfaceLogWithBlobs> listByContactIdContactTypeAndStaffMis(String contactId, String contactType, String staffMis);
}
