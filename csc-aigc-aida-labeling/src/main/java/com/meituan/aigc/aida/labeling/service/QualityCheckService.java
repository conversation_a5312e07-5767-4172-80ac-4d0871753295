package com.meituan.aigc.aida.labeling.service;


import com.meituan.aigc.aida.labeling.param.LabelingQualityDetailPageQuery;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.param.QualityCheckResultSaveParam;
import com.meituan.aigc.aida.labeling.param.quality.check.LabelingQualityCheckItemParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckViewParam;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingQualityCheckItemDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.PageQueryDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.QualityCheckTaskListDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.quality.check.TaskConditionDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingQualityCheckItemVO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingQualityCheckRawCountVO;

import java.util.List;

/**
 * Description 质检服务接口
 *
 * <AUTHOR>
 */
public interface QualityCheckService {

    /**
     * 分页查询质检任务列表详情
     *
     * @param param 质检任务列表查询参数
     * @return 质检任务列表查询结果
     */
    PageQueryDTO<LabelingQualityCheckItemDTO> page(LabelingQualityCheckItemParam param);

    /**
     * 获取开始质检详情
     *
     * @param qualityCheckTaskId
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<LabelingQualityCheckItemVO> getLabelingQualityDetail(LabelingQualityDetailPageQuery query);

    /**
     * 质检任务列表查询
     *
     * @param param 质检任务列表查询参数
     * @return 质检任务列表查询结果
     */
    PageData<QualityCheckTaskListDTO> qualityChecks(QualityCheckParam param);

    /**
     * 质检结果保存
     *
     * @param param 参数
     */
    void saveResult(QualityCheckResultSaveParam param);

    /**
     * 统计
     *
     * @param checkTaskId
     * @return
     */
    LabelingQualityCheckRawCountVO countCheckTaskRawDataNum(Long checkTaskId);

    /**
     * 记录查看任务详情，只要是质检人在质检页面查看了标注信息，就不允许标注人进行修改
     *
     * @param param 质检任务详情查询参数
     */
    void view(QualityCheckViewParam param);

    /**
     * 获取质检任务下可筛选的条件
     *
     * @param qualityCheckId 质检任务ID
     * @return 可筛选条件
     */
    List<TaskConditionDTO> getQualityCheckCondition(Long qualityCheckId);

    /**
     * 质检信息暂存
     * @param param 暂存参数
     */
    void checkTemporaryStorage(QualityCheckResultSaveParam param);
}
