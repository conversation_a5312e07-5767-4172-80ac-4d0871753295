package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 检查项模版DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InspectionItemTemplateDTO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 模版描述
     */
    private String templateDesc;

    /**
     * 标注项信息
     */
    private List<List<InspectionItemDTO>> keys;
} 