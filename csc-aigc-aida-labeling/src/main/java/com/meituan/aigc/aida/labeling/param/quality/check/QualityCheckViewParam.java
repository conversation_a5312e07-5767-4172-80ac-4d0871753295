package com.meituan.aigc.aida.labeling.param.quality.check;

import lombok.Data;

import java.io.Serializable;

@Data
public class QualityCheckViewParam implements Serializable {
    /**
     * 质检任务id
     */
    private Long qualityCheckTaskId;

    /**
     * 任务类型
     *
     * @see com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType
     */
    private Integer type;

    /**
     * 会话ID query维度进入页面时，将整个会话标记为已查看
     */
    private String sessionId;

    /**
     * 原始数据ID，其他场景下，只标记当前页面展示的原始数据
     */
    private Long rawDataId;
}
