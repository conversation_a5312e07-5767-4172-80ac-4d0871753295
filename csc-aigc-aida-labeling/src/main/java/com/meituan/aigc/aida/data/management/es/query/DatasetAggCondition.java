package com.meituan.aigc.aida.data.management.es.query;

import lombok.Data;

/**
 * 数据集聚合查询条件
 * 
 * <AUTHOR>
 * @description 用于构建Elasticsearch聚合查询的条件参数，支持嵌套聚合和普通聚合
 * @date 2025/6/4
 */
@Data
public class DatasetAggCondition {
    
    /**
     * 命中数据的大小限制
     * 用于控制返回的文档数量，0表示不返回具体文档只返回聚合结果
     */
    Integer hitSize;

    /**
     * 聚合查询的名称
     * 用于标识不同的聚合查询，便于结果解析和区分
     */
    String aggName;

    /**
     * 聚合桶的大小
     * 控制聚合结果中返回的桶（bucket）数量，默认20，最大1000
     */
    Integer bucketSize;

    /**
     * Elasticsearch索引名称
     * 指定要查询的ES索引
     */
    String indexName;

    /**
     * 聚合字段名称
     * 指定要进行聚合统计的字段名
     */
    String fieldName;

    /**
     * 嵌套路径
     * 当进行嵌套聚合时，指定嵌套对象的路径
     */
    String nestedPath;

    /**
     * 是否为嵌套聚合
     * true: 嵌套聚合，需要指定nestedPath
     * false: 普通聚合，直接对字段进行聚合
     */
    Boolean isNestedAgg;

    /**
     * 字段的完整路径
     * 包含嵌套路径在内的字段完整路径，用于精确定位要聚合的字段
     */
    String fullPath;

    /**
     * 验证分页参数
     * 对hitSize和bucketSize进行校验和默认值设置
     * hitSize最小为0，bucketSize默认20，最大为1000
     */
    public void validateSize() {
        // 命中大小校验：小于0时设置为0
        if (hitSize == null || hitSize < 0) {
            hitSize = 0;
        }
        // 桶大小校验：默认20，最大1000
        if (bucketSize == null || bucketSize <= 0 || bucketSize > 1000) {
            bucketSize = 20;
        }
    }
}
