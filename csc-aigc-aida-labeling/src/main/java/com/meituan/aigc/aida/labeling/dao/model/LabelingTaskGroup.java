package com.meituan.aigc.aida.labeling.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * 标注分组表实体类
 */
@Data
public class LabelingTaskGroup {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 分组名称
     */
    private String groupName;
    
    /**
     * 父分组ID，用于复标后生成的新分组
     */
    private Long parentId;
    
    /**
     * 状态(1:创建中 2:待回收 3:全量回收 4:部分回收 5:全量复标 6:失败)
     * {@link com.meituan.aigc.aida.labeling.common.enums.LabelingTaskGroupStatus}
     */
    private Integer recycleStatus;
    
    /**
     * 分组总数据量
     */
    private Integer totalDataCount;
    
    /**
     * 回收数据量
     */
    private Integer recycleDataCount;
    
    /**
     * 一致率，单位: 百分数整数(向上取整)
     */
    private Integer consistencyRate;
    
    /**
     * 回收采纳子任务ID, 部分回收时需参考详情表的状态(已采纳/未采纳)
     */
    private Long recycleSubTaskId;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否已删除 0-未删除 1-已删除
     */
    private Boolean isDeleted;
} 