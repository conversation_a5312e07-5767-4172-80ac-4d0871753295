# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with this codebase.

## 🎯 Quick Context

这是一个企业级AI模型训练平台，基于Spring Boot构建，专注于客服对话数据的采集、处理、标注和模型训练全流程管理。

**核心业务流程**: 客服对话数据采集 → 数据清洗处理 → 智能标注 → 质量检查 → 模型训练

**核心模块**:
- `csc-aigc-aida-labeling`: 核心业务逻辑模块，包含标注系统、数据管理、信号追踪等功能
- `csc-aigc-aida-labeling-api`: RPC服务接口定义，提供对外API契约

**核心特性**:
- 🔄 实时数据采集：支持在线聊天和热线通话的实时数据获取
- 🎯 智能信号追踪：基于规则的自动化数据埋点和触发机制  
- 📊 数据集管理：完整的数据集生命周期管理，支持版本控制
- 🏷️ 标注流水线：从任务创建到质检完成的全流程管理
- 📈 质量保障：多层次的质量检查和统计分析
- 🔌 系统集成：与美团内部多个系统深度集成（太平洋、海豚、星脉等）

## 🛠 Essential Commands

```bash
# Build & Run
mvn clean package -P test -DskipTests=true          # Test environment
mvn clean package -P prod -DskipTests=true          # Production
java -jar csc-aigc-aida-labeling/target/*.jar --spring.profiles.active=test

# Testing
mvn test                                             # All tests
mvn test -Dtest=SpecificTestClass                   # Single test

# Code Generation
mvn mybatis-generator:generate                       # Regenerate DB models
```

## 🏗 Architecture Overview

### Tech Stack
- **Backend**: Spring Boot + MyBatis + MySQL (via Zebra)
- **Search**: Elasticsearch 7.x
- **Cache**: Redis (via Squirrel)
- **MQ**: MAFKA (Kafka-based)
- **RPC**: Pigeon framework
- **Config**: Lion dynamic configuration

### Data Flow
```
Customer Service Data → MAFKA Queue → Rule Engine → Processing → ES/Hive Storage
                           ↓
                    Signal Tracking Rules
```

### 详细包结构
```
com.meituan.aigc.aida/
├── common/                           # 公共组件
│   ├── aop/                         # AOP切面：RPC调用统一处理
│   ├── constants/                   # 系统常量定义
│   ├── enums/                       # 通用枚举：ES操作类型等
│   ├── squirrel/                    # Redis客户端封装
│   └── util/                        # 工具类：线程池、对象转换等
├── config/                          # 配置类
│   ├── es/                         # Elasticsearch配置
│   ├── rhino/                      # Rhino线程池配置
│   ├── CrossFilterAutoConfiguration # 跨域配置
│   ├── CorsConfig                  # CORS配置
│   └── UploadFileConfig            # 文件上传配置
├── labeling/                        # 标注系统核心模块
│   ├── controller/                  # Web控制器层
│   │   ├── LabelingTaskController   # 标注任务管理
│   │   ├── QualityCheckController   # 质检管理
│   │   ├── LabelingSubTaskController # 子任务管理
│   │   └── DashboardStatisticsController # 统计面板
│   ├── service/                     # 业务服务层
│   │   ├── TaskService             # 任务核心服务
│   │   ├── QualityCheckService     # 质检服务
│   │   ├── LabelingSubTaskService  # 子任务服务
│   │   └── DashboardStatisticsService # 统计服务
│   ├── dao/                        # 数据访问层
│   │   ├── model/                  # 数据模型（MyBatis生成）
│   │   ├── mapper/                 # MyBatis映射接口
│   │   └── repository/             # 仓储模式封装
│   ├── strategy/                   # 策略模式实现
│   │   ├── dimension/              # 维度策略
│   │   ├── transfer/               # 转交策略
│   │   └── upload/                 # 文件处理策略
│   ├── config/                     # 标注模块配置
│   ├── exception/                  # 异常定义
│   ├── mq/                         # 消息队列
│   ├── job/                        # 定时任务
│   │   └── DashboardDataStatisticalJob # 仪表盘数据统计
│   └── common/                     # 模块公共组件
├── data.management/                 # 数据管理模块  
│   ├── dataset/                    # 数据集管理
│   │   ├── controller/             # 数据集API
│   │   │   ├── DataSetManagementController    # 数据集管理
│   │   │   ├── DataSetVersionController      # 版本管理
│   │   │   ├── DataProcessController         # 数据处理
│   │   │   └── DatasetInsightAndAnalyzeController # 数据洞察
│   │   ├── service/                # 数据集业务逻辑
│   │   ├── dao/                    # 数据集数据访问
│   │   ├── dto/                    # 数据传输对象
│   │   └── strategy/               # 数据集处理策略
│   │       ├── CaseDataSetProcessStrategy    # 案例数据集
│   │       ├── OnlineDataSetProcessStrategy  # 在线数据集
│   │       └── UploadDataSetProcessStrategy  # 上传数据集
│   ├── fetch/                      # 数据获取与信号追踪
│   │   ├── controller/             # 数据获取API
│   │   │   ├── DataFetchTaskController       # 数据获取任务
│   │   │   └── SignalTrackingController     # 信号追踪规则
│   │   ├── service/                # 数据获取服务  
│   │   ├── mq/                     # 消息队列
│   │   │   ├── consumer/           # 消息消费者
│   │   │   │   ├── ChatEventMessageListener     # 在线聊天事件
│   │   │   │   ├── VoiceEventMessageListener    # 语音通话事件
│   │   │   │   ├── DelayTriggerRobotListener   # 延迟触发
│   │   │   │   └── WhiteListMisCallAcNoticeListener # 白名单通知
│   │   │   └── producer/          # 消息生产者
│   │   │       ├── SignalLogsToMafkaProducer    # 信号日志
│   │   │       ├── DelayTriggerRobotToMafkaProducer # 延迟触发
│   │   │       └── ManualLabelingResultsProducer # 人工标注结果
│   │   ├── helper/                 # 辅助工具类
│   │   ├── strategy/               # 数据获取策略
│   │   └── biz/                    # 业务处理逻辑
│   ├── es/                         # Elasticsearch操作
│   │   ├── DatasetEsIndexService   # 数据集ES索引服务
│   │   └── DatasetEsBaseIndexService # ES基础操作服务
│   ├── remote/                     # 远程服务接口
│   │   ├── CaseAnalysisRemoteService  # 案例分析远程服务
│   │   ├── ManualLabelRemoteService   # 人工标注远程服务
│   │   └── LabelRemoteService        # 标注远程服务
│   └── common/                     # 数据管理公共组件
├── es/                             # ES通用组件
│   ├── AbstractBaseEs              # ES操作基类
│   ├── EsDocIndex                 # ES文档索引
│   └── EsEntityBase               # ES实体基类
└── permission/                     # 权限管理
    ├── controller/                 
    │   ├── UserPermissionController  # 用户权限控制器
    │   └── ExternalMetricController  # 外部指标控制器
    └── service/
        ├── PermissionService        # 权限服务
        └── ExternalMetricService    # 外部指标服务
```

## 🔧 核心功能模块详解

### 1. 标注系统 (Labeling System)
**功能概述**: 管理从任务创建到质检完成的完整标注流程

**核心组件**:
- `LabelingTaskController` → 标注任务管理API，支持任务的增删改查
- `TaskServiceImpl` → 任务业务逻辑，包含任务分配、进度跟踪等
- `LabelingSubTaskServiceImpl` → 子任务管理，处理具体的标注工作
- `QualityCheckServiceImpl` → 质检服务，确保标注质量

**核心业务流程**:
```
1. 创建标注任务 → POST /api/v1/labeling-tasks/createTask
2. 任务分组分配 → POST /api/v1/labeling-tasks/groups/distribute  
3. 执行标注工作 → LabelingSubTaskService处理
4. 质检分配 → POST /api/v1/labeling-tasks/quality-check/groups
5. 质检执行 → QualityCheckService处理
6. 数据导出 → POST /api/v1/labeling-tasks/export
```

**数据库表关系**:
- `labeling_task` - 主任务表，记录任务基本信息
- `labeling_task_group` - 任务分组表，支持按组管理
- `labeling_sub_task` - 子任务表，具体的标注任务分配
- `labeling_detail` - 标注详情表，存储标注结果
- `labeling_quality_check_task` - 质检任务表
- `labeling_quality_check_item` - 质检项目表

### 2. 数据管理系统 (Data Management)
**功能概述**: 处理数据获取、数据集管理、ES索引等全生命周期管理

**核心组件**:
- `DataSetManagementController` → 数据集管理API，支持数据集的创建、查询、导出
- `DataSetManagementServiceImpl` → 数据集业务逻辑，处理数据集生命周期
- `DatasetEsIndexService` → ES索引服务，管理数据集在ES中的存储
- `DataFetchTaskServiceImpl` → 数据获取任务服务

**核心业务流程**:
```
1. 解析数据源 → POST /api/v1/DataSet/dataset/parseHeader
2. 创建数据集 → POST /api/v1/DataSet/dataset/create
3. 数据处理 → 数据清洗、格式转换、字段映射
4. ES索引构建 → DatasetEsIndexService.indexDataset()
5. 版本管理 → 支持数据集版本控制
6. 数据导出 → GET /api/v1/DataSet/export
```

**数据库表关系**:
- `data_set` - 数据集主表，记录数据集元信息
- `data_set_version` - 数据集版本表，支持版本管理
- `data_fetch_task` - 数据获取任务表
- `data_fetch_record` - 数据获取记录表

### 3. 信号追踪系统 (Signal Tracking)
**功能概述**: 基于规则自动采集客服对话中的关键信号，支持实时和延时触发AI机器人，实现客服辅助决策和自动化处理

**核心组件**:
- `SignalTrackingController` → 埋点规则管理API，支持规则的增删改查
- `SignalTrackingServiceImpl` → 埋点规则业务逻辑，处理规则配置和状态管理
- `AbstractSignalLoggingTriggerService` → 触发服务基类，定义触发流程骨架
- `OnlineSignalLoggingTriggerServiceImpl` → 在线渠道触发实现，处理聊天消息
- `HotlineSignalLoggingTriggerServiceImpl` → 热线渠道触发实现，处理语音通话
- `AidaSignalLoggingAppInvokeHelper` → AI机器人调用助手，封装机器人调用逻辑
- `DataToHiveService` → 数据存储服务，将触发数据发送到Hive数仓

**系统架构流程**:

```
[配置域 - Configuration Domain]
用户 → 创建埋点规则 → DB(llm_signal_tracking_rules) → 规则配置
                                                        ├─ 实时机器人
                                                        └─ 延时机器人 (+延迟时间)

[运行域 - Runtime Domain]
数据源:
- 在线聊天 → ChatEventMessageListener ─┐
- 语音通话 → VoiceEventMessageListener ──┼─→ SignalLoggingTriggerService
- 白名单 → WhiteListMisCallAcNoticeListener ─┘
                                               ↓
                                         规则匹配引擎
                                         1. 渠道匹配 (Channel match)
                                         2. 标准问ID匹配 (仅在线渠道)
                                         3. 客服MIS匹配 
                                         4. AC接口匹配
                                               ↓
                                         触发机器人
                                         ├─ 实时触发 → 立即执行
                                         └─ 延时触发 → MAFKA延时队列 → 稍后执行
                                                           ↓
                                               AidaSignalLoggingAppInvokeHelper
                                                           ↓
                                               DataToHiveService → MAFKA → Hive存储

[支持组件 - Supporting Components]
- AC接口: 太平洋弹屏触发
- 上下文: Butler上下文工具 (contextUtils)
- 缓存: Guava + Redis 缓存弹屏/客服/参数信息
```

**核心业务流程**:
```
1. 规则配置 → POST /api/v1/signal-tracking-rules/create
2. 实时数据接收 → MAFKA消费者监听各渠道事件
3. 规则匹配 → AbstractSignalLoggingTriggerService.match()
4. 机器人触发 → AidaSignalLoggingAppInvokeHelper.invoke()
5. 数据存储 → DataToHiveService.sendToHive()
```

**数据库表关系**:
- `llm_signal_tracking_rules` - 信号追踪规则表，定义触发条件和机器人配置
- `pacific_ac_interface_log` - 太平洋AC接口调用日志表

## 📝 开发指南

### 添加新功能

**标注功能扩展**:
1. 在 `common/enums/` 添加相关枚举定义
2. 更新数据库表结构后执行: `mvn mybatis-generator:generate`
3. 创建服务类并标注 `@Service`
4. 添加控制器端点并标注 `@RestController`
5. 在 `strategy/` 包下实现相应的策略模式
6. 更新相关的DTO和VO类

**数据源集成**:
1. 在 `mq/consumer/` 创建消费者，使用 `@MdpMafkaMsgReceive` 注解
2. 继承 `AbstractSignalLoggingTriggerService` 实现触发服务
3. 在Lion配置中添加开关: `xxx.consumer.switch=true`
4. 在 `mafka.properties` 中配置消费者信息
5. 实现相应的业务处理逻辑

**数据集处理扩展**:
1. 在 `dataset/strategy/` 实现数据处理策略
2. 扩展 `DatasetEsIndexService` 以支持新的ES索引类型
3. 更新 `DataSetManagementService` 接口
4. 添加相应的配置和验证逻辑

### 通用开发模式

**异常处理模式**:
```java
// 业务异常处理
throw new AidaTrainingCheckException("业务错误信息");
throw new AidaTrainingServiceException("服务异常信息");

// MAFKA消费者异常处理 - 始终返回成功避免死循环
try {
    processMessage(msg);
} catch (Exception e) {
    log.error("消息处理失败", e);
    return ConsumeStatus.CONSUME_SUCCESS; // 重要：避免消息重复消费
}

// RPC调用异常处理
@Around("execution(public * com.meituan.aigc.aida.data.management.dataset.remote..*.*(..))")
public Object rpcPointcut(ProceedingJoinPoint joinPoint) {
    try {
        return joinPoint.proceed();
    } catch (Throwable t) {
        return ServiceResponseDTO.failed(ResultCode.FAIL, "接口异常");
    }
}
```

**配置管理模式**:
```java
// Lion动态配置访问
@Resource
private DataManagementLionConfig lionConfig;
// 使用: lionConfig.getChatConsumerSwitch()

// MDP配置注解使用
@MdpConfig("chat.consumer.switch:false")
private Boolean chatConsumerSwitch;
```

**数据库操作模式**:
```java
// 使用Repository模式而非直接使用Mapper
@Resource
private SignalTrackingRulesRepository repository;

// 分页查询模式
PageHelper.startPage(pageNum, pageSize);
List<Entity> list = repository.selectList(condition);
PageInfo<Entity> pageInfo = new PageInfo<>(list);
PageHelper.clearPage();

// 事务处理
@Transactional(rollbackFor = Exception.class)
public void complexOperation() {
    // 业务逻辑
}
```

**缓存使用模式**:
```java
// Redis缓存操作
@Resource
private SquirrelClient squirrelClient;
squirrelClient.set("category", "key", value, expireSeconds);

// Guava本地缓存
private final Cache<String, Object> localCache = CacheBuilder.newBuilder()
    .maximumSize(1000)
    .expireAfterWrite(10, TimeUnit.MINUTES)
    .build();
```

**策略模式应用**:
```java
// 维度策略工厂
@Resource
private DimensionStrategyFactory dimensionStrategyFactory;
DimensionStrategy strategy = dimensionStrategyFactory.getStrategy(dimensionType);
strategy.process(param);

// 文件处理策略
@Resource
private FileProcessingStrategy fileProcessingStrategy;
fileProcessingStrategy.process(file, param);
```

## 🚀 完整API接口清单

### 标注任务管理接口
```
POST   /api/v1/labeling-tasks/createTask              # 创建标注任务
GET    /api/v1/labeling-tasks/taskList                # 查询任务列表
GET    /api/v1/labeling-tasks/count                   # 获取任务统计
GET    /api/v1/labeling-tasks/groups                  # 根据任务ID查询分组
GET    /api/v1/labeling-tasks/raw-data                # 分页查询原始数据
POST   /api/v1/labeling-tasks/groups/distribute       # 分配标注任务
POST   /api/v1/labeling-tasks/groups/consistency      # 查询分组一致性
DELETE /api/v1/labeling-tasks/delete                  # 删除标注任务
POST   /api/v1/labeling-tasks/export                  # 导出标注数据
POST   /api/v1/labeling-tasks/transfer                # 转交任务
```

### 标注子任务接口
```
GET    /api/v1/labeling-sub-tasks/page                # 子任务分页查询
POST   /api/v1/labeling-sub-tasks/submitLabeling      # 提交标注结果
```

### 质检管理接口
```
POST   /api/v1/labeling-tasks/quality-check/groups    # 质检分配
GET    /api/v1/labeling-tasks/quality-check/groups/details # 质检概览
GET    /api/v1/quality-check/tasks/page               # 质检任务分页查询
POST   /api/v1/quality-check/tasks/submit             # 提交质检结果
```

### 数据集管理接口
```
GET    /api/v1/DataSet/tasks                          # 数据获取任务列表
POST   /api/v1/DataSet/dataset/parseHeader            # 解析文件表头
POST   /api/v1/DataSet/dataset/create                 # 创建数据集
GET    /api/v1/DataSet/page                           # 分页查询数据集
GET    /api/v1/DataSet/pageDetail                     # 分页查询数据集详情
GET    /api/v1/DataSet/field                          # 获取字段枚举
GET    /api/v1/DataSet/field/list                     # 获取字段列表
POST   /api/v1/DataSet/field/update                   # 更新字段类型
GET    /api/v1/DataSet/export                         # 导出数据集
DELETE /api/v1/DataSet/delete                         # 删除数据集
GET    /api/v1/DataSet/publish                        # 发布数据集
GET    /api/v1/DataSet/data/fetch                     # 根据任务ID获取数据任务数据
```

### 数据集版本管理接口
```
POST   /api/v1/DataSet/version/add                    # 新增数据集版本
DELETE /api/v1/DataSet/version/delete                 # 删除数据集版本
PUT    /api/v1/DataSet/version/update/name            # 修改版本名称
GET    /api/v1/DataSet/version/list                   # 获取数据集版本列表
GET    /api/v1/DataSet/version/detail                 # 获取数据集版本详情
```

### 数据集洞察和分析接口
```
GET    /api/v1/data/analysis/distribute               # 查询数据集分布情况
GET    /api/v1/data/analysis/field                    # 查询一级字段下的二级字段列表
POST   /api/v1/data/analysis/page                     # 数据集筛选分页查询
POST   /api/v1/data/analysis/save                     # 保存数据集
```

### 数据获取任务接口
```
POST   /api/v1/data-fetch/robots                      # 查询信号埋点规则机器人列表
POST   /api/v1/data-fetch/create                      # 创建数据获取任务
GET    /api/v1/data-fetch/tasks                       # 任务列表查询
GET    /api/v1/data-fetch/task/detail                 # 任务详情查询
GET    /api/v1/data-fetch/records                     # 任务记录查询
GET    /api/v1/data-fetch/records/export              # 导出数据记录
DELETE /api/v1/data-fetch/task/delete                 # 删除数据获取任务
```

### 数据处理接口
```
POST   /api/v1/data/processing/task/create            # 创建清洗任务
GET    /api/v1/data/processing/task/page              # 获取清洗任务列表
GET    /api/v1/data/processing/custom/label           # 获取打标机器人信息
GET    /api/v1/data/processing/non/custom/scene       # 获取非自定义标签的场景信息
GET    /api/v1/data/processing/non/custom/label       # 获取非自定义标签的标签信息
GET    /api/v1/data/processing/non/custom/robot       # 查询打标机器人
POST   /api/v1/data/processing/non/custom/mark        # 数据打标
DELETE /api/v1/data/processing/delete                 # 删除数据处理任务
```

### 信号追踪接口
```
GET    /api/v1/signal-tracking-rules/list             # 埋点规则列表
GET    /api/v1/signal-tracking-rules/detail           # 埋点规则详情
POST   /api/v1/signal-tracking-rules/create           # 创建埋点规则
PUT    /api/v1/signal-tracking-rules/status           # 更新规则状态
PUT    /api/v1/signal-tracking-rules/delete           # 删除埋点规则
GET    /api/v1/signal-tracking-rules/robot-config     # 获取机器人配置
```

### 仪表盘统计接口
```
GET    /api/v1/dashboard/statistics/overview          # 统计概览
GET    /api/v1/dashboard/statistics/task              # 任务统计
GET    /api/v1/dashboard/statistics/personnel         # 人员统计
```

### 权限管理接口
```
GET    /api/v1/permissions/getUserPermissions         # 获取用户权限列表
```

### 外部指标接口
```
GET    /api/v1/external-labeling-templates/list       # 获取外部指标列表
GET    /api/v1/external-labeling-templates/detail     # 获取外部指标详情
```

### SSO认证接口
```
GET    /api/v1/sso/login                              # SSO登录
GET    /api/v1/sso/logout                             # SSO登出
GET    /api/v1/sso/user/info                          # 获取用户信息
```

### 系统监控接口
```
GET    /api/v1/test/health                            # 健康检查
GET    /monitor/alive                                 # 服务存活检查
```

## 📊 完整数据库表结构

### 标注系统相关表
```sql
-- 标注任务主表
labeling_task (id, task_name, data_set_id, labeling_status, quality_check_status, ...)

-- 任务分组表
labeling_task_group (id, task_id, group_name, group_status, labeling_config, ...)

-- 子任务表
labeling_sub_task (id, task_id, group_id, assignee_mis, sub_task_status, ...)

-- 标注详情表
labeling_detail (id, sub_task_id, session_id, label_result, is_consistent, ...)

-- 质检任务表
labeling_quality_check_task (id, task_id, group_id, check_status, assignee_mis, ...)

-- 质检项目表
labeling_quality_check_item (id, check_task_id, detail_id, check_result, ...)

-- 原始数据表
labeling_task_raw_data (id, task_id, session_id, raw_data, extra_info, ...)

-- 会话数据表
labeling_task_session_data (id, task_id, session_id, session_data, ...)
```

### 数据管理相关表
```sql
-- 数据集主表
data_set (id, name, description, usage_category, training_type, status, ...)

-- 数据集版本表
data_set_version (id, data_set_id, version_name, status, head_config, ...)

-- 数据获取任务表
data_fetch_task (id, task_name, data_source, status, filter_conditions, ...)

-- 数据获取记录表
data_fetch_record (id, task_id, record_data, create_time, ...)

-- 数据处理任务表
data_process_task (id, data_set_id, process_type, status, config, ...)

-- 数据处理配置表
data_process_config (id, config_name, config_type, config_content, ...)
```

### 信号追踪相关表
```sql
-- 信号追踪规则表
llm_signal_tracking_rules (
    id, name, status, channel, 
    typical_question_ids, mis_ids, ac_interface, 
    aida_app_cfg, creator_mis, ...
)

-- 太平洋AC接口日志表
pacific_ac_interface_log (id, ac_id, request_params, response_data, ...)
```

## ⚠️ 重要注意事项

### 开发环境
1. **本地开发**: 使用 `test` profile，避免影响生产数据
2. **外部服务**: 测试环境中大部分外部服务都有Mock实现
3. **数据库变更**: 修改表结构后必须重新生成Model: `mvn mybatis-generator:generate`
4. **消息处理**: 消费失败时记录日志并返回成功，避免死循环重试
5. **缓存管理**: 规则变更时需要清理相关Redis缓存

### 系统集成
1. **SSO认证**: 本地调试时检查 `sso.switch` 配置
2. **ES连接**: 验证ES集群配置和网络连通性
3. **MAFKA消费**: 检查Lion开关和Topic配置
4. **Lion配置**: 确保所有动态配置项都有默认值
5. **美团内部服务**: 海豚、太平洋、星脉等服务的集成点要注意权限和网络

### 性能优化
1. **分页查询**: 大数据量查询必须使用PageHelper分页
2. **ES操作**: 批量操作使用bulk API提高性能
3. **缓存策略**: 合理使用Redis和Guava缓存减少数据库压力
4. **异步处理**: 数据集创建等耗时操作使用异步处理
5. **连接池**: 合理配置数据库和Redis连接池参数

### 部署配置
1. **环境配置**: 不同环境使用不同的profile配置文件
2. **JVM参数**: 根据机器内存自动调整堆大小和GC参数
3. **文件上传**: 支持最大300MB文件上传
4. **健康检查**: 服务启动后通过 `/monitor/alive` 检查状态
5. **日志管理**: 配置合适的日志级别和滚动策略