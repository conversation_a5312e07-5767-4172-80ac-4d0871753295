package com.meituan.aigc.aida.data.management.fetch.service;

import com.meituan.aigc.aida.data.management.fetch.param.DataFetchTaskCreateParam;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchRecordDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskDTO;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;
import com.meituan.aigc.aida.data.management.fetch.vo.RobotVO;
import com.meituan.aigc.aida.labeling.param.PageData;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/7 15:59
 * @Version: 1.0
 */
public interface DataFetchTaskService {

    List<RobotVO> listRobotByParam(RobotListQuery query);

    void createTask(DataFetchTaskCreateParam param);

    /**
     * 分页查询数据拉取任务列表
     *
     * @param taskName 任务名称
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页数据拉取任务列表
     */
    PageData<DataFetchTaskDTO> pageDataFetchTaskList(String taskName, Integer pageNum, Integer pageSize);

    /**
     * 获取数据拉取任务详情
     *
     * @param taskId 任务ID
     * @return 数据拉取任务详情
     */
    DataFetchTaskDTO getDataFetchTaskDetail(Long taskId);

    /**
     * 分页查询数据拉取任务记录列表
     *
     * @param taskId   任务ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页数据拉取任务记录列表
     */
    PageData<DataFetchRecordDTO> pageDataFetchRecordList(Long taskId, Integer pageNum, Integer pageSize, String inputs);

    /**
     * 导出数据拉取任务记录
     *
     * @param taskId 任务ID
     */
    void exportDataFetchRecord(Long taskId);

    /**
     * 删除数据拉取任务及相关记录
     *
     * @param taskId 任务ID
     */
    void deleteDataFetchTask(Long taskId);
}
