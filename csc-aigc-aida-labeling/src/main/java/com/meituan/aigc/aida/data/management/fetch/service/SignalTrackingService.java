package com.meituan.aigc.aida.data.management.fetch.service;

import com.meituan.aigc.aida.data.management.fetch.dto.SignalTrackingRuleDTO;
import com.meituan.aigc.aida.labeling.param.PageData;

/**
 * <AUTHOR>
 * @date 2025-04-03 10:17
 * @description 数据埋点规则服务
 */
public interface SignalTrackingService {

    /**
     * 获获取埋点规则列表
     * 
     * @param name     规则名称
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 埋点规则列表
     */
    PageData<SignalTrackingRuleDTO> pageSignalTrackingRuleList(String name, Integer pageNum, Integer pageSize);

    /**
     * 获取埋点规则详情
     * 
     * @param ruleId 埋点规则ID
     * @return 埋点规则详情
     */
    SignalTrackingRuleDTO getSignalTrackingRuleDetail(Long ruleId);

    /**
     * 创建埋点规则
     * 
     * @param rule 埋点规则信息
     */
    void createSignalTrackingRule(SignalTrackingRuleDTO rule);

    /**
     * 删除埋点规则
     * 
     * @param ruleId 埋点规则ID
     */
    void deleteSignalTrackingRule(Long ruleId);

    /**
     * 更新埋点规则状态
     * 
     * @param ruleId 埋点规则ID
     * @param status 状态
     */
    void updateStatus(Long ruleId, Integer status);

    /**
     * 清除弹窗AC ID缓存
     */
    void clearVerifyExistPopupAcIdCache();

    /**
     * 获取埋点机器人配置
     *
     * @return 埋点机器人配置
     */
    String getSignalTrackingRobotConfig();

}