package com.meituan.aigc.aida.labeling.pojo.dto;

import com.meituan.aigc.aida.labeling.param.Context;
import com.meituan.aigc.aida.labeling.param.Signal;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/3/4 10:17
 * @Version: 1.0
 * 标注结果保存参数
 */
@Data
public class LabelingResultSaveDTO {

    /**
     * 详情数据ID
     */
    private Long id;
    /**
     * 大模型消息ID
     */
    private String messageId;
    /**
     * 三方消息ID
     */
    private String thirdMessageId;
    /**
     * 大模型消息内容
     */
    private String messageContent;
    /**
     * 上下文
     */
    private List<Context> context;
    /**
     * 信号
     */
    private List<Signal> signalList;
    /**
     * 原始数据JSON
     */
    private String rawDataContent;
    /**
     * 原始数据内容映射内容JSON
     */
    private String rawDataMappedContent;
    /**
     * labelingItems
     */
    private List<List<LabelResult>> labelingItems;
}
