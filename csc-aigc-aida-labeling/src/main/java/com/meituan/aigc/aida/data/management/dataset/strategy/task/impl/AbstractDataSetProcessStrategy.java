package com.meituan.aigc.aida.data.management.dataset.strategy.task.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.meituan.aigc.aida.data.management.dataset.enums.DataSetVersionTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.DatasetExportStatusEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.DatasetVersionStatusEnum;
import com.meituan.aigc.aida.data.management.dataset.helper.DatasetHelper;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetManagementService;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategyFactory;
import com.meituan.aigc.aida.data.management.dataset.strategy.task.DataSetProcessStrategy;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.data.management.es.query.BatchResult;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.common.s3.S3Service;
import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.TrainingTypeEnum;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs;

import javax.annotation.Resource;
import java.util.*;

/**
 * 数据集任务处理抽象基类
 */
@Slf4j
public abstract class AbstractDataSetProcessStrategy implements DataSetProcessStrategy {

    @Autowired
    protected DataSetManagementService dataSetManagementService;

    @Resource
    private DataSetVersionRepository dataSetVersionRepository;

    @Resource
    private DataSetRepository dataSetRepository;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private DatasetEsIndexService datasetEsIndexService;

    @Autowired
    private DataFieldStrategyFactory dataFieldStrategyFactory;

    @Autowired
    private DatasetHelper datasetHelper;

    private static final String VERSION_NAME_PREFIX = "v";
    private static final String INITIAL_VERSION_NUMBER = "1.0";

    /**
     * 通用分批保存数据方法
     *
     * @param param          数据集创建参数
     * @param dataSet        数据集对象
     * @param dataSetVersion 数据集版本对象
     */
    protected void commonSaveDataInBatches(DataSetCreateParam param, DataSet dataSet, DataSetVersion dataSetVersion, String dataSource) {
        int pageNum = 1;
        int pageSize = datasetHelper.getDatasetBatchSize(dataSource);
        DataSetPageParam pageParam = new DataSetPageParam();
        BeanUtils.copyProperties(param, pageParam);
        List<DataSetRecordParam> batchDataList = getNextBatchDataList(pageParam, pageNum, pageSize);
        // 使用入参中的表头配置
        DatasetHeadConfig headConfig = dataSetManagementService.handleHeadList(null, param.getHeadList(), batchDataList);

        int size = 0;
        String index = null;
        Date now = new Date();
        while (CollectionUtils.isNotEmpty(batchDataList)) {
            // 累加数据条数
            size += batchDataList.size();

            // 构造es数据
            List<DatasetEsIndex> esIndexList = dataSetManagementService.buildDatasetEsIndices(dataSet.getId(), dataSetVersion.getId(), param.getDataSource(), batchDataList, headConfig, now, now);

            // 批量写入ES
            index = batchInsertToEs(esIndexList, dataSet, dataSetVersion, pageSize);
            if (StringUtils.isNotBlank(index) && StringUtils.isBlank(dataSetVersion.getEsIndexName())) {
                dataSetVersion.setEsIndexName(index);
            }

            pageNum++;
            batchDataList = getNextBatchDataList(pageParam, pageNum, pageSize);
        }

        // 更新数据集统计信息
        updateDataSetStatistics(param, dataSet, dataSetVersion, index, size, headConfig);
    }

    /**
     * 根据数据列表和表头列表推断数据类型
     *
     * @param dataList 数据列表
     * @param headList 表头列表
     * @param fetchId  数据源ID
     * @return 返回推断后的数据类型列表
     */
    protected List<DataFieldParam> inferHeadTypeInBatches(List<DataSetRecordParam> dataList, List<DataFieldParam> headList, Long fetchId, String dataSource) {
        int pageNum = 1;
        int pageSize = datasetHelper.getDatasetBatchSize(dataSource);
        DataSetPageParam pageParam = new DataSetPageParam();
        pageParam.setDataList(dataList);
        pageParam.setHeadList(headList);
        pageParam.setFetchId(fetchId);
        List<DataSetRecordParam> batchDataList = getNextBatchDataList(pageParam, pageNum, pageSize);
        while (CollectionUtils.isNotEmpty(batchDataList)) {
            for (DataFieldParam dataFieldParam : headList) {
                int fieldType = dataSetManagementService.inferFieldTypeFromSampleData(dataFieldParam.getColumnName(), batchDataList);
                Integer historyFieldType = dataFieldParam.getFieldType();
                if (historyFieldType != null) {
                    fieldType = FieldTypeEnum.compareTo(historyFieldType, fieldType);
                }
                dataFieldParam.setFieldType(fieldType);
            }
            pageNum++;
            batchDataList = getNextBatchDataList(pageParam, pageNum, pageSize);
        }
        return headList;
    }

    /**
     * 获取下一批数据列表
     *
     * @param param    数据集创建参数
     * @param pageNum  当前页码
     * @param pageSize 每页数据量
     * @return 返回下一批数据列表
     */
    protected List<DataSetRecordParam> getNextBatchDataList(DataSetPageParam param, int pageNum, int pageSize) {
        List<DataSetRecordParam> dataList = param.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        int startIndex = (pageNum - 1) * pageSize;
        if (startIndex >= dataList.size()) {
            return new ArrayList<>();
        }

        int endIndex = Math.min(startIndex + pageSize, dataList.size());
        return dataList.subList(startIndex, endIndex);
    }

    /**
     * 批量写入ES
     */
    private String batchInsertToEs(List<DatasetEsIndex> esIndexList, DataSet dataSet, DataSetVersion dataSetVersion, int pageSize) {
        if (CollectionUtils.isEmpty(esIndexList)) {
            return null;
        }

        String index = null;
        // 分批处理
        for (int i = 0; i < esIndexList.size(); i += pageSize) {
            int endIndex = Math.min(i + pageSize, esIndexList.size());
            List<DatasetEsIndex> batch = esIndexList.subList(i, endIndex);

            BatchResult result = datasetEsIndexService.batchInsertDatasets(batch, DataSourceEnum.getValueByCode(dataSet.getDataSource()), dataSetVersion.getEsIndexName());
            if (!result.isAllSuccess()) {
                throw new AidaRpcException("数据写入ES失败");
            }
            index = result.getIndex();
            log.info("批量写入ES成功，批次：{}-{}，数量：{}", i, endIndex - 1, batch.size());
        }

        return index;
    }

    /**
     * 创建新数据集
     */
    protected DataSet createNewDataSet(DataSetCreateParam param) {
        DataSet dataSet = new DataSet();
        dataSet.setName(param.getDataSetName());
        dataSet.setDescription(param.getDescription());

        if (StringUtils.isNotBlank(param.getCreatorMis())) {
            dataSet.setCreatorMis(param.getCreatorMis());
        } else {
            dataSet.setCreatorMis(UserUtils.getUser().getLogin());
        }

        if (StringUtils.isNotBlank(param.getCreatorName())) {
            dataSet.setCreatorName(param.getCreatorName());
        } else {
            dataSet.setCreatorName(UserUtils.getUser().getName());
        }

        dataSet.setFetchId(param.getFetchId());
        dataSet.setUsageCategory(param.getUsageCategory());
        dataSet.setUsageType(param.getUsageType());
        dataSet.setTrainingType(param.getTrainingType());
        if (param.getTrainingType() != null) {
            if (TrainingTypeEnum.SFT.getCode().equals(param.getTrainingType())) {
                dataSet.setDataFormat("Prompt + Output");
            } else if (TrainingTypeEnum.DPO.getCode().equals(param.getTrainingType())) {
                dataSet.setDataFormat("Prompt + Chosen + Rejected");
            }
        }
        dataSet.setTaskDescription(param.getTaskDescription());
        dataSet.setDataSetStatus(DatasetExportStatusEnum.CREATE.getCode());
        Date now = new Date();
        dataSet.setCreateTime(now);
        dataSet.setUpdateTime(now);
        dataSet.setIsDeleted(false);
        dataSet.setDataSource(param.getDataSource());
        if (param.getFile() != null) {
            String fileName = param.getDataSetName() + System.currentTimeMillis() + CommonConstants.FileType.XLSX;
            String fileUrl = s3Service.upload(param.getFile(), fileName);
            dataSet.setFilePath(fileUrl);
            dataSet.setFileName(param.getFile().getOriginalFilename());
        }

        if (param.getTargetField() != null || param.getSessionGroupingField() != null) {
            DataSetExtraInfo extraInfo = new DataSetExtraInfo();
            extraInfo.setTargetField(param.getTargetField());
            extraInfo.setSessionGroupingField(param.getSessionGroupingField());
            dataSet.setExtraInfo(JSONObject.toJSONString(extraInfo));
        }

        dataSetRepository.createDataSet(dataSet);
        return dataSet;
    }

    /**
     * 更新数据集统计信息
     */
    protected void updateDataSetStatistics(DataSetCreateParam param, DataSet dataSet, DataSetVersion dataSetVersion, String index, int dataCount, DatasetHeadConfig headConfig) {
        // 更新数据集的最新版本ID和数据条数
        DataSet updateDataSet = new DataSet();
        updateDataSet.setId(dataSet.getId());
        updateDataSet.setLatestVersionId(dataSetVersion.getId());
        if (param.getDataSource() != DataSourceEnum.CASE.getCode() || (param.getIsEnd() != null && param.getIsEnd())) {
            updateDataSet.setDataSetStatus(DatasetExportStatusEnum.FINISHED.getCode());
        }
        int historyDataCount = dataSet.getDataCount() != null ? dataSet.getDataCount() : 0;
        int totalDataCount = historyDataCount + dataCount;
        updateDataSet.setDataCount(totalDataCount);
        updateDataSet.setUpdateTime(new Date());
        dataSetRepository.updateDataSet(updateDataSet);

        // 更新表头和es索引字段
        dataSetVersion.setHeadConfig(JSONObject.toJSONString(headConfig));
        dataSetVersion.setDataCount(totalDataCount);
        // 更新es索引
        if (StringUtils.isNotBlank(index) && StringUtils.isBlank(dataSetVersion.getEsIndexName())) {
            dataSetVersion.setEsIndexName(index);
        }
        dataSetVersion.setUpdateTime(new Date());
        dataSetVersionRepository.updateSelectiveById(dataSetVersion);
    }


    /**
     * 创建数据集版本
     */
    protected DataSetVersion createDataSetVersion(Long dataSetId, DataSetCreateParam param) {
        DataSetVersion version = new DataSetVersion();
        version.setDataSetId(dataSetId);
        version.setVersionName(VERSION_NAME_PREFIX + INITIAL_VERSION_NUMBER);
        version.setDescription(param.getDescription());
        version.setVersionType(DataSetVersionTypeEnum.CREATE_TASK.getCode());
        if (CollectionUtils.isNotEmpty(param.getDataList())) {
            version.setDataCount(param.getDataList().size());
        }
        version.setVersionStatus(DatasetVersionStatusEnum.DRAFT.getCode()); // 正常状态
        Date now = new Date();
        version.setCreateTime(now);
        version.setUpdateTime(now);
        version.setIsDeleted(false);
        dataSetVersionRepository.createDataSetVersion(version);
        return version;
    }

    /**
     * 校验数据记录
     */
    protected void validateDataRecords(DataSetCreateParam param) {
        int pageNum = 1;
        int pageSize = datasetHelper.getDatasetBatchSize(null);
        DataSetPageParam pageParam = new DataSetPageParam();
        BeanUtils.copyProperties(param, pageParam);
        List<DataSetRecordParam> batchDataList = getNextBatchDataList(pageParam, pageNum, pageSize);
        List<DataFieldParam> headList = param.getHeadList();
        while (CollectionUtils.isNotEmpty(batchDataList)) {
            // 构建字段类型映射，支持空数据类型
            Map<String, FieldTypeEnum> fieldTypeMap = new HashMap<>();
            for (DataFieldParam field : headList) {
                if (field.getFieldType() != null) {
                    fieldTypeMap.put(field.getColumnName(), FieldTypeEnum.fromCode(field.getFieldType()));
                }
            }

            for (int i = 0; i < batchDataList.size(); i++) {
                DataSetRecordParam record = batchDataList.get(i);

                Map<String, Object> content = record.getContent();

                // 校验每个字段的数据
                for (DataFieldParam field : headList) {
                    String columnName = field.getColumnName();
                    String value = content.get(columnName) == null ? "" : content.get(columnName).toString();
                    FieldTypeEnum fieldType = fieldTypeMap.get(columnName);

                    // 只有当字段类型不为空时才进行校验
                    if (fieldType != null) {
                        validateFieldValue(columnName, value, fieldType, i + 1);
                    }
                }
            }
            pageNum++;
            batchDataList = getNextBatchDataList(pageParam, pageNum, pageSize);
        }
    }


    /**
     * 校验字段值
     */
    private void validateFieldValue(String columnName, String value, FieldTypeEnum fieldType, int rowIndex) {
        // 空数据默认为正确，不需要校验
        if (StringUtils.isBlank(value)) {
            return;
        }

        try {
            DataFieldStrategy dataFieldStrategy = dataFieldStrategyFactory.getStrategy(fieldType.getCode());
            dataFieldStrategy.validateValue(value);
        } catch (Exception e) {
            throw new AidaTrainingCheckException(
                    String.format("第%d行数据字段[%s]格式错误：%s", rowIndex, columnName, e.getMessage()));
        }
    }
} 