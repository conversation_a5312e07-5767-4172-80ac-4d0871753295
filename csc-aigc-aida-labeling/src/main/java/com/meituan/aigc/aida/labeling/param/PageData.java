package com.meituan.aigc.aida.labeling.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页数据
 *
 * @param <T>
 */
@Data
public class PageData<T> implements Serializable {

    private Long totalCount;

    private List<T> data;

    public PageData() {

    }

    public PageData(Long totalCount, List<T> data) {
        this.totalCount = totalCount;
        this.data = data;
    }
}
