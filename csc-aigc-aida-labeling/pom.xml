<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.meituan.csc.aigc.aida</groupId>
        <artifactId>csc-aigc-aida-training</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>csc-aigc-aida-labeling</artifactId>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dianping.dpsf</groupId>
                <artifactId>dpsf-net</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>mtthrift</artifactId>
                <version>2.11.1</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.octo</groupId>
                <artifactId>dorado-mesh</artifactId>
                <version>1.3.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.sankuai.csccratos</groupId>
            <artifactId>aida-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.csc.aigc.aida</groupId>
            <artifactId>csc-aigc-aida-labeling-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.csc.aigc</groupId>
            <artifactId>runtime-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.3.2</version>
        </dependency>
        <!-- 添加 Apache Commons IO 依赖 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <!--文枢-->
        <dependency>
            <groupId>com.meituan.sec</groupId>
            <artifactId>distribute-thrift-client</artifactId>
        </dependency>
        <!--S3-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
        </dependency>
        <!--MAFKA-->
        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>
        <!--Hive-->
        <dependency>
            <groupId>com.meituan.talostwo</groupId>
            <artifactId>talostwo-sdk-java</artifactId>
        </dependency>
        <!--海豚caseApi-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-haitun-case-api</artifactId>
        </dependency>
        <!-- 星脉 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-aircraft-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--海豚-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-haitun-kefu-api</artifactId>
        </dependency>
        <!--ES7客户端-->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-high-level-client</artifactId>
            <version>0.9.22_ES7</version>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                <groupId>net.sf.jopt-simple</groupId>
                <artifactId>jopt-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.10.2-mt4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--太平洋contact接口-->
        <dependency>
            <groupId>com.sankuai.pacific</groupId>
            <artifactId>csc-pacific-contact-chat-api</artifactId>
        </dependency>
        <!--太平洋新工作台(管家)-->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>csc-workbench-bff-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>csc-workbench-bff-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-pacific-efficiency-api</artifactId>
        </dependency>
        <!--太平洋上下文-->
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>csc-pacific-context-api</artifactId>
        </dependency>
        <!--太平洋通用工具包-->
        <dependency>
            <groupId>com.sankuai.csc</groupId>
            <artifactId>csc-pacific-common-tools</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-analtics-ctx-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.csc.aigc.eval</groupId>
            <artifactId>csc-eval-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.1</version>
            <scope>test</scope>
        </dependency>
        <!--业务场景相关接口-->
        <dependency>
            <groupId>com.sankuai.csccratos</groupId>
            <artifactId>csc-aida-label-client</artifactId>
        </dependency>
        <!--定时任务-->
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <configurationFile>src/main/resources/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>