package com.meituan.aigc.aida.labeling.config;

import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.sec.distributeplatform.thrift.ApiUploadFileService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class UploadFileConfig {

    private static final String REMOTE_APP_KEY = "com.sankuai.sec.distribute.platform";
    private static final String SERVICE_INTERFACE = "com.meituan.sec.distributeplatform.thrift.ApiUploadFileService";
    private static final int REMOTE_SERVER_PORT = CommonConstants.NetworkConfig.DEFAULT_REMOTE_SERVER_PORT;
    private static final int TIMEOUT = CommonConstants.NetworkConfig.DEFAULT_TIMEOUT;
    private static final boolean IS_NETTY_IO = true;

    @Bean
    public ApiUploadFileService.Iface apiUploadFileClient() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        try {
            proxy.setRemoteAppkey(REMOTE_APP_KEY);
            proxy.setServiceInterface(Class.forName(SERVICE_INTERFACE));
            proxy.setRemoteServerPort(REMOTE_SERVER_PORT);
            proxy.setTimeout(TIMEOUT);
            proxy.setNettyIO(IS_NETTY_IO);
            // 初始化实例
            proxy.afterPropertiesSet();
            // 获取代理对象
            return (ApiUploadFileService.Iface) proxy.getObject();
        } catch (Exception e) {
            log.error("UploadFileConfig.apiUploadFileClient error, e:", e);
        }
        return null;
    }

}
