package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: guowenhui
 * @Create: 2025/3/7 15:17
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LabelingDetailInfoVO extends LabelingDetailCommonVO implements Serializable {

    /**
     * 标注状态
     */
    private Integer labelStatus;
    /**
     * 原始数据JSON
     */
    private String rawDataContent;

    /**
     * 原始数据表头JSON
     */
    private String rawDataHeaders;
    /**
     * 原始数据内容映射内容JSON
     */
    private String rawDataMappedContent;

}
