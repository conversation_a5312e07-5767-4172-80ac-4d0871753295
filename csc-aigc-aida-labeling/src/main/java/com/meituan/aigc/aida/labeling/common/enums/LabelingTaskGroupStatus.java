package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 16:48
 * @Version: 1.0
 */
@Getter
public enum LabelingTaskGroupStatus {

    CREATING(1, "创建中"),
    PENDING_RECYCLE(2, "待回收"),
    FULL_RECYCLED(3, "全量回收"),
    PARTIAL_RECYCLED(4, "部分回收"),
    FULL_RELABELING(5, "全量复标"),
    FAILED(6, "失败"),
    RE_LABELING(7, "复标中"),
    RE_LABELED(8, "复标完成"),
    ARCHIVED(9, "已失效"),
    ;

    private final Integer code;

    /**
     * 状态描述
     */
    private final String value;

    LabelingTaskGroupStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    // 根据code获取枚举值
    public static LabelingTaskGroupStatus getByCode(int code) {
        for (LabelingTaskGroupStatus status : LabelingTaskGroupStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    private static final Set<Integer> RECYCLED_STATUS;
    static {
        Set<Integer> set = new HashSet<>(3);
        set.add(FULL_RECYCLED.code);
        set.add(PARTIAL_RECYCLED.code);
        set.add(FULL_RELABELING.code);
        RECYCLED_STATUS = Collections.unmodifiableSet(set);
    }

    /**
     * 判断当前状态是否是回收状态
     * @param status
     * @return
     */
    public static boolean isRecycled(Integer status) {
        return status != null && RECYCLED_STATUS.contains(status);
    }
}
