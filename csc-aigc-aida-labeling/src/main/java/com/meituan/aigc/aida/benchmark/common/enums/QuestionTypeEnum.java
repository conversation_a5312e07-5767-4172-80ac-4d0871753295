package com.meituan.aigc.aida.benchmark.common.enums;

import com.meituan.aigc.aida.common.enums.base.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题主客观枚举
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Getter
@AllArgsConstructor
public enum QuestionTypeEnum implements BaseEnum<Integer> {

    SUBJECTIVE(0, "主观"),
    OBJECTIVE(1, "客观");

    /**
     * 题型代码
     */
    private final Integer code;

    /**
     * 题型描述
     */
    private final String value;

    /**
     * 根据code获取value
     *
     * @param code 题型代码
     * @return 对应的题型描述，如果未找到则返回SUBJECTIVE
     */
    public static String getValueByCode(Integer code) {
        for (QuestionTypeEnum typeEnum : QuestionTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getValue();
            }
        }
        return SUBJECTIVE.value;
    }

    /**
     * 根据code获取枚举对象
     *
     * @param code 题型代码
     * @return 对应的枚举对象，如果未找到则返回SUBJECTIVE
     */
    public static QuestionTypeEnum getByCode(Integer code) {
        for (QuestionTypeEnum typeEnum : QuestionTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return SUBJECTIVE;
    }

    /**
     * 判断是否为客观题
     *
     * @return true为客观题，false为主观题
     */
    public boolean isObjective() {
        return this == OBJECTIVE;
    }

    /**
     * 判断是否为主观题
     *
     * @return true为主观题，false为客观题
     */
    public boolean isSubjective() {
        return this == SUBJECTIVE;
    }
} 