package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/1 19:30
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExportTaskData {

    /**
     * 要导出的子任务ID
     */
    private List<Long> subTaskIds;
    /**
     * 导出数据状态
     */
    private Integer labelDetailStatus;

}
