package com.meituan.aigc.aida.labeling.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 标签回收请求参数
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelRecycleParam implements Serializable {


    /**
     * 主任务ID
     */
    private Long taskId;
    /**
     * 分组ID
     */
    private Long groupId;

    /**
     * mis
     */
    private String labelerMis;
}
