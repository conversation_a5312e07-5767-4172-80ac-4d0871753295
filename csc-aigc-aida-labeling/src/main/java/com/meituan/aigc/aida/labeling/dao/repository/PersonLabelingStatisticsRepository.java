package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonLabelingStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonnelDetailQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DailyStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DailyBizStatisticsVO;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingAndCheckCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonLabelingQueryVO;

import java.util.Date;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 14:57
 * @Version: 1.0
 */
public interface PersonLabelingStatisticsRepository {
    /**
     * 查询标注人员的质检数据
     * @param param 查询条件
     * @return 结果
     */
    List<PersonLabelingQueryVO> queryPersonLabelingStats(PersonLabelingStatisticsDTO param);

    /**
     * 查询标注的每日统计数据
     * @param param 查询条件
     * @return 结果
     */
    List<DailyBizStatisticsVO> queryDailyTrends(DailyStatisticsDTO param);

    /**
     * 查询标注总量
     * @param query 查询条件
     * @return 结果
     */
    LabelingAndCheckCountVO statisticalLabelingData(DataStatisticalQuery query);

    /**
     * 查询标注的人员详情
     * @param param 查询条件
     * @return 结果
     */
    List<PersonnelDetailVO> queryPersonnelDetail(PersonnelDetailQuery param);

    /**
     * 批量新增
     * @param personLabelingStatisticsList 新增内容
     */
    void batchInsert(List<PersonLabelingStatistics> personLabelingStatisticsList);

    /**
     * 查询某天人员标注情况
     * @param userMisList 标注员mis
     * @param statDate    统计日期
     * @param bizType     业务类型
     * @param taskType    任务类型
     * @param dataType    数据类型
     * @return 结果
     */
    List<PersonLabelingStatistics> listPersonStatsByType(List<String> userMisList, Date statDate, Long bizType, Integer taskType, Integer dataType);

    /**
     * 批量更新
     * @param updatePersonLabelingStatistics 要更新的对象
     */
    void batchUpdate(List<PersonLabelingStatistics> updatePersonLabelingStatistics);

    /**
     * 批量删除
     * @param ids 主键
     */
    void batchDelete(List<Long> ids);

}
