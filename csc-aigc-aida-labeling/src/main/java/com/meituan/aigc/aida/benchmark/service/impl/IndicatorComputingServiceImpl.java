package com.meituan.aigc.aida.benchmark.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.aigc.aida.benchmark.common.enums.*;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails;
import com.meituan.aigc.aida.benchmark.dao.repository.LeaderboardDetailsRepository;
import com.meituan.aigc.aida.benchmark.pojo.dto.ExamStatisticsDTO;
import com.meituan.aigc.aida.benchmark.service.IndicatorComputingService;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingDetailRepository;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.meituan.aigc.aida.benchmark.common.constant.BenchmarkConstant.*;

/**
 * Benchmark指标计算服务
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
@Slf4j
@Service
public class IndicatorComputingServiceImpl implements IndicatorComputingService {

    @Resource
    private LabelingDetailRepository labelingDetailRepository;

    @Resource
    private LeaderboardDetailsRepository leaderboardDetailsRepository;

    /**
     * 满分分值
     */
    private static final int FULL_MARK = 3;

    /**
     * 可用分值
     */
    private static final int AVAILABLE_MARK = 2;

    @Override
    public void computingModelIndicator(Long versionId, String modelName, List<Long> taskIds,
                                        Map<Long, List<Long>> subtaskMap, List<String> examList,
                                        List<String> indicatorList) {
        if (Objects.isNull(versionId) || StringUtils.isEmpty(modelName)) {
            log.error("版本ID或模型名称不能为空");
            return;
        }

        log.info("开始计算模型指标, 版本ID: {}, 模型名称: {}, 任务数量: {}, 考点数量: {}",
                versionId, modelName, taskIds.size(), examList.size());

        // 使用线程安全的Map存储每个考点的所有分数值和统计信息
        Map<String, ExamStatisticsDTO> examScoreMap = new ConcurrentHashMap<>();
        // 初始化考点统计信息
        for (String exam : examList) {
            ExamStatisticsDTO examStatisticsDTO = new ExamStatisticsDTO();
            examStatisticsDTO.setRoundCount(taskIds.size());
            examScoreMap.put(exam, examStatisticsDTO);
        }

        int totalProcessedCount = 0;

        for (Long taskId : taskIds) {
            log.info("开始处理任务: {}, 模型: {}", taskId, modelName);

            List<Long> subTaskIds = subtaskMap.get(taskId);
            if (CollectionUtils.isEmpty(subTaskIds)) {
                log.info("任务 {} 没有子任务，跳过", taskId);
                continue;
            }

            int pageNum = 1;
            int pageSize = 500;
            int taskProcessedCount = 0;

            List<LabelTaskDataExportVO> dataList;
            do {
                dataList = labelingDetailRepository.pageExportDataBySubTaskIdAndStatus(
                        subTaskIds, null, pageNum, pageSize);

                if (CollectionUtils.isEmpty(dataList)) {
                    log.info("任务:{}, 第 {} 页查询结果为空", taskId, pageNum);
                    break;
                }

                // 处理当前批次的数据
                for (LabelTaskDataExportVO data : dataList) {
                    processDataItem(data, examList, examScoreMap);
                }

                pageNum++;
                taskProcessedCount += dataList.size();
                log.info("任务 {} 已处理 {} 条数据", taskId, taskProcessedCount);

            } while (dataList.size() == pageSize);

            totalProcessedCount += taskProcessedCount;
            log.info("任务 {} 处理完成，共处理 {} 条数据", taskId, taskProcessedCount);
        }

        // 计算统计指标并保存到数据库
        List<BenchmarkLeaderboardDetails> detailsList = calculateStatisticsAndCreateDetails(
                versionId, modelName, examScoreMap, totalProcessedCount);

        if (!CollectionUtils.isEmpty(detailsList)) {
            // 先删除已存在的记录（如果有）
            leaderboardDetailsRepository.deleteByVersionIdAndModelName(versionId, modelName);

            // 批量插入新记录
            int savedCount = leaderboardDetailsRepository.batchInsert(detailsList);
            log.info("模型 {} 指标计算并保存完成，版本ID: {}, 共处理 {} 条数据，保存 {} 个考点结果",
                    modelName, versionId, totalProcessedCount, savedCount);
        } else {
            log.warn("模型 {} 没有计算出有效的统计结果", modelName);
        }
    }

    /**
     * 计算统计指标并创建数据库记录
     *
     * @param versionId           版本ID
     * @param modelName           模型名称
     * @param examScoreMap        每个考点的统计信息
     * @param totalProcessedCount 总处理数据量
     * @return 数据库记录列表
     */
    private List<BenchmarkLeaderboardDetails> calculateStatisticsAndCreateDetails(Long versionId, String modelName,
                                                                                  Map<String, ExamStatisticsDTO> examScoreMap,
                                                                                  int totalProcessedCount) {
        List<BenchmarkLeaderboardDetails> detailsList = new ArrayList<>();
        Date now = new Date();

        for (Map.Entry<String, ExamStatisticsDTO> entry : examScoreMap.entrySet()) {
            String examName = entry.getKey();
            ExamStatisticsDTO statistics = entry.getValue();

            if (CollectionUtils.isEmpty(statistics.getExamItems())) {
                log.warn("考点 {} 没有有效分数，跳过统计计算", examName);
                continue;
            }

            try {
                List<BigDecimal> scores = statistics.getExamItems().stream()
                        .map(ExamStatisticsDTO.ExamItem::getScore)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 计算平均分（基于实际有分数的样本数量）
                BigDecimal avgByScores = statistics.getTotalScore().divide(BigDecimal.valueOf(statistics.getTotalCount()), 4, RoundingMode.HALF_UP);

                // 计算基于总数据量的平均分
                BigDecimal avgByTotal = statistics.getTotalScore().divide(BigDecimal.valueOf(statistics.getRoundCount()), 4, RoundingMode.HALF_UP);

                // 计算每个数据集的主观均分
                BigDecimal avgBySubjective = statistics.getSubjectiveTotalScore().divide(BigDecimal.valueOf(statistics.getRoundCount()), 4, RoundingMode.HALF_UP);

                // 计算每个数据集的客观均分
                BigDecimal avgByObjective = statistics.getObjectiveTotalScore().divide(BigDecimal.valueOf(statistics.getRoundCount()), 4, RoundingMode.HALF_UP);

                // 计算标准差
                BigDecimal std = calculateStandardDeviation(scores, avgByScores);

                if (ExamPointEnum.COMPREHENSIVE_SCORE.getValue().equals(examName)) {
                    // 计算可用率（2分或3分的样本数量/总样本数量）
                    BigDecimal availabilityRate = BigDecimal.valueOf(statistics.getUsableCount())
                            .divide(BigDecimal.valueOf(statistics.getTotalCount()), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));

                    // 计算满分率（3分的样本数量/总样本数量）
                    BigDecimal perfectRate = BigDecimal.valueOf(statistics.getPerfectCount())
                            .divide(BigDecimal.valueOf(statistics.getTotalCount()), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                    // 创建可用率记录
                    BenchmarkLeaderboardDetails availabilityDetail = createDetailRecord(versionId, modelName,
                            AVAILABILITY_RATE, availabilityRate, statistics.getTotalScore(), avgByTotal, std, now, null, null);
                    detailsList.add(availabilityDetail);

                    // 创建满分率记录
                    BenchmarkLeaderboardDetails perfectDetail = createDetailRecord(versionId, modelName,
                            FULL_SCORE_RATE, perfectRate, statistics.getTotalScore(), avgByTotal, std, now, null, null);
                    detailsList.add(perfectDetail);
                }

                BenchmarkLeaderboardDetails baseDetail = createDetailRecord(versionId, modelName, examName,
                        avgByTotal, statistics.getTotalScore(), avgByScores, std, now, avgByObjective, avgBySubjective);
                detailsList.add(baseDetail);


                log.info("考点: {}, 总样本: {}, 有效样本: {}, 总分: {}, 平均分: {}, 标准差: {}",
                        examName, totalProcessedCount, statistics.getExamItems().size(), statistics.getTotalScore(), avgByScores, std);

            } catch (Exception e) {
                log.error("计算考点 {} 的统计指标时发生异常: {}", examName, e.getMessage(), e);
            }
        }

        return detailsList;
    }

    /**
     * 创建数据库记录的辅助方法
     *
     * @param versionId   版本ID
     * @param modelName   模型名称
     * @param metricName  指标名称
     * @param metricValue 指标值
     * @param sum         总分
     * @param avg         平均分
     * @param std         标准差
     * @param now         当前时间
     * @return 数据库记录
     */
    private BenchmarkLeaderboardDetails createDetailRecord(Long versionId, String modelName, String metricName,
                                                           BigDecimal metricValue, BigDecimal sum, BigDecimal avg,
                                                           BigDecimal std, Date now, BigDecimal objectiveScore, BigDecimal subjectiveScore) {
        BenchmarkLeaderboardDetails detail = new BenchmarkLeaderboardDetails();
        detail.setVersionId(versionId);
        detail.setModelName(modelName);
        detail.setMetricName(metricName);
        detail.setMetricValue(metricValue);
        detail.setSum(sum);
        detail.setAvg(avg);
        detail.setStd(std);
        detail.setObjectiveScore(objectiveScore);
        detail.setSubjectiveScore(subjectiveScore);
        detail.setCreatedAt(now);
        detail.setUpdatedAt(now);
        return detail;
    }

    /**
     * 计算标准差
     *
     * @param scores 分数列表
     * @param avg    平均值
     * @return 标准差
     */
    private BigDecimal calculateStandardDeviation(List<BigDecimal> scores, BigDecimal avg) {
        if (CollectionUtils.isEmpty(scores) || scores.size() < 2) {
            return BigDecimal.ZERO;
        }

        try {
            // 计算方差：Σ(xi - avg)² / (n-1)
            BigDecimal variance = scores.stream()
                    .map(score -> {
                        BigDecimal diff = score.subtract(avg);
                        return diff.multiply(diff);
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(scores.size() - 1), 6, RoundingMode.HALF_UP);

            // 计算标准差：√variance
            return sqrt(variance);

        } catch (Exception e) {
            log.error("计算标准差时发生异常: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算平方根（使用牛顿迭代法）
     *
     * @param value 需要开方的值
     * @return 平方根
     */
    private BigDecimal sqrt(BigDecimal value) {
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        if (value.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("不能计算负数的平方根");
        }

        // 使用牛顿迭代法计算平方根
        BigDecimal x = value;
        BigDecimal lastX;

        int iterations = 0;
        final int maxIterations = 50;
        final BigDecimal precision = new BigDecimal("0.0001");

        do {
            lastX = x;
            x = x.add(value.divide(x, 6, RoundingMode.HALF_UP))
                    .divide(BigDecimal.valueOf(2), 6, RoundingMode.HALF_UP);
            iterations++;
        } while (x.subtract(lastX).abs().compareTo(precision) > 0 && iterations < maxIterations);

        return x.setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 处理单条数据，提取考点分数并收集统计信息
     *
     * @param data         标注数据
     * @param examList     考点列表
     * @param examScoreMap 考点统计信息映射表
     */
    private void processDataItem(LabelTaskDataExportVO data, List<String> examList,
                                 Map<String, ExamStatisticsDTO> examScoreMap) {
        try {
            String labelingDataJson = null;
            // 优先使用质检修改后的标注数据
            if (StringUtils.hasText(data.getQualityCheckModifiedLabelingItems())) {
                labelingDataJson = data.getQualityCheckModifiedLabelingItems();
            } else if (StringUtils.hasText(data.getQualityCheckItemsResult())) {
                labelingDataJson = data.getLabelingItemsResult();
            }

            if (StringUtils.isEmpty(labelingDataJson)) {
                log.info("会话 {} 没有可用的标注数据，跳过", data.getSessionId());
                return;
            }

            // 解析JSON数据
            List<List<LabelResult>> labelResultList = JSON.parseObject(labelingDataJson,
                    new TypeReference<List<List<LabelResult>>>() {
                    });

            if (CollectionUtils.isEmpty(labelResultList)) {
                log.info("会话 {} 解析后的标注数据为空", data.getSessionId());
                return;
            }

            Map<String, String> labelReferMap = new HashMap<>();
            if (Objects.nonNull(data.getRawDataContent())) {
                labelReferMap = JSON.parseObject(data.getRawDataContent(), new TypeReference<Map<String, String>>() {
                });
            }

            // 遍历解析后的数据，匹配考点并收集分数
            for (List<LabelResult> innerList : labelResultList) {
                if (CollectionUtils.isEmpty(innerList)) {
                    continue;
                }

                for (LabelResult labelResult : innerList) {
                    if (Objects.isNull(labelResult) || StringUtils.isEmpty(labelResult.getName())) {
                        continue;
                    }

                    String examName = labelResult.getName();
                    String scoreValue = labelResult.getValue();


                    // 检查是否是目标考点并且有有效分数
                    if (examList.contains(examName)) {
                        BigDecimal score = getScore(scoreValue);
                        ExamStatisticsDTO statistics = examScoreMap.get(examName);
                        updateIndicatorStatics(score, statistics, labelReferMap);
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理数据项时发生异常，会话ID: {}, 错误信息: {}", data.getSessionId(), e.getMessage(), e);
        }
    }

    /**
     * 提取分数
     *
     * @param scoreValue 分值字符串
     * @return 得分
     */
    private BigDecimal getScore(String scoreValue) {
        if (StringUtils.isEmpty(scoreValue)) {
            return BigDecimal.ZERO;
        } else {
            return new BigDecimal(scoreValue);
        }
    }

    /**
     * 更新指标统计信息
     *
     * @param score         分数
     * @param statistics    统计信息
     * @param labelReferMap 原始数据映射
     */
    private void updateIndicatorStatics(BigDecimal score, ExamStatisticsDTO statistics, Map<String, String> labelReferMap) {
        if (Objects.isNull(statistics) || Objects.isNull(score)) {
            return;
        }

        try {
            // 创建并配置ExamItem
            ExamStatisticsDTO.ExamItem examItem = createExamItem(score, labelReferMap);
            statistics.getExamItems().add(examItem);

            // 根据分数值更新统计计数
            int scoreInt = score.intValue();
            if (scoreInt == FULL_MARK) {
                statistics.incrementPerfectCount();
                statistics.incrementUsableCount();
            } else if (scoreInt >= AVAILABLE_MARK) {
                statistics.incrementUsableCount();
            }

            // 根据题目类型更新统计
            if (QuestionTypeEnum.OBJECTIVE.getValue().equals(examItem.getIsObjective())) {
                statistics.incrementObjectiveCount();
                // 更新客观题总分
                BigDecimal currentObjectiveTotal = statistics.getObjectiveTotalScore();
                if (currentObjectiveTotal == null) {
                    currentObjectiveTotal = BigDecimal.ZERO;
                }
                statistics.setObjectiveTotalScore(currentObjectiveTotal.add(score));
            } else if (QuestionTypeEnum.SUBJECTIVE.getValue().equals(examItem.getIsObjective())) {
                statistics.incrementSubjectiveCount();
                // 更新主观题总分
                BigDecimal currentSubjectiveTotal = statistics.getSubjectiveTotalScore();
                if (currentSubjectiveTotal == null) {
                    currentSubjectiveTotal = BigDecimal.ZERO;
                }
                statistics.setSubjectiveTotalScore(currentSubjectiveTotal.add(score));
            }

            // 更新总分
            BigDecimal currentTotal = statistics.getTotalScore();
            if (currentTotal == null) {
                currentTotal = BigDecimal.ZERO;
            }
            statistics.setTotalScore(currentTotal.add(score));
            statistics.addTotalCount();
        } catch (Exception e) {
            log.error("更新指标统计时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建ExamItem对象并设置属性
     *
     * @param score         分数
     * @param labelReferMap 原始数据映射
     * @return 配置好的ExamItem对象
     */
    private ExamStatisticsDTO.ExamItem createExamItem(BigDecimal score, Map<String, String> labelReferMap) {
        ExamStatisticsDTO.ExamItem examItem = new ExamStatisticsDTO.ExamItem();
        examItem.setScore(score);

        // 从原始数据中提取题目属性
        if (labelReferMap != null && !labelReferMap.isEmpty()) {
            // 设置主客观类型
            String questionType = labelReferMap.getOrDefault(SUBJECTIVE_OBJECTIVE, null);
            examItem.setIsObjective(questionType);

            // 设置难易程度
            String difficulty = labelReferMap.getOrDefault(DIFFICULTY_LEVEL, null);
            examItem.setLevel(difficulty);
            // 设置长度分级
            String length = labelReferMap.getOrDefault(LENGTH_TAG, null);
            examItem.setLengthLevel(length);

            // 设置强对抗标识
            String strongAdversarial = labelReferMap.getOrDefault(IS_STRONG_ANTI_SCENE, null);
            examItem.setIsStrong(strongAdversarial);
        }
        return examItem;
    }
}
