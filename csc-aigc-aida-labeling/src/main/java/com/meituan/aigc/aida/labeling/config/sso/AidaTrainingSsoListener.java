package com.meituan.aigc.aida.labeling.config.sso;

import com.dianping.cat.Cat;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingSsoAuthException;
import com.sankuai.it.sso.sdk.enums.AuthFailedCodeEnum;
import com.sankuai.it.sso.sdk.listener.SSOListener;
import com.sankuai.it.sso.sdk.utils.WebUtil;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Slf4j
public class AidaTrainingSsoListener implements SSOListener {
    @Override
    public String onRedirectingToSSOLogin(HttpServletRequest req, HttpServletResponse res, String callbackUri) {
        log.info("onRedirectingToSSOLogin, callbackUri:{}, url:{}", callbackUri, req.getRequestURI());
        return callbackUri;
    }

    @Override
    public String onRedirectingToOriginalUrl(HttpServletRequest req, HttpServletResponse res, String ssoid,
            String originalUrl) {
        log.info("onRedirectingToOriginalUrl, ssoid:{}, originalUrl:{}", ssoid, originalUrl);
        return originalUrl;
    }

    @Override
    public boolean onSSOAuthed(HttpServletRequest req, HttpServletResponse res, User user) {
        log.info("onSSOAuthed login success! url:{}, user:{}", req.getRequestURI(), user);
        return true;
    }

    @Override
    public boolean onSSOAuthFailed(HttpServletRequest req, HttpServletResponse res, AuthFailedCodeEnum failedCode) {
        if ("OPTIONS".equals(req.getMethod())) {
            return true;
        }

        log.info("onSSOAuthFailed, url:{}, authFailedCodeEnum:{}, ip={}", req.getRequestURI(), failedCode,
                req.getRemoteAddr());

        String errorMessage = "SSO鉴权失败,错误信息:" + failedCode.toString();
        Cat.logError(errorMessage, new AidaTrainingSsoAuthException(errorMessage, failedCode));

        if (AuthFailedCodeEnum.SSOID_EXPIRED.equals(failedCode) || AuthFailedCodeEnum.SSOID_NULL.equals(failedCode)
                || AuthFailedCodeEnum.SSOID_INVALID.equals(failedCode)) {
            AuthHelper.ssoFailed(res, failedCode.getReason());
            return true;
        }

        return false;
    }

    @Override
    public void onSSOLogouted(HttpServletRequest req, HttpServletResponse res, User user) {
        WebUtil.removeCookie(res, "ssoid");
        log.info("onSSOLogouted, url:{}, user:{}", req.getRequestURI(), user);
    }

    @Override
    public void onRefresSsoId(HttpServletRequest req, HttpServletResponse res, String ssoid) {

    }
}
