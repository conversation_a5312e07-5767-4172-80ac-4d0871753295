package com.meituan.aigc.aida.data.management.dataset.dto;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-28 22:26
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldMappingDTO implements Serializable {

    /**
     * 字段类型
     */
    FieldTypeEnum fieldType;

    /**
     * ES字段
     * ES中实际存储的列名
     */
    String esField;

    /**
     * JSON类型的一级JSON路径
     * 嵌套JSON一级的路径名称
     */
    List<String> firstLevelJsonKey;

    /**
     * JSON类型的二级JSON路径
     * 嵌套JSON二级的路径名称
     */
    List<String> secondLevelJsonKey;
}
