package com.meituan.aigc.aida.data.management.fetch.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class LlmSignalTrackingRules {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 标准问ID
     */
    private String typicalQuestionIds;

    /**
     * 客服mis号
     */
    private String misIds;

    /**
     * 太平洋弹屏AC接口ID
     */
    private String acInterface;

    /**
     * AI搭机器人配置
     */
    private String aidaAppCfg;

    /**
     * 创建人mis
     */
    private String creatorMis;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 是否删除
     */
    private Boolean isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}