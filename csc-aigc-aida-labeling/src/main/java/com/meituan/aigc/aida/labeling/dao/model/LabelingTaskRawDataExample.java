package com.meituan.aigc.aida.labeling.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabelingTaskRawDataExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LabelingTaskRawDataExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(String value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(String value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(String value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(String value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(String value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(String value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLike(String value) {
            addCriterion("session_id like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLike(String value) {
            addCriterion("session_id not like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<String> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<String> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(String value1, String value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(String value1, String value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionTimeIsNull() {
            addCriterion("session_time is null");
            return (Criteria) this;
        }

        public Criteria andSessionTimeIsNotNull() {
            addCriterion("session_time is not null");
            return (Criteria) this;
        }

        public Criteria andSessionTimeEqualTo(Date value) {
            addCriterion("session_time =", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeNotEqualTo(Date value) {
            addCriterion("session_time <>", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeGreaterThan(Date value) {
            addCriterion("session_time >", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("session_time >=", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeLessThan(Date value) {
            addCriterion("session_time <", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeLessThanOrEqualTo(Date value) {
            addCriterion("session_time <=", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeIn(List<Date> values) {
            addCriterion("session_time in", values, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeNotIn(List<Date> values) {
            addCriterion("session_time not in", values, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeBetween(Date value1, Date value2) {
            addCriterion("session_time between", value1, value2, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeNotBetween(Date value1, Date value2) {
            addCriterion("session_time not between", value1, value2, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andRawDataContentIsNull() {
            addCriterion("raw_data_content is null");
            return (Criteria) this;
        }

        public Criteria andRawDataContentIsNotNull() {
            addCriterion("raw_data_content is not null");
            return (Criteria) this;
        }

        public Criteria andRawDataContentEqualTo(String value) {
            addCriterion("raw_data_content =", value, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentNotEqualTo(String value) {
            addCriterion("raw_data_content <>", value, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentGreaterThan(String value) {
            addCriterion("raw_data_content >", value, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentGreaterThanOrEqualTo(String value) {
            addCriterion("raw_data_content >=", value, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentLessThan(String value) {
            addCriterion("raw_data_content <", value, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentLessThanOrEqualTo(String value) {
            addCriterion("raw_data_content <=", value, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentLike(String value) {
            addCriterion("raw_data_content like", value, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentNotLike(String value) {
            addCriterion("raw_data_content not like", value, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentIn(List<String> values) {
            addCriterion("raw_data_content in", values, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentNotIn(List<String> values) {
            addCriterion("raw_data_content not in", values, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentBetween(String value1, String value2) {
            addCriterion("raw_data_content between", value1, value2, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataContentNotBetween(String value1, String value2) {
            addCriterion("raw_data_content not between", value1, value2, "rawDataContent");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersIsNull() {
            addCriterion("raw_data_headers is null");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersIsNotNull() {
            addCriterion("raw_data_headers is not null");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersEqualTo(String value) {
            addCriterion("raw_data_headers =", value, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersNotEqualTo(String value) {
            addCriterion("raw_data_headers <>", value, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersGreaterThan(String value) {
            addCriterion("raw_data_headers >", value, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersGreaterThanOrEqualTo(String value) {
            addCriterion("raw_data_headers >=", value, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersLessThan(String value) {
            addCriterion("raw_data_headers <", value, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersLessThanOrEqualTo(String value) {
            addCriterion("raw_data_headers <=", value, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersLike(String value) {
            addCriterion("raw_data_headers like", value, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersNotLike(String value) {
            addCriterion("raw_data_headers not like", value, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersIn(List<String> values) {
            addCriterion("raw_data_headers in", values, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersNotIn(List<String> values) {
            addCriterion("raw_data_headers not in", values, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersBetween(String value1, String value2) {
            addCriterion("raw_data_headers between", value1, value2, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataHeadersNotBetween(String value1, String value2) {
            addCriterion("raw_data_headers not between", value1, value2, "rawDataHeaders");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentIsNull() {
            addCriterion("raw_data_mapped_content is null");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentIsNotNull() {
            addCriterion("raw_data_mapped_content is not null");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentEqualTo(String value) {
            addCriterion("raw_data_mapped_content =", value, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentNotEqualTo(String value) {
            addCriterion("raw_data_mapped_content <>", value, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentGreaterThan(String value) {
            addCriterion("raw_data_mapped_content >", value, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentGreaterThanOrEqualTo(String value) {
            addCriterion("raw_data_mapped_content >=", value, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentLessThan(String value) {
            addCriterion("raw_data_mapped_content <", value, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentLessThanOrEqualTo(String value) {
            addCriterion("raw_data_mapped_content <=", value, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentLike(String value) {
            addCriterion("raw_data_mapped_content like", value, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentNotLike(String value) {
            addCriterion("raw_data_mapped_content not like", value, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentIn(List<String> values) {
            addCriterion("raw_data_mapped_content in", values, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentNotIn(List<String> values) {
            addCriterion("raw_data_mapped_content not in", values, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentBetween(String value1, String value2) {
            addCriterion("raw_data_mapped_content between", value1, value2, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andRawDataMappedContentNotBetween(String value1, String value2) {
            addCriterion("raw_data_mapped_content not between", value1, value2, "rawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andExtraInfoIsNull() {
            addCriterion("extra_info is null");
            return (Criteria) this;
        }

        public Criteria andExtraInfoIsNotNull() {
            addCriterion("extra_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtraInfoEqualTo(String value) {
            addCriterion("extra_info =", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoNotEqualTo(String value) {
            addCriterion("extra_info <>", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoGreaterThan(String value) {
            addCriterion("extra_info >", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoGreaterThanOrEqualTo(String value) {
            addCriterion("extra_info >=", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoLessThan(String value) {
            addCriterion("extra_info <", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoLessThanOrEqualTo(String value) {
            addCriterion("extra_info <=", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoLike(String value) {
            addCriterion("extra_info like", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoNotLike(String value) {
            addCriterion("extra_info not like", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoIn(List<String> values) {
            addCriterion("extra_info in", values, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoNotIn(List<String> values) {
            addCriterion("extra_info not in", values, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoBetween(String value1, String value2) {
            addCriterion("extra_info between", value1, value2, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoNotBetween(String value1, String value2) {
            addCriterion("extra_info not between", value1, value2, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNull() {
            addCriterion("message_id is null");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNotNull() {
            addCriterion("message_id is not null");
            return (Criteria) this;
        }

        public Criteria andMessageIdEqualTo(String value) {
            addCriterion("message_id =", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotEqualTo(String value) {
            addCriterion("message_id <>", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThan(String value) {
            addCriterion("message_id >", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanOrEqualTo(String value) {
            addCriterion("message_id >=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThan(String value) {
            addCriterion("message_id <", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanOrEqualTo(String value) {
            addCriterion("message_id <=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLike(String value) {
            addCriterion("message_id like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotLike(String value) {
            addCriterion("message_id not like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIn(List<String> values) {
            addCriterion("message_id in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotIn(List<String> values) {
            addCriterion("message_id not in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdBetween(String value1, String value2) {
            addCriterion("message_id between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotBetween(String value1, String value2) {
            addCriterion("message_id not between", value1, value2, "messageId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}