package com.meituan.aigc.aida.data.management.fetch.dto;

import lombok.Data;

import java.util.Map;

/**
 * @Author: guowenhui
 * @Create: 2025/4/30 16:37
 * @Version: 1.0
 */
@Data
public class AidaAppCfgDTO {

    /**
     * 实时触发机器人
     */
    private Map<String, String> realTimeTriggerRobot;

    /**
     * 延迟触发机器人
     */
    private Map<String, String> delayTriggerRobot;

    /**
     * 延迟触发时间 (秒)
     */
    private Integer delayTime;

}
