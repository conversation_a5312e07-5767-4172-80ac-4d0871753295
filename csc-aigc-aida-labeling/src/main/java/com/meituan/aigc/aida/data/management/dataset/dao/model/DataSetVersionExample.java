package com.meituan.aigc.aida.data.management.dataset.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DataSetVersionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DataSetVersionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDataSetIdIsNull() {
            addCriterion("data_set_id is null");
            return (Criteria) this;
        }

        public Criteria andDataSetIdIsNotNull() {
            addCriterion("data_set_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataSetIdEqualTo(Long value) {
            addCriterion("data_set_id =", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdNotEqualTo(Long value) {
            addCriterion("data_set_id <>", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdGreaterThan(Long value) {
            addCriterion("data_set_id >", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_set_id >=", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdLessThan(Long value) {
            addCriterion("data_set_id <", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdLessThanOrEqualTo(Long value) {
            addCriterion("data_set_id <=", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdIn(List<Long> values) {
            addCriterion("data_set_id in", values, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdNotIn(List<Long> values) {
            addCriterion("data_set_id not in", values, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdBetween(Long value1, Long value2) {
            addCriterion("data_set_id between", value1, value2, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdNotBetween(Long value1, Long value2) {
            addCriterion("data_set_id not between", value1, value2, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andVersionNameIsNull() {
            addCriterion("version_name is null");
            return (Criteria) this;
        }

        public Criteria andVersionNameIsNotNull() {
            addCriterion("version_name is not null");
            return (Criteria) this;
        }

        public Criteria andVersionNameEqualTo(String value) {
            addCriterion("version_name =", value, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameNotEqualTo(String value) {
            addCriterion("version_name <>", value, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameGreaterThan(String value) {
            addCriterion("version_name >", value, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameGreaterThanOrEqualTo(String value) {
            addCriterion("version_name >=", value, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameLessThan(String value) {
            addCriterion("version_name <", value, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameLessThanOrEqualTo(String value) {
            addCriterion("version_name <=", value, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameLike(String value) {
            addCriterion("version_name like", value, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameNotLike(String value) {
            addCriterion("version_name not like", value, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameIn(List<String> values) {
            addCriterion("version_name in", values, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameNotIn(List<String> values) {
            addCriterion("version_name not in", values, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameBetween(String value1, String value2) {
            addCriterion("version_name between", value1, value2, "versionName");
            return (Criteria) this;
        }

        public Criteria andVersionNameNotBetween(String value1, String value2) {
            addCriterion("version_name not between", value1, value2, "versionName");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andHeadConfigIsNull() {
            addCriterion("head_config is null");
            return (Criteria) this;
        }

        public Criteria andHeadConfigIsNotNull() {
            addCriterion("head_config is not null");
            return (Criteria) this;
        }

        public Criteria andHeadConfigEqualTo(String value) {
            addCriterion("head_config =", value, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigNotEqualTo(String value) {
            addCriterion("head_config <>", value, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigGreaterThan(String value) {
            addCriterion("head_config >", value, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigGreaterThanOrEqualTo(String value) {
            addCriterion("head_config >=", value, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigLessThan(String value) {
            addCriterion("head_config <", value, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigLessThanOrEqualTo(String value) {
            addCriterion("head_config <=", value, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigLike(String value) {
            addCriterion("head_config like", value, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigNotLike(String value) {
            addCriterion("head_config not like", value, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigIn(List<String> values) {
            addCriterion("head_config in", values, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigNotIn(List<String> values) {
            addCriterion("head_config not in", values, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigBetween(String value1, String value2) {
            addCriterion("head_config between", value1, value2, "headConfig");
            return (Criteria) this;
        }

        public Criteria andHeadConfigNotBetween(String value1, String value2) {
            addCriterion("head_config not between", value1, value2, "headConfig");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameIsNull() {
            addCriterion("es_index_name is null");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameIsNotNull() {
            addCriterion("es_index_name is not null");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameEqualTo(String value) {
            addCriterion("es_index_name =", value, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameNotEqualTo(String value) {
            addCriterion("es_index_name <>", value, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameGreaterThan(String value) {
            addCriterion("es_index_name >", value, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameGreaterThanOrEqualTo(String value) {
            addCriterion("es_index_name >=", value, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameLessThan(String value) {
            addCriterion("es_index_name <", value, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameLessThanOrEqualTo(String value) {
            addCriterion("es_index_name <=", value, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameLike(String value) {
            addCriterion("es_index_name like", value, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameNotLike(String value) {
            addCriterion("es_index_name not like", value, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameIn(List<String> values) {
            addCriterion("es_index_name in", values, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameNotIn(List<String> values) {
            addCriterion("es_index_name not in", values, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameBetween(String value1, String value2) {
            addCriterion("es_index_name between", value1, value2, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andEsIndexNameNotBetween(String value1, String value2) {
            addCriterion("es_index_name not between", value1, value2, "esIndexName");
            return (Criteria) this;
        }

        public Criteria andVersionTypeIsNull() {
            addCriterion("version_type is null");
            return (Criteria) this;
        }

        public Criteria andVersionTypeIsNotNull() {
            addCriterion("version_type is not null");
            return (Criteria) this;
        }

        public Criteria andVersionTypeEqualTo(Integer value) {
            addCriterion("version_type =", value, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeNotEqualTo(Integer value) {
            addCriterion("version_type <>", value, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeGreaterThan(Integer value) {
            addCriterion("version_type >", value, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("version_type >=", value, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeLessThan(Integer value) {
            addCriterion("version_type <", value, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeLessThanOrEqualTo(Integer value) {
            addCriterion("version_type <=", value, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeIn(List<Integer> values) {
            addCriterion("version_type in", values, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeNotIn(List<Integer> values) {
            addCriterion("version_type not in", values, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeBetween(Integer value1, Integer value2) {
            addCriterion("version_type between", value1, value2, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("version_type not between", value1, value2, "versionType");
            return (Criteria) this;
        }

        public Criteria andVersionSourceIsNull() {
            addCriterion("version_source is null");
            return (Criteria) this;
        }

        public Criteria andVersionSourceIsNotNull() {
            addCriterion("version_source is not null");
            return (Criteria) this;
        }

        public Criteria andVersionSourceEqualTo(String value) {
            addCriterion("version_source =", value, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceNotEqualTo(String value) {
            addCriterion("version_source <>", value, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceGreaterThan(String value) {
            addCriterion("version_source >", value, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceGreaterThanOrEqualTo(String value) {
            addCriterion("version_source >=", value, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceLessThan(String value) {
            addCriterion("version_source <", value, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceLessThanOrEqualTo(String value) {
            addCriterion("version_source <=", value, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceLike(String value) {
            addCriterion("version_source like", value, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceNotLike(String value) {
            addCriterion("version_source not like", value, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceIn(List<String> values) {
            addCriterion("version_source in", values, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceNotIn(List<String> values) {
            addCriterion("version_source not in", values, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceBetween(String value1, String value2) {
            addCriterion("version_source between", value1, value2, "versionSource");
            return (Criteria) this;
        }

        public Criteria andVersionSourceNotBetween(String value1, String value2) {
            addCriterion("version_source not between", value1, value2, "versionSource");
            return (Criteria) this;
        }

        public Criteria andDataCountIsNull() {
            addCriterion("data_count is null");
            return (Criteria) this;
        }

        public Criteria andDataCountIsNotNull() {
            addCriterion("data_count is not null");
            return (Criteria) this;
        }

        public Criteria andDataCountEqualTo(Integer value) {
            addCriterion("data_count =", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountNotEqualTo(Integer value) {
            addCriterion("data_count <>", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountGreaterThan(Integer value) {
            addCriterion("data_count >", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_count >=", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountLessThan(Integer value) {
            addCriterion("data_count <", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountLessThanOrEqualTo(Integer value) {
            addCriterion("data_count <=", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountIn(List<Integer> values) {
            addCriterion("data_count in", values, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountNotIn(List<Integer> values) {
            addCriterion("data_count not in", values, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountBetween(Integer value1, Integer value2) {
            addCriterion("data_count between", value1, value2, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountNotBetween(Integer value1, Integer value2) {
            addCriterion("data_count not between", value1, value2, "dataCount");
            return (Criteria) this;
        }

        public Criteria andVersionStatusIsNull() {
            addCriterion("version_status is null");
            return (Criteria) this;
        }

        public Criteria andVersionStatusIsNotNull() {
            addCriterion("version_status is not null");
            return (Criteria) this;
        }

        public Criteria andVersionStatusEqualTo(Integer value) {
            addCriterion("version_status =", value, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusNotEqualTo(Integer value) {
            addCriterion("version_status <>", value, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusGreaterThan(Integer value) {
            addCriterion("version_status >", value, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("version_status >=", value, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusLessThan(Integer value) {
            addCriterion("version_status <", value, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusLessThanOrEqualTo(Integer value) {
            addCriterion("version_status <=", value, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusIn(List<Integer> values) {
            addCriterion("version_status in", values, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusNotIn(List<Integer> values) {
            addCriterion("version_status not in", values, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusBetween(Integer value1, Integer value2) {
            addCriterion("version_status between", value1, value2, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andVersionStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("version_status not between", value1, value2, "versionStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}