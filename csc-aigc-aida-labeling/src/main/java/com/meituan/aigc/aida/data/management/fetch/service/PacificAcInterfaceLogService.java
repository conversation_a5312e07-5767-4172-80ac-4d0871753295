package com.meituan.aigc.aida.data.management.fetch.service;

import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/9 20:48
 * @Version: 1.0
 */
public interface PacificAcInterfaceLogService {


    /**
     * 根据联络id、联络类型和客服mis查询接口日志
     *
     * @param contactId
     * @param contactType
     * @param staffMis
     * @return
     */
    List<PacificAcInterfaceLogWithBlobs> listByContactIdContactTypeAndStaffMis(String contactId, String contactType, String staffMis);

}
