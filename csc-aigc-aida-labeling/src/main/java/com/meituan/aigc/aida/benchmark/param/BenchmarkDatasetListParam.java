package com.meituan.aigc.aida.benchmark.param;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Benchmark数据集列表查询参数
 * 
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
public class BenchmarkDatasetListParam implements Serializable {

    /**
     * benchmark版本ID
     */
    @NotNull(message = "版本ID不能为空")
    private Long versionId;

    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    private Integer pageSize = 10;
} 