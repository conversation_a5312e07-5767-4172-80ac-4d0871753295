package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTaskExample;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckSampleCountPO;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LabelingQualityCheckTaskMapper {
    long countByExample(LabelingQualityCheckTaskExample example);

    int deleteByExample(LabelingQualityCheckTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LabelingQualityCheckTask record);

    int insertSelective(LabelingQualityCheckTask record);

    List<LabelingQualityCheckTask> selectByExample(LabelingQualityCheckTaskExample example);

    LabelingQualityCheckTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LabelingQualityCheckTask record, @Param("example") LabelingQualityCheckTaskExample example);

    int updateByExample(@Param("record") LabelingQualityCheckTask record, @Param("example") LabelingQualityCheckTaskExample example);

    int updateByPrimaryKeySelective(LabelingQualityCheckTask record);

    int updateByPrimaryKey(LabelingQualityCheckTask record);

    int batchInsert(@Param("list") List<LabelingQualityCheckTask> list);

    List<LabelingQualityCheckTask> listByTaskId(@Param("taskId") Long taskId);

    int updateByTaskId(LabelingQualityCheckTask record);

    List<QualityCheckTaskPO> listByTaskIdAndNameAndQualityCheckerMis(QualityCheckParam param);

    Integer countByTaskIdAndStatus(@Param("taskId") Long taskId, @Param("statusList")List<Integer> statusList);

    /**
     * 根据质检任务id统计质检样本数量
     *
     * @param taskId 质检任务id
     * @return 质检样本数量
     */
    QualityCheckSampleCountPO countSampleSizeByTaskId(Long taskId);
}