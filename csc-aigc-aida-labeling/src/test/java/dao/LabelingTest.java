package dao;

import com.meituan.aigc.aida.labeling.Application;
import com.meituan.aigc.aida.labeling.param.LabelingDetailParam;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingDetailDataDTO;
import com.meituan.aigc.aida.labeling.service.LabelingSubTaskService;
import com.meituan.aigc.aida.labeling.service.TaskService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * <AUTHOR>
 * @date 2025-03-04 15:20
 * @description
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class LabelingTest {

    @Autowired
    private LabelingSubTaskService labelingSubTaskService;

    @Autowired
    private TaskService taskService;


    @Test
    public void test(){
        LabelingDetailParam param = new LabelingDetailParam();
        param.setSubTaskId(1L);
        param.setPageNum(1);
        param.setPageSize(10);
        LabelingDetailDataDTO labelingDetailDataDTO = labelingSubTaskService.pageDetailData(param);
        System.out.println(labelingDetailDataDTO.toString());

//        List<LabelTaskGroupDTO> labelTaskGroupDTOS = taskService.listGroupsByTaskId(1L);
//        System.out.println(labelTaskGroupDTOS);

    }
}
