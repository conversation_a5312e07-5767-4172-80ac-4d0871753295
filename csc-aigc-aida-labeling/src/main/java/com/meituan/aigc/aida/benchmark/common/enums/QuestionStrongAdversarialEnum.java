package com.meituan.aigc.aida.benchmark.common.enums;

import com.meituan.aigc.aida.common.enums.base.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题强对抗枚举
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Getter
@AllArgsConstructor
public enum QuestionStrongAdversarialEnum implements BaseEnum<Integer> {

    NO(0, "否"),
    YES(1, "是");

    /**
     * 强对抗标识代码
     */
    private final Integer code;

    /**
     * 强对抗标识描述
     */
    private final String value;

    /**
     * 根据code获取value
     *
     * @param code 强对抗标识代码
     * @return 对应的强对抗标识描述，如果未找到则返回NO
     */
    public static String getValueByCode(Integer code) {
        for (QuestionStrongAdversarialEnum adversarialEnum : QuestionStrongAdversarialEnum.values()) {
            if (adversarialEnum.getCode().equals(code)) {
                return adversarialEnum.getValue();
            }
        }
        return NO.value;
    }

    /**
     * 根据code获取枚举对象
     *
     * @param code 强对抗标识代码
     * @return 对应的枚举对象，如果未找到则返回NO
     */
    public static QuestionStrongAdversarialEnum getByCode(Integer code) {
        for (QuestionStrongAdversarialEnum adversarialEnum : QuestionStrongAdversarialEnum.values()) {
            if (adversarialEnum.getCode().equals(code)) {
                return adversarialEnum;
            }
        }
        return NO;
    }

    /**
     * 判断是否为强对抗
     *
     * @return true为强对抗，false为非强对抗
     */
    public boolean isStrongAdversarial() {
        return this == YES;
    }

    /**
     * 根据布尔值获取枚举对象
     *
     * @param isStrong 是否强对抗
     * @return 对应的枚举对象
     */
    public static QuestionStrongAdversarialEnum fromBoolean(boolean isStrong) {
        return isStrong ? YES : NO;
    }
} 