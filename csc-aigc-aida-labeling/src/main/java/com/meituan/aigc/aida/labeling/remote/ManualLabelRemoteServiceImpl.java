package com.meituan.aigc.aida.labeling.remote;

import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.common.enums.LabelingTaskType;
import com.meituan.aigc.aida.labeling.common.enums.TaskSourceEnum;
import com.meituan.aigc.aida.labeling.common.s3.S3Service;
import com.meituan.aigc.aida.labeling.param.LabelingTaskParam;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.aigc.aida.labeling.remote.autoanalysis.api.ManualLabelRemoteService;
import com.meituan.aigc.aida.labeling.remote.autoanalysis.dto.ManualLabelCreateRequestDTO;
import com.meituan.aigc.aida.labeling.remote.common.enums.ResultCode;
import com.meituan.aigc.aida.labeling.service.TaskService;
import com.meituan.aigc.aida.labeling.remote.common.dto.ManualLabelResultDTO;
import com.meituan.aigc.aida.labeling.remote.common.dto.ServiceResponseDTO;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import org.springframework.mock.web.MockMultipartFile;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 自动分析工具人工标注服务实现类
 * 
 * <AUTHOR>
 * @date 2025/05/07
 */
@MdpPigeonServer
@Slf4j
public class ManualLabelRemoteServiceImpl implements ManualLabelRemoteService {

    @Resource
    private TaskService taskService;

    @Resource
    private S3Service s3Service;

    @Override
    public ServiceResponseDTO<Long> createManualLabelTask(ManualLabelCreateRequestDTO request) {
        // 参数校验
        if (Objects.isNull(request) || StringUtils.isBlank(request.getTaskName())) {
            log.info("创建人工标注任务失败, 参数校验不通过, request: {}", request);
            return ServiceResponseDTO.failed(ResultCode.VALIDATE_FAILED, "参数校验失败");
        }

        // 参数转换
        List<List<LabelResult>> taskParams = new ArrayList<>();
        for (List<ManualLabelResultDTO> dtoList : request.getLabelResultList()) {
            List<LabelResult> labelResultList = dtoList.stream()
                    .map(this::convertToLabelResult)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(labelResultList)) {
                taskParams.add(labelResultList);
            }
        }
        if (CollectionUtils.isEmpty(taskParams)) {
            log.info("创建人工标注任务失败, 参数校验不通过, taskParams: {}", taskParams);
            return ServiceResponseDTO.failed(ResultCode.VALIDATE_FAILED, "参数校验失败");
        }

        LabelingTaskParam param = LabelingTaskParam.builder()
                .file(downloadFileFromS3Url(request.getFileS3Url(), request.getFileOriginalName()))
                .taskName(request.getTaskName())
                .taskType(LabelingTaskType.ONLINE_SESSION_LABELING.getCode())
                .dataType(LabelTaskDataType.SESSION_ANNOTATION.getCode())
                .taskSource(TaskSourceEnum.CASE_ANALYSIS.getCode())
                .responsiblePerson(request.getResponsiblePerson())
                .responsiblePersonName(request.getResponsiblePersonName())
                .labelResultList(taskParams)
                .build();

        Long taskId = taskService.createLabelingTask(param);
        log.info("创建人工标注任务成功, request: {}, taskId: {}", request, taskId);
        return ServiceResponseDTO.success(taskId);
    }
    
    /**
     * 将ManualLabelResultDTO转换为LabelResult
     *
     * @param dto ManualLabelResultDTO对象
     * @return 转换后的LabelResult对象
     */
    private LabelResult convertToLabelResult(ManualLabelResultDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        return LabelResult.builder()
                .id(dto.getId())
                .name(dto.getName())
                .dataType(dto.getDataType())
                .itemType(dto.getItemType())
                .isCompare(dto.getIsCompare())
                .isRequired(dto.getIsRequired())
                .parentId(dto.getParentId())
                .parentEnumValue(dto.getParentEnumValue())
                .value(dto.getValue())
                .enumList(dto.getEnumList())
                .build();
    }

    /**
     * 从S3链接下载文件并转换为MultipartFile对象
     * 
     * @param s3Url S3文件链接
     * @return 转换后的MultipartFile对象
     */
    public MultipartFile downloadFileFromS3Url(String s3Url, String originalFileName) {
        if (StringUtils.isBlank(s3Url)) {
            log.warn("S3链接为空");
            return null;
        }

        try {
            // 从S3下载文件内容
            byte[] fileContent = s3Service.download(s3Url);
            if (fileContent == null || fileContent.length == 0) {
                log.warn("从S3下载的文件内容为空, url: {}", s3Url);
                return null;
            }

            // 确定文件类型
            String contentType = determineContentType(originalFileName);
            if (StringUtils.isBlank(contentType)) {
                log.warn("不支持的文件类型: {}, 仅支持xlsx和xls格式", originalFileName);
                return null;
            }

            // 创建MultipartFile对象
            return new MockMultipartFile(
                    originalFileName,
                    originalFileName,
                    contentType,
                    fileContent);
        } catch (Exception e) {
            log.error("从S3下载文件失败, url: {}", s3Url, e);
            return null;
        }
    }

    /**
     * 根据文件名确定内容类型
     * 
     * @param fileName 文件名
     * @return 内容类型
     */
    private String determineContentType(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            log.warn("文件名为空");
            return null;
        }
        
        if (fileName.endsWith(CommonConstants.FileType.XLSX)) {
            return "application/octet-stream";
        } else if (fileName.endsWith(CommonConstants.FileType.XLS)) {
            return "application/octet-stream";
        } else {
            log.warn("不支持的文件类型: {}, 仅支持xlsx和xls格式", fileName);
            return null;
        }
    }
}
