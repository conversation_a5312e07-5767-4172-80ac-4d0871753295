package com.meituan.aigc.aida.data.management.dataset.strategy.field.impl;

import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategy;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Slf4j
@Component
public class DefaultDataFieldStrategy implements DataFieldStrategy {

    private static final Integer DEFAULT_STRATEGY_TYPE = -1;

    @Override
    public Integer getStrategyType() {
        return DEFAULT_STRATEGY_TYPE;
    }

    @Override
    public void validateValue(String value) throws Exception {

    }

    @Override
    public void processFieldData(String columnName, String value, Map<String, Object> textData, Map<String, Object> flattenedData, Map<String, Date> dateData, Map<String, Object> nestedData) {

    }
}
