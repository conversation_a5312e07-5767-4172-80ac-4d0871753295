package com.meituan.aigc.aida.labeling.remote.caseanalysis.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-26 11:09
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataSetTryLockDTO implements Serializable {
    /**
     * 锁Id
     */
    private String lockId;
}
