package com.meituan.aigc.aida.labeling.controller;

import com.meituan.aigc.aida.labeling.param.LabelingQualityDetailPageQuery;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.param.QualityCheckResultSaveParam;
import com.meituan.aigc.aida.labeling.param.quality.check.LabelingQualityCheckItemParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckViewParam;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingQualityCheckItemDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.PageQueryDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.QualityCheckTaskListDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.quality.check.TaskConditionDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingQualityCheckItemVO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingQualityCheckRawCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.meituan.aigc.aida.labeling.service.QualityCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/quality-check-tasks")
@Slf4j
public class QualityCheckController {

    @Autowired
    private QualityCheckService qualityCheckService;

    /**
     * 分页查询质检任务列表详情
     */
    @PostMapping("/data")
    public Result<PageQueryDTO<LabelingQualityCheckItemDTO>> page(@RequestBody LabelingQualityCheckItemParam param) {
        PageQueryDTO<LabelingQualityCheckItemDTO> result = qualityCheckService.page(param);
        return Result.ok(result);
    }

    /**
     * 查询开始质检任务详情
     *
     * @param query 质检任务详情查询参数
     * @return 质检任务详情
     */
    @PostMapping("/getDetailById")
    public Result<PageData<LabelingQualityCheckItemVO>> pageLabelingQualityDetail(@RequestBody LabelingQualityDetailPageQuery query) {
        PageData<LabelingQualityCheckItemVO> result = qualityCheckService.getLabelingQualityDetail(query);
        return Result.ok(result);
    }

    /**
     * 质检任务列表
     *
     * @param param 质检任务列表查询参数
     * @return 质检任务列表查询结果
     */
    @PostMapping("/quality-checks")
    public Result<PageData<QualityCheckTaskListDTO>> qualityChecks(@RequestBody QualityCheckParam param) {
        PageData<QualityCheckTaskListDTO> result = qualityCheckService.qualityChecks(param);
        return Result.ok(result);
    }

    /**
     * 质检结果保存
     *
     * @param param 质检结果
     * @return 响应
     */
    @PostMapping("/save")
    public Result<?> saveResult(@RequestBody QualityCheckResultSaveParam param) {
        qualityCheckService.saveResult(param);
        return Result.create();
    }

    /**
     * 原数据维度统计质检任务已检和未检数量
     *
     * @param checkTaskId 质检任务第
     * @return 统计结果
     */
    @GetMapping("/countCheckTaskRawDataNum")
    public Result<LabelingQualityCheckRawCountVO> countCheckTaskRawDataNum(@RequestParam Long checkTaskId) {
        return Result.ok(qualityCheckService.countCheckTaskRawDataNum(checkTaskId));
    }

    /**
     * 记录查看任务详情，只要是质检人在质检页面查看了标注信息，就不允许标注人进行修改
     *
     * @param param 质检任务详情查询参数
     * @return 标记是否成功
     */
    @PostMapping("/view")
    public Result<?> view(@RequestBody QualityCheckViewParam param) {
        qualityCheckService.view(param);
        return Result.create();
    }

    /**
     * 获取质检任务下可筛选的条件
     *
     * @param qualityCheckId 质检任务ID
     * @return 可筛选条件
     */
    @GetMapping("/condition/list")
    public Result<List<TaskConditionDTO>> getQualityCheckCondition(@RequestParam Long qualityCheckId) {
        return Result.ok(qualityCheckService.getQualityCheckCondition(qualityCheckId));
    }
}
