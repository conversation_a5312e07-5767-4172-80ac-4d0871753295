package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetCreateResponseParam implements Serializable {
    /**
     * 数据集id
     */
    private Long dataSetId;
    /**
     * 数据传输锁
     */
    private String lockId;
}
