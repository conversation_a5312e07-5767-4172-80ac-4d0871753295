package com.meituan.aigc.aida.labeling.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RawDataExtraInfo implements Serializable {
    /**
     * 三方消息ID
     */
    private String llmMessageId;
    /**
     * 大模型消息内容
     */
    private String messageContent;
    /**
     * 上下文
     */
    private List<Context> context;
    /**
     * 信号
     */
    private List<Signal> signalList;
    /**
     * 任务复标前原数据ID
     */
    private Long reLabelingTaskRawDataId;

}
