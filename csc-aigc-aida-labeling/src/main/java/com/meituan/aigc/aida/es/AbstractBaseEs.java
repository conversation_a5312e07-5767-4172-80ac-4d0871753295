package com.meituan.aigc.aida.es;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.*;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

/**
 * ES服务基础抽象类
 *
 * <AUTHOR>
 * @date 2023/03/16
 */
@Slf4j
public abstract class AbstractBaseEs<T> {

    public final RestHighLevelClient esClient;
    /**
     * 当前实例的泛型
     */
    private final Class<T> genericType;

    @SuppressWarnings("unchecked")
    public AbstractBaseEs(RestHighLevelClient esClient) {
        this.esClient = esClient;
        this.genericType = (Class<T>) getSuperClassGenericType(this.getClass());
    }

    /**
     * 单数据插入
     *
     * @return {@link org.elasticsearch.action.index.IndexResponse}
     * <AUTHOR>
     * @date 2023/3/16 19:45
     */
    public IndexResponse insert(T t, String index) {
        if (null == t) {
            log.warn("{} insert failed, data is null", index);
        }

        IndexRequest indexRequest = new IndexRequest(index);
        if (t instanceof EsEntityBase && StringUtils.isNotBlank(((EsEntityBase) t).getDocumentId())) {
            indexRequest.id(((EsEntityBase) t).getDocumentId());
        }
        try {
            IndexResponse indexRes;
            indexRequest.source(JSONUtil.toJsonStr(t), XContentType.JSON);
            indexRes = esClient.index(indexRequest, RequestOptions.DEFAULT);
            return indexRes;
        } catch (IOException e) {
            log.error("{} insert error, data ={}", index, t);
            throw new RuntimeException(e);
        }
    }

    /**
     * 单数据更新
     *
     * @param t          更新数据
     * @param documentId 文档Id
     * @param index      索引
     * @return {@link UpdateResponse}
     */
    public UpdateResponse update(T t, String documentId, String index) {
        UpdateRequest updateRequest = new UpdateRequest(index, documentId);
        updateRequest.doc(JSONUtil.toJsonStr(t), XContentType.JSON);

        UpdateResponse updateResponse;
        try {
            updateResponse = esClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("{} update error, data ={}", index, t);
            throw new RuntimeException(e);
        }
        return updateResponse;
    }

    /**
     * 单数据删除
     *
     * @param documentId 文档Id
     * @param index      索引
     * @return {@link DeleteResponse}
     */
    public DeleteResponse delete(String documentId, String index) {
        DeleteRequest deleteRequest = new DeleteRequest(index, documentId);
        DeleteResponse deleteResponse;
        try {
            deleteResponse = esClient.delete(deleteRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("{} delete error, documentId ={}", index, documentId);
            throw new RuntimeException(e);
        }
        return deleteResponse;
    }

    /**
     * 批量更新
     *
     * @param list  数据列表（每个对象必须包含documentId）
     * @param index 索引名称
     * @return {@link BulkResponse}
     * <AUTHOR>
     * @date 2025/5/24
     */
    public BulkResponse batchUpdate(List<T> list, String index) {
        if (CollUtil.isEmpty(list)) {
            log.warn("{} batchUpdate failed, list is empty", index);
            return null;
        }

        BulkRequest bulkRequest = new BulkRequest();
        for (T t : list) {
            // 获取documentId，这里假设T继承了EsEntityBase，有getDocumentId方法
            String documentId = null;
            if (t instanceof EsEntityBase) {
                documentId = ((EsEntityBase) t).getDocumentId();
            }

            if (StrUtil.isBlank(documentId)) {
                log.error("{} batchUpdate error, documentId is blank for object: {}", index, t);
                throw new IllegalArgumentException("DocumentId is required for update operation");
            }

            UpdateRequest updateRequest = new UpdateRequest(index, documentId)
                    .doc(JSONUtil.toJsonStr(t), XContentType.JSON);
            bulkRequest.add(updateRequest);
        }

        try {
            return esClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("{} batchUpdate error, data ={}", index, JSONUtil.toJsonStr(list));
            throw new RuntimeException(e);
        }
    }

    /**
     * 批量插入
     *
     * @return {@link IndexResponse}
     * <AUTHOR>
     * @date 2023/3/16 19:53
     */
    public BulkResponse batchInsert(List<T> list, String index) {
        if (CollUtil.isEmpty(list)) {
            log.warn("{} batchInsert failed, list is empty", index);
            return null;
        }

        BulkRequest bulkRequest = new BulkRequest();
        list.forEach(t -> {
            IndexRequest indexRequest = new IndexRequest(index).source(JSONUtil.toJsonStr(t), XContentType.JSON);
            if (t instanceof EsEntityBase && StringUtils.isNotBlank(((EsEntityBase) t).getDocumentId())) {
                indexRequest.id(((EsEntityBase) t).getDocumentId());
            }
            bulkRequest.add(indexRequest);
        });

        try {
            return esClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("{} batchInsert error, data ={}", index, JSONUtil.toJsonStr(list));
            throw new RuntimeException(e);
        }
    }

    /**
     * 批量删除
     *
     * @param list  数据列表（每个对象必须包含documentId）
     * @param index 索引名称
     * @return {@link BulkResponse}
     */
    public BulkResponse batchDelete(List<T> list, String index) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("{} batchDelete failed, list is empty", index);
            return null;
        }

        BulkRequest bulkRequest = new BulkRequest();

        list.forEach(t -> {
            String documentId = null;
            if (t instanceof EsEntityBase) {
                documentId = ((EsEntityBase) t).getDocumentId();
            }
            DeleteRequest deleteRequest = new DeleteRequest(index).id(documentId);
            bulkRequest.add(deleteRequest);
        });

        try {
            return esClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("{} batchDelete error, data={}", index, list);
            throw new RuntimeException(e);
        }
    }

    /**
     * term精确查询
     * <p>termQuery与matchQuery区别:
     * <li>match query使用分词器对查询字符串进行分词，然后将分词后的词条进行匹配,匹配结果可以是部分匹配，即只要查询字符串中的任意一个词条能够匹配上文档中的一个词条，就算匹配成功</li>
     * <li>而term query查询会将查询字符串看做一个完整的词项，然后尝试将它与索引中的相应词项进行精确匹配。这意味着如果查询字符串包含多个词项，那么它只会匹配到包含所有这些词项的文档，但是这些词项不必按照任何特定的顺序出现。</li>
     *
     * @param key   查询的key
     * @param value 查询的值
     * @param from  类似于分页中的pageNo
     * @param size  每页数目
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/16 22:01
     */
    public List<T> termQuery(String key, Object value, int from, int size, String index) {
        if (StrUtil.isBlank(key) || null == value) {
            log.warn("{} termQueryWithSort failed, key or value is null", index);
            return ListUtil.empty();
        }

        try {
            return termQueryWithSort(key, value, from, size, null, null, index);
        } catch (Exception e) {
            log.error("termQuery error, key ={}, value ={}, from ={}, size ={}", key, value, from, size, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * term精确查询带排序
     * <p>termQuery与matchQuery区别:
     * <li>match query使用分词器对查询字符串进行分词，然后将分词后的词条进行匹配,匹配结果可以是部分匹配，即只要查询字符串中的任意一个词条能够匹配上文档中的一个词条，就算匹配成功</li>
     * <li>而term query查询会将查询字符串看做一个完整的词项，然后尝试将它与索引中的相应词项进行精确匹配。这意味着如果查询字符串包含多个词项，那么它只会匹配到包含所有这些词项的文档，但是这些词项不必按照任何特定的顺序出现。</li>
     *
     * @param key       查询的key
     * @param value     查询的值
     * @param from      类似于分页中的pageNo
     * @param size      每页数目
     * @param sortName  要排序的字段
     * @param sortOrder {@link SortOrder}
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/16 22:01
     */
    public List<T> termQueryWithSort(String key, Object value, int from, int size, String sortName, SortOrder sortOrder, String index) {
        if (StrUtil.isBlank(key) || null == value) {
            log.warn("{} termQueryWithSort failed, key or value is null", index);
            return ListUtil.empty();
        }

        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery(key, value)).from(from).size(size);
        if (StrUtil.isBlankIfStr(sortName) && null != sortOrder) {
            searchSourceBuilder.sort(sortName, sortOrder);
        }
        searchRequest.source(searchSourceBuilder);

        try {
            List<T> resList = getQuerySearchHits(searchRequest, index);
            log.info("{} termQueryWithSort success, key ={}, value ={}, from ={}, size ={} resList ={}", index, key, value, from, size, resList);
            return resList;
        } catch (Exception e) {
            log.error("termQueryWithSort error, key ={}, value ={}, from ={}, size ={}", key, value, from, size, e);
            throw new RuntimeException(e);
        }
    }


    /**
     * matchQuery
     * <p>termQuery与matchQuery区别:
     * <li>match query使用分词器对查询字符串进行分词，然后将分词后的词条进行匹配,匹配结果可以是部分匹配，即只要查询字符串中的任意一个词条能够匹配上文档中的一个词条，就算匹配成功</li>
     * <li>而term query查询会将查询字符串看做一个完整的词项，然后尝试将它与索引中的相应词项进行精确匹配。这意味着如果查询字符串包含多个词项，那么它只会匹配到包含所有这些词项的文档，但是这些词项不必按照任何特定的顺序出现。</li>
     *
     * @param key   查询的key
     * @param value 查询的值
     * @param from  类似于分页中的pageNo
     * @param size  每页数目
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/17 11:16
     */
    public List<T> matchQuery(String key, Object value, int from, int size, String index) {
        if (StrUtil.isBlank(key) || null == value) {
            log.warn("{} matchQuery failed, key or value is null", index);
            return ListUtil.empty();
        }

        try {
            return matchQueryWithSort(key, value, from, size, null, null, index);
        } catch (Exception e) {
            log.error("{} matchQuery error, key ={}, value ={}, from ={}, size ={}", index, key, value, from, size, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * matchQuery带排序
     * <p>termQuery与matchQuery区别:
     * <li>match query使用分词器对查询字符串进行分词，然后将分词后的词条进行匹配,匹配结果可以是部分匹配，即只要查询字符串中的任意一个词条能够匹配上文档中的一个词条，就算匹配成功</li>
     * <li>而term query查询会将查询字符串看做一个完整的词项，然后尝试将它与索引中的相应词项进行精确匹配。这意味着如果查询字符串包含多个词项，那么它只会匹配到包含所有这些词项的文档，但是这些词项不必按照任何特定的顺序出现。</li>
     *
     * @param key       查询的key
     * @param value     查询的值
     * @param from      类似于分页中的pageNo
     * @param size      每页数目
     * @param sortName  要排序的字段
     * @param sortOrder {@link SortOrder}
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/17 11:16
     */
    public List<T> matchQueryWithSort(String key, Object value, int from, int size, String sortName, SortOrder sortOrder, String index) {
        if (StrUtil.isBlank(key) || null == value) {
            log.warn("{} matchQuery failed, key or value is null", index);
            return ListUtil.empty();
        }

        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.matchQuery(key, value)).from(from).size(size);
        if (StrUtil.isBlankIfStr(sortName) && null != sortOrder) {
            searchSourceBuilder.sort(sortName, sortOrder);
        }
        searchRequest.source(searchSourceBuilder);

        try {
            return getQuerySearchHits(searchRequest, index);
        } catch (Exception e) {
            log.error("{} matchQuery error, key ={}, value ={}, from ={}, size ={}", index, key, value, from, size, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * matchPhraseQuery
     * <p>查询会将整个查询字符串视为一个短语，而不是将其分成单个词项。它只会匹配包含查询字符串中所有单词的文档，并且这些单词必须以查询字符串中的相同顺序出现。
     *
     * @param key   查询的key
     * @param value 查询的值
     * @param from  类似于分页中的pageNo
     * @param size  每页数目
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/17 11:14
     */
    public List<T> matchPhaseQuery(String key, Object value, int from, int size, String index) {
        if (StrUtil.isBlank(key) || null == value) {
            log.warn("{} matchPhaseQuery failed, key or value is null", index);
            return ListUtil.empty();
        }

        try {
            return matchPhaseQueryWithSort(key, value, from, size, null, null, index);
        } catch (Exception e) {
            log.error("{} matchPhaseQuery error, key ={}, value ={}, from ={}, size ={}", index, key, value, from, size, e);
            throw new RuntimeException(e);
        }

    }

    /**
     * matchPhraseQuery 带排序
     * <p>查询会将整个查询字符串视为一个短语，而不是将其分成单个词项。它只会匹配包含查询字符串中所有单词的文档，并且这些单词必须以查询字符串中的相同顺序出现。
     *
     * @param key       查询的key
     * @param value     查询的值
     * @param from      类似于分页中的pageNo
     * @param size      每页数目
     * @param sortName  要排序的字段
     * @param sortOrder {@link SortOrder}
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/17 11:17
     */
    public List<T> matchPhaseQueryWithSort(String key, Object value, int from, int size, String sortName, SortOrder sortOrder, String index) {
        if (StrUtil.isBlank(key) || null == value) {
            log.warn("{} matchPhaseQueryWithSort failed, key or value is null", index);
            return ListUtil.empty();
        }

        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.matchPhraseQuery(key, value)).from(from).size(size);
        if (StrUtil.isBlankIfStr(sortName) && null != sortOrder) {
            searchSourceBuilder.sort(sortName, sortOrder);
        }
        searchRequest.source(searchSourceBuilder);

        try {
            return getQuerySearchHits(searchRequest, index);
        } catch (Exception e) {
            log.error("{} matchPhaseQueryWithSort error, key ={}, value ={}, from ={}, size ={}", index, key, value, from, size, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * scroll查询
     * <p>当我们做大数据查询时可考虑使用,实时性和效率都可以保证,优势如下:
     * <p>
     * <li>避免了数据漏检的问题：传统的分页查询方式需要通过指定每一页的起始位置和结束位置来获取数据，但这种方式容易出现数据漏检的问题。特别是在高并发环境下，当有新的数据插入到分页查询结果中时，就有可能导致漏检数据。而使用 searchScroll API，您可以保持游标的位置，直到检索完整个结果集，从而避免了数据漏检的问题。</li>
     * <li>提高了查询效率：传统的分页查询方式每次只能获取一页的数据，如果要获取多页的数据，就需要多次查询。而使用 searchScroll API，您可以一次性获取多页的数据，并在获取下一页数据时避免了重新查询索引的开销，因此能够提高查询效率。</li>
     * <li>支持数据实时更新：传统的分页查询方式通常无法处理数据的实时更新。如果在分页查询过程中有新的数据插入或删除，就可能导致数据漏检或者重复检索。而使用 searchScroll API，您可以保持游标的位置，即使在查询过程中有新的数据插入或删除，也能够正确地获取所有数据。</li>
     * <li>支持大数据量查询：传统的分页查询方式在处理大数据量时，需要进行大量的磁盘 I/O 和网络传输，因此效率比较低。而使用 searchScroll API，Elasticsearch 可以将查询结果缓存在内存中，避免了大量的磁盘 I/O 和网络传输，从而支持高效的大数据量查询。</li>
     * </p>
     *
     * @param key   查询的key
     * @param value 查询的值
     * @param size  每页数目
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/17 11:36
     */
    public List<T> scrollTermQuery(String key, Object value, int size, String index) {
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery(key, value)).size(size);
        searchRequest.source(searchSourceBuilder);
        return scrollQuery(searchRequest, "1m", index);
    }

    /**
     * scroll查询
     *
     * @param searchRequest 查询请求
     * @param scrollTime    scroll时间
     * @return {@link List<T>}
     */
    public List<T> scrollQuery(SearchRequest searchRequest, String scrollTime, String index) {
        try {
            searchRequest.scroll(scrollTime);
            SearchResponse searchResponse;
            searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            List<T> resList = new ArrayList<>(parseQuerySearchHits(searchHits, index));

            while (searchHits.getHits().length > 0) {
                searchResponse = esClient.scroll(new SearchScrollRequest(searchResponse.getScrollId()).scroll(scrollTime), RequestOptions.DEFAULT);
                searchHits = searchResponse.getHits();
                resList.addAll(parseQuerySearchHits(searchHits, index));
            }
            log.info("{} scrollQuery success, resultSize ={}", index, resList.size());
            return resList;
        } catch (IOException e) {
            log.error("{} scrollQuery error, searchRequest ={}", index, JSONUtil.toJsonStr(searchRequest), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * scroll搜索
     *
     * @param scrollId   滚动Id
     * @param scrollTime 滚动时间
     * @param index      索引
     * @return {@link SearchResponse}
     */
    public SearchResponse scrollSearch(String scrollId, String scrollTime, String index) {
        SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
        searchScrollRequest.scroll(scrollTime);
        SearchResponse searchResponse;
        try {
            searchResponse = esClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
            if (null == searchResponse) {
                throw new RuntimeException("scrollSearch Response is null");
            }
        } catch (Exception e) {
            log.error("{} scrollSearch error, searchScrollRequest = {}", index, searchScrollRequest);
            throw new RuntimeException(e);
        }
        log.info("{} scrollSearch success, result={}", index, searchResponse);
        return searchResponse;
    }

    /**
     * 清理scroll上下文
     * <p>当scroll查询完成或不再需要时，应该主动清理scroll上下文以释放ES集群资源。
     * 如果不手动清理，ES会在scroll_timeout时间后自动清理，但主动清理是最佳实践。
     *
     * @param scrollId scroll查询的ID
     * <AUTHOR>
     * @date 2025/05/29
     */
    public void clearScroll(String scrollId) {
        if (StringUtils.isBlank(scrollId)) {
            log.warn("clearScroll failed, scrollId is blank");
            return;
        }

        try {
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);

            ClearScrollResponse clearScrollResponse = esClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

            if (clearScrollResponse != null && clearScrollResponse.isSucceeded()) {
                log.info("clearScroll success, scrollId: {}, freed: {}",
                        scrollId, clearScrollResponse.getNumFreed());
            } else {
                log.warn("clearScroll failed, scrollId: {}, response: {}",
                        scrollId, clearScrollResponse);
            }
        } catch (Exception e) {
            log.error("clearScroll unexpected error, scrollId: {}", scrollId, e);
        }
    }

    /**
     * 搜索请求处理
     * <p>
     * 除了以上基本的通用请求外,实际场景查询语句必然非常复杂,基类无法考虑到面面俱全,所以只提供了通用的请求处理方案,该方法会返回一个已经解析完毕的当前泛型的{@link List<T>}
     * </p>
     *
     * @param searchRequest {@link SearchRequest}
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/17 11:48
     */
    public List<T> getQuerySearchHits(SearchRequest searchRequest, String index) {
        SearchResponse searchResponse = null;
        List<T> ts;
        try {
            searchResponse = handleCommonQuerySearchRequest(searchRequest, index);
            // 获取查询命中结果
            SearchHits searchHits = searchResponse.getHits();
            ts = parseQuerySearchHits(searchHits, index);
        } catch (Exception e) {
            log.error("{} handleQuerySearchRequest error, searchRequest ={}", index, JSONUtil.toJsonStr(searchRequest), e);
            throw new RuntimeException(e);
        }
        log.info("{} handleQuerySearchRequest success, result ={}", index, ts);

        return ts;
    }

    /**
     * 搜索请求hit处理,该方法会返回一个已经解析完毕的当前泛型的{@link List<T>}
     *
     * @param searchHits {@link SearchHits}
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2023/3/17 12:00
     */
    public List<T> parseQuerySearchHits(SearchHits searchHits, String index) {
        List<T> resList = new ArrayList<>();
        try {
            if (null != searchHits.getTotalHits() && searchHits.getTotalHits().value < 1) {
                return ListUtil.empty();
            }
            Arrays.stream(searchHits.getHits()).forEach(h -> {
                Map<String, Object> sourceAsMap = h.getSourceAsMap();
                sourceAsMap.put("documentId", h.getId());
                sourceAsMap.put("version", h.getVersion());
                resList.add(JSONUtil.toBean(JSONUtil.toJsonStr(sourceAsMap), this.genericType));
            });
        } catch (Exception e) {
            log.error("{} parseQuerySearchHits error, searchHits ={}", index, JSONUtil.toJsonStr(searchHits), e);
            throw new RuntimeException(e);
        }

        return resList;
    }

    /**
     * Terms聚合查询—多聚合查询
     * <p>Terms聚合是一种分桶聚合，它基于字段值创建一个桶，每个桶对应字段的一个唯一值。
     * 每个桶包含匹配该值的所有文档。Terms聚合特别适合统计字段的不同值及其出现次数。
     *
     * @param fields 要聚合的字段列表，Map的key为聚合名称，value为字段名
     * @param size   返回的桶数量上限
     * @param index  索引名称
     * @return 聚合结果，最外层Map的key为查询名称，里层Map的key为字段值，value为该值对应的文档数量
     * <AUTHOR>
     * @date 2025/05/23
     */
    public Map<String, Map<String, Long>> multiFieldTermsAggregation(Map<String, String> fields, int size, String index) {
        if (MapUtils.isEmpty(fields)) {
            log.warn("{} multiFieldTermsAggregation failed, fields is empty", index);
            return new HashMap<>();
        }

        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);

        // 为每个字段创建独立的Terms聚合
        fields.forEach((aggName, fieldName) -> {
            if (StrUtil.isNotBlank(fieldName)) {
                TermsAggregationBuilder aggregation =
                        AggregationBuilders.terms(aggName)
                                .field(fieldName)
                                .size(size);
                searchSourceBuilder.aggregation(aggregation);
            }
        });

        searchRequest.source(searchSourceBuilder);

        try {
            SearchResponse searchResponse = handleCommonQuerySearchRequest(searchRequest, index);
            return parseAggregations(searchResponse.getAggregations(), index);
        } catch (Exception e) {
            log.error("{} multiFieldTermsAggregation error", index, e);
            throw new RuntimeException("Multi-field terms aggregation failed", e);
        }
    }

    /**
     * 解析聚合结果
     * <p>解析Elasticsearch的聚合查询结果，将多个Terms聚合的结果转换为嵌套Map结构。
     * 外层Map的key为聚合名称，内层Map的key为聚合桶的值，value为文档计数。
     * <p>注意：此方法仅支持Terms类型的聚合，其他类型聚合会被跳过并记录警告日志
     *
     * @param aggregations ES聚合查询结果对象
     * @param index        索引名称（用于日志记录）
     * @return 聚合结果的嵌套Map，结构为：Map<聚合名称, Map<桶值, 文档数>>；如果输入为null返回空Map
     * @throws RuntimeException 当解析过程出现不可恢复的错误时抛出
     * <AUTHOR>
     * @date 2025/05/23
     */
    public Map<String, Map<String, Long>> parseAggregations(Aggregations aggregations, String index) {
        Map<String, Map<String, Long>> resultList = new HashMap<>();

        // 参数验证
        if (aggregations == null) {
            log.info("{} parseAggregations failed, aggregations is null", index);
            return resultList;
        }

        Map<String, Aggregation> aggregationMap = aggregations.getAsMap();
        if (MapUtils.isEmpty(aggregationMap)) {
            log.info("{} parseAggregations no aggregations found", index);
            return resultList;
        }

        try {
            aggregationMap.forEach((key, value) -> {
                // 类型安全检查
                if (value instanceof Terms) {
                    Map<String, Long> resultMap = new HashMap<>();
                    Terms terms = (Terms) value;

                    // 处理每个桶
                    for (Terms.Bucket bucket : terms.getBuckets()) {
                        String bucketKey = bucket.getKeyAsString();
                        long docCount = bucket.getDocCount();

                        // 防止null key
                        if (bucketKey != null) {
                            resultMap.put(bucketKey, docCount);
                        } else {
                            log.warn("{} parseAggregations found null bucket key in aggregation: {}",
                                    index, key);
                        }
                    }

                    resultList.put(key, resultMap);
                    log.info("{} parseAggregations processed Terms aggregation: {}, bucket count: {}",
                            index, key, resultMap.size());

                } else if (value != null) {
                    // 记录非Terms类型的聚合
                    log.warn("{} parseAggregations skipped non-Terms aggregation: {}, type: {}",
                            index, key, value.getClass().getSimpleName());
                }
            });

            log.info("{} parseAggregations success, total aggregations: {}",
                    index, resultList.size());

        } catch (ClassCastException e) {
            log.error("{} parseAggregations type cast error", index, e);
            throw new RuntimeException("Failed to parse aggregations due to type mismatch", e);
        } catch (Exception e) {
            log.error("{} parseAggregations unexpected error", index, e);
            throw new RuntimeException("Failed to parse aggregations", e);
        }

        return resultList;
    }

    /**
     * Terms聚合查询（支持过滤条件）—多聚合查询
     * <p>基于指定查询条件进行Terms聚合，可以统计满足特定条件的文档中字段值的分布情况。
     *
     * @param fields       要聚合的字段列表，Map的key为聚合名称，value为字段名
     * @param size         返回的桶数量上限
     * @param queryBuilder 过滤查询条件
     * @param index        索引名称
     * @return 聚合结果，Map的key为字段值，value为该值对应的文档数量
     * <AUTHOR>
     * @date 2025/05/23
     */
    public Map<String, Long> multiTermsAggregationWithFilter(Map<String, String> fields, int size,
                                                             QueryBuilder queryBuilder,
                                                             String index) {
        if (MapUtils.isEmpty(fields)) {
            log.warn("{} termsAggregationWithFilter failed, field is null or empty", index);
            return new HashMap<>();
        }

        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // 设置查询条件
        if (queryBuilder != null) {
            searchSourceBuilder.query(queryBuilder);
        }

        // 设置size为0，因为我们只关注聚合结果
        searchSourceBuilder.size(0);

        // 为每个查询字段创建Terms聚合
        fields.forEach((aggName, fieldName) -> {
            if (StringUtils.isNotBlank(fieldName)) {
                TermsAggregationBuilder aggregation =
                        AggregationBuilders.terms(aggName)
                                .field(fieldName)
                                .size(size);
                searchSourceBuilder.aggregation(aggregation);
            }
        });

        searchRequest.source(searchSourceBuilder);

        try {
            SearchResponse searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);
            Terms terms = searchResponse.getAggregations().get("term_agg");

            Map<String, Long> resultMap = new HashMap<>();
            for (Terms.Bucket bucket : terms.getBuckets()) {
                resultMap.put(bucket.getKeyAsString(), bucket.getDocCount());
            }

            log.info("{} termsAggregationWithFilter success, fields={}, size={}, resultSize={}",
                    index, fields, size, resultMap.size());
            return resultMap;
        } catch (Exception e) {
            log.error("{} termsAggregationWithFilter error, fields={}, size={}",
                    index, fields, size, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 通配符查询
     * <p>支持使用通配符进行模糊匹配：
     * <li>* 表示任意多个字符</li>
     * <li>? 表示单个字符</li>
     *
     * @param key   查询的key
     * @param value 查询的值（支持通配符）
     * @param from  类似于分页中的pageNo
     * @param size  每页数目
     * @param index 索引名称
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2025/05/24
     */
    public List<T> wildcardQuery(String key, String value, int from, int size, String index) {
        if (StringUtils.isBlank(key) || null == value) {
            log.warn("{} wildcardQuery failed, key or value is null", index);
            return ListUtil.empty();
        }

        try {
            return wildcardQueryWithSort(key, value, from, size, null, null, index);
        } catch (Exception e) {
            log.error("{} wildcardQuery error, key ={}, value ={}, from ={}, size ={}", index, key, value, from, size, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 通配符查询带排序
     * <p>支持使用通配符进行模糊匹配：
     * <li>* 表示任意多个字符</li>
     * <li>? 表示单个字符</li>
     *
     * @param key       查询的key
     * @param value     查询的值（支持通配符）
     * @param from      类似于分页中的pageNo
     * @param size      每页数目
     * @param sortName  要排序的字段
     * @param sortOrder {@link SortOrder}
     * @param index     索引名称
     * @return {@link List<T>}
     * <AUTHOR>
     * @date 2025/05/24
     */
    public List<T> wildcardQueryWithSort(String key, String value, int from, int size, String sortName, SortOrder sortOrder, String index) {
        if (StringUtils.isBlank(key) || null == value) {
            log.warn("{} wildcardQueryWithSort failed, key or value is null", index);
            return ListUtil.empty();
        }

        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.wildcardQuery(key, value)).from(from).size(size);
        if (!StrUtil.isBlankIfStr(sortName) && null != sortOrder) {
            searchSourceBuilder.sort(sortName, sortOrder);
        }
        searchRequest.source(searchSourceBuilder);

        try {
            return getQuerySearchHits(searchRequest, index);
        } catch (Exception e) {
            log.error("{} wildcardQueryWithSort error, key ={}, value ={}, from ={}, size ={}", index, key, value, from, size, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 通用搜索请求处理
     * <p>
     * 除了以上基本的通用请求外,实际场景查询语句必然非常复杂,基类无法考虑到面面俱全,所以只提供了通用的请求处理方案,该方法会返回查询结果
     * </p>
     *
     * @param searchRequest {@link SearchRequest}
     * @return {@link SearchResponse}
     * <AUTHOR>
     * @date 2025/5/23
     */
    public SearchResponse handleCommonQuerySearchRequest(SearchRequest searchRequest, String index) {
        SearchResponse searchResponse = null;
        try {
            searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);
            if (null == searchResponse) {
                throw new RuntimeException("searchResponse is null");
            }
        } catch (IOException e) {
            log.error("{} handleQuerySearchRequest error, searchRequest ={}", index, JSONUtil.toJsonStr(searchRequest), e);
            throw new RuntimeException(e);
        }
        log.info("{} handleQuerySearchRequest success, result ={}", index, searchResponse);
        return searchResponse;
    }

    /**
     * 通用更新请求处理—带条件
     *
     * @param updateByQueryRequest {@link UpdateRequest}
     * @param index                索引名称
     * @return {@link UpdateResponse}
     */
    public BulkByScrollResponse handleCommonUpdateByQueryRequest(UpdateByQueryRequest updateByQueryRequest, String index) {
        BulkByScrollResponse updateResponse = null;
        try {
            updateResponse = esClient.updateByQuery(updateByQueryRequest, RequestOptions.DEFAULT);
            if (null == updateResponse) {
                throw new RuntimeException("updateResponse is null");
            }
        } catch (IOException e) {
            log.error("{} handleUpdateRequest error, updateRequest ={}", index, JSONUtil.toJsonStr(updateResponse), e);
            throw new RuntimeException(e);
        }
        log.info("{} handleUpdateRequest success, result ={}", index, updateResponse);
        return updateResponse;
    }

    @SuppressWarnings("unchecked")
    private static Class<Object> getSuperClassGenericType(final Class clazz) {
        Type genType = clazz.getGenericSuperclass();
        if (!(genType instanceof ParameterizedType)) {
            return Object.class;
        }

        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
        if (!(params[0] instanceof Class)) {
            return Object.class;
        }

        return (Class<Object>) params[0];
    }
}