<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.LabelingTaskGroupMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="recycle_status" jdbcType="TINYINT" property="recycleStatus" />
    <result column="total_data_count" jdbcType="INTEGER" property="totalDataCount" />
    <result column="recycle_data_count" jdbcType="INTEGER" property="recycleDataCount" />
    <result column="consistency_rate" jdbcType="INTEGER" property="consistencyRate" />
    <result column="recycle_sub_task_id" jdbcType="BIGINT" property="recycleSubTaskId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, group_name, parent_id, recycle_status, total_data_count, recycle_data_count, 
    consistency_rate, recycle_sub_task_id, create_time, update_time, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroupExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from labeling_task_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from labeling_task_group
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from labeling_task_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroupExample">
    delete from labeling_task_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup" useGeneratedKeys="true" keyProperty="id">
    insert into labeling_task_group (id, task_id, group_name, 
      parent_id, recycle_status, total_data_count, 
      recycle_data_count, consistency_rate, recycle_sub_task_id, 
      create_time, update_time, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR}, 
      #{parentId,jdbcType=BIGINT}, #{recycleStatus,jdbcType=TINYINT}, #{totalDataCount,jdbcType=INTEGER}, 
      #{recycleDataCount,jdbcType=INTEGER}, #{consistencyRate,jdbcType=INTEGER}, #{recycleSubTaskId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup">
    insert into labeling_task_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="recycleStatus != null">
        recycle_status,
      </if>
      <if test="totalDataCount != null">
        total_data_count,
      </if>
      <if test="recycleDataCount != null">
        recycle_data_count,
      </if>
      <if test="consistencyRate != null">
        consistency_rate,
      </if>
      <if test="recycleSubTaskId != null">
        recycle_sub_task_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="recycleStatus != null">
        #{recycleStatus,jdbcType=TINYINT},
      </if>
      <if test="totalDataCount != null">
        #{totalDataCount,jdbcType=INTEGER},
      </if>
      <if test="recycleDataCount != null">
        #{recycleDataCount,jdbcType=INTEGER},
      </if>
      <if test="consistencyRate != null">
        #{consistencyRate,jdbcType=INTEGER},
      </if>
      <if test="recycleSubTaskId != null">
        #{recycleSubTaskId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroupExample" resultType="java.lang.Long">
    select count(*) from labeling_task_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update labeling_task_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.recycleStatus != null">
        recycle_status = #{record.recycleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.totalDataCount != null">
        total_data_count = #{record.totalDataCount,jdbcType=INTEGER},
      </if>
      <if test="record.recycleDataCount != null">
        recycle_data_count = #{record.recycleDataCount,jdbcType=INTEGER},
      </if>
      <if test="record.consistencyRate != null">
        consistency_rate = #{record.consistencyRate,jdbcType=INTEGER},
      </if>
      <if test="record.recycleSubTaskId != null">
        recycle_sub_task_id = #{record.recycleSubTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update labeling_task_group
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      group_name = #{record.groupName,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      recycle_status = #{record.recycleStatus,jdbcType=TINYINT},
      total_data_count = #{record.totalDataCount,jdbcType=INTEGER},
      recycle_data_count = #{record.recycleDataCount,jdbcType=INTEGER},
      consistency_rate = #{record.consistencyRate,jdbcType=INTEGER},
      recycle_sub_task_id = #{record.recycleSubTaskId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup">
    update labeling_task_group
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="recycleStatus != null">
        recycle_status = #{recycleStatus,jdbcType=TINYINT},
      </if>
      <if test="totalDataCount != null">
        total_data_count = #{totalDataCount,jdbcType=INTEGER},
      </if>
      <if test="recycleDataCount != null">
        recycle_data_count = #{recycleDataCount,jdbcType=INTEGER},
      </if>
      <if test="consistencyRate != null">
        consistency_rate = #{consistencyRate,jdbcType=INTEGER},
      </if>
      <if test="recycleSubTaskId != null">
        recycle_sub_task_id = #{recycleSubTaskId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup">
    update labeling_task_group
    set task_id = #{taskId,jdbcType=BIGINT},
      group_name = #{groupName,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      recycle_status = #{recycleStatus,jdbcType=TINYINT},
      total_data_count = #{totalDataCount,jdbcType=INTEGER},
      recycle_data_count = #{recycleDataCount,jdbcType=INTEGER},
      consistency_rate = #{consistencyRate,jdbcType=INTEGER},
      recycle_sub_task_id = #{recycleSubTaskId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listByTaskId" parameterType="java.lang.Long" resultType="long">
      SELECT
       recycle_sub_task_id
      FROM
          labeling_task_group
      WHERE
          task_id = #{taskId,jdbcType=BIGINT}
  </select>

  <insert id="insertGroup" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup"
          useGeneratedKeys="true" keyProperty="id">
    INSERT INTO labeling_task_group (
    task_id,
    group_name,
    parent_id,
    recycle_status,
    total_data_count
    ) VALUES (
    #{taskId},
    #{groupName},
    #{parentId},
    #{recycleStatus},
    #{totalDataCount}
    )
  </insert>

</mapper>