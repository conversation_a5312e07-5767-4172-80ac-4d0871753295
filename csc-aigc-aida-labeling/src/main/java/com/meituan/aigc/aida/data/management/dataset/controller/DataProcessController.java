package com.meituan.aigc.aida.data.management.dataset.controller;

import com.meituan.aigc.aida.data.management.dataset.dto.*;
import com.meituan.aigc.aida.data.management.dataset.param.DataProcessCreateTaskParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataProcessTaskPageParam;
import com.meituan.aigc.aida.data.management.dataset.param.LabelRobotParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataMarkParam;
import com.meituan.aigc.aida.data.management.dataset.param.SignalUpdateParam;
import com.meituan.aigc.aida.data.management.dataset.service.DataProcessService;
import com.meituan.aigc.aida.data.management.fetch.vo.RobotVO;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据清洗和打标控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/data/processing")
public class DataProcessController {

    @Autowired
    private DataProcessService dataProcessService;

    /**
     * 创建清洗任务
     *
     * @param param 创建清洗任务参数
     * @return 创建结果
     */
    @PostMapping("/task/create")
    public Result<?> createProcessTask(@RequestBody DataProcessCreateTaskParam param) {
        dataProcessService.createProcessTask(param);
        return Result.create();
    }

    /**
     * 获取清洗任务列表
     *
     * @param param 查询参数
     * @return 清洗任务分页列表
     */
    @GetMapping("/task/page")
    public Result<PageData<DataProcessTaskDTO>> pageListProcessTask(DataProcessTaskPageParam param) {
        return Result.ok(dataProcessService.listProcessTask(param));
    }

    /**
     * 获取打标机器人信息
     *
     * @param param 机器人参数
     * @return 机器人信息
     */
    @GetMapping("/custom/label")
    public Result<LabelRobotInfoDTO> getLabelRobotInfo(LabelRobotParam param) {
        return Result.ok(dataProcessService.getLabelRobotInfo(param));
    }

    /**
     * 获取非自定义标签的场景信息
     *
     * @return 场景列表
     */
    @GetMapping("/non/custom/scene")
    public Result<SceneListDTO> getNonCustomScene() {
        return Result.ok(dataProcessService.getNonCustomScene());
    }

    /**
     * 获取非自定义标签的标签信息
     *
     * @param sceneId 场景ID
     * @return 标签列表
     */
    @GetMapping("/non/custom/label")
    public Result<LabelListDTO> getNonCustomLabel(@RequestParam Long sceneId) {
        return Result.ok(dataProcessService.getNonCustomLabel(sceneId));
    }

    /**
     * 查询打标机器人
     *
     * @param sceneId     场景ID
     * @param labelIdList 标签ID列表
     * @return 打标机器人列表
     */
    @GetMapping("/non/custom/robot")
    public Result<List<NonCustomRobotDTO>> queryNonCustomRobot(@RequestParam Long sceneId, @RequestParam String labelIdList) {
        return Result.ok(dataProcessService.queryNonCustomRobot(sceneId, Arrays.stream(labelIdList.split(",")).collect(Collectors.toList())));
    }

    /**
     * 数据打标
     *
     * @param param 打标参数
     * @return 打标结果
     */
    @PostMapping("/non/custom/mark")
    public Result<?> markData(@RequestBody DataMarkParam param) {
        dataProcessService.markData(param);
        return Result.create();
    }

    @DeleteMapping("/delete")
    public Result<?> deleteData(@RequestParam Long taskId) {
        dataProcessService.deleteData(taskId);
        return Result.create();
    }

    /**
     * 获取机器人列表
     *
     * @param dataSetId 数据集ID
     * @return 机器人列表
     */
    @GetMapping("/robot/list")
    public Result<List<RobotVO>> getRobotList(@RequestParam("dataSetId") Long dataSetId, @RequestParam("versionId") Long versionId) {
        return Result.ok(dataProcessService.getRobotList(dataSetId, versionId));
    }

    /**
     * 获取机器人版本列表
     *
     * @param robotId 机器人ID
     * @return 机器人版本列表
     */
    @GetMapping("/robot/version/list")
    public Result<SignalRefreshRobotVersionInfo> getRobotVersionList(@RequestParam("robotId") String robotId) {
        return Result.ok(dataProcessService.getRobotVersionList(robotId));
    }

    /**
     * 信号回刷
     *
     * @param param 信号回刷参数
     * @return 回刷结果
     */
    @PostMapping("/signal/update")
    public Result<?> updateSignal(@RequestBody SignalUpdateParam param) {
        dataProcessService.updateSignal(param);
        return Result.create();
    }

} 