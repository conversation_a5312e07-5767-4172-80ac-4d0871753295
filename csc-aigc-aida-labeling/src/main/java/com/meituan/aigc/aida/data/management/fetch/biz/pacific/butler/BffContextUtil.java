package com.meituan.aigc.aida.data.management.fetch.biz.pacific.butler;

import com.google.common.collect.Lists;
import com.meituan.aigc.aida.common.util.ObjectConverter;
import com.sankuai.csc.workbench.bff.api.common.enums.ContactTypeEnum;
import com.sankuai.csc.workbench.bff.api.workbench.dto.InitButlerResDTO;
import com.sankuai.csc.workbench.bff.api.workbench.dto.TabInfoResDTO;
import com.sankuai.csc.workbench.bff.common.util.DeflateUtil;
import com.sankuai.mdp.csc.pacific.context.api.dto.response.ContextInstanceParamRespDto;
import com.sankuai.mdp.csc.pacific.context.api.dto.response.ContextInstanceRespDto;
import com.sankuai.mdp.csc.pacific.context.api.enums.ContextCategoryEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 管家上下文工具类
 *
 * <AUTHOR>
 */
public class BffContextUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(BffContextUtil.class);
    private static final String LOG_PREFIX = "[ContextUtil]";

    public static InitButlerResDTO getContextTabListValue(ContextInstanceRespDto contextInstanceRespDto, ContextCategoryEnum contextCategoryEnum, String mainKey) {
        if (null == contextInstanceRespDto || CollectionUtils.isEmpty(contextInstanceRespDto.getContextInstanceParamRespDtoList())) {
            return null;
        }

        Map<String, String> standardParam = new HashMap<>();
        contextInstanceRespDto.getContextInstanceParamRespDtoList()
                .forEach(i -> {
                    if (null != i && contextCategoryEnum.getCode().equals(i.getCategoryCode())) {
                        standardParam.put(i.getSpCode(), i.getSpValue());
                    }
                });
        try {
            String tabMainKey = standardParam.get(mainKey);
            if (Objects.isNull(tabMainKey)) {
                return null;
            }
            InitButlerResDTO initButlerResDTO = ObjectConverter.convert(tabMainKey, InitButlerResDTO.class);
            if (Objects.isNull(initButlerResDTO)) {
                return null;
            }

            List<InitButlerResDTO.ServiceButlerResDTO> serviceButlerList = Lists.newArrayList();
            // 主tab置空
            standardParam.put(mainKey, null);
            // 遍历
            standardParam.values().forEach(s -> {
                if (StringUtils.isNotBlank(s)) {
                    try {
                        String decode = DeflateUtil.unzipString(s);
                        String value = StringUtils.isNotBlank(decode) ? decode : s;
                        String trimmed = value.trim();
                        // 简单预判断是否为JSON格式
                        if (!trimmed.startsWith("{") || !trimmed.endsWith("}")) {
                            serviceButlerList.add(null);
                            return;
                        }
                        serviceButlerList.add(ObjectConverter.convert(trimmed, InitButlerResDTO.ServiceButlerResDTO.class));
                    } catch (Exception e) {
                        serviceButlerList.add(null);
                    }
                }
            });

            initButlerResDTO.setServiceButlerList(serviceButlerList.stream()
                    .filter(Objects::nonNull)
                    .filter(j -> j.getServiceButlerId() != null)
                    .sorted(Comparator.comparingInt(InitButlerResDTO.ServiceButlerResDTO::getRank))
                    .collect(Collectors.toList()));
            return initButlerResDTO;
        } catch (Exception e) {
            return null;
        }
    }

    public static InitButlerResDTO getCloudContextTabListValue(ContextInstanceRespDto contextInstanceRespDto, ContextCategoryEnum tabListCategory, String spcodeTab) {
        List<ContextInstanceParamRespDto> contextInstanceParamRespDtoList = contextInstanceRespDto.getContextInstanceParamRespDtoList();
        if (CollectionUtils.isEmpty(contextInstanceParamRespDtoList)) {
            return new InitButlerResDTO();
        }
        InitButlerResDTO initButlerResDTO = new InitButlerResDTO();

        // 初始化currentServiceButlerId
        ContextInstanceParamRespDto butlerParam =
                contextInstanceParamRespDtoList.stream().filter(contextInstanceParamRespDto -> Objects.equals(contextInstanceParamRespDto.getCategoryCode(), "serviceButlerCategory")
                        && Objects.equals(contextInstanceParamRespDto.getSpCode(), "serviceButlerId")).findAny().orElse(null);
        if (null != butlerParam) {
            Long currentServiceButlerId = Long.parseLong(butlerParam.getSpValue());
            initButlerResDTO.setCurrentServiceButlerId(currentServiceButlerId);
        }

        ContextInstanceParamRespDto tabListParam =
                contextInstanceParamRespDtoList.stream().filter(contextInstanceParamRespDto -> Objects.equals(contextInstanceParamRespDto.getCategoryCode(), tabListCategory.getCode())
                && Objects.equals(contextInstanceParamRespDto.getSpCode(), spcodeTab)).findAny().orElse(null);
        // tabList为空的情况
        if (null == tabListParam || StringUtils.isBlank(tabListParam.getSpValue())) {
            return getCannotHandelInitButlerResDTO(contextInstanceRespDto, initButlerResDTO);
        }

        // tabList值无法序列化的情况，与为空的情况同样处理
        String spValue = tabListParam.getSpValue();
        TabListHolder tabListHolderClass = ObjectConverter.convert(spValue, TabListHolder.class);
        if (null == tabListHolderClass) {
            return getCannotHandelInitButlerResDTO(contextInstanceRespDto, initButlerResDTO);
        }

        // 正常情况
        initButlerResDTO.setContactId(tabListHolderClass.getContactId());
        initButlerResDTO.setContactType(tabListHolderClass.getContactType());
        initButlerResDTO.setFollowData(null);
        initButlerResDTO.setIsCompatibleOldOncall(false);

        InitButlerResDTO.ServiceButlerResDTO serviceButlerResDTO = new InitButlerResDTO.ServiceButlerResDTO();
        serviceButlerResDTO.setServiceButlerId(initButlerResDTO.getCurrentServiceButlerId());
        serviceButlerResDTO.setServiceButlerName(null);
        serviceButlerResDTO.setRank(0);
        TabInfoResDTO tabInfoResDTO = new TabInfoResDTO();
        TabInfoResDTO.TabDTO tabDTO = new TabInfoResDTO.TabDTO();
        tabDTO.setName("首页");
        List<TabInfoResDTO.TabDTO> tabList = tabListHolderClass.getTabList();
        if (null != tabList) {
            tabList.add(tabDTO);
        }
        tabInfoResDTO.setTabList(tabList);
        tabInfoResDTO.setCurrentChannelId(tabListHolderClass.getCurrentChannelId());
        tabInfoResDTO.setCurrentChannelType(String.valueOf(tabListHolderClass.getCurrentChannelType()));
        serviceButlerResDTO.setTabInfo(tabInfoResDTO);
        List<InitButlerResDTO.ServiceButlerResDTO> serviceButlerResDTOList = new ArrayList<>();
        serviceButlerResDTOList.add(serviceButlerResDTO);
        initButlerResDTO.setServiceButlerList(serviceButlerResDTOList);

        return initButlerResDTO;
    }

    /**
     * 无法处理的情况
     *
     * @param contextInstanceRespDto
     * @param initButlerResDTO
     * @return
     */
    @NotNull
    private static InitButlerResDTO getCannotHandelInitButlerResDTO(ContextInstanceRespDto contextInstanceRespDto, InitButlerResDTO initButlerResDTO) {
        initButlerResDTO.setContactId(contextInstanceRespDto.getChannelId());
        initButlerResDTO.setContactType(ContactTypeEnum.CHAT.getType());
        initButlerResDTO.setFollowData(null);
        initButlerResDTO.setIsCompatibleOldOncall(false);
        InitButlerResDTO.ServiceButlerResDTO serviceButlerResDTO = new InitButlerResDTO.ServiceButlerResDTO();
        serviceButlerResDTO.setServiceButlerId(initButlerResDTO.getCurrentServiceButlerId());
        serviceButlerResDTO.setServiceButlerName(null);
        serviceButlerResDTO.setRank(0);
        TabInfoResDTO tabInfoResDTO = new TabInfoResDTO();
        TabInfoResDTO.TabDTO tabDTO = new TabInfoResDTO.TabDTO();
        tabDTO.setName("首页");
        tabInfoResDTO.setTabList(Lists.newArrayList(tabDTO));
        tabInfoResDTO.setCurrentChannelId(null);
        tabInfoResDTO.setCurrentChannelType(null);
        serviceButlerResDTO.setTabInfo(tabInfoResDTO);
        List<InitButlerResDTO.ServiceButlerResDTO> serviceButlerResDTOList = new ArrayList<>();
        serviceButlerResDTOList.add(serviceButlerResDTO);
        initButlerResDTO.setServiceButlerList(serviceButlerResDTOList);
        return initButlerResDTO;
    }

    @Data
    private static class TabListHolder {
        private String contactId;
        private String contactType;
        private List<TabInfoResDTO.TabDTO> tabList;
        private String currentChannelId;
        private Integer currentChannelType;
    }
}
