package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: guo<PERSON><PERSON>
 * @Create: 2025/2/28 16:41
 * @Version: 1.0
 */
@Getter
public enum LabelingTaskType {

    EVALUATION_SET_LABELING(1, "评测集标注"),
    TRAINING_SET_LABELING(2, "训练集标注"),
    ONLINE_SESSION_LABELING(3, "线上会话标注"),
    ONLINE_QUERY_LABELING(4, "线上会话Query标注"),
    ;

    private final int code;
    private final String value;

    LabelingTaskType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public String toString() {
        return "LabelingTaskType{" +
                "code=" + code +
                ", value='" + value + '\'' +
                '}';
    }

    // 根据code获取对应的枚举常量
    public static LabelingTaskType getByCode(int code) {
        for (LabelingTaskType taskType : LabelingTaskType.values()) {
            if (taskType.getCode() == code) {
                return taskType;
            }
        }
        return null;
    }
}
