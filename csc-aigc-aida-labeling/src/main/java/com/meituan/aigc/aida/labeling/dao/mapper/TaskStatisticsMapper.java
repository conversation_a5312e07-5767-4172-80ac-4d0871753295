package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.TaskStatistics;
import com.meituan.aigc.aida.labeling.dao.model.TaskStatisticsExample;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DashboardTaskListQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DashboardTaskItemVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.TaskStatisticalVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskStatisticsMapper {
    long countByExample(TaskStatisticsExample example);

    int deleteByExample(TaskStatisticsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TaskStatistics record);

    int insertSelective(TaskStatistics record);

    List<TaskStatistics> selectByExample(TaskStatisticsExample example);

    TaskStatistics selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TaskStatistics record, @Param("example") TaskStatisticsExample example);

    int updateByExample(@Param("record") TaskStatistics record, @Param("example") TaskStatisticsExample example);

    int updateByPrimaryKeySelective(TaskStatistics record);

    int updateByPrimaryKey(TaskStatistics record);

    List<DashboardTaskItemVO> listTasks(@Param("query") DashboardTaskListQuery query);

    TaskStatisticalVO statisticalTaskData(@Param("query") DataStatisticalQuery query);

    void batchInsert(@Param("taskStatisticsList") List<TaskStatistics> taskStatisticsList);
}