package com.meituan.aigc.aida.benchmark.service;

import com.meituan.aigc.aida.benchmark.param.BenchmarkDatasetListParam;
import com.meituan.aigc.aida.benchmark.param.IndicatorComputeTaskParam;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkDatasetListVO;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkVersionListVO;

/**
 * Benchmark管理Service
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
public interface BenchmarkManagementService {

    /**
     * 获取所有Benchmark版本列表
     *
     * @return Benchmark版本列表VO
     */
    BenchmarkVersionListVO listAllBenchmarkVersions();

    /**
     * 分页查询Benchmark数据集列表
     *
     * @param param 查询参数
     * @return Benchmark数据集列表VO
     */
    BenchmarkDatasetListVO listBenchmarkDatasets(BenchmarkDatasetListParam param);

    /**
     * Benchmark指标计算
     *
     * @param param 计算参数
     */
    void indicatorComputing(IndicatorComputeTaskParam param);
} 