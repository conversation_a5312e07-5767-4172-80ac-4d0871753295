package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.Data;

import java.util.List;

/**
 * 非自定义打标机器人DTO
 */
@Data
public class NonCustomRobotDTO {
    /**
     * 标签ID
     */
    private String labelId;
    
    /**
     * 标签名称
     */
    private String labelName;
    
    /**
     * 机器人配置列表
     */
    private List<RobotConfig> robotConfigList;
    
    /**
     * 机器人配置
     */
    @Data
    public static class RobotConfig {
        /**
         * 机器人ID
         */
        private String robotId;
        
        /**
         * 机器人名称
         */
        private String robotName;
        
        /**
         * 机器人版本ID
         */
        private String robotVersionId;
        
        /**
         * 机器人版本名称
         */
        private String robotVersionName;
        
        /**
         * 输入参数
         */
        private List<ParamInfo> inputParam;
        
        /**
         * 输出参数
         */
        private List<ParamInfo> outputParam;
    }
    
    /**
     * 参数信息
     */
    @Data
    public static class ParamInfo {
        /**
         * 参数名称
         */
        private String name;
        
        /**
         * 参数code
         */
        private String code;
    }
} 