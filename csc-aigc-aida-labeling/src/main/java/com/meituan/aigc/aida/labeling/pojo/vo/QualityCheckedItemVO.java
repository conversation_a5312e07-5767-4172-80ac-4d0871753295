package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: guowenhui
 * @Create: 2025/3/28 17:41
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QualityCheckedItemVO {

    /**
     * 质检详情ID
     */
    private Long qualityCheckedItemId;
    /**
     * 原始数据JSON
     */
    private String rawDataContent;
    /**
     * 原始数据表头
     */
    private String rawDataHeaders;
    /**
     * 原始数据内容映射内容JSON
     */
    private String rawDataMappedContent;
    /**
     * 标注项信息JSON
     */
    private String labelingItems;

    /**
     * 标注数据ID
     */
    private Long labelingDataId;
    /**
     * 标注人mis
     */
    private String labelerMis;

    /**
     * 标注人姓名
     */
    private String labelerName;
    /**
     * 标注时间
     */
    private String labelTime;
    /**
     * 质检状态
     * @see com.meituan.aigc.aida.labeling.common.enums.QualityCheckDataStatus
     */
    private Integer qualityCheckStatus;
    /**
     * 质检结果
     */
    private Integer qualityCheckResult;
    /**
     * 质检反馈
     */
    private String qualityCheckRemark;
    /**
     * 质检人
     */
    private String qualityChecker;
    /**
     * 质检时间
     */
    private String qualityCheckTime;
    /**
     * 质检员修改后的标注信息
     */
    private String modifiedLabelingItems;

}
