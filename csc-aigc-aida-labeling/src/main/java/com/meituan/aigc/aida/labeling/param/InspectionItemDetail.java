package com.meituan.aigc.aida.labeling.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-10 19:50
 * @description 标注项数据
 */
@Data
public class InspectionItemDetail implements Serializable {
    /**
     * 标注项ID（新增时为空）
     */
    private Long id;
    /**
     * 标注项名称
     */
    private String name;
    /**
     * 数据类型 1-文本 2-枚举
     * @see com.meituan.aigc.aida.labeling.common.enums.LabelingDataType
     */
    private Integer dataType;
    /**
     * 是否背靠背一致率标注项 1-是 0-否
     * @see com.meituan.aigc.aida.labeling.common.enums.CompareItemsConsistent
     */
    private Integer isCompare;

    /**
     * 是否必填 1-是 2-否
     */
    private Integer isRequired;
    /**
     * 父指标ID，如果是父指标为空
     */
    private Long parentId;
    /**
     * 父指标枚举，当父指标选择该枚举后需要填子指标
     */
    private String parentEnumValue;
    /**
     * 枚举列表
     */
    private List<String> enumList;
}
