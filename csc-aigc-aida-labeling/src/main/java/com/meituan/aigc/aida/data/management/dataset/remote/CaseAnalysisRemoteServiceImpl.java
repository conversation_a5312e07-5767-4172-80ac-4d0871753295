package com.meituan.aigc.aida.data.management.dataset.remote;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.dataset.service.CaseAnalysisService;
import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.api.CaseAnalysisRemoteService;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetCreateResponseDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetTryLockDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.*;
import com.meituan.aigc.aida.labeling.remote.common.dto.PageDataWithHead;
import com.meituan.aigc.aida.labeling.remote.common.dto.ServiceResponseDTO;
import com.meituan.aigc.aida.labeling.remote.common.enums.ResultCode;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025-05-26 11:25
 * @description
 */
@MdpPigeonServer
@Slf4j
public class CaseAnalysisRemoteServiceImpl implements CaseAnalysisRemoteService {


    @Autowired
    private CaseAnalysisService caseAnalysisService;

    @Override
    public ServiceResponseDTO<DataSetCreateResponseDTO> createDataSet(CreateDataSetRequestParam param) {
        return ServiceResponseDTO.success(caseAnalysisService.createDataSet(param));
    }

    @Override
    public ServiceResponseDTO<?> updateDataSet(UpdateDataSetRequestParam param) {
        caseAnalysisService.updateDataSet(param);
        return ServiceResponseDTO.success("success");
    }

    @Override
    public ServiceResponseDTO<DataSetTryLockDTO> getLockIdByDataSetId(Long dataSetId) {
        return ServiceResponseDTO.success(caseAnalysisService.getLockIdByDataSetId(dataSetId));
    }

    @Override
    public ServiceResponseDTO<Boolean> releaseDataSetLock(Long dataSetId, String lockId) {
        return ServiceResponseDTO.success(caseAnalysisService.releaseDataSetLock(dataSetId, lockId));
    }

    @Override
    public ServiceResponseDTO<DatasetQueryResultDTO> pageQueryDataSet(DataSetPageQueryParam param) {
//        String a="{\n" +
//                "\t\"code\": 200,\n" +
//                "\t\"message\": \"操作成功\",\n" +
//                "\t\"data\": {\n" +
//                "\t\t\t\"totalCount\": 100,\n" +
//                "\t\t\t\"headList\": [{\n" +
//                "\t\t\t\t\"columnName\": \"sessionId\",\n" +
//                "\t\t\t\t\"fieldType\": 1\n" +
//                "\t\t\t},{\n" +
//                "\t\t\t\t\"columnName\": \"autoQuestionTag\",\n" +
//                "\t\t\t\t\"fieldType\": 4\n" +
//                "\t\t\t},{\n" +
//                "\t\t\t\t\"columnName\": \"autoQuestionTagId\",\n" +
//                "\t\t\t\t\"fieldType\": 4\n" +
//                "\t\t\t},{\n" +
//                "\t\t\t\t\"columnName\": \"manualQuestionTag\",\n" +
//                "\t\t\t\t\"fieldType\": 4\n" +
//                "\t\t\t},{\n" +
//                "\t\t\t\t\"columnName\": \"manualQuestionTagId\",\n" +
//                "\t\t\t\t\"fieldType\": 4\n" +
//                "\t\t\t},{\n" +
//                "\t\t\t\t\"columnName\": \"reason\",\n" +
//                "\t\t\t\t\"fieldType\": 1\n" +
//                "\t\t\t},{\n" +
//                "\t\t\t\t\"columnName\": \"sessionSummary\",\n" +
//                "\t\t\t\t\"fieldType\": 1\n" +
//                "\t\t\t},{\n" +
//                "\t\t\t\t\"columnName\": \"otherFieldsJson\",\n" +
//                "\t\t\t\t\"fieldType\": 2\n" +
//                "\t\t\t}],\n" +
//                "\t\t\t\"data\": [{\n" +
//                "\t\t\t\t\"dataId\": \"123\",\n" +
//                "\t\t\t\t\"content\": {\n" +
//                "          \t\t\t\"sessionId\":\"1923045289032978487\",\n" +
//                "          \t\t\t\"sessionTime\":1746333609000,\n" +
//                "          \t\t\t\"sceneId\":17,\n" +
//                "          \t\t\t\"autoQuestionTag\":[\"工程问题-工作流执行异常\", \"工程问题-agent超时\"],\n" +
//                "          \t\t\t\"autoQuestionTagId\": [1, 2],\n" +
//                "          \t\t\t\"manualQuestionTag\":[\"模型优化-模型幻觉\", \"商家诉求-切换标问\"],\n" +
//                "          \t\t\t\"manualQuestionTagId\": [16, 18],\n" +
//                "          \t\t\t\"reason\": \"工程问题，工作流执行异常\",\n" +
//                "          \t\t\t\"sessionSummary\": \"出现工作流执行异常\",\n" +
//                "          \t\t\t\"otherFieldsJson\": {\"user_first_description_clarity\":\"清晰\",\"agent_response_summary\":\"客服进行标准接待流程并确认转接人工处理\",\"llm_compare\":\"false\",\"temp_label\":\"\",\"user_intent\":\"要求修改账户绑定的联系电话\"}\n" +
//                "        \t\t}\n" +
//                "\t\t\t}]\n" +
//                "\t\t}\n" +
//                "}";
//        return JSONObject.parseObject(a,new TypeReference<ServiceResponseDTO<PageDataWithHead<DataFieldParam, DataSetRecordParam>>>(){});
        return ServiceResponseDTO.success(caseAnalysisService.pageQueryDataSet(param));
    }
}
