package com.meituan.aigc.aida.data.management.dataset.strategy.field.impl;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签字段处理策略
 */
@Component
public class TagFieldStrategy extends AbstractDataFieldStrategy {

    @Override
    public Integer getStrategyType() {
        return FieldTypeEnum.TAG.getCode();
    }

    @Override
    public void validateValue(String value) {
        // 标签：不做特殊校验
    }

    @Override
    public void processFieldData(String columnName, String value,
                                Map<String, Object> textData,
                                Map<String, Object> flattenedData,
                                Map<String, Date> dateData,
                                Map<String, Object> nestedData) {
        // 标签：转换为数组格式，原数据以逗号分隔
        if (StringUtils.isBlank(value)) {
            textData.put(columnName, new ArrayList<>());
            return;
        }

        String[] tags = value.split(",");
        List<String> tagList = Arrays.stream(tags)
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        textData.put(columnName, tagList);
    }
} 