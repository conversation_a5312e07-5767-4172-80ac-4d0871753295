package com.meituan.aigc.aida.data.management.dataset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.aigc.aida.data.management.dataset.common.DataSetInvokeEs;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.dianping.lion.client.util.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.data.management.dataset.dto.*;
import com.meituan.aigc.aida.data.management.dataset.enums.DatasetVersionStatusEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.EsStoreTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.FileTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.dataset.param.UpdateFieldParam;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetManagementService;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategyFactory;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.impl.JsonFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.impl.ProcessedSignalFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.task.DataSetProcessStrategyFactory;
import com.meituan.aigc.aida.data.management.dataset.strategy.task.impl.OnlineDataSetProcessStrategy;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.data.management.es.query.BatchResult;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.data.management.es.query.DatasetPageResult;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchTaskRepository;
import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.meituan.aigc.aida.labeling.util.ThreadPoolUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.inf.xmdlog.ConfigUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * <AUTHOR>
 *
 * <p>
 * 数据集管理服务
 * @since 2025-05-23 16:23
 */
@Service
@Slf4j
public class DataSetManagementServiceImpl implements DataSetManagementService {
    @Resource
    private DataSetVersionRepository dataSetVersionRepository;

    @Resource
    private DataSetRepository dataSetRepository;

    @Resource
    private DatasetEsIndexService datasetEsIndexService;

    @Resource
    private DataSetInvokeEs dataSetInvokeEs;

    @Resource
    private com.meituan.aigc.aida.labeling.common.s3.S3Service s3Service;

    @Resource
    private PushElephantService pushElephantService;

    private static final String LION_FIELD_TYPE_REGEX_MAPPING_KEY = "aida.data.field.type.regex.mapping";

    // 新增常量
    private static final int DEFAULT_PAGE_NUM = 0;
    private static final int SAMPLE_SIZE = 10;
    private static final int MIN_DATA_SIZE_FOR_MIDDLE_SAMPLE = 20;
    private static final int ES_DATA_QUERY_PAGE_SIZE = 1000;
    private static final String ES_COMMON_DATA_PREFIX = "commonData";
    private static final String FLATTEN_PREFIX = "flattenedData";
    private static final String TEXT_PREFIX = "textData";

    private static final List<Integer> FLATTEN_FIELD_TYPE = Lists.newArrayList(FieldTypeEnum.JSON.getCode(), FieldTypeEnum.PROCESSED_SIGNAL.getCode());

    /**
     * 处理数据集创建任务的线程池
     */
    private static final ExecutorService DATASET_CREATE_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(100, 400, 200, "dataset-create-%d");

    /**
     * 系统可用的CPU核心数
     */
    private static final int CPU_CORE_COUNT = 4;

    /**
     * 处理标注任务导出的线程池
     */
    private static final ExecutorService EXPORT_DATA_SET_DATA_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "dataset-export-%d");

    @Autowired
    private DataSetProcessStrategyFactory dataSetProcessStrategyFactory;

    @Autowired
    private JsonFieldStrategy jsonFieldStrategy;

    @Autowired
    private DataFieldStrategyFactory dataFieldStrategyFactory;

    @Autowired
    private ProcessedSignalFieldStrategy processedSignalFieldStrategy;

    @Autowired
    private DataFetchTaskRepository dataFetchTaskRepository;

    @Override
    public DataFetchContent getDataFetchByTaskId(Long taskId) {
        CheckUtil.paramCheck(Objects.nonNull(taskId),"任务id不能为空");
        DataFetchTask task = dataFetchTaskRepository.getTaskById(taskId);
        CheckUtil.paramCheck(Objects.nonNull(task),"任务不存在");
        DataFetchContent dataFetchContent = new DataFetchContent();
        dataFetchContent.setTaskName(task.getName());
        dataFetchContent.setStatus(task.getTaskStatus());
        dataFetchContent.setCount(task.getDataCount());
        // 数据获取解析字段
        OnlineDataSetProcessStrategy strategy = (OnlineDataSetProcessStrategy) dataSetProcessStrategyFactory.getStrategy(DataSourceEnum.ONLINE.getCode());
        DataSetParseHeadParam param = new DataSetParseHeadParam();
        param.setDataSource(DataSourceEnum.ONLINE.getCode());
        param.setFetchId(taskId);
        List<DataFieldParam> dataFieldParams = strategy.parseHeader(param);
        if(CollectionUtils.isNotEmpty(dataFieldParams)){
            dataFetchContent.setFieldCount(dataFieldParams.size());
        }
        return dataFetchContent;
    }

    @Override
    public List<DataFieldParam> parseFileHeader(DataSetParseHeadParam param) {
        return dataSetProcessStrategyFactory.getStrategy(param.getDataSource()).parseHeader(param);
    }

    @Override
    public DataSetCreateResponseParam createDataSet(DataSetCreateParam param) {
        return dataSetProcessStrategyFactory.getStrategy(param.getDataSource()).createDataSet(param);
    }

    @Override
    public void createDataSetAsync(DataSetCreateParam param) {
        User user = UserUtils.getUser();
        param.setCreatorMis(user.getLogin());
        param.setCreatorName(user.getName());
        DATASET_CREATE_EXECUTOR.submit(() -> {
            try {
                createDataSet(param);
                String url = Lion.getString(ConfigUtil.getAppkey(), LionConstant.DATASET_MANAGE_URL);
                String messageContentFormat = "数据集【%s】创建成功，[请点击查看|%s]";
                String message = String.format(messageContentFormat, param.getDataSetName(), url);
                // 发送大象
                pushElephantService.pushElephant(message, user.getLogin());
            } catch (Exception e) {
                log.error("异步创建数据集失败, param:{}", JSONObject.toJSONString(param), e);
                pushElephantService.pushElephant(String.format("数据集【%s】创建失，失败原因：%s", param.getDataSetName(), e.getMessage()), user.getLogin());
            }
        });
    }

    @Override
    public PageData<DataSetDTO> pageDataset(String name, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<DataSet> dataSets = dataSetRepository.listDataSetByName(name);
        if (CollectionUtils.isEmpty(dataSets)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<DataSet> pageInfo = new PageInfo<>(dataSets);
        PageHelper.clearPage();
        List<DataSetDTO> dataSetList = dataSets.stream().map(this::builderDataSetDTO).collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), dataSetList);
    }

    @Override
    public PageData<DataSetRecordDTO> pageDataSetDetail(Long dataSetId, Long versionId, Integer pageNum, Integer pageSize) {
        // 1. 获取数据集和版本信息
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        DataSetVersion dataSetVersion;
        if (Objects.nonNull(versionId)) {
            dataSetVersion = dataSetVersionRepository.getById(versionId);
        } else {
            // 如果versionId为空，则获取最新版本
            CheckUtil.paramCheck(Objects.nonNull(dataSet.getLatestVersionId()), "数据集最新版本不存在");
            dataSetVersion = dataSetVersionRepository.getById(dataSet.getLatestVersionId());
        }
        CheckUtil.paramCheck(Objects.nonNull(dataSetVersion), "数据集版本不存在");

        // 2. 构建ES查询条件
        DatasetEsQueryCondition condition = dataSetInvokeEs.getDatasetEsQueryConditionWithPage(dataSet, dataSetVersion, pageNum, pageSize);

        // 3. 调用ES查询服务
        DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);

        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageData<>(0L, Collections.emptyList());
        }

        // 4. 转换结果
        List<DataSetRecordDTO> recordDTOList = pageResult.getRecords().stream()
                .filter(Objects::nonNull)
                .map(esIndex -> convertToDataSetRecordDTO(esIndex, dataSetVersion.getHeadConfig()))
                .collect(Collectors.toList());

        return new PageData<>(pageResult.getTotal(), recordDTOList);
    }

    @Override
    public List<FieldEnumDTO> listFieldEnum() {
        return Arrays.stream(FieldTypeEnum.values())
                .map(fieldType -> {
                    FieldEnumDTO dto = new FieldEnumDTO();
                    dto.setCode(fieldType.getCode());
                    dto.setName(fieldType.getDescription());
                    dto.setDescription(fieldType.getExample());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<DataSourceField> listField(Long dataSetId, Long versionId) {
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(versionId), "数据集版本ID不能为空");
        DataSetVersion version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(dataSetId, versionId);
        CheckUtil.paramCheck(Objects.nonNull(version), "数据集版本不存在");
        DatasetHeadConfig datasetHeadConfig = JSON.parseObject(version.getHeadConfig(),
                new TypeReference<DatasetHeadConfig>() {
                });
        if (Objects.isNull(datasetHeadConfig) || CollectionUtils.isEmpty(datasetHeadConfig.getHeadList())) {
            return Collections.emptyList();
        }
        List<DataContentFieldDTO> headList = datasetHeadConfig.getHeadList();
        return headList.stream().filter(Objects::nonNull).map(this::convertToDataSourceField).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateField(UpdateFieldParam updateFieldParam) {

        // 参数校验
        checkUpdateFieldParam(updateFieldParam);

        // 1. 获取数据集版本信息
        DataSet dataSet = dataSetRepository.getDataSetById(updateFieldParam.getDataSetId());
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        DataSetVersion dataSetVersion = dataSetVersionRepository.getById(updateFieldParam.getVersionId());
        CheckUtil.paramCheck(Objects.nonNull(dataSetVersion), "数据集版本不存在");

        // 2. 解析原始的headConfig
        DatasetHeadConfig headConfig = JSON.parseObject(dataSetVersion.getHeadConfig(),
                new TypeReference<DatasetHeadConfig>() {
                });
        // 3. 根据传入的字段列表更新headConfig
        List<DataContentFieldDTO> headList = headConfig.getHeadList();
        if (CollectionUtils.isEmpty(headList)) {
            log.warn("数据集版本的表头配置为空，dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
            return;
        }
        // 4. 获取更新的字段
        List<DataContentFieldDTO> updateHeadList = getHeadConfig(dataSet,dataSetVersion,updateFieldParam, headList);
        headConfig.setHeadList(updateHeadList);
        dataSetVersion.setHeadConfig(JSON.toJSONString(headConfig));
        dataSetVersion.setUpdateTime(new Date());
        dataSetVersionRepository.updateSelectiveById(dataSetVersion);

        // 5. 更新ES中的fieldMapping字段
        updateEsFieldMapping(dataSet, dataSetVersion, JSON.toJSONString(headConfig));
        log.info("成功更新数据集字段配置和ES字段映射, dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
    }

    @Override
    public void exportDataSet(Long dataSetId, Long versionId, Integer fileType) {
        // 1. 参数校验
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(versionId), "数据集版本ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(fileType), "导出文件类型不能为空");

        // 2. 获取数据集和版本信息
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        DataSetVersion dataSetVersion = dataSetVersionRepository.getById(versionId);
        CheckUtil.paramCheck(Objects.nonNull(dataSetVersion), "数据集版本不存在");

        // 3. 获取当前用户信息
        User user = UserUtils.getUser();
        String userMis = user != null ? user.getLogin() : "unknown";

        // 4. 异步执行导出任务
        EXPORT_DATA_SET_DATA_EXECUTOR.submit(() -> {
            try {
                // 5. 执行导出
                String fileName = String.format("%s_v%s_%s", dataSet.getName(), dataSetVersion.getVersionName(),
                        DateUtil.formatDate(new Date(), "yyyyMMddHHmmss"));
                String fileUrl = exportDataSetToFile(dataSet, dataSetVersion, fileType, fileName);

                // 6. 发送通知
                String fileTypeStr = FileTypeEnum.getDescByCode(fileType);
                String message = String.format("数据集【%s】(版本:%s)导出%s文件成功，[点击下载|%s]",
                        dataSet.getName(), dataSetVersion.getVersionName(), fileTypeStr, fileUrl);
                pushElephantService.pushElephant(message, userMis);

                log.info("数据集导出成功, dataSetId:{}, versionId:{}, fileType:{}, 用户:{}",
                        dataSetId, versionId, fileType, userMis);
            } catch (Exception e) {
                log.error("数据集导出失败, dataSetId:{}, versionId:{}, fileType:{}, 用户:{}",
                        dataSetId, versionId, fileType, userMis, e);
                String message = String.format("数据集【%s】(版本:%s)导出失败，原因：%s",
                        dataSet.getName(), dataSetVersion.getVersionName(), e.getMessage());
                pushElephantService.pushElephant(message, userMis);
            }
        });
    }

    /**
     * 将数据集导出为文件
     *
     * @param dataSet 数据集
     * @param dataSetVersion 数据集版本
     * @param fileType 文件类型 (1: Excel, 2: JSONL, 3: JSON)
     * @param fileName 文件名
     * @return 文件URL
     */
    private String exportDataSetToFile(DataSet dataSet, DataSetVersion dataSetVersion, Integer fileType, String fileName) throws Exception {
        // 1. 构建查询条件
        DatasetEsQueryCondition condition = dataSetInvokeEs.buildScrollCondition(dataSet, dataSetVersion, null);

        // 2. 解析表头配置
        DatasetHeadConfig headConfig = JSON.parseObject(dataSetVersion.getHeadConfig(),
                new TypeReference<DatasetHeadConfig>() {});
        CheckUtil.paramCheck(Objects.nonNull(headConfig) && CollectionUtils.isNotEmpty(headConfig.getHeadList()),
                "数据集表头配置为空");

        // 3. 获取对应的导出策略
        DataSetExportStrategy exportStrategy = getExportStrategy(fileType);

        // 4. 使用策略执行导出
        File tempFile = exportStrategy.export(dataSet, dataSetVersion, condition, headConfig, fileName);

        // 5. 上传文件到S3

        String fileUrl;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            try (FileInputStream fileInputStream = new FileInputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            fileUrl = s3Service.upload(new ByteArrayInputStream(outputStream.toByteArray()),
                    fileName + exportStrategy.getFileExtension());
        }

        // 6. 删除临时文件
        if (tempFile.exists()) {
            tempFile.delete();
        }
        return fileUrl;
    }

    /**
     * 获取导出策略
     *
     * @param fileType 文件类型
     * @return 导出策略
     */
    private DataSetExportStrategy getExportStrategy(Integer fileType) {
        if (fileType == FileTypeEnum.EXCEL.getCode()) {
            return new ExcelExportStrategy();
        } else if (fileType == FileTypeEnum.JSON.getCode()) {
            return new JsonExportStrategy();
        } else if (fileType == FileTypeEnum.JSONL.getCode()) {
            return new JsonlExportStrategy();
        } else {
            throw new IllegalArgumentException("不支持的导出类型: " + fileType);
        }
    }

    /**
     * 数据集导出策略接口
     */
    private interface DataSetExportStrategy {
        /**
         * 导出数据集
         *
         * @param dataSet 数据集
         * @param dataSetVersion 数据集版本
         * @param condition ES查询条件
         * @param headConfig 表头配置
         * @param fileName 文件名
         * @return 临时文件
         */
        File export(DataSet dataSet, DataSetVersion dataSetVersion, DatasetEsQueryCondition condition,
                 DatasetHeadConfig headConfig, String fileName) throws Exception;

        /**
         * 获取文件扩展名
         *
         * @return 文件扩展名（包含.）
         */
        String getFileExtension();
    }

    /**
     * Excel导出策略
     */
    private class ExcelExportStrategy implements DataSetExportStrategy {
        @Override
        public File export(DataSet dataSet, DataSetVersion dataSetVersion, DatasetEsQueryCondition condition,
                        DatasetHeadConfig headConfig, String fileName) throws Exception {
            File tempFile = File.createTempFile(fileName, getFileExtension());
            exportToExcel(condition, headConfig, tempFile);
            return tempFile;
        }

        @Override
        public String getFileExtension() {
            return ".xlsx";
        }
    }

    /**
     * JSONL导出策略
     */
    private class JsonlExportStrategy implements DataSetExportStrategy {
        @Override
        public File export(DataSet dataSet, DataSetVersion dataSetVersion, DatasetEsQueryCondition condition,
                        DatasetHeadConfig headConfig, String fileName) throws Exception {
            File tempFile = File.createTempFile(fileName, getFileExtension());
            exportToJsonl(condition, headConfig, tempFile);
            return tempFile;
        }

        @Override
        public String getFileExtension() {
            return ".jsonl";
        }
    }

    /**
     * JSON导出策略
     */
    private class JsonExportStrategy implements DataSetExportStrategy {
        @Override
        public File export(DataSet dataSet, DataSetVersion dataSetVersion, DatasetEsQueryCondition condition,
                        DatasetHeadConfig headConfig, String fileName) throws Exception {
            //目前不支持JSON导出
            return null;
        }

        @Override
        public String getFileExtension() {
            return ".json";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataSet(Long dataSetId) {
        // 参数校验
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        // 1. 将数据集的isDeleted字段置为1
        dataSet.setIsDeleted(Boolean.TRUE);
        dataSet.setUpdateTime(new Date());
        dataSetRepository.updateDataSet(dataSet);
        log.info("数据集已删除, dataSetId:{}", dataSetId);

        // 2. 查询所有版本
        List<DataSetVersion> versions = dataSetVersionRepository.listVersionByDataSetId(dataSetId);
        if (CollectionUtils.isEmpty(versions)) {
            log.info("数据集没有版本数据, dataSetId:{}", dataSetId);
            return;
        }

        // 3. 删除所有版本
        for (DataSetVersion version : versions) {
            version.setIsDeleted(Boolean.TRUE);
            version.setUpdateTime(new Date());
            dataSetVersionRepository.updateSelectiveById(version);

            // 4. 删除该版本的ES数据
            dataSetInvokeEs.deleteVersionEsData(dataSet, version);
        }
        log.info("删除数据集完成, dataSetId:{}", dataSetId);
    }

    @Override
    public void publishDataSet(Long dataSetId, Long versionId) {
        DataSetVersion version = getVersion(dataSetId, versionId);
        // 草稿状态可发布
        if(Objects.equals(version.getVersionStatus(), DatasetVersionStatusEnum.DRAFT.getCode())){
            version.setVersionStatus(DatasetVersionStatusEnum.PUBLISHED.getCode());
            version.setUpdateTime(new Date());
            dataSetVersionRepository.updateSelectiveById(version);
        }
    }

    @Override
    public DatasetHeadConfig handleHeadList(DatasetHeadConfig historyHeadConfig, List<DataFieldParam> headList, List<DataSetRecordParam> dataList) {
        DatasetHeadConfig headConfig = new DatasetHeadConfig();
        // 处理默认的空类型和es字段映射
        List<DataContentFieldDTO> fieldList = new ArrayList<>();

        // 获取Lion配置的通用字段
        Set<String> commonFields = getCommonFieldsFromLion();

        // 为es字段映射准备计数器
        int textSegmentIndex = 1;
        int flattenSegmentIndex = 1;
        Map<String, DataContentFieldDTO> fieldMap = new HashMap<>();
        if (historyHeadConfig != null) {
            List<DatasetHeadConfig.HeadConfig> headConfigList = historyHeadConfig.getHeadConfig();
            Map<String, DatasetHeadConfig.HeadConfig> headConfigMap = headConfigList.stream().collect(Collectors.toMap(DatasetHeadConfig.HeadConfig::getStoreType, Function.identity()));
            if (Objects.nonNull(headConfigMap.get(EsStoreTypeEnum.TEXT.getName()))) {
                textSegmentIndex = headConfigMap.get(EsStoreTypeEnum.TEXT.getName()).getMaxIndex();
            }
            if (Objects.nonNull(headConfigMap.get(EsStoreTypeEnum.FLATTEN.getName()))) {
                flattenSegmentIndex = headConfigMap.get(EsStoreTypeEnum.FLATTEN.getName()).getMaxIndex();
            }
            // 构建字段映射，为了后续找到是否有历史已经映射过的字段
            if (CollectionUtils.isNotEmpty(historyHeadConfig.getHeadList())) {
                fieldMap = historyHeadConfig.getHeadList().stream().collect(Collectors.toMap(DataContentFieldDTO::getColumnName, Function.identity()));
            }
        }

        List<DatasetHeadConfig.HeadConfig> headConfigList = new ArrayList<>();
        for (DataFieldParam field : headList) {
            DataContentFieldDTO fieldDTO = new DataContentFieldDTO();
            fieldDTO.setColumnName(field.getColumnName());

            Integer fieldType = field.getFieldType();
            // 如果字段类型为空，则使用采样数据推断字段类型
            if (field.getFieldType() == null) {
                fieldType = inferFieldTypeFromSampleData(field.getColumnName(), dataList);
                log.info("字段[{}]类型推断结果：{}", field.getColumnName(), fieldType);
            }
            fieldDTO.setFieldType(fieldType);

            // 已经有映射关系的使用旧的映射
            if (fieldMap.containsKey(field.getColumnName())) {
                fieldDTO.setEsKey(fieldMap.get(field.getColumnName()).getEsKey());
                fieldDTO.setEsColumnName(fieldMap.get(field.getColumnName()).getEsColumnName());
            } else {
                // 设置ES字段映射（拆分为esKey和esColumnName）
                EsFieldMapping esMapping = generateEsFieldMapping(field, commonFields, textSegmentIndex, flattenSegmentIndex);
                fieldDTO.setEsKey(esMapping.getEsKey());
                fieldDTO.setEsColumnName(esMapping.getEsColumnName());
                // 如果不是通用字段，则递增segment索引
                if (!commonFields.contains(field.getColumnName())) {
                    if (FLATTEN_FIELD_TYPE.contains(field.getFieldType())) {
                        flattenSegmentIndex++;
                    } else {
                        textSegmentIndex++;
                    }
                }
            }

            fieldList.add(fieldDTO);
        }
        Set<String> newFieldSet = fieldList.stream().map(DataContentFieldDTO::getColumnName).collect(Collectors.toSet());
        // 补充历史数据中没有出现的记录
        if (historyHeadConfig != null) {
            List<DataContentFieldDTO> historyFieldList = historyHeadConfig.getHeadList();
            for (DataContentFieldDTO historyField : historyFieldList) {
                if (!newFieldSet.contains(historyField.getColumnName())) {
                    fieldList.add(historyField);
                }
            }
        }
        headConfigList.add(new DatasetHeadConfig.HeadConfig(EsStoreTypeEnum.TEXT.getName(), textSegmentIndex));
        headConfigList.add(new DatasetHeadConfig.HeadConfig(EsStoreTypeEnum.FLATTEN.getName(), flattenSegmentIndex));
        headConfig.setHeadConfig(headConfigList);
        headConfig.setHeadList(fieldList);
        return headConfig;
    }

    @Override
    public List<DatasetEsIndex> buildDatasetEsIndices(Long dataSetId, Long versionId, Integer dataSource, List<DataSetRecordParam> dataList, DatasetHeadConfig headConfig, Date createTime, Date updateTime) {
        List<DatasetEsIndex> esIndexList = new ArrayList<>();
        for (DataSetRecordParam record : dataList) {
            DatasetEsIndex esIndex = new DatasetEsIndex();
            esIndex.setDatasetId(dataSetId.toString());
            esIndex.setVersionId(versionId.toString());
            esIndex.setDataSource(dataSource.toString());
            esIndex.setCreateTime(createTime);
            esIndex.setUpdateTime(updateTime);
            esIndex.setFieldMapping(JSONObject.toJSONString(headConfig));
            String dataId = record.getDataId();
            if (StringUtils.isBlank(dataId)) {
                dataId = UUID.randomUUID().toString();
            }
            esIndex.setDocumentId(dataId);

            // 处理数据内容
            Map<String, Object> textData = new HashMap<>();
            Map<String, Object> content = record.getContent();
            Map<String, Object> flattenedData = new HashMap<>();
            Map<String, Date> dateData = new HashMap<>();
            Map<String, Object> nestedData = new HashMap<>();
            // 新增：通用数据存储map
            Map<String, Object> commonData = new HashMap<>();

            // 遍历fieldList处理数据
            for (DataContentFieldDTO field : headConfig.getHeadList()) {
                String columnName = field.getColumnName();
                String value = content.get(columnName) == null ? "" : content.get(columnName).toString();
                Integer fieldType = field.getFieldType();
                String esKey = field.getEsKey();
                String esColumnName = field.getEsColumnName();

                if (StringUtils.isNotBlank(value)) {
                    // 使用策略模式处理不同类型的数据
                    DataFieldStrategy dataFieldStrategy = dataFieldStrategyFactory.getStrategy(fieldType);

                    // 根据esKey判断存储位置
                    if (ES_COMMON_DATA_PREFIX.equals(esKey)) {
                        // 通用字段：处理后存储到commonData中，使用esColumnName作为key
                        processFieldDataToCommonData(dataFieldStrategy, esColumnName, value, commonData, dateData);
                    } else {
                        // 普通字段：使用esColumnName作为字段名存储到对应的数据结构中
                        dataFieldStrategy.processFieldData(esColumnName, value, textData, flattenedData, dateData, nestedData);
                    }
                }
            }

            esIndex.setTextData(textData);
            esIndex.setFlattenedData(flattenedData);
            esIndex.setDateData(dateData);
            esIndex.setNestedData(nestedData);
            // 设置通用数据
            if (!commonData.isEmpty()) {
                esIndex.setCommonData(commonData);
            }

            esIndexList.add(esIndex);
        }

        return esIndexList;
    }

    /**
     * 获取数据集版本
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @return 数据集版本信息
     */
    private DataSetVersion getVersion(Long dataSetId, Long versionId) {
        CheckUtil.paramCheck(Objects.nonNull(dataSetId) && Objects.nonNull(versionId), "数据集ID或版本ID不能为空");
        DataSetVersion version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(dataSetId, versionId);
        CheckUtil.paramCheck(Objects.nonNull(version), "数据集版本不存在");
        return version;
    }

    /**
     * 处理字段数据到通用数据存储
     *
     * @param dataFieldStrategy 数据字段处理器
     * @param esColumnName      ES字段名
     * @param value             字段值
     * @param commonData        通用数据存储map
     * @param dateData          日期数据存储map
     */
    private void processFieldDataToCommonData(DataFieldStrategy dataFieldStrategy, String esColumnName, String value,
                                              Map<String, Object> commonData, Map<String, Date> dateData) {
        // 创建临时存储maps用于处理
        Map<String, Object> tempTextData = new HashMap<>();
        Map<String, Object> tempFlattenedData = new HashMap<>();
        Map<String, Date> tempDateData = new HashMap<>();
        Map<String, Object> tempNestedData = new HashMap<>();

        // 使用处理器处理数据
        dataFieldStrategy.processFieldData(esColumnName, value, tempTextData, tempFlattenedData, tempDateData, tempNestedData);

        // 将处理结果存储到commonData中
        if (!tempTextData.isEmpty()) {
            commonData.putAll(tempTextData);
        }
        if (!tempFlattenedData.isEmpty()) {
            commonData.putAll(tempFlattenedData);
        }
        if (!tempNestedData.isEmpty()) {
            commonData.putAll(tempNestedData);
        }
        // 日期数据单独处理
        if (!tempDateData.isEmpty()) {
            dateData.putAll(tempDateData);
        }
    }

    /**
     * 生成ES字段映射
     *
     * @param field               表头配置
     * @param commonFields        通用字段集合
     * @param textSegmentIndex    文本类型数据段索引
     * @param flattenSegmentIndex 扁平化类型数据段索引
     * @return ES字段映射对象
     */
    private EsFieldMapping generateEsFieldMapping(DataFieldParam field, Set<String> commonFields, int textSegmentIndex, int flattenSegmentIndex) {

        if (commonFields.contains(field.getColumnName())) {
            return new EsFieldMapping(ES_COMMON_DATA_PREFIX, field.getColumnName());
        } else if (FLATTEN_FIELD_TYPE.contains(field.getFieldType())) {
            return new EsFieldMapping(FLATTEN_PREFIX, "segment_" + flattenSegmentIndex);
        }
        return new EsFieldMapping(TEXT_PREFIX, "segment_" + textSegmentIndex);
    }

    /**
     * ES字段映射内部类
     */
    private static class EsFieldMapping {
        private final String esKey;
        private final String esColumnName;

        public EsFieldMapping(String esKey, String esColumnName) {
            this.esKey = esKey;
            this.esColumnName = esColumnName;
        }

        public String getEsKey() {
            return esKey;
        }

        public String getEsColumnName() {
            return esColumnName;
        }
    }

    /**
     * 从采样数据推断字段类型
     *
     * @param columnName 字段名称
     * @param dataList   数据列表
     * @return 推断的字段类型
     */
    @Override
    public int inferFieldTypeFromSampleData(String columnName, List<DataSetRecordParam> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("数据列表为空，字段[{}]使用默认文本类型", columnName);
            return FieldTypeEnum.TEXT.getCode();
        }

        // 获取采样数据
        List<String> sampleValues = getSampleValues(columnName, dataList);
        if (CollectionUtils.isEmpty(sampleValues)) {
            log.warn("字段[{}]采样数据为空，使用默认文本类型", columnName);
            return FieldTypeEnum.TEXT.getCode();
        }

        // 推断字段类型
        Set<Integer> fieldTypes = new HashSet<>();
        for (String value : sampleValues) {
            if (StringUtils.isNotBlank(value)) {
                int fieldType = matchFieldTypeByValue(value);
                fieldTypes.add(fieldType);
            }
        }

        // 如果所有采样数据的字段类型都相同，则使用该类型，否则使用文本类型
        if (fieldTypes.size() == 1) {
            Integer inferredType = fieldTypes.iterator().next();
            log.info("字段[{}]类型推断成功，采样数量：{}，推断类型：{}", columnName, sampleValues.size(), inferredType);
            return inferredType;
        } else {
            log.info("字段[{}]类型推断结果不一致，采样数量：{}，类型集合：{}，使用文本类型",
                    columnName, sampleValues.size(), fieldTypes);
            return FieldTypeEnum.TEXT.getCode();
        }
    }

    /**
     * 根据数据值匹配数据类型
     * 优先级：Lion正则匹配、处理后信号、json格式，如果都不满足，则匹配为文本
     */
    private int matchFieldTypeByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return FieldTypeEnum.TEXT.getCode();
        }

        // 1. Lion正则匹配
        Integer lionMatchType = matchByLionRegex(value);
        if (lionMatchType != null) {
            return lionMatchType;
        }

        // 2. 处理后信号格式检查
        if (isProcessedSignalFormat(value)) {
            return FieldTypeEnum.PROCESSED_SIGNAL.getCode();
        }

        // 3. JSON格式检查
        if (isJsonFormat(value)) {
            return FieldTypeEnum.JSON.getCode();
        }

        // 4. 默认为文本
        return FieldTypeEnum.TEXT.getCode();
    }

    /**
     * 通过Lion配置的正则表达式匹配数据类型
     */
    private Integer matchByLionRegex(String value) {
        try {
            String configValue = Lion.getString(ConfigUtil.getAppkey(), LION_FIELD_TYPE_REGEX_MAPPING_KEY);
            if (StringUtils.isBlank(configValue)) {
                return null;
            }

            Map<String, String> regexMapping = JSON.parseObject(configValue, new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isEmpty(regexMapping)) {
                return null;
            }

            for (Map.Entry<String, String> entry : regexMapping.entrySet()) {
                String typeCode = entry.getKey();
                String regex = entry.getValue();

                if (StringUtils.isNotBlank(regex) && Pattern.matches(regex, value)) {
                    return Integer.parseInt(typeCode);
                }
            }
        } catch (Exception e) {
            log.warn("Lion配置正则匹配失败", e);
        }

        return null;
    }

    /**
     * 检查是否为JSON格式
     */
    private boolean isJsonFormat(String value) {
        try {
            jsonFieldStrategy.validateValue(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为处理后信号格式
     */
    private boolean isProcessedSignalFormat(String value) {
        try {
            processedSignalFieldStrategy.validateValue(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取字段的采样值
     *
     * @param columnName 字段名称
     * @param dataList   数据列表
     * @return 采样值列表
     */
    private List<String> getSampleValues(String columnName, List<DataSetRecordParam> dataList) {
        List<String> allValues = dataList.stream()
                .map(DataSetRecordParam::getContent)
                .filter(Objects::nonNull)
                .map(content -> content.get(columnName))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allValues)) {
            return Collections.emptyList();
        }

        // 根据数据量决定采样策略
        if (allValues.size() <= MIN_DATA_SIZE_FOR_MIDDLE_SAMPLE) {
            // 数据量小于20条时，取前10条数据
            return allValues.stream()
                    .limit(SAMPLE_SIZE)
                    .collect(Collectors.toList());
        } else {
            // 数据量大于20条时，取中间10条数据
            int startIndex = (allValues.size() - SAMPLE_SIZE) / 2;
            int endIndex = startIndex + SAMPLE_SIZE;
            return allValues.subList(startIndex, Math.min(endIndex, allValues.size()));
        }
    }

    /**
     * 从Lion获取通用字段配置
     *
     * @return 通用字段集合
     */
    private Set<String> getCommonFieldsFromLion() {
        try {
            String commonFieldsConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstant.LION_COMMON_FIELDS_KEY);
            if (StringUtils.isBlank(commonFieldsConfig)) {
                log.info("Lion配置[{}]为空，返回空的通用字段集合", LionConstant.LION_COMMON_FIELDS_KEY);
                return Collections.emptySet();
            }

            Set<String> commonFields = Arrays.stream(commonFieldsConfig.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            log.info("从Lion获取通用字段配置成功，字段数量：{}", commonFields.size());
            return commonFields;
        } catch (Exception e) {
            log.warn("从Lion获取通用字段配置失败", e);
            return Collections.emptySet();
        }
    }

    /**
     * 构建数据集DTO
     *
     * @param dataSet 数据集对象
     * @return 数据集DTO对象
     */
    private DataSetDTO builderDataSetDTO(DataSet dataSet) {
        if (Objects.isNull(dataSet)) {
            return null;
        }
        DataSetDTO dataSetDTO = new DataSetDTO();
        try {
            dataSetDTO.setId(dataSet.getId());
            dataSetDTO.setName(dataSet.getName());
            if (Objects.nonNull(dataSet.getLatestVersionId())) {
                dataSetDTO.setLatestVersionId(dataSet.getLatestVersionId());
                DataSetVersion dataSetVersion = dataSetVersionRepository.getById(dataSet.getLatestVersionId());
                dataSetDTO.setLatestVersionName(dataSetVersion.getVersionName());
                dataSetDTO.setPublishStatus(dataSetVersion.getVersionStatus());
            }
            dataSetDTO.setCreatorMis(dataSet.getCreatorMis());
            dataSetDTO.setCreatorName(dataSet.getCreatorName());
            dataSetDTO.setUsageCategory(dataSet.getUsageCategory());
            dataSetDTO.setDataSource(dataSet.getDataSource());
            dataSetDTO.setDataCount(dataSet.getDataCount());
            dataSetDTO.setCreateTime(DateUtil.formatDate(dataSet.getCreateTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD));
        } catch (Exception e) {
            log.error("日期转换失败，数据集id:{}", dataSet.getId(), e);
        }
        return dataSetDTO;
    }

    /**
     * 构建数据集ES索引
     *
     * @param esIndex ES索引记录
     */
    private DataSetRecordDTO convertToDataSetRecordDTO(DatasetEsIndex esIndex, String headConfig) {
        DataSetRecordDTO recordDTO = new DataSetRecordDTO();
        recordDTO.setId(esIndex.getDocumentId());
        recordDTO.setCreateTime(esIndex.getCreateTime());
        recordDTO.setUpdateTime(esIndex.getUpdateTime());
        Map<String, Object> content = new HashMap<>();
        DatasetHeadConfig datasetHeadConfig = JSON.parseObject(headConfig,
                new TypeReference<DatasetHeadConfig>() {
                });
        Map<String, String> columnNameMap = datasetHeadConfig.getHeadList().stream()
                .collect(Collectors.toMap(dataContentField -> dataContentField.getEsKey() + "." + dataContentField.getEsColumnName(), DataContentFieldDTO::getColumnName));

        // 转换文本数据
        if (esIndex.getTextData() != null) {
            esIndex.getTextData().forEach((key, value) -> {
                if (value != null) {
                    content.put(columnNameMap.getOrDefault(TEXT_PREFIX + "." + key, key), value);
                }
            });
        }

        // 转换平铺数据
        if (esIndex.getFlattenedData() != null) {
            esIndex.getFlattenedData().forEach((key, value) -> {
                if (value != null) {
                    content.put(columnNameMap.getOrDefault(FLATTEN_PREFIX + "." + key, key), value);
                }
            });
        }

        if (esIndex.getCommonData() != null) {
            esIndex.getCommonData().forEach((key, value) -> {
                if (value != null) {
                    content.put(key, value.toString());
                }
            });
        }
        recordDTO.setContent(JSONObject.toJSONString(content));
        return recordDTO;
    }

    /**
     * 数据内容字段DTO转换为数据源字段
     *
     * @param dataContentFieldDTO 数据内容字段DTO对象
     * @return 返回转换后的数据源字段对象
     */
    private DataSourceField convertToDataSourceField(DataContentFieldDTO dataContentFieldDTO) {
        DataSourceField dataSourceField = new DataSourceField();
        dataSourceField.setFieldName(dataContentFieldDTO.getColumnName());
        dataSourceField.setFieldType(dataContentFieldDTO.getFieldType());
        dataSourceField.setFieldTypeName(FieldTypeEnum.getDescriptionByCode(dataContentFieldDTO.getFieldType()));
        return dataSourceField;
    }

    private static void checkUpdateFieldParam(UpdateFieldParam updateFieldParam) {
        CheckUtil.paramCheck(Objects.nonNull(updateFieldParam), "更新字段参数不能为空");
        CheckUtil.paramCheck(Objects.nonNull(updateFieldParam.getDataSetId()), "数据集ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(updateFieldParam.getVersionId()), "数据集版本ID不能为空");
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(updateFieldParam.getHeadList()), "字段列表不能为空");
    }

    /**
     * 更新ES中的fieldMapping字段
     *
     * @param dataSet           数据集对象
     * @param dataSetVersion    数据集版本对象
     * @param updatedHeadConfig 更新后的headConfig字段
     */
    private void updateEsFieldMapping(DataSet dataSet, DataSetVersion dataSetVersion, String updatedHeadConfig) {
        String dataSource = DataSourceEnum.getValueByCode(dataSet.getDataSource());
        String indexName = dataSetVersion.getEsIndexName();

        if (StringUtils.isBlank(indexName)) {
            log.warn("数据集版本未关联ES索引，无需更新ES, dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
            return;
        }
        // 构建查询条件
        DatasetEsQueryCondition condition = dataSetInvokeEs.buildScrollCondition(dataSet, dataSetVersion, null);
        // 分批查询并更新
        boolean hasMore = true;
        while (hasMore) {
            DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);
            List<DatasetEsIndex> records = pageResult.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            for (DatasetEsIndex esIndex : records) {
                // 更新fieldMapping字段
                esIndex.setFieldMapping(updatedHeadConfig);
                esIndex.setUpdateTime(new Date());
            }
            // 批量更新ES
            BatchResult batchResult = datasetEsIndexService.batchUpdateDatasets(records, dataSource, indexName);
            if (!batchResult.isAllSuccess()) {
                log.error("更新ES字段映射失败，dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
            }
            // 检查是否有更多数据
            hasMore = pageResult.getHasNext() != null && pageResult.getHasNext();
            if(hasMore) {
                condition.setScrollId(pageResult.getScrollId());
            }
        }
    }

    /**
     * 获取更新后的headConfig
     *
     * @param updateFieldParam 更新字段参数
     * @param headList         表头列表
     * @return 更新后的headConfig
     */
    private List<DataContentFieldDTO> getHeadConfig(DataSet dataSet, DataSetVersion dataSetVersion, UpdateFieldParam updateFieldParam, List<DataContentFieldDTO> headList) {
        // 创建字段名到字段对象的映射
        Map<String, DataContentFieldDTO> fieldMap = headList.stream()
                .collect(Collectors.toMap(DataContentFieldDTO::getColumnName, Function.identity(), (v1, v2) -> v1));

        // 获取样本数据进行校验
        DatasetEsQueryCondition condition = dataSetInvokeEs.getDatasetEsQueryConditionWithPage(
                dataSet, dataSetVersion, DEFAULT_PAGE_NUM, SAMPLE_SIZE);
        DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);
        List<DatasetEsIndex> records = pageResult.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.warn("无法获取样本数据进行校验, dataSetId:{}, versionId:{}",
                    dataSet.getId(), dataSetVersion.getId());
            return new ArrayList<>(fieldMap.values());
        }
        Map<String, List<String>> fieldValueSamples = new HashMap<>();

        // 提取样本数据中的字段值
        for (DatasetEsIndex esIndex : records) {
            Map<String, Object> rowData = convertEsIndexToRowData(esIndex,
                    JSON.parseObject(dataSetVersion.getHeadConfig(), new TypeReference<DatasetHeadConfig>() {
                    }));

            for (Map.Entry<String, Object> entry : rowData.entrySet()) {
                String fieldName = entry.getKey();
                String value = convertValueToString(entry.getValue());
                fieldValueSamples.computeIfAbsent(fieldName, k -> new ArrayList<>())
                        .add(value);
            }
        }

        // 遍历更新参数中的字段列表，更新字段类型
        for (DataSourceField field : updateFieldParam.getHeadList()) {
            String fieldName = field.getFieldName();
            Integer fieldType = field.getFieldType();

            if (StringUtils.isBlank(fieldName) || Objects.isNull(fieldType)) {
                log.warn("字段名称或类型为空，跳过更新, field:{}", JSON.toJSONString(field));
                continue;
            }

            DataContentFieldDTO existingField = fieldMap.get(fieldName);
            if (Objects.isNull(existingField)) {
                log.warn("字段不存在于原表头配置中，跳过更新, fieldName:{}", fieldName);
                continue;
            }

            // 对特殊类型（JSON和加工后信号）进行校验
            if (needsTypeValidation(fieldType)) {
                boolean validationPassed = validateFieldType(fieldName, fieldType, fieldValueSamples.get(fieldName));

                if (validationPassed) {
                    // 校验通过，更新字段类型
                    existingField.setFieldType(fieldType);
                    log.info("字段类型更新校验通过, fieldName:{}, newType:{}", fieldName, fieldType);
                } else {
                    throw new AidaRpcException(String.format("字段类型校验不通过, fieldName:%s,fieldType:%s,originalType:%s", fieldName, fieldType, existingField.getFieldType()));
                }
            } else {
                // 普通类型无需校验，直接更新
                existingField.setFieldType(fieldType);
            }
        }
        // 将更新后的字段列表返回
        return new ArrayList<>(fieldMap.values());
    }

    /**
     * 判断是否需要对字段类型进行校验
     *
     * @param fieldType 字段类型
     * @return 是否需要校验
     */
    private boolean needsTypeValidation(Integer fieldType) {
        return fieldType != null && (
                fieldType.equals(FieldTypeEnum.JSON.getCode()) ||
                fieldType.equals(FieldTypeEnum.PROCESSED_SIGNAL.getCode())
        );
    }

    /**
     * 校验字段类型是否与样本数据匹配
     *
     * @param fieldName 字段名称
     * @param fieldType 目标字段类型
     * @param sampleValues 样本值列表
     * @return 校验是否通过
     */
    private boolean validateFieldType(String fieldName, Integer fieldType, List<String> sampleValues) {
        if (CollectionUtils.isEmpty(sampleValues)) {
            log.warn("字段[{}]样本数据为空，无法校验", fieldName);
            return false;
        }

        try {
            // 获取对应的字段处理策略
            DataFieldStrategy strategy = dataFieldStrategyFactory.getStrategy(fieldType);

            if (strategy == null) {
                log.warn("未找到字段类型[{}]对应的处理策略", fieldType);
                return false;
            }

            // 校验所有非空样本值
            for (String value : sampleValues) {
                if (StringUtils.isNotBlank(value)) {
                    try {
                        strategy.validateValue(value);
                    } catch (Exception e) {
                        log.warn("字段[{}]值[{}]不符合类型[{}]的校验规则: {}",
                                fieldName, value, fieldType, e.getMessage());
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("字段类型校验异常, fieldName:{}, fieldType:{}", fieldName, fieldType, e);
            return false;
        }
    }

    /**
     * 导出数据到Excel文件
     */
    private void exportToExcel(DatasetEsQueryCondition condition, DatasetHeadConfig headConfig, File file) throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("数据");

            // 1. 创建表头
            List<DataContentFieldDTO> headList = headConfig.getHeadList();
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headList.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headList.get(i).getColumnName());
            }

            // 2. 填充数据
            int rowIndex = 1;
            boolean hasMore = true;

            while (hasMore) {
                DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);
                List<DatasetEsIndex> records = pageResult.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                   break;
                }
                for (DatasetEsIndex esIndex : records) {
                    Row dataRow = sheet.createRow(rowIndex++);
                    Map<String, Object> rowData = convertEsIndexToRowData(esIndex, headConfig);

                    for (int i = 0; i < headList.size(); i++) {
                        Cell cell = dataRow.createCell(i);
                        String columnName = headList.get(i).getColumnName();
                        Object value = rowData.get(columnName);
                        setCellValue(cell, value);
                    }
                }

                hasMore = pageResult.getHasNext() != null && pageResult.getHasNext();
                if(hasMore){
                    condition.setScrollId(pageResult.getScrollId());
                }
            }

            // 3. 写入文件
            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                workbook.write(outputStream);
            }
        }
    }

    /**
     * 导出数据到JSONL文件
     */
    private void exportToJsonl(DatasetEsQueryCondition condition, DatasetHeadConfig headConfig, File file) throws Exception {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            boolean hasMore = true;

            while (hasMore) {
                DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);
                List<DatasetEsIndex> records = pageResult.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                    break;
                }
                for (DatasetEsIndex esIndex : records) {
                    Map<String, Object> rowData = convertEsIndexToRowData(esIndex, headConfig);
                    writer.write(JSON.toJSONString(rowData));
                    writer.newLine();
                }
                hasMore = pageResult.getHasNext() != null && pageResult.getHasNext();
                if(hasMore){
                    condition.setScrollId(pageResult.getScrollId());
                }
            }
        }
    }

    /**
     * 将ES索引数据转换为行数据
     */
    private Map<String, Object> convertEsIndexToRowData(DatasetEsIndex esIndex, DatasetHeadConfig headConfig) {
        Map<String, Object> rowData = new HashMap<>();
        List<DataContentFieldDTO> headList = headConfig.getHeadList();

        Map<String, String> columnNameMap = headList.stream()
                .collect(Collectors.toMap(
                    field -> field.getEsKey() + "." + field.getEsColumnName(),
                    DataContentFieldDTO::getColumnName,
                    (v1, v2) -> v1 // 处理可能的重复键
                ));

        // 处理文本数据
        if (esIndex.getTextData() != null) {
            esIndex.getTextData().forEach((key, value) -> {
                if (value != null) {
                    String columnName = columnNameMap.getOrDefault(TEXT_PREFIX + "." + key, key);
                    rowData.put(columnName, value);
                }
            });
        }

        // 处理扁平化数据
        if (esIndex.getFlattenedData() != null) {
            esIndex.getFlattenedData().forEach((key, value) -> {
                if (value != null) {
                    String columnName = columnNameMap.getOrDefault(FLATTEN_PREFIX + "." + key, key);
                    rowData.put(columnName, value);
                }
            });
        }

        // 处理通用数据
        if (esIndex.getCommonData() != null) {
            esIndex.getCommonData().forEach((key, value) -> {
                if (value != null) {
                    rowData.put(key, value);
                }
            });
        }

        return rowData;
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 将值转换为字符串，保持JSON格式不被破坏
     *
     * @param value 原始值
     * @return 转换后的字符串
     */
    private String convertValueToString(Object value) {
        if (value == null) {
            return null;
        }

        // 如果已经是字符串，直接返回
        if (value instanceof String) {
            return (String) value;
        }

        // 如果是复杂对象（Map、List等），使用JSON序列化保持格式
        if (value instanceof Map || value instanceof List || value instanceof Object[]) {
            try {
                return JSON.toJSONString(value);
            } catch (Exception e) {
                log.warn("将复杂对象转换为JSON字符串失败，使用toString方法: {}", e.getMessage());
                return value.toString();
            }
        }

        // 其他类型直接转换为字符串
        return value.toString();
    }
}
