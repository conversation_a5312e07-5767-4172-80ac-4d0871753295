package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据集字段路径信息
 *
 * <AUTHOR>
 * @date 2025/6/4
 */
@Data
public class DatasetFieldPathInfo {
    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 完整路径
     */
    private String fullPath;

    /**
     * 扁平化路径
     */
    private String flattenPath;

    /**
     * 文本路径
     */
    private String textPath;

    /**
     * 公共数据关键字路径
     */
    private String commonDataKeywordPath;

    /**
     * 字段映射信息
     */
    private FieldMappingDTO fieldMappingDTO;

    /**
     * 一级JSON-Path
     */
    private List<String> firstLevelJsonPath = new ArrayList<>();

    /**
     * 是否为公共字段
     */
    private Boolean isCommonField;
}
