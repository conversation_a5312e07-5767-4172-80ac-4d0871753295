package com.meituan.aigc.aida.labeling.controller;

import com.meituan.aigc.aida.labeling.param.*;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingDetailDataDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingSubTaskListDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.quality.check.TaskConditionDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelDetailNumCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingDetailBySessionVO;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.meituan.aigc.aida.labeling.service.LabelingSubTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/api/v1/labeling-sub-tasks")
@Slf4j
public class LabelingSubTaskController {

    @Resource
    private LabelingSubTaskService labelingSubTaskService;

    /**
     * 转交标注任务
     *
     * @param param 转交参数
     * @return 是否成功
     */
    @PostMapping("/transfer")
    public Result<?> transfer(@RequestBody LabelingSubTaskTransferParam param) {
        labelingSubTaskService.transfer(param);
        return Result.create();
    }

    /**
     * 分页查询标注详情数据
     *
     * @param param 查询条件
     * @return 标注详情数据
     */
    @PostMapping("/pageDetailData")
    public Result<LabelingDetailDataDTO> pageDetailData(@RequestBody LabelingDetailParam param) {
        LabelingDetailDataDTO labelingDetailDataDTO = labelingSubTaskService.pageDetailData(param);
        return Result.ok(labelingDetailDataDTO);
    }

    /**
     * 保存标注结果
     *
     * @param param 入参
     * @return 结果
     */
    @PostMapping("/detail/save")
    public Result<?> saveLabelingResult(@RequestBody LabelingResultSaveParam param) {
        labelingSubTaskService.saveLabelingResult(param);
        return Result.create();
    }

    /**
     * 查询子任务列表
     *
     * @param param 查询参数
     * @return 子任务列表
     */
    @PostMapping("/sub-tasks")
    public Result<PageData<LabelingSubTaskListDTO>> subTasks(@RequestBody LabelingListParam param) {
        PageData<LabelingSubTaskListDTO> result = labelingSubTaskService.subTasks(param);
        return Result.ok(result);
    }

    @PostMapping("/detail/pageBySession")
    public Result<PageData<LabelingDetailBySessionVO>> pageBySession(
            @RequestBody @Valid LabelingDetailBySessionQueryParam param) {
        PageData<LabelingDetailBySessionVO> result = labelingSubTaskService.pageBySession(param);
        return Result.ok(result);
    }

    @GetMapping("/detail/count")
    public Result<LabelDetailNumCountVO> countBySubTaskId(@RequestParam @Valid Long subTaskId) {
        LabelDetailNumCountVO result = labelingSubTaskService.countNumBySubTask(subTaskId);
        return Result.ok(result);
    }

    /**
     * 获取标注子任务下可筛选的条件
     *
     * @param labelingSubTaskId 标注子任务ID
     * @return 可筛选条件
     */
    @GetMapping("/condition/list")
    public Result<List<TaskConditionDTO>> getQualityCheckCondition(@RequestParam Long labelingSubTaskId) {
        return Result.ok(labelingSubTaskService.getLabelingCondition(labelingSubTaskId));
    }

    /**
     * 保存会话的上下文信息
     *
     * @param param 上下文参数
     * @return 是否成功
     */
    @PostMapping("/save/context")
    public Result<?> saveContext(@RequestBody LabelingTaskContextParam param) {
        labelingSubTaskService.saveContext(param);
        return Result.create();
    }
}
