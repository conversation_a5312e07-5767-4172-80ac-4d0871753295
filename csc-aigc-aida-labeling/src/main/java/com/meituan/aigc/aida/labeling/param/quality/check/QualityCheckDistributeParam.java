package com.meituan.aigc.aida.labeling.param.quality.check;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QualityCheckDistributeParam implements Serializable {
    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 抽样数量
     */
    private Integer dataCount;

    /**
     * 质检人
     */
    private List<QualityCheckMisParam> labelers;

    /**
     * 分组配置
     */
    private List<GroupConfig> groupConfigList;

    @Data
    public static class GroupConfig implements Serializable {
        /**
         * 分组ID
         */
        private Long groupId;
        /**
         * 分组比例
         */
        private Integer ratio;
        /**
         * 分组数量
         */
        private Integer count;
    }
}
