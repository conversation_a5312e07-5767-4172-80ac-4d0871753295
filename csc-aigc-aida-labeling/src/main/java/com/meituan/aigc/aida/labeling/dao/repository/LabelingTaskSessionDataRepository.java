package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskSessionData;

import java.util.List;

public interface LabelingTaskSessionDataRepository {

    /**
     * 根据任务ID和会话ID查询会话数据
     *
     * @param taskId        任务ID
     * @param sessionIdList 会话ID列表
     * @return 会话数据列表
     */
    List<LabelingTaskSessionData> listByTaskIdAndSessionIdList(Long taskId, List<String> sessionIdList);

    /**
     * 根据任务ID和会话ID查询会话数据
     *
     * @param taskId    任务ID
     * @param sessionId 会话ID
     * @return 会话数据列表
     */
    List<LabelingTaskSessionData> listByTaskIdAndSessionIdListWithBlobs(Long taskId, List<String> sessionId);

    /**
     * 插入会话数据
     *
     * @param labelingTaskSessionData 会话数据实体
     */
    void insert(LabelingTaskSessionData labelingTaskSessionData);

    /**
     * 根据ID更新会话数据
     *
     * @param labelingTaskSessionData 会话数据实体
     */
    void updateById(LabelingTaskSessionData labelingTaskSessionData);
}
