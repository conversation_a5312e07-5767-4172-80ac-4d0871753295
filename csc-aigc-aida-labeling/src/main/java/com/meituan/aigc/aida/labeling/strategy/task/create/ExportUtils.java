package com.meituan.aigc.aida.labeling.strategy.task.create;

import com.meituan.aigc.aida.labeling.exception.AidaTrainingServiceException;
import com.meituan.aigc.aida.labeling.service.UploadPlatformService;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.sankuai.meituan.auth.util.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/3/4 16:09
 * @Version: 1.0
 */
@Slf4j
@Component
public class ExportUtils {


    @Resource
    private UploadPlatformService uploadPlatformService;

    @Resource
    private PushElephantService pushElephantService;

    /**
     * @param workbook
     * @param taskName
     */
    public void uploadS3AndSendDxMessage(SXSSFWorkbook workbook, String taskName, String message, String mis) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            workbook.write(byteArrayOutputStream);
            byteArrayOutputStream.flush();
            String fileName = taskName + "_" + System.currentTimeMillis() + ".xlsx";
            byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            String url = uploadPlatformService.uploadS3AndEncryption(byteArrayInputStream, fileName, fileName, "xlsx", mis);

            log.info("任务导出url生成成功,url={},mis={}", url, mis);

            // 清理临时文件
            workbook.dispose();
            byteArrayOutputStream.close();
            String defaultMessage = "任务「%s」导出成功[请点击|%s]下载。";
            pushElephantService.pushElephant(String.format(StringUtils.isBlank(message) ? defaultMessage : message, taskName, url), Collections.singletonList(mis));
        } catch (Exception e) {
            log.error("导出标注数据异常，taskName: {}, 异常信息:", taskName, e);
            throw new AidaTrainingServiceException(e);
        } finally {
            try {
                if (Objects.nonNull(byteArrayInputStream)) {
                    byteArrayInputStream.close();
                }
                byteArrayOutputStream.close();
            } catch (IOException e) {
                log.error("导出标注数据关流异常，taskName: {}, 异常信息:", taskName, e);
            }
        }
    }

}
