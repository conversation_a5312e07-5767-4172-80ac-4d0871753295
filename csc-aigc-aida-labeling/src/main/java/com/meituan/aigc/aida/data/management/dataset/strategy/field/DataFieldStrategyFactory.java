package com.meituan.aigc.aida.data.management.dataset.strategy.field;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据字段处理策略工厂
 */
@Slf4j
@Component
public class DataFieldStrategyFactory {

    private static final Integer DEFAULT_STRATEGY_TYPE = -1;

    @Resource
    private List<DataFieldStrategy> dataFieldStrategyList;

    private Map<Integer, DataFieldStrategy> strategyMap;

    @PostConstruct
    public void init() {
        if (MapUtils.isEmpty(strategyMap)) {
            strategyMap = dataFieldStrategyList.stream()
                    .collect(Collectors.toMap(DataFieldStrategy::getStrategyType, Function.identity()));
            log.info("数据字段处理策略初始化完成，共加载 {} 个策略", strategyMap.size());
        }
    }

    /**
     * 根据字段类型获取对应的策略
     *
     * @param fieldType 字段类型
     * @return 对应的策略实现
     */
    public DataFieldStrategy getStrategy(Integer fieldType) {
        if (fieldType == null) {
            fieldType = DEFAULT_STRATEGY_TYPE;
        }
        DataFieldStrategy dataFieldStrategy = strategyMap.get(fieldType);
        if (dataFieldStrategy == null) {
            dataFieldStrategy = strategyMap.get(DEFAULT_STRATEGY_TYPE);
        }
        return dataFieldStrategy;
    }
} 