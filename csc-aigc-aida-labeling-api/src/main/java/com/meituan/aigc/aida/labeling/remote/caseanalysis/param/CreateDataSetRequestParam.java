package com.meituan.aigc.aida.labeling.remote.caseanalysis.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-26 10:26
 * @description 创建数据集请求
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateDataSetRequestParam implements Serializable {
    /**
     * 数据集ID 首次为空
     */
    private Long dataSetId;
    /**
     * 数据传输锁，首次为空，获取后需要传入
     */
    private String lockId;
    /**
     * 创建人mis
     */
    private String creatorMis;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 数据集名称
     */
    private String dataSetName;
    /**
     * 数据集描述
     */
    private String description;
    /**
     * 数据用途，传2 1-模型训练 2-会话分析 3-自定义
     *
     * @see com.meituan.aigc.aida.labeling.remote.common.enums.UsageCategoryEnum
     */
    private Integer usageCategory;
    /**
     * 数据来源，传3 (1:数据获取导入 2:文件上传 3:case分析)
     *
     * @see com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum
     */
    private Integer dataSource;
    /**
     * 训练类型(1:sft/2:dpo/3:session维度)
     * @see com.meituan.aigc.aida.labeling.remote.common.enums.TrainingTypeEnum
     */
    private Integer trainingType;
    /**
     * 是否最后一批，为true会释放锁，后续再无法使用
     */
    private Boolean isEnd;

    /**
     * 表头列表
     */
    private List<DataFieldParam> headList;

    /**
     * 数据列表
     */
    private List<DataSetRecordParam> dataList;
}
