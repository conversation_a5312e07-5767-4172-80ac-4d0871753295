package com.meituan.aigc.aida.labeling.pojo.dto.group;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GroupConfigList implements Serializable {
    /**
     * 分组数据列表
     */
    private List<GroupData> groupDataList;

    @Data
    public static class GroupData implements Serializable {
        /**
         * 分组ID
         */
        private Long groupId;
        /**
         * 分组名称
         */
        private String groupName;
        /**
         * 分组总数量
         */
        private Integer totalCount;
        /**
         * 分组分配数量
         */
        private Integer assignCount;
        /**
         * 子任务配置列表
         */
        private List<SubTaskConfig> subTaskConfigList;
    }

    @Data
    public static class SubTaskConfig implements Serializable{
        /**
         * 子任务ID
         */
        private Long id;
        /**
         * 标注人mis
         */
        private String labelMis;
        /**
         * 标注人姓名
         */
        private String labelName;
    }
}
