package com.meituan.aigc.aida.labeling.strategy.task.create.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingDetailRepository;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingQualityCheckItemRepository;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingOperationException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO;
import com.meituan.aigc.aida.labeling.pojo.vo.QualityCheckModifiedDataCountVO;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategy;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategyFactory;
import com.meituan.aigc.aida.labeling.strategy.task.create.ExportUtils;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用导出任务创建策略
 *
 * <AUTHOR> yanxiao07
 * @date : 2025/3/11
 */
@Slf4j
@Component
public class CommonCreateTask {

    /**
     * 会话ID字段名，用于导入/导出数据时标识会话记录的唯一标识符
     */
    protected static final String SESSION_ID = CommonConstants.FieldName.SESSION_ID;

    /**
     * 大模型消息ID字段名，用于导入/导出数据时标识大模型消息的唯一标识符
     */
    protected static final String MESSAGE_ID = CommonConstants.FieldName.MESSAGE_ID;

    /**
     * 消息内容字段名，用于导入/导出数据时标识消息的具体内容
     */
    protected static final String MESSAGE_CONTENT = CommonConstants.FieldName.MESSAGE_CONTENT;

    /**
     * 上下文
     */
    protected static final String CONTEXT = CommonConstants.FieldName.CONTEXT;

    /**
     * 信号
     */
    protected static final String SIGNAL = CommonConstants.FieldName.SIGNAL;

    /**
     * 时间字段名，用于导入/导出数据时标识记录的时间戳信息
     */
    protected static final String TIME = CommonConstants.FieldName.TIME;

    /**
     * 会话字段
     */
    private static final List<String> SESSION_FIELD = Arrays.asList(SESSION_ID, TIME);

    /**
     * 质检结果
     */
    private static final String QUALITY_CHECK_RESULT = CommonConstants.FieldName.QUALITY_CHECK_RESULT;

    /**
     * 标注人MIS
     */
    private static final String LABELING_MIS = CommonConstants.FieldName.LABELING_MIS;

    /**
     * 标注人姓名
     */
    private static final String LABELING_NAME = CommonConstants.FieldName.LABELING_NAME;

    /**
     * 标注时间
     */
    private static final String LABELING_TIME = CommonConstants.FieldName.LABELING_TIME;

    /**
     * 质检人MIS
     */
    private static final String QUALITY_CHECK_MIS = CommonConstants.FieldName.QUALITY_CHECK_MIS;

    /**
     * 质检人姓名
     */
    private static final String QUALITY_CHECK_NAME = CommonConstants.FieldName.QUALITY_CHECK_NAME;

    /**
     * 质检时间
     */
    private static final String QUALITY_CHECK_TIME = CommonConstants.FieldName.QUALITY_CHECK_TIME;

    @Resource
    protected LabelingDetailRepository labelingDetailRepository;

    @Resource
    protected ExportUtils exportUtils;

    @Resource
    protected PushElephantService pushElephantService;

    @Resource
    protected CreateTaskStrategyFactory createTaskStrategyFactory;

    @Resource
    protected LionConfig lionConfig;

    @Resource
    private LabelingQualityCheckItemRepository labelingQualityCheckItemRepository;

    protected Integer getTaskType() {
        throw new AidaTrainingOperationException("当前数据类型未实现");
    }

    /**
     * 校验表头
     *
     * @param file         上传文件
     * @param customFields 自定义字段
     */
    protected void checkHeader(MultipartFile file, List<String> customFields) {
        List<String> mustFields = new ArrayList<>(SESSION_FIELD);
        if (CollectionUtils.isNotEmpty(customFields)) {
            mustFields.addAll(customFields);
        }
        try {
            // 解析表头
            Set<String> headerNameSet = createTaskStrategyFactory.getFileHeader(file, mustFields.size());
            // 表头处理
            headerProcessing(mustFields);
            // 统计缺少的表头
            List<String> missingHeaders = new ArrayList<>();
            mustFields.forEach(field -> {
                if (!headerNameSet.contains(field)) {
                    missingHeaders.add(field);
                }
            });
            // 校验是否缺少表头
            CheckUtil.paramCheck(CollectionUtils.isEmpty(missingHeaders),
                    String.format("上传文件中缺少【%s】字段", String.join(",", missingHeaders)));
        } catch (Exception e) {
            log.error("表头解析失败,失败原因：", e);
            throw new AidaTrainingCheckException("表头解析失败,请检查文件格式是否和模版一致");
        }
    }

    /**
     * 处理数据
     *
     * @param rowDataList  行数据列表
     * @param headerList   表头列表
     * @param targetFields 目标字段
     * @return 处理后的数据列表
     */
    protected List<LabelingTaskRawData> processData(List<List<String>> rowDataList, List<String> headerList,
                                                    List<String> targetFields) {
        Set<String> duplicates = new HashSet<>();
        List<LabelingTaskRawData> rawDataList = new ArrayList<>();
        for (int i = 0; i < rowDataList.size(); i++) {
            List<String> rawData = rowDataList.get(i);
            LabelingTaskRawData labelingTaskRawData = new LabelingTaskRawData();
            // 初始化一个映射，用于存储原始数据映射的内容
            Map<String, String> rawDataMappedContent = new LinkedHashMap<>();
            // 初始化一个映射，用于存储原始数据的内容
            Map<String, String> rawDataContent = new LinkedHashMap<>();
            // 初始化一个列表，用于存储原始数据内容的表头，保证表头的顺序，用做后续展示原始数据内容的顺序
            List<String> rawDataHeader = new ArrayList<>();
            // 初始化一个布尔值变量skipRow，用于判断是否跳过当前行
            boolean skipRow = false;
            for (String head : headerList) {
                switch (head) {
                    // TODO: 2025/4/16 当前先不支持使用messageId，依赖精确匹配后再放开这个功能
//                    case MESSAGE_ID:
//                        String messageId = rawData.get(headerList.indexOf(head));
//                        if (getTaskType() == LabelTaskDataType.QUERY_ANNOTATION.getCode()) {
//                            CheckUtil.paramCheck(StringUtils.isNotBlank(messageId),
//                                    String.format("数据中第【%s】行messageId为空", i + 3));
//                        }
//                        labelingTaskRawData.setMessageId(messageId);
//                        break;
                    case SESSION_ID:
                        String sessionId = rawData.get(headerList.indexOf(head));
                        //加上俩行数和java中从0开始计数，实际行数应该加3
                        CheckUtil.paramCheck(StringUtils.isNotBlank(sessionId),
                                String.format("数据中第【%s】行sessionId为空", i + 3));
                        CheckUtil.paramCheck(sessionId.length() < 255,
                                String.format("数据中第【%s】行sessionId超长", i + 3));
                        // 调用钩子方法检查sessionId是否重复
                        if (checkDuplicateSessionId(sessionId, duplicates, i)) {
                            // 如果sessionId重复，则skipRow = true 跳过当前行
                            skipRow = true;
                            break;
                        }
                        labelingTaskRawData.setSessionId(sessionId);
                        break;
                    case TIME:
                        String sessionTime = rawData.get(headerList.indexOf(head));
                        if (StringUtils.isNotBlank(sessionTime)) {
                            if (sessionTime.matches(CommonConstants.DatePattern.DATE_TIME_PATTERN)) {
                                labelingTaskRawData.setSessionTime(
                                        DateUtil.parseToDate(sessionTime, DateUtil.DATE_PATTERN_YYYY_MM_DD));
                            } else if (sessionTime.matches(CommonConstants.DatePattern.DATE_TIME_PATTERN2)) {
                                labelingTaskRawData.setSessionTime(
                                        DateUtil.parseToDate(sessionTime, CommonConstants.DatePattern.DATE_TIME2));
                            } else {
                                log.warn("创建任务导入日期格式不标准，不作处理,原数据:{}", JSON.toJSONString(rawData));
                            }
                        }
                        break;
                    default:
                        // 如果targetFields不为空且包含head，则将rawData中的对应列的值放入rawDataMappedContent（原始数据映射内容）中。
                        if (CollectionUtils.isNotEmpty(targetFields) && targetFields.contains(head)) {
                            rawDataMappedContent.put(head, rawData.get(headerList.indexOf(head)));
                        } else {
                            rawDataHeader.add(head);
                            rawDataContent.put(head, rawData.get(headerList.indexOf(head)));
                        }
                        break;
                }
                // 如果skipRow为true，则跳出循环，结束当前行的处理。
                if (skipRow) {
                    break;
                }
            }
            // 如果skipRow为true，则跳过当前行的处理，继续处理下一行。
            if (skipRow) {
                continue;
            }
            if (MapUtils.isNotEmpty(rawDataContent)) {
                labelingTaskRawData.setRawDataContent(JSON.toJSONString(rawDataContent));
            }
            if (MapUtils.isNotEmpty(rawDataMappedContent)) {
                labelingTaskRawData.setRawDataMappedContent(JSON.toJSONString(rawDataMappedContent));
            }
            labelingTaskRawData.setRawDataHeaders(JSON.toJSONString(rawDataHeader));
            rawDataList.add(labelingTaskRawData);
        }
        return rawDataList;
    }

    /**
     * 检查SessionId是否重复的钩子方法，子类可以覆盖此方法实现特定的重复性检查逻辑
     *
     * @param sessionId  当前行的sessionId
     * @param duplicates 已存在的sessionId集合
     * @param rowIndex   当前行索引
     * @return 如果sessionId重复并且需要跳过当前行处理则返回true，否则返回false
     */
    protected boolean checkDuplicateSessionId(String sessionId, Set<String> duplicates, int rowIndex) {
        // 默认实现：不检查重复性
        return false;
    }

    /**
     * 表头处理，子类可以覆盖此方法实现表头特殊处理逻辑
     *
     * @param mustFields 当前行的sessionId
     */
    protected void headerProcessing(List<String> mustFields) {
        // 默认实现：不处理表头字段
    }

    /**
     * 通用的导出方法，子类可以直接调用或覆盖
     *
     * @param taskName          任务名称
     * @param subTaskIds        子任务ID集合
     * @param labelDetailStatus 标注详情状态
     * @param logFlag           日志标识
     * @param sheetName         表格名称
     */
    protected void commonExport(String taskName, List<Long> subTaskIds, Integer labelDetailStatus,
                                String logFlag, String sheetName, String mis) {
        log.info("开始导出{}任务数据，taskName: {}", logFlag, taskName);
        // 创建工作簿，使用SXSSF模式处理大数据量
        try (SXSSFWorkbook workbook = new SXSSFWorkbook(500)) {
            Sheet sheet = workbook.createSheet(sheetName);

            // 准备导出所需的字段数据
            ExportFieldData fieldData = prepareExportFields(subTaskIds, labelDetailStatus, logFlag, taskName);
            if (fieldData == null) {
                return;
            }

            // 创建表头样式
            CellStyle headerStyle = CreateTaskStrategy.createHeaderStyle(workbook);

            // 调用子类实现创建表头和填充数据
            int[] result = createHeaderAndFillData(workbook, sheet, headerStyle, fieldData, subTaskIds, labelDetailStatus, taskName);

            // 设置冻结窗格，冻结前两行
            sheet.createFreezePane(0, 2);

            // 上传文件到S3并发送大象消息
            exportUtils.uploadS3AndSendDxMessage(workbook, taskName, "", mis);
            log.info("标注任务数据导出完成，taskName: {}, 总数据量: {}", taskName, result[1]);
        } catch (Exception e) {
            log.error("导出{}任务数据发生异常，taskName: {}, 异常信息:", logFlag, taskName, e);
            pushElephantService.pushElephant(String.format("任务「%s」导出失败，请前往[训练系统|%s]重新导出。", taskName, lionConfig.getCreateDataSetNotifyElephantUrl()),
                    Collections.singletonList(UserUtils.getUser().getLogin()));
        }
    }

    /**
     * 准备导出所需的字段数据
     *
     * @param subTaskIds        子任务ID集合
     * @param labelDetailStatus 标注详情状态
     * @param logFlag           日志标识
     * @param taskName          任务名称
     * @return 导出字段数据对象，如果准备失败则返回null
     */
    private ExportFieldData prepareExportFields(List<Long> subTaskIds, Integer labelDetailStatus,
                                                String logFlag, String taskName) {

        // 先查询一条数据，用于提取字段
        List<LabelTaskDataExportVO> sampleData = labelingDetailRepository
                .pageExportDataBySubTaskIdAndStatus(subTaskIds, labelDetailStatus, 1, 1);

        if (CollectionUtils.isEmpty(sampleData)) {
            log.error("导出{}任务数据，taskName: {}, 标注结果数据为空!", logFlag, taskName);
            return null;
        }

        // 设置动态表头
        LabelTaskDataExportVO sample = sampleData.get(0);
        return extractFieldsFromSample(sample);
    }

    /**
     * 从样本数据中提取字段信息
     *
     * @param sample             样本数据
     * @param rawDataFields      原始数据字段集合
     * @param labelingFields     标注结果字段集合
     * @param qualityCheckFields 质检结果字段集合
     */
    private ExportFieldData extractFieldsFromSample(LabelTaskDataExportVO sample) {

        // 获取标注和质检结果的所有字段
        List<String> rawDataFields = new ArrayList<>();
        List<InspectionItemData> labelingFields = new ArrayList<>();
        List<InspectionItemData> qualityCheckFields = new ArrayList<>();
        // 质检员修改后标注内容
        List<InspectionItemData> qualityCheckModifiedLabelingItems = new ArrayList<>();
        // 质检员修改后原始数据内容映射内容
        List<String> qualityCheckModifiedRawDataMappedContent = new ArrayList<>();

        // 原数据
        String rawDataContent = sample.getRawDataContent();
        if (StringUtils.isNotBlank(rawDataContent)) {
            if (StringUtils.isNotBlank(sample.getRawDataHeaders())) {
                LinkedHashMap<String, Object> orderedMap = getMapByHeaders(sample.getRawDataHeaders(), rawDataContent);
                // 将排序后的map存入
                rawDataFields.addAll(orderedMap.keySet());
            } else {
                JSONObject rawDataJson = JSON.parseObject(rawDataContent);
                rawDataFields.addAll(rawDataJson.keySet());
            }
        }
        // 原始数据内容映射内容
        String rawDataMappedContent = sample.getRawDataMappedContent();
        QualityCheckModifiedDataCountVO qualityCheckModifiedDataCount = labelingQualityCheckItemRepository.countQualityCheckModifiedDataByTaskId(sample.getSubTaskId());
        if (qualityCheckModifiedDataCount.getModifiedRawDataMappedContentNum() > 0 && StringUtils.isNotBlank(rawDataMappedContent)) {
            qualityCheckModifiedRawDataMappedContent.addAll(JSON.parseObject(rawDataMappedContent).keySet());
        }

        /*
         * 标注结果
         * 标注结果样式如下 [[{"id": 3, "name": "是否正确", "value": "否", "dataType": 2, "enumList":
         * ["是", "否"], "itemType": 1, "isCompare": 1}], [{"id": 4, "name": "备注",
         * "value": "2", "dataType": 1, "itemType": 1, "isCompare": 2}]]
         * 取name作为表头，value作为值
         */
        if (StringUtils.isNotBlank(sample.getLabelingItemsResult())) {
            labelingFields.addAll(parseLabelingItemsResult(sample.getLabelingItemsResult(), sample));
            labelingFields.add(new InspectionItemData(LABELING_MIS));
            labelingFields.add(new InspectionItemData(LABELING_NAME));
            labelingFields.add(new InspectionItemData(LABELING_TIME));
        }

        // 质检结果
        qualityCheckFields.add(new InspectionItemData(QUALITY_CHECK_RESULT));
        if (StringUtils.isNotBlank(sample.getQualityCheckItemsResult())) {
            qualityCheckFields.addAll(parseLabelingItemsResult(sample.getQualityCheckItemsResult(), sample));
            qualityCheckFields.add(new InspectionItemData(QUALITY_CHECK_MIS));
            qualityCheckFields.add(new InspectionItemData(QUALITY_CHECK_NAME));
            qualityCheckFields.add(new InspectionItemData(QUALITY_CHECK_TIME));
        }

        // 质检员修改后标注内容
        if (StringUtils.isNotBlank(sample.getLabelingItemsResult()) && qualityCheckModifiedDataCount.getModifiedLabelingItemsNum() > 0) {
            qualityCheckModifiedLabelingItems.addAll(parseLabelingItemsResult(sample.getLabelingItemsResult(), sample));
        }
        return new ExportFieldData(sample, rawDataFields, labelingFields, qualityCheckFields, qualityCheckModifiedRawDataMappedContent, qualityCheckModifiedLabelingItems);
    }

    /**
     * 根据headers中的顺序去匹配rawDataContent中的key
     *
     * @param rawDataHeaders
     * @param rawDataContent
     * @return
     */
    private static LinkedHashMap<String, Object> getMapByHeaders(String rawDataHeaders, String rawDataContent) {
        List<String> headers = JSON.parseArray(rawDataHeaders, String.class);
        //根据headers中的顺序去匹配rawDataContent中的key
        JSONObject rawDataJson = JSON.parseObject(rawDataContent);
        LinkedHashMap<String, Object> orderedMap = new LinkedHashMap<>();
        // 1. 首先按照headers中的顺序添加键值对
        for (String key : headers) {
            if (rawDataJson.containsKey(key)) {
                orderedMap.put(key, rawDataJson.get(key));
            }
        }
        // 2. 添加在rawDataContent中存在但不在headers中的键值对
        List<String> collect = rawDataJson.keySet().stream().filter(key -> !headers.contains(key)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            for (String key : collect) {
                orderedMap.put(key, rawDataJson.get(key));
            }
        }
        return orderedMap;
    }

    /**
     * 解析标注结果JSON字符串，提取name作为表头，value作为值
     *
     * @param labelTaskDataExport 标注详情
     * @return 解析后的name列表
     */
    protected List<InspectionItemData> parseLabelingItemsResult(String labelingItemsResult,
                                                                LabelTaskDataExportVO labelTaskDataExport) {
        List<InspectionItemData> nameSet = new ArrayList<>();
        if (StringUtils.isBlank(labelingItemsResult)) {
            return nameSet;
        }

        try {
            // 解析外层数组
            List<List<LabelResult>> labelingItems = JSON.parseObject(labelingItemsResult,
                    new TypeReference<List<List<LabelResult>>>() {
                    });
            if (CollectionUtils.isEmpty(labelingItems)) {
                return nameSet;
            }
            // 遍历每个分组
            for (List<LabelResult> labelResults : labelingItems) {
                if (CollectionUtils.isEmpty(labelResults)) {
                    continue;
                }
                // 遍历分组中的每个标注项
                for (LabelResult labelResult : labelResults) {
                    if (Objects.isNull(labelResult)) {
                        continue;
                    }
                    // 提取name
                    nameSet.add(new InspectionItemData(labelResult.getName(), labelResult.getId()));
                }
            }
        } catch (Exception e) {
            log.error("标注详情{}解析标注结果数据异常: {}", JSON.toJSONString(labelTaskDataExport), labelingItemsResult, e);

        }
        return nameSet;
    }

    /**
     * 导出字段数据类，用于封装导出过程中的字段信息
     */
    @Getter
    protected static class ExportFieldData {
        private final LabelTaskDataExportVO sample;
        private final List<String> rawDataFields;
        private final List<InspectionItemData> labelingFields;
        private final List<InspectionItemData> qualityCheckFields;
        private final List<String> qualityCheckModifiedRawDataMappedContent;
        private final List<InspectionItemData> qualityCheckModifiedLabelingItems;


        public ExportFieldData(LabelTaskDataExportVO sample, List<String> rawDataFields,
                               List<InspectionItemData> labelingFields, List<InspectionItemData> qualityCheckFields,
                               List<String> qualityCheckModifiedRawDataMappedContent, List<InspectionItemData> qualityCheckModifiedLabelingItems) {
            this.sample = sample;
            this.rawDataFields = rawDataFields;
            this.labelingFields = labelingFields;
            this.qualityCheckFields = qualityCheckFields;
            this.qualityCheckModifiedRawDataMappedContent = qualityCheckModifiedRawDataMappedContent;
            this.qualityCheckModifiedLabelingItems = qualityCheckModifiedLabelingItems;
        }

    }

    @Data
    public static class InspectionItemData {
        /**
         * 标注项名称
         */
        private String name;
        /**
         * 标注项ID
         */
        private Long id;

        public InspectionItemData() {

        }

        public InspectionItemData(String name) {
            this.name = name;
        }

        public InspectionItemData(String name, Long id) {
            this.name = name;
            this.id = id;
        }
    }

    /**
     * 创建表头和填充数据的钩子方法，子类必须实现
     *
     * @param workbook          工作簿
     * @param sheet             表格
     * @param headerStyle       表头样式
     * @param subTaskIds        子任务ID集合
     * @param labelDetailStatus 标注详情状态
     * @param taskName          任务名称
     * @return int数组，[0]为列数，[1]为数据总量
     */
    protected int[] createHeaderAndFillData(SXSSFWorkbook workbook, Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData,
                                            List<Long> subTaskIds, Integer labelDetailStatus, String taskName) {
        throw new UnsupportedOperationException("子类必须实现此方法");
    }

    /**
     * 创建并合并标题单元格
     *
     * @param sheet       工作表
     * @param headerRow   标题行
     * @param headerStyle 单元格样式
     * @param colIndex    当前列索引
     * @param title       标题文本
     * @param colSpan     需要合并的列数
     * @return 更新后的列索引
     */
    protected int createMergedHeaderCell(Sheet sheet, Row headerRow, CellStyle headerStyle,
                                         int colIndex, String title, int colSpan) {
        // 创建标题单元格并设置内容和样式
        Cell cell = headerRow.createCell(colIndex);
        cell.setCellValue(title);
        cell.setCellStyle(headerStyle);

        // 记录起始列索引
        int startCol = colIndex;
        // 更新列索引，向右移动列跨度数量
        colIndex += colSpan;
        // 计算结束列索引
        int endCol = colIndex - 1;

        // 只有当有多列需要合并时才执行合并操作
        if (startCol < endCol) {
            // 合并单元格，范围为：第0行的startCol到endCol列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, startCol, endCol));
        }

        // 返回更新后的列索引，用于后续列的创建
        return colIndex;
    }

    /**
     * 分批查询并写入数据的通用方法
     *
     * @param sheet             表格
     * @param startRowNum       开始行号
     * @param subTaskIds        子任务ID集合
     * @param labelDetailStatus 标注详情状态
     * @param logFlag           日志标识
     * @param taskName          任务名称
     * @param processor         数据处理器接口
     * @return 总数据量
     */
    protected int processDataInBatches(Sheet sheet, int startRowNum, List<Long> subTaskIds,
                                       Integer labelDetailStatus, String logFlag, String taskName,
                                       ExportDataProcessor processor) {
        int pageNum = 1;
        int pageSize = 500;
        int rowNum = startRowNum;
        int totalCount = 0;
        List<LabelTaskDataExportVO> dataList;

        do {
            dataList = labelingDetailRepository.pageExportDataBySubTaskIdAndStatus(
                    subTaskIds, labelDetailStatus, pageNum, pageSize);

            if (CollectionUtils.isEmpty(dataList)) {
                log.warn("导出{}任务数据，taskName: {}, 第{}次查询结果为空!", logFlag, taskName, pageNum);
                break;
            }

            log.info("导出{}任务数据，taskName: {}, 查询第{}页数据，获取{}条记录", logFlag, taskName, pageNum, dataList.size());

            for (LabelTaskDataExportVO data : dataList) {
                Row dataRow = sheet.createRow(rowNum++);
                processor.processRow(dataRow, data);
            }

            pageNum++;
            totalCount += dataList.size();
        } while (dataList.size() == pageSize);

        return totalCount;
    }

    /**
     * 导出数据处理器接口
     */
    protected interface ExportDataProcessor {
        /**
         * 处理单行数据
         *
         * @param row  当前行
         * @param data 数据对象
         */
        void processRow(Row row, LabelTaskDataExportVO data);
    }

    /**
     * 创建单元格并设置值和样式
     *
     * @param row       行对象
     * @param colIndex  列索引
     * @param value     单元格值
     * @param cellStyle 单元格样式
     * @return 更新后的列索引
     */
    protected int createCell(Row row, int colIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(colIndex++);
        cell.setCellValue(value);
        cell.setCellStyle(cellStyle);
        return colIndex;
    }

    /**
     * 写入标注结果和质检结果到Excel行
     *
     * @param row                当前行
     * @param data               数据对象
     * @param cellIndex          单元格起始索引
     * @param labelingFields     标注字段集合
     * @param qualityCheckFields 质检字段集合
     */
    protected static void writeLabelingAndQualityCheckResults(Row row, LabelTaskDataExportVO data, int cellIndex, List<InspectionItemData> labelingFields,
                                                              List<InspectionItemData> qualityCheckFields, List<InspectionItemData> qualityCheckModifiedLabelingItems) {

        int currentCellIndex = cellIndex;

        // 解析并写入标注结果
        List<List<LabelResult>> labelingItems = new ArrayList<>();
        if (StringUtils.isNotBlank(data.getLabelingItemsResult())) {
            labelingItems = JSON.parseObject(data.getLabelingItemsResult(),
                    new TypeReference<List<List<LabelResult>>>() {
                    });
        }

        for (InspectionItemData field : labelingFields) {
            Cell cell = row.createCell(currentCellIndex++);
            if (LABELING_MIS.equals(field.getName())) {
                CreateTaskStrategy.setCellValueByType(cell, data.getLabelMis());
                continue;
            }
            if (LABELING_NAME.equals(field.getName())) {
                CreateTaskStrategy.setCellValueByType(cell, data.getLabelName());
                continue;
            }
            if (LABELING_TIME.equals(field.getName())) {
                CreateTaskStrategy.setCellValueByType(cell, data.getLabelTime());
                continue;
            }
            String valueByName = getValueByName(labelingItems, field);
            if (StringUtils.isNotBlank(valueByName)) {
                CreateTaskStrategy.setCellValueByType(cell, valueByName);
            } else {
                CreateTaskStrategy.setCellValueByType(cell, "");
            }
        }

        // 解析并写入质检结果
        List<List<LabelResult>> checkItems = new ArrayList<>();
        if (StringUtils.isNotBlank(data.getQualityCheckItemsResult())) {
            checkItems = JSON.parseObject(data.getQualityCheckItemsResult(),
                    new TypeReference<List<List<LabelResult>>>() {
                    });
        }
        for (InspectionItemData field : qualityCheckFields) {
            Cell cell = row.createCell(currentCellIndex++);
            //单独处理质检结果
            if (QUALITY_CHECK_RESULT.equals(field.getName())) {
                if (Objects.nonNull(data.getQualityCheckResult())) {
                    CreateTaskStrategy.setCellValueByType(cell,
                            1 == data.getQualityCheckResult() ? "正确"
                                    : "错误");
                }
                continue;
            }
            if (QUALITY_CHECK_MIS.equals(field.getName())) {
                CreateTaskStrategy.setCellValueByType(cell, data.getQualityCheckMis());
                continue;
            }
            if (QUALITY_CHECK_NAME.equals(field.getName())) {
                CreateTaskStrategy.setCellValueByType(cell, data.getQualityCheckName());
                continue;
            }
            if (QUALITY_CHECK_TIME.equals(field.getName())) {
                // 质检人不为空时，写入质检时间，否则写入空字符串
                if (StringUtils.isNotBlank(data.getQualityCheckMis())) {
                    CreateTaskStrategy.setCellValueByType(cell, data.getQualityCheckTime());
                } else {
                    CreateTaskStrategy.setCellValueByType(cell, "");
                }
                continue;
            }
            String valueByName = getValueByName(checkItems, field);
            if (StringUtils.isNotBlank(valueByName)) {
                CreateTaskStrategy.setCellValueByType(cell, valueByName);
            } else {
                CreateTaskStrategy.setCellValueByType(cell, "");
            }
        }

        if (CollectionUtils.isNotEmpty(qualityCheckModifiedLabelingItems)) {
            // 解析并写入质检人修改后的标注结果
            List<List<LabelResult>> modifiedLabelingItems = new ArrayList<>();
            if (StringUtils.isNotBlank(data.getQualityCheckModifiedLabelingItems())) {
                modifiedLabelingItems = JSON.parseObject(data.getQualityCheckModifiedLabelingItems(),
                        new TypeReference<List<List<LabelResult>>>() {
                        });
            }

            for (InspectionItemData field : labelingFields) {
                Cell cell = row.createCell(currentCellIndex++);
                String valueByName = getValueByName(modifiedLabelingItems, field);
                if (StringUtils.isNotBlank(valueByName)) {
                    CreateTaskStrategy.setCellValueByType(cell, valueByName);
                } else {
                    CreateTaskStrategy.setCellValueByType(cell, "");
                }
            }
        }


    }

    protected static String getValueByName(List<List<LabelResult>> labelResultLists, InspectionItemData field) {
        for (List<LabelResult> labelResultList : labelResultLists) {
            for (LabelResult labelResult : labelResultList) {
                if (Objects.isNull(labelResult.getId()) || Objects.isNull(field.getId())) {
                    if (Objects.equals(field.getName(), labelResult.getName())) {
                        return labelResult.getValue();
                    }
                } else {
                    if (Objects.equals(field.getId(), labelResult.getId())) {
                        return labelResult.getValue();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 提取原始数据映射对象
     *
     * @param data 导出数据对象
     * @return JSON对象
     */
    protected static JSONObject extractRawDataMappedObject(LabelTaskDataExportVO data) {
        JSONObject rawDataMappedObject = null;
        if (StringUtils.isNotBlank(data.getModifiedRawDataMappedContent())) {
            rawDataMappedObject = JSON.parseObject(data.getModifiedRawDataMappedContent());
        } else if (StringUtils.isNotBlank(data.getRawDataMappedContent())) {
            rawDataMappedObject = JSON.parseObject(data.getRawDataMappedContent());
        } else if (StringUtils.isNotBlank(data.getRawDataContent())) {
            rawDataMappedObject = JSON.parseObject(data.getRawDataContent());
        }
        return Objects.isNull(rawDataMappedObject) ? new JSONObject() : rawDataMappedObject;
    }

    /**
     * 创建第二行表头（具体字段）- 用于没有原始数据字段和自定义字段的场景
     *
     * @param sheet       工作表
     * @param headerStyle 表头样式
     */
    protected void createSecondHeaderRow(Sheet sheet, CellStyle headerStyle,
                                         ExportFieldData fieldData) {
        List<InspectionItemData> labelingFields = fieldData.getLabelingFields();
        List<InspectionItemData> qualityCheckFields = fieldData.getQualityCheckFields();
        List<InspectionItemData> qualityCheckModifiedLabelingItems = fieldData.getQualityCheckModifiedLabelingItems();
        Row secondHeaderRow = sheet.createRow(1);
        int colIndex = 0;

        // 会话ID列
        colIndex = createCell(secondHeaderRow, colIndex, SESSION_ID, headerStyle);
        colIndex = createCell(secondHeaderRow, colIndex, TIME, headerStyle);


        // 标注结果字段
        for (InspectionItemData field : labelingFields) {
            colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
        }

        // 质检结果字段
        for (InspectionItemData field : qualityCheckFields) {
            colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
        }

        // 质检员修改后的标注结果
        if (CollectionUtils.isNotEmpty(qualityCheckModifiedLabelingItems)) {
            // 标注结果字段
            for (InspectionItemData field : qualityCheckModifiedLabelingItems) {
                colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
            }
        }
    }

    /**
     * 创建第二行表头（具体字段）- 带原始数据字段和自定义字段
     *
     * @param sheet        工作表
     * @param headerStyle  表头样式
     * @param customFields 自定义字段列表
     */
    protected void createSecondHeaderRow(Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData, List<String> customFields) {
        List<String> rawDataFields = fieldData.getRawDataFields();
        List<InspectionItemData> labelingFields = fieldData.getLabelingFields();
        List<InspectionItemData> qualityCheckFields = fieldData.getQualityCheckFields();
        List<InspectionItemData> qualityCheckModifiedLabelingItems = fieldData.getQualityCheckModifiedLabelingItems();
        List<String> qualityCheckModifiedRawDataMappedContent = fieldData.getQualityCheckModifiedRawDataMappedContent();
        Row secondHeaderRow = sheet.createRow(1);
        int colIndex = 0;

        // 会话分组字段
        colIndex = createCell(secondHeaderRow, colIndex, SESSION_ID, headerStyle);
        colIndex = createCell(secondHeaderRow, colIndex, TIME, headerStyle);

        // 自定义训练入参字段
        for (String field : customFields) {
            colIndex = createCell(secondHeaderRow, colIndex, field, headerStyle);
        }

        if (CollectionUtils.isNotEmpty(qualityCheckModifiedRawDataMappedContent)) {
            // 质检员修改后的自定义训练入参字段（这里使用customFields是为了保持顺序一致）
            for (String field : customFields) {
                colIndex = createCell(secondHeaderRow, colIndex, field, headerStyle);
            }
        }

        // 标注参考项字段
        List<String> rawDataFieldsList = new ArrayList<>(rawDataFields);
        for (String field : rawDataFieldsList) {
            colIndex = createCell(secondHeaderRow, colIndex, field, headerStyle);
        }

        // 标注结果字段
        for (InspectionItemData field : labelingFields) {
            colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
        }

        // 质检结果字段
        for (InspectionItemData field : qualityCheckFields) {
            colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
        }

        // 质检员修改后的标注结果字段
        if (CollectionUtils.isNotEmpty(qualityCheckModifiedLabelingItems)) {
            for (InspectionItemData field : qualityCheckModifiedLabelingItems) {
                colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
            }
        }

    }
}
