package com.meituan.aigc.aida.labeling.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-03 16:09
 * @description 标注子任务查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelingDetailParam implements Serializable {

    /**
     * 子任务id
     */
    private Long subTaskId;

    /**
     * 状态（可为空）
     * {@link com.meituan.aigc.aida.labeling.common.enums.LabelingDetailStatus}
     */
    private Integer status;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页记录数
     */
    private Integer pageSize;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 标注人mis
     */
    private String labelerMis;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 高级筛选
     */
    private List<AdvancedFilter> advancedFilters;

}
