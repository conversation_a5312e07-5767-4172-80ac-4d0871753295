package com.meituan.aigc.aida.data.management.dataset.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.zebra.util.StringUtils;
import com.meituan.aigc.aida.data.management.dataset.common.DataSetInvokeEs;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.meituan.aigc.aida.data.management.dataset.dto.DataContentFieldDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DataSetVersionDTO;
import com.meituan.aigc.aida.data.management.dataset.enums.*;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCopyVersionParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetVersionParam;
import com.meituan.aigc.aida.data.management.dataset.param.DatasetHeadConfig;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetManagementService;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetVersionService;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.data.management.es.query.BatchResult;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.data.management.es.query.DatasetPageResult;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-30 17:00
 * @description
 */
@Service
@Slf4j
public class DataSetVersionServiceImpl implements DataSetVersionService {

    @Resource
    private DataSetVersionRepository dataSetVersionRepository;

    @Resource
    private DataSetRepository dataSetRepository;

    @Autowired
    private DataSetManagementService dataSetManagementService;

    @Autowired
    private DatasetEsIndexService datasetEsIndexService;

    @Autowired
    private DataSetInvokeEs dataSetInvokeEs;

    private static final int ES_DATA_QUERY_PAGE_SIZE = 1000;
    private static final String ES_COMMON_DATA_PREFIX = "commonData";
    private static final String FLATTEN_PREFIX = "flattenedData";
    private static final String TEXT_PREFIX = "textData";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addVersion(DataSetVersionParam dataSetVersionParam) {
        // 1. 检查参数并获取数据集
        checkCreateVersionParam(dataSetVersionParam);

        // 2. 创建新版本的ES索引（目前只支持已有版本继承）
        DataSetCopyVersionParam copyVersionParam = new DataSetCopyVersionParam();
        copyVersionParam.setDataSetId(dataSetVersionParam.getDataSetId());
        copyVersionParam.setVersionId(Long.valueOf(dataSetVersionParam.getVersionSource()));
        copyVersionParam.setVersionName(dataSetVersionParam.getVersionName());
        copyVersionParam.setVersionDescription(dataSetVersionParam.getVersionDescription());
        copyVersionParam.setVersionType(dataSetVersionParam.getVersionType());
        copyVersion(copyVersionParam);
    }

    @Override
    public Long copyVersion(DataSetCopyVersionParam dataSetCopyVersionParam) {
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetCopyVersionParam.getDataSetId());
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        DataSetVersion sourceVersion = dataSetVersionRepository.getById(dataSetCopyVersionParam.getVersionId());
        CheckUtil.paramCheck(Objects.nonNull(sourceVersion), "源版本不存在");

        // 2. 创建新版本
        DataSetVersion newVersion = createDataSetVersion(dataSetCopyVersionParam, dataSet, sourceVersion);

        // 3. 通过滚动方式，复制数据到新版本
        CopyVersionResult copyResult = copyDataBetweenVersions(dataSet, sourceVersion, newVersion, dataSetCopyVersionParam);

        // 4. 更新数据集统计信息
        updateDataSetStatistics(dataSet, newVersion, copyResult.getTotalCount(), copyResult.getNewIndexName());

        return newVersion.getId();
    }

    /**
     * 复制版本结果对象
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class CopyVersionResult {
        private int totalCount;
        private String newIndexName;
    }

    /**
     * 通过滚动方式复制数据到新版本
     *
     * @param dataSet                 数据集对象
     * @param sourceVersion           源版本对象
     * @param newVersion              新版本对象
     * @param dataSetCopyVersionParam 数据集复制版本参数对象
     * @return 包含总数和索引名的结果对象
     */
    private CopyVersionResult copyDataBetweenVersions(DataSet dataSet, DataSetVersion sourceVersion, DataSetVersion newVersion, DataSetCopyVersionParam dataSetCopyVersionParam) {
        DatasetEsQueryCondition condition = dataSetInvokeEs.buildScrollCondition(dataSet, sourceVersion, dataSetCopyVersionParam.getConditionList());

        boolean hasNext = true;
        int totalCount = 0;
        String newIndexName = null;
        while (hasNext) {
            // 执行查询
            DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);
            List<DatasetEsIndex> records = pageResult.getRecords();

            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            DatasetHeadConfig sourceHeadConfig = JSONObject.parseObject(sourceVersion.getHeadConfig(), DatasetHeadConfig.class);
            DatasetHeadConfig newHeadConfig = JSONObject.parseObject(newVersion.getHeadConfig(), DatasetHeadConfig.class);
            Map<String, DataContentFieldDTO> newFieldMap = newHeadConfig.getHeadList().stream().collect(Collectors.toMap(DataContentFieldDTO::getColumnName, Function.identity()));

            // 更新数据：设置新的版本ID
            for (DatasetEsIndex record : records) {
                record.setDocumentId(UUID.randomUUID().toString()); // 重新生成uuid
                record.setVersionId(newVersion.getId().toString());
                // 如果有配置列过滤条件，则新版本的segment_字段会发生变化，需要重新存储映射的es字段
                if (CollectionUtils.isNotEmpty(dataSetCopyVersionParam.getSavedColumnList())) {
                    Map<String, Object> newTextData = new HashMap<>();
                    Map<String, Object> newCommonData = new HashMap<>();
                    Map<String, Object> newFlattenedData = new HashMap<>();
                    for (DataContentFieldDTO dataContentField : sourceHeadConfig.getHeadList()) {
                        // 过滤不需要保留的列
                        if (!newFieldMap.containsKey(dataContentField.getColumnName())) {
                            continue;
                        }
                        // 新版本中的es列名
                        String newEsColumnName = newFieldMap.get(dataContentField.getColumnName()).getEsColumnName();
                        if (ES_COMMON_DATA_PREFIX.equals(dataContentField.getEsKey())) {
                            if (record.getCommonData().containsKey(dataContentField.getEsColumnName())) {
                                newCommonData.put(newEsColumnName, record.getCommonData().get(dataContentField.getEsColumnName()));
                            }
                        } else if (FLATTEN_PREFIX.equals(dataContentField.getEsKey())) {
                            if (record.getFlattenedData().containsKey(dataContentField.getEsColumnName())) {
                                newFlattenedData.put(newEsColumnName, record.getFlattenedData().get(dataContentField.getEsColumnName()));
                            }
                        } else {
                            if (record.getTextData().containsKey(dataContentField.getEsColumnName())) {
                                newTextData.put(newEsColumnName, record.getTextData().get(dataContentField.getEsColumnName()));
                            }
                        }
                    }
                    record.setTextData(newTextData);
                    record.setCommonData(newCommonData);
                    record.setFlattenedData(newFlattenedData);
                }
            }

            // 批量写入ES
            BatchResult result = datasetEsIndexService.batchInsertDatasets(records, DataSourceEnum.getValueByCode(dataSet.getDataSource()), newIndexName);
            newIndexName = result.getIndex();
            totalCount += records.size();

            // 检查是否有下一页
            hasNext = pageResult.getHasNext() != null && pageResult.getHasNext();
            if (hasNext) {
                condition.setScrollId(pageResult.getScrollId());
            }
        }

        return new CopyVersionResult(totalCount, newIndexName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVersion(Long dataSetId, Long versionId) {
        // 删除版本数据
        DataSetVersion version = getVersion(dataSetId, versionId);
        version.setIsDeleted(Boolean.TRUE);
        version.setUpdateTime(new Date());
        dataSetVersionRepository.updateSelectiveById(version);

        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        // 删除ES数据
        dataSetInvokeEs.deleteVersionEsData(dataSet, version);
    }

    @Override
    public void updateVersionName(Long dataSetId, Long versionId, String versionName) {
        DataSetVersion version = getVersion(dataSetId, versionId);
        if (StringUtils.isNotBlank(versionName)) {
            version.setVersionName(versionName);
        }
        version.setUpdateTime(new Date());
        dataSetVersionRepository.updateSelectiveById(version);
    }

    @Override
    public List<DataSetVersionDTO> listVersion(Long dataSetId) {
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        List<DataSetVersion> dataSetVersions = dataSetVersionRepository.listVersionByDataSetId(dataSetId);
        if (CollectionUtils.isEmpty(dataSetVersions)) {
            return Collections.emptyList();
        }
        return dataSetVersions.stream()
                .map(this::builderDataSetVersionDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public DataSetVersionDTO getByDataSetIdAndVersionId(Long dataSetId, Long versionId) {
        DataSetVersion version = getVersion(dataSetId, versionId);
        return builderDataSetVersionDTO(version);
    }

    /**
     * 获取数据集版本
     *
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @return 数据集版本信息
     */
    private DataSetVersion getVersion(Long dataSetId, Long versionId) {
        CheckUtil.paramCheck(Objects.nonNull(dataSetId) && Objects.nonNull(versionId), "数据集ID或版本ID不能为空");
        DataSetVersion version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(dataSetId, versionId);
        CheckUtil.paramCheck(Objects.nonNull(version), "数据集版本不存在");
        return version;
    }

    /**
     * 构建数据集版本DTO
     *
     * @param dataSetVersion 数据集版本信息
     * @return 数据集版本DTO
     */
    private DataSetVersionDTO builderDataSetVersionDTO(DataSetVersion dataSetVersion) {
        if (Objects.isNull(dataSetVersion)) {
            return null;
        }
        DataSetVersionDTO dataSetVersionDTO = new DataSetVersionDTO();
        try {
            dataSetVersionDTO.setVersionId(dataSetVersion.getId());
            dataSetVersionDTO.setVersionName(dataSetVersion.getVersionName());
            if (Objects.nonNull(dataSetVersion.getVersionStatus())) {
                dataSetVersionDTO.setVersionStatus(dataSetVersion.getVersionStatus());
            }
            dataSetVersionDTO.setDataCount(dataSetVersion.getDataCount());
            if (Objects.nonNull(dataSetVersion.getDataSetId())) {
                DataSet dataSet = dataSetRepository.getDataSetById(dataSetVersion.getDataSetId());
                CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");
                dataSetVersionDTO.setDataSetName(dataSet.getName());
                dataSetVersionDTO.setDataFormat(dataSet.getDataFormat());
                dataSetVersionDTO.setCreatorMis(dataSet.getCreatorMis());
                dataSetVersionDTO.setCreatorName(dataSet.getCreatorName());
                String usageTypeName = null;
                String trainingTypeName = null;
                if (Objects.nonNull(dataSet.getUsageType())) {
                    usageTypeName = TrainingUsageTypeEnum.getDescByCode(dataSet.getUsageType());
                }
                if (Objects.nonNull(dataSet.getTrainingType())) {
                    trainingTypeName = TrainingTypeEnum.getDescByCode(dataSet.getTrainingType());
                }
                if (StringUtils.isNotBlank(usageTypeName) && StringUtils.isNotBlank(trainingTypeName)) {
                    dataSetVersionDTO.setDataPurpose(usageTypeName + " >" + trainingTypeName);
                }
            }

            if (Objects.nonNull(dataSetVersion.getCreateTime())) {
                dataSetVersionDTO.setCreateTime(DateUtil.formatDate(dataSetVersion.getCreateTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD));
            }
            if (Objects.nonNull(dataSetVersion.getUpdateTime())) {
                dataSetVersionDTO.setUpdateTime(DateUtil.formatDate(dataSetVersion.getUpdateTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD));
            }
            return dataSetVersionDTO;
        } catch (Exception e) {
            log.error("日期转换失败，数据集版本id:{}", dataSetVersion.getId(), e);
        }
        return dataSetVersionDTO;
    }

    /**
     * 创建版本参数校验
     *
     * @param dataSetVersionParam 数据集版本参数
     */
    private void checkCreateVersionParam(DataSetVersionParam dataSetVersionParam) {
        CheckUtil.paramCheck(Objects.nonNull(dataSetVersionParam), "参数不能为空");
        CheckUtil.paramCheck(Objects.nonNull(dataSetVersionParam.getDataSetId()), "数据集ID不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(dataSetVersionParam.getVersionName()), "版本名称不能为空");
        CheckUtil.paramCheck(Objects.nonNull(dataSetVersionParam.getVersionType()), "版本类型不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(dataSetVersionParam.getVersionSource()), "源版本不能为空");
    }

    /**
     * 创建数据集版本
     *
     * @param dataSetCopyVersionParam 数据集版本参数
     * @param dataSet                 数据集信息
     * @return 创建的数据集版本信息
     */
    private DataSetVersion createDataSetVersion(DataSetCopyVersionParam dataSetCopyVersionParam, DataSet dataSet, DataSetVersion sourceVersion) {
        Date now = new Date();
        DataSetVersion newVersion = new DataSetVersion();
        newVersion.setDataSetId(dataSet.getId());
        newVersion.setVersionName(dataSetCopyVersionParam.getVersionName());
        newVersion.setDescription(dataSetCopyVersionParam.getVersionDescription());
        newVersion.setHeadConfig(filterHeadConfig(dataSetCopyVersionParam, sourceVersion.getHeadConfig()));
        newVersion.setVersionType(dataSetCopyVersionParam.getVersionType());
        newVersion.setVersionSource(sourceVersion.getId().toString()); // 记录源版本ID
        newVersion.setVersionStatus(DatasetVersionStatusEnum.DRAFT.getCode());
        newVersion.setDataCount(0); // 初始为0，后续更新
        newVersion.setCreateTime(now);
        newVersion.setUpdateTime(now);

        // 插入版本数据
        dataSetVersionRepository.createDataSetVersion(newVersion);

        return newVersion;
    }

    /**
     * 过滤表头配置
     *
     * @param dataSetCopyVersionParam 数据集版本参数
     * @param sourceHeadConfig        源版本表头配置
     * @return 过滤后的表头配置
     */
    private String filterHeadConfig(DataSetCopyVersionParam dataSetCopyVersionParam, String sourceHeadConfig) {
        if (StringUtils.isBlank(sourceHeadConfig)) {
            return sourceHeadConfig;
        }

        // 如果没有需要保存的列，直接返回源配置
        List<String> savedColumnList = dataSetCopyVersionParam.getSavedColumnList();
        if (CollectionUtils.isEmpty(savedColumnList)) {
            return sourceHeadConfig;
        }

        // 因为过滤了一部分数据，所以需要重新统计segment的映射关系
        DatasetHeadConfig datasetHeadConfig = JSONObject.parseObject(sourceHeadConfig, DatasetHeadConfig.class);
        DatasetHeadConfig filteredConfig = new DatasetHeadConfig();
        List<DatasetHeadConfig.HeadConfig> filteredHeadConfigList = new ArrayList<>();
        List<DataContentFieldDTO> dataContentFieldDTOList = new ArrayList<>();
        Set<String> savedColumnSet = new HashSet<>(savedColumnList);
        int textSegmentIndex = 1;
        int flattenSegmentIndex = 1;
        for (DataContentFieldDTO dataContentFieldDTO : datasetHeadConfig.getHeadList()) {
            if (savedColumnSet.contains(dataContentFieldDTO.getColumnName())) {
                DataContentFieldDTO newDataContentFieldDTO = new DataContentFieldDTO();
                newDataContentFieldDTO.setColumnName(dataContentFieldDTO.getColumnName());
                String esKey = dataContentFieldDTO.getEsKey();
                newDataContentFieldDTO.setEsKey(dataContentFieldDTO.getEsKey());
                if (ES_COMMON_DATA_PREFIX.equals(esKey)) {
                    newDataContentFieldDTO.setEsColumnName(dataContentFieldDTO.getEsColumnName());
                } else if (FLATTEN_PREFIX.equals(esKey)) {
                    newDataContentFieldDTO.setEsColumnName("segment_" + flattenSegmentIndex++);
                } else {
                    newDataContentFieldDTO.setEsColumnName("segment_" + textSegmentIndex++);
                }
                newDataContentFieldDTO.setStoreType(dataContentFieldDTO.getStoreType());
                newDataContentFieldDTO.setFieldType(dataContentFieldDTO.getFieldType());
                newDataContentFieldDTO.setTreeNodes(dataContentFieldDTO.getTreeNodes());
                dataContentFieldDTOList.add(newDataContentFieldDTO);
            }
        }
        filteredHeadConfigList.add(new DatasetHeadConfig.HeadConfig(EsStoreTypeEnum.TEXT.getName(), textSegmentIndex));
        filteredHeadConfigList.add(new DatasetHeadConfig.HeadConfig(EsStoreTypeEnum.FLATTEN.getName(), flattenSegmentIndex));
        filteredConfig.setHeadConfig(filteredHeadConfigList);
        filteredConfig.setHeadList(dataContentFieldDTOList);

        return JSONObject.toJSONString(filteredConfig);
    }

    /**
     * 更新数据集统计信息
     *
     * @param dataSet        数据集信息
     * @param dataSetVersion 数据集版本信息
     * @param totalCount     数据集版本数据总数
     * @param indexName      索引名称
     */
    private void updateDataSetStatistics(DataSet dataSet, DataSetVersion dataSetVersion, int totalCount, String indexName) {
        DataSet updateDataSet = new DataSet();
        updateDataSet.setId(dataSet.getId());
        updateDataSet.setLatestVersionId(dataSetVersion.getId());
        updateDataSet.setUpdateTime(new Date());
        dataSetRepository.updateDataSet(updateDataSet);

        DataSetVersion updateVersion = new DataSetVersion();
        updateVersion.setId(dataSetVersion.getId());
        updateVersion.setDataCount(totalCount);
        if (StringUtils.isNotBlank(indexName) && StringUtils.isBlank(dataSetVersion.getEsIndexName())) {
            updateVersion.setEsIndexName(indexName);
        }
        updateVersion.setUpdateTime(new Date());
        dataSetVersionRepository.updateSelectiveById(updateVersion);
    }
}
