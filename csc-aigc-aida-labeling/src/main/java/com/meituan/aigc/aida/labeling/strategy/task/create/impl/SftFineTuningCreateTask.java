package com.meituan.aigc.aida.labeling.strategy.task.create.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategy;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * SFT训练数据导出任务创建策略
 *
 * @Author: guowenhui
 * @Create: 2025/3/3 16:48
 * @Version: 1.0
 */
@Slf4j
@Component
public class SftFineTuningCreateTask extends CommonCreateTask implements CreateTaskStrategy {

    /**
     * SFT训练入参字段
     */
    private static final List<String> CUSTOM_FIELD_FINE_TUNE_TEMPLATE = Arrays.asList("prompt", "output");

    private static final String LOG_FLAG = LabelTaskDataType.SFT_FINE_TUNING.getValue();

    @Override
    public Integer getTaskType() {
        return LabelTaskDataType.SFT_FINE_TUNING.getCode();
    }

    @Override
    public void checkHeader(MultipartFile file) {
        checkHeader(file, CUSTOM_FIELD_FINE_TUNE_TEMPLATE);
    }

    @Override
    public List<LabelingTaskRawData> processData(List<List<String>> rowData, List<String> headerList) {
        return processData(rowData, headerList, CUSTOM_FIELD_FINE_TUNE_TEMPLATE);
    }

    @Override
    public void export(String taskName, List<Long> subTaskIds, Integer labelDetailStatus, String mis) {
        commonExport(taskName, subTaskIds, labelDetailStatus, LOG_FLAG, "标注数据", mis);
    }

    @Override
    protected int[] createHeaderAndFillData(SXSSFWorkbook workbook, Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData,
                                            List<Long> subTaskIds, Integer labelDetailStatus, String taskName) {
        // 创建表头行
        int colIndex = createHeaderRows(sheet, headerStyle, fieldData);

        // 设置列宽
        setupColumnWidths(sheet, colIndex);

        // 处理数据
        int totalCount = processSftData(sheet, subTaskIds, labelDetailStatus,
                fieldData, taskName);

        return new int[]{colIndex, totalCount};
    }

    /**
     * 创建表头行
     *
     * @param sheet       工作表
     * @param headerStyle 表头样式
     * @return 最终列索引
     */
    private int createHeaderRows(Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData) {
        // 创建第一行表头（主分类）
        int colIndex = createFirstHeaderRow(sheet, headerStyle, fieldData);

        // 创建第二行表头（具体字段）
        createSecondHeaderRow(sheet, headerStyle, fieldData, CUSTOM_FIELD_FINE_TUNE_TEMPLATE);

        return colIndex;
    }

    /**
     * 创建第一行表头（主分类）
     *
     * @param sheet       工作表
     * @param headerStyle 表头样式
     * @return 最终列索引
     */
    private int createFirstHeaderRow(Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData) {
        List<String> rawDataFields = fieldData.getRawDataFields();
        List<InspectionItemData> labelingFields = fieldData.getLabelingFields();
        List<InspectionItemData> qualityCheckFields = fieldData.getQualityCheckFields();
        List<String> qualityCheckModifiedRawDataMappedContent = fieldData.getQualityCheckModifiedRawDataMappedContent();
        List<InspectionItemData> qualityCheckModifiedLabelingItems = fieldData.getQualityCheckModifiedLabelingItems();

        Row firstHeaderRow = sheet.createRow(0);
        int colIndex = 0;

        // 会话分组列 (sessionId, time)
        colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "会话分组", 2);

        // SFT训练入参列 (prompt, output)
        colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "SFT训练入参", 2);

        // SFT训练入参列 (prompt, output)-质检员修改后
        if (CollectionUtils.isNotEmpty(qualityCheckModifiedRawDataMappedContent)) {
            colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "SFT训练入参-质检员修改后", 2);
        }

        // 原数据列
        if (CollectionUtils.isNotEmpty(rawDataFields)) {
            colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "标注参考项", rawDataFields.size());
        }

        // 标注结果列
        if (CollectionUtils.isNotEmpty(labelingFields)) {
            colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "标注结果", labelingFields.size());
        }

        // 质检结果列
        if (CollectionUtils.isNotEmpty(qualityCheckFields)) {
            colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "质检结果", qualityCheckFields.size());
        }

        // 标注结果列-质检员修改后内容
        if (CollectionUtils.isNotEmpty(qualityCheckModifiedLabelingItems)) {
            colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "标注结果-质检员修改后", qualityCheckModifiedLabelingItems.size());
        }

        return colIndex;
    }

    /**
     * 设置列宽
     *
     * @param sheet    工作表
     * @param colIndex 列数
     */
    private void setupColumnWidths(Sheet sheet, int colIndex) {
        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 8000);
        sheet.setColumnWidth(3, 8000);
        for (int i = 4; i < colIndex; i++) {
            // 约20个字符宽
            sheet.setColumnWidth(i, 4000);
        }
    }

    /**
     * 处理SFT数据
     *
     * @param sheet             工作表
     * @param subTaskIds        子任务ID集合
     * @param labelDetailStatus 标注详情状态
     * @param taskName          任务名称
     * @return 处理的数据总数
     */
    private int processSftData(Sheet sheet, List<Long> subTaskIds, Integer labelDetailStatus, ExportFieldData fieldData, String taskName) {
        // 创建数据处理器
        SftDataProcessor processor = new SftDataProcessor(fieldData.getRawDataFields(), fieldData.getLabelingFields(), fieldData.getQualityCheckFields(),
                fieldData.getQualityCheckModifiedRawDataMappedContent(), fieldData.getQualityCheckModifiedLabelingItems());

        // 批量处理数据
        return processDataInBatches(sheet, 2, subTaskIds, labelDetailStatus, LOG_FLAG, taskName, processor);
    }

    /**
     * SFT数据处理器
     */
    private static class SftDataProcessor implements ExportDataProcessor {
        private final List<String> rawDataFieldsList;
        private final List<InspectionItemData> labelingFields;
        private final List<InspectionItemData> qualityCheckFields;
        private final List<String> qualityCheckModifiedRawDataMappedContent;
        private final List<InspectionItemData> qualityCheckModifiedLabelingItems;


        public SftDataProcessor(List<String> rawDataFieldsList, List<InspectionItemData> labelingFields, List<InspectionItemData> qualityCheckFields,
                                List<String> qualityCheckModifiedRawDataMappedContent, List<InspectionItemData> qualityCheckModifiedLabelingItems) {
            this.rawDataFieldsList = rawDataFieldsList;
            this.labelingFields = labelingFields;
            this.qualityCheckFields = qualityCheckFields;
            this.qualityCheckModifiedRawDataMappedContent = qualityCheckModifiedRawDataMappedContent;
            this.qualityCheckModifiedLabelingItems = qualityCheckModifiedLabelingItems;
        }

        @Override
        public void processRow(Row row, LabelTaskDataExportVO data) {
            int cellIndex = 0;

            // 写入sessionId和time
            row.createCell(cellIndex++).setCellValue(data.getSessionId());
            String time = data.getSessionTime() != null
                    ? DateUtil.formatDate(data.getSessionTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD)
                    : "";
            row.createCell(cellIndex++).setCellValue(time);

            // 提取并写入原始映射数据
            fillRawDataCells(row, data, cellIndex);
        }

        private void fillRawDataCells(Row row, LabelTaskDataExportVO data, int cellIndex) {
            int currentCellIndex = cellIndex;
            JSONObject rawDataMappedObject = CommonCreateTask.extractRawDataMappedObject(data);
            // 防止rawDataMappedObject为null
            if (rawDataMappedObject == null) {
                rawDataMappedObject = new JSONObject();
            }

            // 写入SFT训练入参字段（prompt和output）
            for (String field : CUSTOM_FIELD_FINE_TUNE_TEMPLATE) {
                Cell cell = row.createCell(currentCellIndex++);
                CreateTaskStrategy.setCellValueByType(cell, Objects.nonNull(rawDataMappedObject.get(field)) ? rawDataMappedObject.get(field) : "");
            }

            // 提取并写入质检员修改后的原始数据映射内容
            if (CollectionUtils.isNotEmpty(qualityCheckModifiedRawDataMappedContent)) {
                JSONObject modifiedRawDataMappedContent = StringUtils.isNotBlank(data.getQualityCheckModifiedRawDataMappedContent())
                        ? JSON.parseObject(data.getQualityCheckModifiedRawDataMappedContent()) : new JSONObject();
                // 写入SFT训练入参字段（prompt和output）
                for (String field : CUSTOM_FIELD_FINE_TUNE_TEMPLATE) {
                    Cell cell = row.createCell(currentCellIndex++);
                    CreateTaskStrategy.setCellValueByType(cell, Objects.nonNull(modifiedRawDataMappedContent.get(field)) ? modifiedRawDataMappedContent.get(field) : "");
                }
            }

            // 写入原始数据字段
            JSONObject rawDataObject = null;
            if (StringUtils.isNotBlank(data.getRawDataContent())) {
                try {
                    rawDataObject = JSON.parseObject(data.getRawDataContent());
                } catch (Exception e) {
                    log.error("解析原始数据内容失败: {}", data.getSessionId(), e);
                }
            }
            // 防止rawDataObject为null
            if (rawDataObject == null) {
                rawDataObject = new JSONObject();
            }

            for (String field : rawDataFieldsList) {
                Cell cell = row.createCell(currentCellIndex++);
                if (rawDataObject.containsKey(field)) {
                    CreateTaskStrategy.setCellValueByType(cell, Objects.nonNull(rawDataObject.get(field)) ? rawDataObject.get(field) : "");
                }
            }

            // 使用公共方法处理标注结果和质检结果的写入
            CommonCreateTask.writeLabelingAndQualityCheckResults(row, data, currentCellIndex, labelingFields,
                    qualityCheckFields, qualityCheckModifiedLabelingItems);
        }
    }

}
