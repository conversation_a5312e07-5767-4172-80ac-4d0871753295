package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 更新数据集请求参数
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集管理 - 更新数据集请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetUpdateParam implements Serializable {
    /**
     * 数据集ID
     */
    private Long dataSetId;
    
    /**
     * 数据传输锁
     */
    private String lockId;
    
    /**
     * 是否最后一批，为true会释放锁，后续再无法使用
     */
    private Boolean isEnd;
    
    /**
     * 数据列
     */
    private List<DataFieldInfo> headList;
    
    /**
     * 数据列表
     */
    private List<DataSetRecord> dataList;
} 