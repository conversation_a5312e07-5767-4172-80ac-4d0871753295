package com.meituan.aigc.aida.labeling.service;

import com.meituan.aigc.aida.labeling.pojo.dto.ExternalMetricDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-21 10:41
 * @description
 */
public interface ExternalMetricService {

    /**
     * 获取外部指标列表
     *
     * @return 指标列表
     */
    List<ExternalMetricDTO> listExternalMetric();

    /**
     * 获取外部指标详情
     *
     * @param metricId     指标ID
     * @param currentIndex 当前主键
     * @return 指标详情
     */
    List<LabelResult> getExternalMetricDetail(Long metricId, Long currentIndex);

}
