package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-23 15:30
 * @description
 */
@Data
public class DataSetDTO implements Serializable {
    /**
     * 数据集id
     */
    private Long id;
    /**
     * 数据集名称
     */
    private String name;
    /**
     * 最新版本
     */
    private Long latestVersionId;

    /**
     * 最新版本名称
     */
    private String latestVersionName;
    /**
     *  发布状态
     */
    private Integer publishStatus;
    /**
     * 创建者mis
     */
    private String creatorMis;
    /**
     * 创建者名称
     */
    private String creatorName;
    /**
     * 用途分类
     */
    private Integer usageCategory;
    /**
     * 数据来源
     */
    private Integer dataSource;
    /**
     *  数据量
     */
    private Integer dataCount;
    /**
     * 创建时间
     */
    private String createTime;
}
