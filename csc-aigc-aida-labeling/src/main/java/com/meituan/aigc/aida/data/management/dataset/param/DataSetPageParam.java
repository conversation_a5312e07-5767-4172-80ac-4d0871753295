package com.meituan.aigc.aida.data.management.dataset.param;

import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DataSetPageParam implements Serializable {
    /**
     * 数据获取任务id
     */
    private Long fetchId;
    /**
     * 数据源字段列表
     */
    private List<DataFieldParam> headList;
    /**
     * 数据列表
     */
    private List<DataSetRecordParam> dataList;
}
