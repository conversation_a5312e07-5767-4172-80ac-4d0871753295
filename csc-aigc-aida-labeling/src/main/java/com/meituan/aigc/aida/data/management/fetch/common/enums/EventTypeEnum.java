package com.meituan.aigc.aida.data.management.fetch.common.enums;


import lombok.Getter;

/**
 * 事件类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum EventTypeEnum {
    CHAT_SESSION_IN_PROCESS("CHAT_SESSION_IN_PROCESS", "在线会话进行中"),
    CHAT_SESSION_END("CHAT_SESSION_END", "在线会话结束"),
    CHAT_SESSION_START("CHAT_SESSION_START", "在线会话开始"),
    ;

    private final String code;
    private final String name;

    EventTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public static EventTypeEnum findByCode(String code) {
        for (EventTypeEnum skillType : EventTypeEnum.values()) {
            if (skillType.getCode().equals(code)) {
                return skillType;
            }
        }
        return null;
    }
}
