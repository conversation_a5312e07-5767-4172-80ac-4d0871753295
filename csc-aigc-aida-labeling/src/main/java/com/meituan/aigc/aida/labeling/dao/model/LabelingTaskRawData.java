package com.meituan.aigc.aida.labeling.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * 标注任务原数据表实体类
 */
@Data
public class LabelingTaskRawData {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 所属任务ID
     */
    private Long taskId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 会话时间
     */
    private Date sessionTime;
    
    /**
     * 原始数据内容
     */
    private String rawDataContent;

    /**
     * 原始数据表头
     */
    private String rawDataHeaders;
    
    /**
     * 原始数据内容映射内容
     */
    private String rawDataMappedContent;
    
    /**
     * 扩展信息(json格式)
     */
    private String extraInfo;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 大模型消息ID
     */
    private String messageId;
} 