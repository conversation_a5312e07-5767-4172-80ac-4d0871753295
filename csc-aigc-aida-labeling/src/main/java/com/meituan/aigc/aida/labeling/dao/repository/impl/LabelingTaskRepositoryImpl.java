package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.dao.mapper.LabelingTaskMapper;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskExample;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskRepository;
import com.meituan.aigc.aida.labeling.job.dashboard.po.TaskStatisticsPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:10
 * @Version: 1.0
 */
@Repository
@Slf4j
public class LabelingTaskRepositoryImpl implements LabelingTaskRepository {

    @Resource
    private LabelingTaskMapper labelingTaskMapper;

    /**
     * 根据主键查询详情
     *
     * @param taskId
     * @return
     */
    @Override
    public LabelingTask getById(Long taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }
        return labelingTaskMapper.selectByPrimaryKey(taskId);
    }

    @Override
    public int updateTaskStatusById(Long taskId, Integer status, Date labelingFinishTime) {
        LabelingTask labelingTask = new LabelingTask();
        labelingTask.setId(taskId);
        labelingTask.setLabelingStatus(status);
        labelingTask.setUpdateTime(new Date());
        if (Objects.nonNull(labelingFinishTime)) {
            labelingTask.setLabelingFinishTime(labelingFinishTime);
        }
        return labelingTaskMapper.updateByPrimaryKeySelective(labelingTask);
    }

    @Override
    public int updateTaskQualityCheckStatusById(Long taskId, Integer qualityCheckStatus, Date checkFinishTime) {
        LabelingTask labelingTask = new LabelingTask();
        labelingTask.setId(taskId);
        labelingTask.setQualityCheckStatus(qualityCheckStatus);
        labelingTask.setUpdateTime(new Date());
        if (Objects.nonNull(checkFinishTime)){
            labelingTask.setCheckFinishTime(checkFinishTime);
        }
        return labelingTaskMapper.updateByPrimaryKeySelective(labelingTask);
    }

    @Override
    public int updateByPrimaryKeySelective(LabelingTask labelingTask) {
        return labelingTaskMapper.updateByPrimaryKeySelective(labelingTask);
    }

    @Override
    public int insert(LabelingTask labelingTask) {
        return labelingTaskMapper.insert(labelingTask);
    }

    @Override
    public List<LabelingTask> listByIdNameAndMis(String name, Long taskId, String mis, boolean isAdmin) {
        LabelingTaskExample example = new LabelingTaskExample();
        LabelingTaskExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(name)) {
            criteria.andTaskNameLike("%" + name + "%");
        }
        if (Objects.nonNull(taskId)) {
            criteria.andIdEqualTo(taskId);
        }
        if (!isAdmin && StringUtils.isNotBlank(mis)) {
            criteria.andLabelingManagerEqualTo(mis);
        }
        criteria.andIsDeletedEqualTo(false);
        example.setOrderByClause("id DESC , update_time desc");
        return labelingTaskMapper.selectByExample(example);
    }

    @Override
    public List<LabelingTask> listByIdList(List<Long> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return new ArrayList<>();
        }
        LabelingTaskExample example = new LabelingTaskExample();
        LabelingTaskExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(taskIdList)
                .andIsDeletedEqualTo(Boolean.FALSE);
        return labelingTaskMapper.selectByExample(example);
    }

    @Override
    public List<TaskStatisticsPO> statisticsTaskData(Date startTime, Date endTime) {
        return labelingTaskMapper.statisticsTaskData(startTime, endTime);
    }

    @Override
    public List<LabelingTask> listTaskByCreateTimeAndStatus(Date startTime, Date endTime, List<Integer> labelingStatus) {
        return labelingTaskMapper.listTaskByCreateTimeAndStatus(startTime, endTime, labelingStatus);
    }

    @Override
    public int insertSelective(LabelingTask labelingTask) {
        return labelingTaskMapper.insertSelective(labelingTask);
    }
}
