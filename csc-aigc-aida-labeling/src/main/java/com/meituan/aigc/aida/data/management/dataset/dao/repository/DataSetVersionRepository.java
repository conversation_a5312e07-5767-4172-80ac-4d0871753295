package com.meituan.aigc.aida.data.management.dataset.dao.repository;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-27 10:39
 * @description
 */
public interface DataSetVersionRepository {

    /**
     * 根据数据集ID和版本ID获取版本信息
     */
    DataSetVersion getVersionByDataSetIdAndVersionId(Long dataSetId, Long versionId);

    /**
     * 根据数据集ID获取版本列表
     * @param dataSetId 数据集ID
     * @return 版本列表
     */
    List<DataSetVersion> listVersionByDataSetId(Long dataSetId);

    /**
     * 根据版本ID获取版本信息
     *
     * @param versionId 版本ID
     * @return 版本信息
     */
    DataSetVersion getById(Long versionId);

    /**
     * 创建新版本
     */
    Long createDataSetVersion(DataSetVersion version);

    /**
     * 根据ID更新版本信息
     *
     * @param version 版本信息
     */
    void updateSelectiveById(DataSetVersion version);
}
