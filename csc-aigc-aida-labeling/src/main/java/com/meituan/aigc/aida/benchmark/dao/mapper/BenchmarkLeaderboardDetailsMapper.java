package com.meituan.aigc.aida.benchmark.dao.mapper;

import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetailsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BenchmarkLeaderboardDetailsMapper {
    long countByExample(BenchmarkLeaderboardDetailsExample example);

    int deleteByExample(BenchmarkLeaderboardDetailsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BenchmarkLeaderboardDetails record);

    int insertSelective(BenchmarkLeaderboardDetails record);

    int batchInsert(@Param("records") List<BenchmarkLeaderboardDetails> records);

    List<BenchmarkLeaderboardDetails> selectByExample(BenchmarkLeaderboardDetailsExample example);

    BenchmarkLeaderboardDetails selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BenchmarkLeaderboardDetails record, @Param("example") BenchmarkLeaderboardDetailsExample example);

    int updateByExample(@Param("record") BenchmarkLeaderboardDetails record, @Param("example") BenchmarkLeaderboardDetailsExample example);

    int updateByPrimaryKeySelective(BenchmarkLeaderboardDetails record);

    int updateByPrimaryKey(BenchmarkLeaderboardDetails record);
}