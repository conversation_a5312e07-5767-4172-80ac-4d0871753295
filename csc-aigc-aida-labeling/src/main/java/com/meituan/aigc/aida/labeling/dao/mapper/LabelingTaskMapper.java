package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskExample;
import com.meituan.aigc.aida.labeling.job.dashboard.po.TaskStatisticsPO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface LabelingTaskMapper {

    int deleteByExample(LabelingTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LabelingTask record);

    int insertSelective(LabelingTask record);

    List<LabelingTask> selectByExampleWithBLOBs(LabelingTaskExample example);

    List<LabelingTask> selectByExample(LabelingTaskExample example);

    LabelingTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LabelingTask record, @Param("example") LabelingTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") LabelingTask record, @Param("example") LabelingTaskExample example);

    int updateByExample(@Param("record") LabelingTask record, @Param("example") LabelingTaskExample example);

    int updateByPrimaryKeySelective(LabelingTask record);

    int updateByPrimaryKeyWithBLOBs(LabelingTask record);

    int updateByPrimaryKey(LabelingTask record);

    List<TaskStatisticsPO> statisticsTaskData(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<LabelingTask> listTaskByCreateTimeAndStatus(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("labelingStatus") List<Integer> labelingStatus);
}