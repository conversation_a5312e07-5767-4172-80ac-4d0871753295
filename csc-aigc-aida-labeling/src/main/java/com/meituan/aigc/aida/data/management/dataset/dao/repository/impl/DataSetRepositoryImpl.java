package com.meituan.aigc.aida.data.management.dataset.dao.repository.impl;

import com.dianping.zebra.util.StringUtils;
import com.meituan.aigc.aida.data.management.dataset.dao.mapper.DataSetMapper;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetExample;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-27 10:36
 * @description
 */
@Repository
public class DataSetRepositoryImpl implements DataSetRepository {

    @Resource
    private DataSetMapper dataSetMapper;

    @Override
    public DataSet getDataSetById(Long dataSetId) {
        if (Objects.isNull(dataSetId)) {
            return null;
        }
        return dataSetMapper.selectByPrimaryKey(dataSetId);
    }

    @Override
    public Long createDataSet(DataSet dataSet) {
        dataSetMapper.insertSelective(dataSet);
        return dataSet.getId();
    }

    @Override
    public void updateDataSet(DataSet dataSet) {
        dataSetMapper.updateByPrimaryKeySelective(dataSet);
    }

    @Override
    public List<DataSet> listDataSetByName(String name) {
        DataSetExample dataSetExample = new DataSetExample();
        DataSetExample.Criteria criteria = dataSetExample.createCriteria();
        if(StringUtils.isNotBlank(name)){
            criteria.andNameLike("%" + name + "%");
        }
        criteria.andIsDeletedEqualTo(Boolean.FALSE);
        dataSetExample.setOrderByClause("update_time desc");
        return dataSetMapper.selectByExample(dataSetExample);
    }
}
