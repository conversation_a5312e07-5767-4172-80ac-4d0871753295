package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 16:40
 * @Version: 1.0
 */
@Getter
public enum LabelingDataType {
    TEXT(1, "文本"),
    ENUMERATION(2, "枚举");

    private final int code;
    private final String value;

    LabelingDataType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LabelingDataType getByCode(int code) {
        for (LabelingDataType type : LabelingDataType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    /**
     * 通过枚举名称获取对应的枚举值
     *
     * @param name 枚举名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static LabelingDataType getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        try {
            return LabelingDataType.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 通过枚举名称获取对应的code值
     *
     * @param name 枚举名称
     * @return 对应的code值，如果未找到则返回null
     */
    public static Integer getCodeByName(String name) {
        LabelingDataType enumValue = getByName(name);
        return enumValue != null ? enumValue.getCode() : null;
    }
}
