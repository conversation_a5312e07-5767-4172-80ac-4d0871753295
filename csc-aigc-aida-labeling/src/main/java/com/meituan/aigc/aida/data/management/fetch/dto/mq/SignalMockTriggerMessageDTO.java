package com.meituan.aigc.aida.data.management.fetch.dto.mq;

import com.meituan.aigc.aida.data.management.fetch.common.enums.ChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChatMessageFromEnum;
import com.meituan.aigc.aida.data.management.fetch.dto.pacific.PacificButlerContextParamDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * Signal Mock触发消息DTO
 *
 * <AUTHOR>
 * @date 2024/04/03
 */
@Data
public class SignalMockTriggerMessageDTO implements Serializable {

    /**
     * 联系类型
     * @see ChannelEnum#code
     */
    private String contactType;

    /**
     * 联系ID
     */
    private String contactId;

    /**
     * 客服MIS
     */
    private String staffMis;

    /**
     * 消息发送时间, 毫秒时间戳
     */
    private String messageOccurredTime;

    /**
     * 消息来源: STAFF: 客服发送的消息, CUSTOMER: 用户发送的消息
     * {@link ChatMessageFromEnum#code}
     */
    private String chatMessageFromType;

    /**
     * 消息ID, 用于标识一条消息的唯一业务消息ID, 比如在线的海豚消息ID
     */
    private String messageId;

    /**
     * 用户消息内容
     */
    private String userMsgContent;

    /**
     * 客服发送消息内容
     */
    private String staffMessageContent;

    /**
     * 上下文参数DTO
     */
    private PacificButlerContextParamDTO contextParam;

}
