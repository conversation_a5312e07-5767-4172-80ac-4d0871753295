package com.meituan.aigc.aida.labeling.strategy.dimension.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.meituan.aigc.aida.labeling.common.enums.DimensionTypeEnum;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingDetailRepository;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskRawDataRepository;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckDistributeParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckMisParam;
import com.meituan.aigc.aida.labeling.pojo.vo.DistributionDimensionRequest;
import com.meituan.aigc.aida.labeling.strategy.dimension.DimensionStrategy;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QueryDimension extends CommonDimension implements DimensionStrategy {

    @Autowired
    private LabelingTaskRawDataRepository labelingTaskRawDataRepository;

    @Autowired
    private LabelingDetailRepository labelingDetailRepository;

    @Override
    public int labelingDistribute(DistributionDimensionRequest request) {
        Long taskId = request.getLabelingTask().getId();
        log.info("开始分配详情, taskId: {}, groupId: {}, totalDataCount: {}", taskId, request.getGroupId(), request.getTotalDataCount());
        // 根据分配的数据量分页查询原始数据
        List<LabelingTaskRawData> rawDataList = labelingTaskRawDataRepository.pageQueryByTaskId(taskId, request.getLastGroupIndex(),
                request.getTotalDataCount());
        log.info("为分组 {} 分配数据，起始索引:{}, 数据量: {}", request.getGroupId(), request.getLastGroupIndex(), rawDataList.size());
        // 分配数据给各个子任务
        for (LabelingSubTask subTask : request.getLabelingSubTasks()) {
            // 新增标注详情数据
            List<LabelingDetail> details = getLabelingDetails(request.getLabelingTask(), rawDataList, subTask);
            // 批量插入标注详情 ,超过一定数量后分批插入
            int batchSize = getBatchSize();
            if (details.size() > batchSize) {
                List<List<LabelingDetail>> partition = Lists.partition(details, batchSize);
                for (List<LabelingDetail> labelingDetails : partition) {
                    labelingDetailRepository.batchInsertDetail(labelingDetails);
                }
            } else {
                labelingDetailRepository.batchInsertDetail(details);
            }
            log.info("为子任务 {} 分配了 {} 条数据", subTask.getName(), details.size());
        }
        int newLastGroupIndex = request.getLastGroupIndex() + rawDataList.size();
        log.info("当前分组数据分配成功, taskId: {}, groupId: {}, 新的索引位置:{}", taskId, request.getGroupId(), newLastGroupIndex);
        return newLastGroupIndex;
    }

    @Override
    public Map<String, List<LabelingDetail>> qualityCheckDistribute(QualityCheckDistributeParam param, LabelingTask labelingTask, List<LabelingSubTask> subTaskList) {
        // 每个分组中先选取一个子任务做随机分配，完成后统一录入所有分组数据
        List<LabelingDetail> labelingDetailList = getLabelingDetailList(param, subTaskList);
        List<QualityCheckMisParam> qualityCheckMisParams = param.getLabelers();
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(labelingDetailList) && CollectionUtils.isNotEmpty(qualityCheckMisParams), "质检分配参数无效!");
        //质检mis号列表
        List<String> misList = qualityCheckMisParams.stream().map(QualityCheckMisParam::getQualityCheckMis)
                .collect(Collectors.toList());
        // 随机打乱 ID 集合
        List<LabelingDetail> shuffledIds = new ArrayList<>(labelingDetailList);
        Collections.shuffle(shuffledIds, ThreadLocalRandom.current());

        // 抽取指定数量的 ID
        List<LabelingDetail> sampledIds = shuffledIds.subList(0, shuffledIds.size());

        // 初始化结果集合
        Map<String, List<LabelingDetail>> result = new LinkedHashMap<>();
        for (String mis : misList) {
            result.put(mis, new ArrayList<>());
        }

        // 平均分配 ID
        int personCount = misList.size();
        for (int i = 0; i < sampledIds.size(); i++) {
            String mis = misList.get(i % personCount);
            result.get(mis).add(sampledIds.get(i));
        }

        return result;
    }

    @Override
    public Integer getDimensionType() {
        return DimensionTypeEnum.QUERY.getCode();
    }

    /**
     * 获取可分配标注信息
     *
     * @param param       请求参数
     * @param subTaskList 子任务列表
     * @return 可分配标注信息列表
     */
    private List<LabelingDetail> getLabelingDetailList(QualityCheckDistributeParam param, List<LabelingSubTask> subTaskList) {
        Map<Long, List<LabelingSubTask>> groupIdSubTaskMap = subTaskList.stream().collect(Collectors.groupingBy(LabelingSubTask::getGroupId));
        List<LabelingDetail> labelingDetailList = new ArrayList<>();
        // 每个分组中先选取一个子任务做随机分配，完成后统一录入所有分组数据
        for (QualityCheckDistributeParam.GroupConfig groupConfig : param.getGroupConfigList()) {
            if (groupConfig.getCount() == 0) {
                continue;
            }
            List<LabelingSubTask> groupSubTaskList = groupIdSubTaskMap.get(groupConfig.getGroupId());
            if (CollectionUtils.isEmpty(groupSubTaskList)) {
                continue;
            }
            LabelingSubTask labelingSubTask = groupSubTaskList.get(0);
            PageHelper.startPage(1, groupConfig.getCount());
            // 获取指定数量的可分配标注信息
            List<LabelingDetail> assignLabelingDetailList = labelingDetailRepository.listAssignLabelingDetail(labelingSubTask.getId());
            PageHelper.clearPage();
            if (CollectionUtils.isEmpty(assignLabelingDetailList)) {
                continue;
            }
            labelingDetailList.addAll(assignLabelingDetailList);
        }
        return labelingDetailList;
    }
}
