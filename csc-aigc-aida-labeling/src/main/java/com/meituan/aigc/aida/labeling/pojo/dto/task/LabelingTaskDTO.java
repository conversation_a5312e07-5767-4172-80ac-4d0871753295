package com.meituan.aigc.aida.labeling.pojo.dto.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 标注任务DTO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LabelingTaskDTO implements Serializable {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据来源
     */
    private String dataSetSource;

    /**
     * 数据量
     */
    private Integer dataTotalCount;

    /**
     * 标注状态
     */
    private Integer labelingStatus;

    /**
     * 质检状态
     */
    private Integer qualityCheckStatus;

    /**
     * 标注负责人Mis
     */
    private String responsiblePersonMis;

    /**
     * 标注负责人姓名
     */
    private String responsiblePersonName;

    /**
     * 标注任务类型 1:评测集标注 2:训练集标注 3:线上会话标注
     */
    private Integer taskType;

    /**
     * 分配类型 1:session 2:query
     */
    private Integer assignType;

    /**
     * 数据类型
     * @see com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType
     */
    private Integer dataType;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}

