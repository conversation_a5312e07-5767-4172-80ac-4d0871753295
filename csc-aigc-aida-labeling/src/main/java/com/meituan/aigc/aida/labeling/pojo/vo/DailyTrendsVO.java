package com.meituan.aigc.aida.labeling.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyTrendsVO {
    /**
     * 日期列表
     */
    @JsonFormat(pattern = "MM-dd")
    private List<String> dates;
    /**
     * 选定时间段的inspected量
     */
    private List<InspectedPoint> series;
    /**
     * 上个时间段的inspected量
     */
    private List<InspectedPoint> lastWeekSeries;
    /**
     * 周同比
     */
    private List<InspectedWeekPoint> weekOnWeekChange;
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InspectedPoint {
        /**
         * 业务名
         */
        private String name;
        /**
         * inspected数量
         */
        private List<Integer> data;
    }

    /**
     * 周同比
     */
    @Data
    public static class InspectedWeekPoint {
        /**
         * 业务名
         */
        private String name;
        /**
         * 周同比
         */
        private List<Double> data;
    }
}


