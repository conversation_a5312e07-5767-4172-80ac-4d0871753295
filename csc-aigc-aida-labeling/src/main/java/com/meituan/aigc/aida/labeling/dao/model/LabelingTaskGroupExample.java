package com.meituan.aigc.aida.labeling.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabelingTaskGroupExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LabelingTaskGroupExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNull() {
            addCriterion("group_name is null");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNotNull() {
            addCriterion("group_name is not null");
            return (Criteria) this;
        }

        public Criteria andGroupNameEqualTo(String value) {
            addCriterion("group_name =", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotEqualTo(String value) {
            addCriterion("group_name <>", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThan(String value) {
            addCriterion("group_name >", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("group_name >=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThan(String value) {
            addCriterion("group_name <", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThanOrEqualTo(String value) {
            addCriterion("group_name <=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLike(String value) {
            addCriterion("group_name like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotLike(String value) {
            addCriterion("group_name not like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameIn(List<String> values) {
            addCriterion("group_name in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotIn(List<String> values) {
            addCriterion("group_name not in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameBetween(String value1, String value2) {
            addCriterion("group_name between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotBetween(String value1, String value2) {
            addCriterion("group_name not between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Long value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Long value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Long value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Long value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Long value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Long> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Long> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Long value1, Long value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Long value1, Long value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusIsNull() {
            addCriterion("recycle_status is null");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusIsNotNull() {
            addCriterion("recycle_status is not null");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusEqualTo(Byte value) {
            addCriterion("recycle_status =", value, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusNotEqualTo(Byte value) {
            addCriterion("recycle_status <>", value, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusGreaterThan(Byte value) {
            addCriterion("recycle_status >", value, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("recycle_status >=", value, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusLessThan(Byte value) {
            addCriterion("recycle_status <", value, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusLessThanOrEqualTo(Byte value) {
            addCriterion("recycle_status <=", value, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusIn(List<Byte> values) {
            addCriterion("recycle_status in", values, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusNotIn(List<Byte> values) {
            addCriterion("recycle_status not in", values, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusBetween(Byte value1, Byte value2) {
            addCriterion("recycle_status between", value1, value2, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andRecycleStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("recycle_status not between", value1, value2, "recycleStatus");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountIsNull() {
            addCriterion("total_data_count is null");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountIsNotNull() {
            addCriterion("total_data_count is not null");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountEqualTo(Integer value) {
            addCriterion("total_data_count =", value, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountNotEqualTo(Integer value) {
            addCriterion("total_data_count <>", value, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountGreaterThan(Integer value) {
            addCriterion("total_data_count >", value, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_data_count >=", value, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountLessThan(Integer value) {
            addCriterion("total_data_count <", value, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountLessThanOrEqualTo(Integer value) {
            addCriterion("total_data_count <=", value, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountIn(List<Integer> values) {
            addCriterion("total_data_count in", values, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountNotIn(List<Integer> values) {
            addCriterion("total_data_count not in", values, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountBetween(Integer value1, Integer value2) {
            addCriterion("total_data_count between", value1, value2, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andTotalDataCountNotBetween(Integer value1, Integer value2) {
            addCriterion("total_data_count not between", value1, value2, "totalDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountIsNull() {
            addCriterion("recycle_data_count is null");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountIsNotNull() {
            addCriterion("recycle_data_count is not null");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountEqualTo(Integer value) {
            addCriterion("recycle_data_count =", value, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountNotEqualTo(Integer value) {
            addCriterion("recycle_data_count <>", value, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountGreaterThan(Integer value) {
            addCriterion("recycle_data_count >", value, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("recycle_data_count >=", value, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountLessThan(Integer value) {
            addCriterion("recycle_data_count <", value, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountLessThanOrEqualTo(Integer value) {
            addCriterion("recycle_data_count <=", value, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountIn(List<Integer> values) {
            addCriterion("recycle_data_count in", values, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountNotIn(List<Integer> values) {
            addCriterion("recycle_data_count not in", values, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountBetween(Integer value1, Integer value2) {
            addCriterion("recycle_data_count between", value1, value2, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andRecycleDataCountNotBetween(Integer value1, Integer value2) {
            addCriterion("recycle_data_count not between", value1, value2, "recycleDataCount");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateIsNull() {
            addCriterion("consistency_rate is null");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateIsNotNull() {
            addCriterion("consistency_rate is not null");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateEqualTo(Integer value) {
            addCriterion("consistency_rate =", value, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateNotEqualTo(Integer value) {
            addCriterion("consistency_rate <>", value, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateGreaterThan(Integer value) {
            addCriterion("consistency_rate >", value, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("consistency_rate >=", value, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateLessThan(Integer value) {
            addCriterion("consistency_rate <", value, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateLessThanOrEqualTo(Integer value) {
            addCriterion("consistency_rate <=", value, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateIn(List<Integer> values) {
            addCriterion("consistency_rate in", values, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateNotIn(List<Integer> values) {
            addCriterion("consistency_rate not in", values, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateBetween(Integer value1, Integer value2) {
            addCriterion("consistency_rate between", value1, value2, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andConsistencyRateNotBetween(Integer value1, Integer value2) {
            addCriterion("consistency_rate not between", value1, value2, "consistencyRate");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdIsNull() {
            addCriterion("recycle_sub_task_id is null");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdIsNotNull() {
            addCriterion("recycle_sub_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdEqualTo(Long value) {
            addCriterion("recycle_sub_task_id =", value, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdNotEqualTo(Long value) {
            addCriterion("recycle_sub_task_id <>", value, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdGreaterThan(Long value) {
            addCriterion("recycle_sub_task_id >", value, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("recycle_sub_task_id >=", value, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdLessThan(Long value) {
            addCriterion("recycle_sub_task_id <", value, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("recycle_sub_task_id <=", value, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdIn(List<Long> values) {
            addCriterion("recycle_sub_task_id in", values, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdNotIn(List<Long> values) {
            addCriterion("recycle_sub_task_id not in", values, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdBetween(Long value1, Long value2) {
            addCriterion("recycle_sub_task_id between", value1, value2, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andRecycleSubTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("recycle_sub_task_id not between", value1, value2, "recycleSubTaskId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}