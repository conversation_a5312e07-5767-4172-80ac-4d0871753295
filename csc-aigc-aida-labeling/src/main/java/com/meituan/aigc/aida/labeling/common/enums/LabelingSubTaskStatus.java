package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 16:44
 * @Version: 1.0
 */
@Getter
public enum LabelingSubTaskStatus {
    CREATING(1, "创建中"),
    IN_LABELING(2, "标注中"),
    WAITING_RECYCLED(3, "待回收"),
    RECYCLED(4, "已回收"),
    FAILED(5, "失败"),
    RE_LABELING(6, "复标中"),
    RE_LABELED(7, "复标完成"),
    ARCHIVED(8, "已失效"),
    ;

    private final int code;
    private final String value;

    LabelingSubTaskStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LabelingSubTaskStatus getByCode(int code) {
        for (LabelingSubTaskStatus status : LabelingSubTaskStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 可标注状态(标注中，待回收，已回收，复标中，复标完成)
     *
     * @return 状态
     */
    public static List<Integer> getLabelingStatus() {
        return Arrays.asList(IN_LABELING.getCode(), WAITING_RECYCLED.getCode(), RECYCLED.getCode(), RE_LABELING.getCode(), RE_LABELED.getCode());
    }


}
