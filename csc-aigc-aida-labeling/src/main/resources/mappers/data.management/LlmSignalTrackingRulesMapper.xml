<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.data.management.fetch.dao.mapper.LlmSignalTrackingRulesMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="channel" jdbcType="TINYINT" property="channel" />
    <result column="typical_question_ids" jdbcType="VARCHAR" property="typicalQuestionIds" />
    <result column="mis_ids" jdbcType="VARCHAR" property="misIds" />
    <result column="ac_interface" jdbcType="VARCHAR" property="acInterface" />
    <result column="aida_app_cfg" jdbcType="VARCHAR" property="aidaAppCfg" />
    <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, status, channel, typical_question_ids, mis_ids, ac_interface, aida_app_cfg, 
    creator_mis, creator_name, is_delete, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRulesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from llm_signal_tracking_rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from llm_signal_tracking_rules
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from llm_signal_tracking_rules
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRulesExample">
    delete from llm_signal_tracking_rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules" useGeneratedKeys="true" keyProperty="id">
    insert into llm_signal_tracking_rules (id, name, status, 
      channel, typical_question_ids, mis_ids, 
      ac_interface, aida_app_cfg, creator_mis, 
      creator_name, is_delete, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{channel,jdbcType=TINYINT}, #{typicalQuestionIds,jdbcType=VARCHAR}, #{misIds,jdbcType=VARCHAR}, 
      #{acInterface,jdbcType=VARCHAR}, #{aidaAppCfg,jdbcType=VARCHAR}, #{creatorMis,jdbcType=VARCHAR}, 
      #{creatorName,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules" useGeneratedKeys="true" keyProperty="id">
    insert into llm_signal_tracking_rules
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="typicalQuestionIds != null">
        typical_question_ids,
      </if>
      <if test="misIds != null">
        mis_ids,
      </if>
      <if test="acInterface != null">
        ac_interface,
      </if>
      <if test="aidaAppCfg != null">
        aida_app_cfg,
      </if>
      <if test="creatorMis != null">
        creator_mis,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=TINYINT},
      </if>
      <if test="typicalQuestionIds != null">
        #{typicalQuestionIds,jdbcType=VARCHAR},
      </if>
      <if test="misIds != null">
        #{misIds,jdbcType=VARCHAR},
      </if>
      <if test="acInterface != null">
        #{acInterface,jdbcType=VARCHAR},
      </if>
      <if test="aidaAppCfg != null">
        #{aidaAppCfg,jdbcType=VARCHAR},
      </if>
      <if test="creatorMis != null">
        #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRulesExample" resultType="java.lang.Long">
    select count(*) from llm_signal_tracking_rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update llm_signal_tracking_rules
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=TINYINT},
      </if>
      <if test="record.typicalQuestionIds != null">
        typical_question_ids = #{record.typicalQuestionIds,jdbcType=VARCHAR},
      </if>
      <if test="record.misIds != null">
        mis_ids = #{record.misIds,jdbcType=VARCHAR},
      </if>
      <if test="record.acInterface != null">
        ac_interface = #{record.acInterface,jdbcType=VARCHAR},
      </if>
      <if test="record.aidaAppCfg != null">
        aida_app_cfg = #{record.aidaAppCfg,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorMis != null">
        creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update llm_signal_tracking_rules
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      channel = #{record.channel,jdbcType=TINYINT},
      typical_question_ids = #{record.typicalQuestionIds,jdbcType=VARCHAR},
      mis_ids = #{record.misIds,jdbcType=VARCHAR},
      ac_interface = #{record.acInterface,jdbcType=VARCHAR},
      aida_app_cfg = #{record.aidaAppCfg,jdbcType=VARCHAR},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      creator_name = #{record.creatorName,jdbcType=VARCHAR},
      is_delete = #{record.isDelete,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules">
    update llm_signal_tracking_rules
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=TINYINT},
      </if>
      <if test="typicalQuestionIds != null">
        typical_question_ids = #{typicalQuestionIds,jdbcType=VARCHAR},
      </if>
      <if test="misIds != null">
        mis_ids = #{misIds,jdbcType=VARCHAR},
      </if>
      <if test="acInterface != null">
        ac_interface = #{acInterface,jdbcType=VARCHAR},
      </if>
      <if test="aidaAppCfg != null">
        aida_app_cfg = #{aidaAppCfg,jdbcType=VARCHAR},
      </if>
      <if test="creatorMis != null">
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules">
    update llm_signal_tracking_rules
    set name = #{name,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      channel = #{channel,jdbcType=TINYINT},
      typical_question_ids = #{typicalQuestionIds,jdbcType=VARCHAR},
      mis_ids = #{misIds,jdbcType=VARCHAR},
      ac_interface = #{acInterface,jdbcType=VARCHAR},
      aida_app_cfg = #{aidaAppCfg,jdbcType=VARCHAR},
      creator_mis = #{creatorMis,jdbcType=VARCHAR},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listEnableByMisIdsAndTypicalQuestionIds" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
    FROM llm_signal_tracking_rules
    <where>
      status = 2
      AND is_delete = 0
      <if test="staffMis != null">
        AND FIND_IN_SET(#{staffMis}, mis_ids) > 0
      </if>
      <if test="typicalQuestionId != null">
        AND FIND_IN_SET(#{typicalQuestionId}, typical_question_ids) > 0
      </if>
    </where>
  </select>
  <select id="listEnableByMisIdsAndTypicalQuestionIdsIsBlank" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM llm_signal_tracking_rules
    <where>
      status = 2
      AND is_delete = 0
      AND (typical_question_ids = null OR typical_question_ids = '')
      <if test="staffMis != null">
        AND FIND_IN_SET(#{staffMis}, mis_ids) > 0
      </if>
    </where>
  </select>

</mapper>