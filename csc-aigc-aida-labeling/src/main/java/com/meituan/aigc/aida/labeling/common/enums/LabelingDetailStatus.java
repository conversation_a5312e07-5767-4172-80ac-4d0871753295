package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 16:45
 * @Version: 1.0
 */
@Getter
public enum LabelingDetailStatus {
    WAITING_LABELING(1, "待标注"),
    LABELED(2, "已标注"),
    WAITING_RE_LABELING(3, "待复标"),
    RE_LABELED(4, "已复标"),
    ;

    private final int code;
    private final String value;

    LabelingDetailStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LabelingDetailStatus getByCode(int code) {
        for (LabelingDetailStatus status : LabelingDetailStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 待标注状态码
     * @return 结果
     */
    public static List<Integer> waitingLabelingCode(){
        return Arrays.asList(WAITING_LABELING.getCode(), WAITING_RE_LABELING.getCode());
    }

}
