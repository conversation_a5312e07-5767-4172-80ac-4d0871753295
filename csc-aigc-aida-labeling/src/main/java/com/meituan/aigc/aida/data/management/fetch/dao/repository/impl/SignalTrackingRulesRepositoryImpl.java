package com.meituan.aigc.aida.data.management.fetch.dao.repository.impl;

import com.dianping.zebra.util.StringUtils;
import com.meituan.aigc.aida.data.management.fetch.common.enums.LlmSignalTrackingRulesStatus;
import com.meituan.aigc.aida.data.management.fetch.dao.mapper.LlmSignalTrackingRulesMapper;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRulesExample;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.SignalTrackingRulesRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-03 11:13
 * @description 信号埋点规则仓储实现
 */
@Repository
public class SignalTrackingRulesRepositoryImpl implements SignalTrackingRulesRepository {
    @Resource
    private LlmSignalTrackingRulesMapper llmSignalTrackingRulesMapper;

    @Override
    public List<LlmSignalTrackingRules> listSignalTrackingRule(String name, String creatorName) {
        LlmSignalTrackingRulesExample example = new LlmSignalTrackingRulesExample();
        LlmSignalTrackingRulesExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        if (StringUtils.isNotBlank(creatorName)) {
            criteria.andCreatorNameLike("%" + creatorName + "%");
        }
        criteria.andIsDeleteEqualTo(Boolean.FALSE);
        example.setOrderByClause("status DESC, update_time DESC");
        return llmSignalTrackingRulesMapper.selectByExample(example);
    }

    @Override
    public LlmSignalTrackingRules getSignalTrackingRuleById(Long ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }
        return llmSignalTrackingRulesMapper.selectByPrimaryKey(ruleId);
    }

    @Override
    public void insertSignalTrackingRule(LlmSignalTrackingRules rule) {
        if (Objects.isNull(rule)) {
            return;
        }
        llmSignalTrackingRulesMapper.insertSelective(rule);
    }

    @Override
    public void updateSignalTrackingRule(LlmSignalTrackingRules rule) {
        if (Objects.isNull(rule)) {
            return;
        }
        llmSignalTrackingRulesMapper.updateByPrimaryKeySelective(rule);
    }

    @Override
    public void updateStatus(Long ruleId, Integer status) {
        if (Objects.isNull(ruleId)) {
            return;
        }
        LlmSignalTrackingRules rule = new LlmSignalTrackingRules();
        rule.setId(ruleId);
        rule.setStatus(status);
        rule.setUpdateTime(new Date());
        llmSignalTrackingRulesMapper.updateByPrimaryKeySelective(rule);
    }

    @Override
    public List<LlmSignalTrackingRules> listEnableByMisIdsAndTypicalQuestionIds(String staffMis, String typicalQuestionId, Integer channel) {
        return llmSignalTrackingRulesMapper.listEnableByMisIdsAndTypicalQuestionIds(staffMis, typicalQuestionId, channel);
    }

    @Override
    public List<LlmSignalTrackingRules> listEnableByMisIdsAndTypicalQuestionIdsIsBlank(String staffMis, Integer channel) {
        return llmSignalTrackingRulesMapper.listEnableByMisIdsAndTypicalQuestionIdsIsBlank(staffMis, channel);
    }

    @Override
    public List<LlmSignalTrackingRules> listAllEnabledRules() {
        LlmSignalTrackingRulesExample example = new LlmSignalTrackingRulesExample();
        LlmSignalTrackingRulesExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo((byte) LlmSignalTrackingRulesStatus.ENABLE.getCode()); // 状态为启用
        criteria.andIsDeleteEqualTo(Boolean.FALSE); // 未删除
        return llmSignalTrackingRulesMapper.selectByExample(example);
    }
}
