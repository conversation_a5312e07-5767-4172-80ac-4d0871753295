package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.Data;

/**
 * 清洗任务DTO
 */
@Data
public class DataProcessTaskDTO {
    /**
     * 清洗任务ID
     */
    private Long id;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 清洗状态: 1-待处理 2-数据打标中 3-处理完成
     */
    private Integer taskStatus;
    
    /**
     * 源数据集Id
     */
    private Long sourceDatasetId;
    
    /**
     * 源数据集名称
     */
    private String soucreDatasetName;
    
    /**
     * 源数据集版本Id
     */
    private Long sourceDatasetVersionId;
    
    /**
     * 源数据集版本名称
     */
    private String soucreDatasetVersionName;
    
    /**
     * 清洗后的数据集版本ID
     */
    private Long processedDatasetVersionId;
    
    /**
     * 清洗后的数据集版本名称
     */
    private String processedDatasetVersionName;
    
    /**
     * 创建人
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    private String createTime;
}
