package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON>
 * @Create: 2025/2/28 16:43
 * @Version: 1.0
 */
@Getter
public enum LabelingTaskStatus {
    CREATE(1, "创建中"),
    WAITING_ASSIGNED(2, "待分配"),
    ASSIGNING(3, "分配中"),
    IN_LABELING(4, "标注中"),
    COMPLETE(5, "标注完成"),
    FAILED(7, "失败");

    private final int code;
    private final String value;

    LabelingTaskStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LabelingTaskStatus getByCode(int code) {
        for (LabelingTaskStatus status : LabelingTaskStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
