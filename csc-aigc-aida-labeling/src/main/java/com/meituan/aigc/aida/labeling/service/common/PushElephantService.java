package com.meituan.aigc.aida.labeling.service.common;

import com.alibaba.fastjson.JSON;
import com.meituan.csc.aigc.runtime.api.AidaRobotXmMessageRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class PushElephantService {

    @Autowired
    private AidaRobotXmMessageRemoteService aidaRobotXmMessageRemoteService;

    public void pushElephant(String message, String mis) {
        pushElephant(message, Collections.singletonList(mis));
    }

    public void pushElephant(String message, List<String> misList) {
        try {
            aidaRobotXmMessageRemoteService.aidaRobotPushDirectMessage(message, JSON.toJSONString(misList));
        } catch (Exception e) {
            log.error("大象消息推送失败, message:{}, misIdList:{}", message, JSON.toJSONString(misList), e);
        }
    }
}
