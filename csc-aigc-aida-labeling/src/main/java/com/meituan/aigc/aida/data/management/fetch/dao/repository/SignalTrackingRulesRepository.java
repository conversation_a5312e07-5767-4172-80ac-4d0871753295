package com.meituan.aigc.aida.data.management.fetch.dao.repository;

import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;

import java.util.List;

/**
 * 信号埋点规则表
 *
 * <AUTHOR>
 * @date 2025-04-03 11:08
 * @description
 */
public interface SignalTrackingRulesRepository {
    /**
     * 获获取埋点规则列表
     *
     * @param name 埋点规则名称
     * @return 埋点规则列表
     */
    List<LlmSignalTrackingRules> listSignalTrackingRule(String name);

    /**
     * 获取埋点规则详情
     *
     * @param ruleId 埋点规则ID
     * @return 埋点规则详情
     */
    LlmSignalTrackingRules getSignalTrackingRuleById(Long ruleId);

    /**
     * 插入埋点规则
     *
     * @param rule
     */
    void insertSignalTrackingRule(LlmSignalTrackingRules rule);

    /**
     * 更新埋点规则
     *
     * @param rule 埋点规则信息
     */
    void updateSignalTrackingRule(LlmSignalTrackingRules rule);


    /**
     * 更新埋点规则状态
     *
     * @param ruleId 埋点规则ID
     * @param status 状态
     * @return 更新数量
     */
    void updateStatus(Long ruleId, Integer status);

    /**
     * 根据客服Mis和标问Id查询规则
     * @param staffMis 客服Mis
     * @param typicalQuestionId 标问Id
     * @return 满足条件的规则
     */
    List<LlmSignalTrackingRules> listEnableByMisIdsAndTypicalQuestionIds(String staffMis, String typicalQuestionId);

    /**
     * 根据Mis查询标问Id为空的规则
     * @param staffMis 客服Mis
     * @return 满足条件的规则
     */
    List<LlmSignalTrackingRules> listEnableByMisIdsAndTypicalQuestionIdsIsBlank(String staffMis);
}
