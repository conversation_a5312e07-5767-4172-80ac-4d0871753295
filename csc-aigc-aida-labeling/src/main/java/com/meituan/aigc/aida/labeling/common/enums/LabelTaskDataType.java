package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON>
 * @Create: 2025/2/28 16:42
 * @Version: 1.0
 */
@Getter
public enum LabelTaskDataType {
    MODEL_EVALUATION(1, "模型评测"),
    AGENT_EVALUATION(2, "Agent评测]"),
    SFT_FINE_TUNING(3, "有监督微调(SFT)"),
    DPO_ALIGNMENT(4, "偏好对齐(DPO)"),
    SESSION_ANNOTATION(5, "Session维度"),
    QUERY_ANNOTATION(6, "Query维度");

    private final int code;
    private final String value;

    LabelTaskDataType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public String toString() {
        return "DataType{" +
                "code=" + code +
                ", value='" + value + '\'' +
                '}';
    }

    // 根据code获取对应的枚举常量
    public static LabelTaskDataType getByCode(int code) {
        for (LabelTaskDataType dataType : LabelTaskDataType.values()) {
            if (dataType.getCode() == code) {
                return dataType;
            }
        }
        return null;
    }
}
