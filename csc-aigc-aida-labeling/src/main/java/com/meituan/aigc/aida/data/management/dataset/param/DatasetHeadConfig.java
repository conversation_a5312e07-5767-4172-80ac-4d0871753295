package com.meituan.aigc.aida.data.management.dataset.param;

import com.meituan.aigc.aida.data.management.dataset.dto.DataContentFieldDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
public class DatasetHeadConfig implements Serializable {

    private List<HeadConfig> headConfig;

    /**
     * 数据集头信息
     */
    private List<DataContentFieldDTO> headList;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HeadConfig implements Serializable {
        /**
         * 存储类型 text/flatten
         */
        private String storeType;
        /**
         * 最大计数
         */
        private int maxIndex;
    }
}
