package com.meituan.aigc.aida.benchmark.dao.repository.impl;

import com.meituan.aigc.aida.benchmark.dao.mapper.BenchmarkLeaderboardDetailsMapper;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetailsExample;
import com.meituan.aigc.aida.benchmark.dao.repository.LeaderboardDetailsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Benchmark排行榜详情Repository实现类
 *
 * <AUTHOR>
 * @description Benchmark排行榜详情数据访问层实现
 * @date 2025/6/25
 */
@Slf4j
@Repository
public class LeaderboardDetailsRepositoryImpl implements LeaderboardDetailsRepository {

    @Resource
    private BenchmarkLeaderboardDetailsMapper benchmarkLeaderboardDetailsMapper;

    @Override
    public int batchInsert(List<BenchmarkLeaderboardDetails> details) {
        if (CollectionUtils.isEmpty(details)) {
            log.warn("批量插入排行榜详情数据为空，跳过操作");
            return 0;
        }

        return benchmarkLeaderboardDetailsMapper.batchInsert(details);
    }

    @Override
    public List<BenchmarkLeaderboardDetails> listByVersionIdAndModelName(Long versionId, String modelName) {
        if (Objects.isNull(versionId) || StringUtils.isBlank(modelName)) {
            log.warn("查询参数不完整，versionId：{}，modelName：{}", versionId, modelName);
            return Collections.emptyList();
        }

        try {
            BenchmarkLeaderboardDetailsExample example = new BenchmarkLeaderboardDetailsExample();
            BenchmarkLeaderboardDetailsExample.Criteria criteria = example.createCriteria();
            criteria.andVersionIdEqualTo(versionId)
                    .andModelNameEqualTo(modelName)
                    .andIsDeletedEqualTo(Boolean.FALSE);
            
            return benchmarkLeaderboardDetailsMapper.selectByExample(example);
        } catch (Exception e) {
            log.error("查询排行榜详情失败，versionId：{}，modelName：{}，错误信息：{}", 
                    versionId, modelName, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<BenchmarkLeaderboardDetails> listByVersionId(Long versionId) {
        if (Objects.isNull(versionId)) {
            log.warn("版本ID不能为空");
            return Collections.emptyList();
        }

        try {
            BenchmarkLeaderboardDetailsExample example = new BenchmarkLeaderboardDetailsExample();
            BenchmarkLeaderboardDetailsExample.Criteria criteria = example.createCriteria();
            criteria.andVersionIdEqualTo(versionId)
                    .andIsDeletedEqualTo(Boolean.FALSE);
            
            return benchmarkLeaderboardDetailsMapper.selectByExample(example);
        } catch (Exception e) {
            log.error("根据版本ID查询排行榜详情失败，versionId：{}，错误信息：{}", versionId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public int deleteByVersionIdAndModelName(Long versionId, String modelName) {
        if (Objects.isNull(versionId) || StringUtils.isBlank(modelName)) {
            log.warn("删除参数不完整，versionId：{}，modelName：{}", versionId, modelName);
            return 0;
        }

        try {
            BenchmarkLeaderboardDetailsExample example = new BenchmarkLeaderboardDetailsExample();
            BenchmarkLeaderboardDetailsExample.Criteria criteria = example.createCriteria();
            criteria.andVersionIdEqualTo(versionId)
                    .andModelNameEqualTo(modelName);
            
            int deletedCount = benchmarkLeaderboardDetailsMapper.deleteByExample(example);
            log.info("删除排行榜详情数据，versionId：{}，modelName：{}，删除数量：{}", 
                    versionId, modelName, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除排行榜详情失败，versionId：{}，modelName：{}，错误信息：{}", 
                    versionId, modelName, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int deleteById(Long id) {
        if (Objects.isNull(id)) {
            log.warn("删除记录的ID不能为空");
            return 0;
        }

        try {
            int deletedCount = benchmarkLeaderboardDetailsMapper.deleteByPrimaryKey(id);
            log.info("根据ID删除排行榜详情数据，id：{}，删除数量：{}", id, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("根据ID删除排行榜详情失败，id：{}，错误信息：{}", id, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int insert(BenchmarkLeaderboardDetails detail) {
        if (Objects.isNull(detail)) {
            log.warn("插入的排行榜详情数据不能为空");
            return 0;
        }

        try {
            int result = benchmarkLeaderboardDetailsMapper.insertSelective(detail);
            if (result > 0) {
                log.debug("插入排行榜详情成功，模型：{}，版本ID：{}", detail.getModelName(), detail.getVersionId());
            }
            return result;
        } catch (Exception e) {
            log.error("插入排行榜详情失败，模型：{}，版本ID：{}，错误信息：{}", 
                    detail.getModelName(), detail.getVersionId(), e.getMessage(), e);
            return 0;
        }
    }
}
