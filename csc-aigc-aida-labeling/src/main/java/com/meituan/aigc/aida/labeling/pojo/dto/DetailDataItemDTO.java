package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2025-03-03 16:29
 * @description 标注详情数据项
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DetailDataItemDTO {

    /**
     * 详情Id
     */
    private Long id;
    /**
     * 数据id
     */
    private Long dataId;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 是否已存储上下文
     */
    private Boolean hasContext;
    /**
     * 会话时间
     */
    private String sessionTime;

    /**
     * 大模型消息ID
     */
    private String messageId;
    /**
     * 大模型消息内容
     */
    private String messageContent;

    /**
     * 三方消息ID
     */
    private String thirdMessageId;

    /**
     * extra_info
     */
    private String extraInfo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 标注人mis
     */
    private String labelerMis;

    /**
     * 标注人名称
     */
    private String labelerName;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 原始数据内容
     */
    private String rawDataContent;

    /**
     * 原始数据表头
     */
    private String rawDataHeaders;
    /**
     * 原始数据内容映射内容JSON
     */
    private String rawDataMappedContent;
    /**
     * 自定义标注项JSON
     */
    private String labelingItemsResult;
}
