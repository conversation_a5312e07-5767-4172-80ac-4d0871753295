package com.meituan.aigc.aida.data.management.fetch.controller;

import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchRecordDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskDTO;
import com.meituan.aigc.aida.data.management.fetch.param.DataFetchTaskCreateParam;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;
import com.meituan.aigc.aida.data.management.fetch.service.DataFetchTaskService;
import com.meituan.aigc.aida.data.management.fetch.vo.RobotVO;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/7 15:58
 * @Version: 1.0
 */
@RestController
@RequestMapping("/api/v1/data-fetch")
public class DataFetchTaskController {

    @Autowired
    private DataFetchTaskService dataFetchTaskService;


    /**
     * 查询信号埋点规则机器人列表
     *
     * @param query 请求参数
     * @return 机器人列表
     */
    @PostMapping("/robots")
    public Result<List<RobotVO>> listRobotByParam(@RequestBody RobotListQuery query) {
        return Result.ok(dataFetchTaskService.listRobotByParam(query));
    }

    /**
     * 创建数据获取任务
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/create")
    public Result<?> createTask(@RequestBody DataFetchTaskCreateParam param) {
        dataFetchTaskService.createTask(param);
        return Result.create();
    }

    /**
     * 任务列表查询
     *
     * @param taskName 任务名称
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 任务列表
     */
    @GetMapping("/tasks")
    public Result<PageData<DataFetchTaskDTO>> listTask(@RequestParam(value = "taskName", required = false) String taskName,
                                                       @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                       @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return Result.ok(dataFetchTaskService.pageDataFetchTaskList(taskName, pageNum, pageSize));
    }

    /**
     * 任务详情查询
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/task/detail")
    public Result<DataFetchTaskDTO> getTaskDetail(@RequestParam("taskId") Long taskId) {
        return Result.ok(dataFetchTaskService.getDataFetchTaskDetail(taskId));
    }

    /**
     * 任务记录查询
     *
     * @param taskId   任务ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return
     */
    @GetMapping("/records")
    public Result<PageData<DataFetchRecordDTO>> getTaskRecord(@RequestParam(value = "taskId", required = false) Long taskId,
                                                              @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                              @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                              @RequestParam(value = "inputs", required = false) String inputs) {
        return Result.ok(dataFetchTaskService.pageDataFetchRecordList(taskId, pageNum, pageSize, inputs));
    }

    /**
     * 导出数据记录
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @GetMapping("/records/export")
    public Result<?> exportDataFetchRecord(@RequestParam("taskId") Long taskId) {
        dataFetchTaskService.exportDataFetchRecord(taskId);
        return Result.create();
    }

    /**
     * 删除数据获取任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @DeleteMapping("/task/delete")
    public Result<?> deleteTask(@RequestParam("taskId") Long taskId) {
        dataFetchTaskService.deleteDataFetchTask(taskId);
        return Result.create();
    }
}
