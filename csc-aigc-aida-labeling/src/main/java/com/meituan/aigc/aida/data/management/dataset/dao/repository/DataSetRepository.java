package com.meituan.aigc.aida.data.management.dataset.dao.repository;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-26 11:52
 * @description 数据集仓储层
 */
public interface DataSetRepository {

    /**
     * 根据ID获取数据集
     */
    DataSet getDataSetById(Long dataSetId);

    /**
     * 创建新数据集
     */
    Long createDataSet(DataSet dataSet);

    /**
     * 更新数据集
     */
    void updateDataSet(DataSet dataSet);

    /**
     * 查询数据集列表
     */
    List<DataSet> listDataSetByName(String name);
}
