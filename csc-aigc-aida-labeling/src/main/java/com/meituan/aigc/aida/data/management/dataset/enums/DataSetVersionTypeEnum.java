package com.meituan.aigc.aida.data.management.dataset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-06-03 14:19
 * @description 数据集版本类型枚举
 */
@Getter
@AllArgsConstructor
public enum DataSetVersionTypeEnum {
    /**
     * 版本类型 1:使用已有版本继承/2:使用已有版本合并/3:从错题集导入/4:创建任务/5:标注结果回收/6:数据处理/7:洞察与分析导出
     */
    INHERIT(1, "使用已有版本继承"),
    MERGE(2, "使用已有版本合并"),
    IMPORT(3, "从错题集导入"),
    CREATE_TASK(4, "创建任务"),
    RECYCLE(5, "标注结果回收"),
    DATA_PROCESS(6, "数据处理"),
    INSIGHT_EXPORT(7, "洞察与分析导出"),
    ;

    private final Integer code;
    private final String desc;
}
