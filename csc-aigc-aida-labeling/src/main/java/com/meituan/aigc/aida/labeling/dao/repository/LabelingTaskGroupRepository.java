package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelTaskGroupDTO;

import java.util.List;


/**
 * @Author: guo<PERSON>hui
 * @Create: 2025/2/28 11:09
 * @Version: 1.0
 */
public interface LabelingTaskGroupRepository {
    /**
     * 根据任务ID查询分组列表
     *
     * @param taskId
     * @return
     */
    List<LabelTaskGroupDTO> listLabelGroupByTaskId(Long taskId);

    /**
     * 根据分组ID查询分组信息
     *
     * @param groupId
     * @return
     */
    LabelingTaskGroup getLabelGroupById(Long groupId);

    /**
     * 添加分组数据
     *
     * @param labelingTaskGroup 分组实体
     */
    void insertLabelingTaskGroup(LabelingTaskGroup labelingTaskGroup);

    /**
     * 更新分组状态
     *
     * @param group
     * @return
     */
    void updateGroup(LabelingTaskGroup group);

    /**
     * 查询任务所有分组
     *
     * @param taskId 任务ID
     * @return 分组内容
     */
    List<LabelingTaskGroup> listAllLabelGroupByTaskId(Long taskId);

    /**
     * 根据分组ID更新分组名称
     *
     * @param groupId
     * @param groupName
     */
    void updateGroupNameById(Long groupId, String groupName);

    /**
     * 根据任务ID更新分组状态
     *
     * @param taskId 任务ID
     * @param status 分组状态
     */
    void updateGroupStatusByTaskId(Long taskId, Integer status);

    /**
     * 插入分组
     *
     * @param group 分组实体
     */
    void insert(LabelingTaskGroup group);

    /**
     * 根据主键更新
     * @param labelingTaskGroup 更新内容
     */
    void updateById(LabelingTaskGroup labelingTaskGroup);
}
