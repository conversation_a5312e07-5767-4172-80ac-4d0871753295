package com.meituan.aigc.aida.data.management.dataset.strategy.field.impl;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 文本字段处理策略
 */
@Component
public class TextFieldStrategy extends AbstractDataFieldStrategy {

    @Override
    public Integer getStrategyType() {
        return FieldTypeEnum.TEXT.getCode();
    }

    @Override
    public void validateValue(String value) {
        // 文本类型不做特殊校验
    }

    @Override
    public void processFieldData(String columnName, String value,
                                Map<String, Object> textData,
                                Map<String, Object> flattenedData,
                                Map<String, Date> dateData,
                                Map<String, Object> nestedData) {
        // 文本类型：不做加工，直接存储
        textData.put(columnName, value);
    }
} 