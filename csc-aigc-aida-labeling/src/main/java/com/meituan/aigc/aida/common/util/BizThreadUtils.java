package com.meituan.aigc.aida.common.util;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.rhino.threadpool.component.ShutdownPolicy;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.Objects;
import java.util.concurrent.*;

/**
 * 业务线程池工具类
 *
 * <AUTHOR>
 */
public class BizThreadUtils {

    private static final ConcurrentMap<String, ListeningExecutorService> THREAD_POOL_CACHE = new ConcurrentHashMap<>();

    /**
     * rhino管理线程池优势：动态参数调整、mtrace跨线程传递、cat埋点、线程池监控
     * <a href="https://km.sankuai.com/page/**********">Rhino线程池最佳实践</a>
     *
     * @param rhinoKey         rhinoKey
     * @param coreSize         coreSize
     * @param maxSize          maxSize
     * @param queueSize        queueSize
     * @param keepAliveTime    keepAliveTime
     * @param timeUnit         timeUnit
     * @param threadNameFormat threadNameFormat
     * @return ExecutorService
     */
    public static ExecutorService createRhinoThreadPool(String rhinoKey, int coreSize, int maxSize, int queueSize, int keepAliveTime, TimeUnit timeUnit, String threadNameFormat) {
        ListeningExecutorService executorService = THREAD_POOL_CACHE.get(rhinoKey);
        if (Objects.nonNull(executorService)) {
            return executorService;
        }
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("rhino-" + threadNameFormat)
                .setDaemon(true)
                .build();
        DefaultThreadPoolProperties.Setter setter = DefaultThreadPoolProperties.Setter()
                .withCoreSize(coreSize).withMaxSize(maxSize).withMaxQueueSize(queueSize)
                .withKeepAliveTimeMinutes(keepAliveTime).withKeepAliveTimeUnit(timeUnit)
                .withShutdownPolicy(ShutdownPolicy.SHUTDOWN_GRACEFULLY)
                .withRejectHandler(new ThreadPoolExecutor.AbortPolicy()).withThreadFactory(threadFactory);
        ThreadPool threadPool = Rhino.newThreadPool(rhinoKey, setter);
        ThreadPoolExecutor executor = threadPool.getExecutor();
        ListeningExecutorService listeningExecutorService = MoreExecutors.listeningDecorator(executor);
        // 避免覆盖
        ListeningExecutorService oldValue = THREAD_POOL_CACHE.putIfAbsent(rhinoKey, listeningExecutorService);
        // 第一次初始化
        if (Objects.isNull(oldValue)) {
            MoreExecutors.addDelayedShutdownHook(listeningExecutorService, 10, TimeUnit.SECONDS);
            return listeningExecutorService;
        }
        return oldValue;
    }
}
