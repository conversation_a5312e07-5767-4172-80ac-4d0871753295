package com.meituan.aigc.aida.data.management.dataset.service.impl;

import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dto.*;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetFieldUtils;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.meituan.aigc.aida.data.management.dataset.service.DatasetAnalysisService;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetQueryConditionUtils;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.data.management.es.query.DatasetPageResult;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetQueryCondition;
import com.meituan.aigc.aida.labeling.remote.common.enums.ConditionTypeEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetVersionService;
import com.meituan.aigc.aida.data.management.dataset.enums.DataSetVersionTypeEnum;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.aigc.aida.data.management.dataset.common.constants.DatasetCommonConstants.*;


/**
 * 数据集分析服务实现
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集分析相关业务逻辑实现
 */
@Slf4j
@Service
public class DatasetAnalysisServiceImpl implements DatasetAnalysisService {

    @Resource
    private DataSetVersionRepository dataSetVersionRepository;

    @Resource
    private DatasetEsIndexService datasetEsIndexService;

    @Resource
    private DataSetVersionService dataSetVersionService;

    @Resource
    private DataSetRepository dataSetRepository;

    @Resource
    private DataManagementLionConfig dataLion;


    @Override
    public DatasetDistributeResponseDTO queryDatasetDistribute(DatasetDistributeQueryParam param) {
        log.info("开始查询数据集分布情况, 参数: {}", param);
        List<String> columnPathParts = new ArrayList<>();
        columnPathParts.add(param.getColumnName());

        try {
            // 1. 获取数据集版本信息
            DataSetVersion datasetVersion = getDatasetVersion(param.getDatasetId(), param.getDatasetVersionId());
            // 2. 获取需要统计的字段
            Map<String, FieldMappingDTO> fieldTypeMap = DatasetFieldUtils.convertHeadConfig2FieldMapping(datasetVersion.getHeadConfig());
            Set<String> commonFields = DatasetFieldUtils.getCommonFieldsFromLion();
            DatasetFieldPathInfo fieldPathInfo = DatasetFieldUtils.buildFieldPathInfo(columnPathParts, fieldTypeMap, commonFields);
            DatasetEsQueryCondition datasetEsQueryCondition = new DatasetEsQueryCondition();

            // 3.构建基础查询参数
            buildBasicSearchCondition(datasetEsQueryCondition, datasetVersion, fieldPathInfo);

            // 调用ES查询服务
            DatasetPageResult pageResult = datasetEsIndexService.pageQuery(datasetEsQueryCondition);

            // 4. 解析聚合查询结果并组装返回数据
            DatasetDistributeResponseDTO responseDTO = parseAggregationResult(pageResult, datasetVersion.getDataCount());
            log.info("查询数据集分布情况完成, 参数: {}, 结果项数: {}", param, responseDTO.getResultList().size());
            return responseDTO;

        } catch (Exception e) {
            log.error("查询数据集分布情况失败, 参数: {}", param, e);
            throw new RuntimeException("查询数据集分布情况失败", e);
        }
    }

    @Override
    public DatasetFieldListResponseDTO querySubFields(DatasetDistributeQueryParam param) {
        // 1. 获取数据集版本信息
        DataSetVersion datasetVersion = getDatasetVersion(param.getDatasetId(), param.getDatasetVersionId());
        CheckUtil.paramCheck(Objects.nonNull(datasetVersion) && StringUtils.isNotBlank(datasetVersion.getHeadConfig()), "数据集版本信息为空");

        // 2. 获取表头信息
        Map<String, FieldMappingDTO> fieldTypeMap = DatasetFieldUtils.convertHeadConfig2FieldMapping(datasetVersion.getHeadConfig());
        CheckUtil.paramCheck(MapUtils.isNotEmpty(fieldTypeMap) && fieldTypeMap.containsKey(param.getColumnName()), "表头信息为空或一级字段不存在");

        // 3. 构建返回结果
        FieldMappingDTO fieldMappingDTO = fieldTypeMap.get(param.getColumnName());
        DatasetFieldListResponseDTO responseDTO = new DatasetFieldListResponseDTO();
        responseDTO.setFieldList(fieldMappingDTO.getFirstLevelJsonKey());

        log.info("查询一级字段下的二级字段列表完成, 参数: {}, 二级字段数量: {}", param, CollectionUtils.isNotEmpty(fieldMappingDTO.getFirstLevelJsonKey()) ? fieldMappingDTO.getFirstLevelJsonKey().size() : 0);
        return responseDTO;
    }

    @Override
    public DatasetFilterPageResponseDTO queryDatasetPage(DatasetFilterPageQueryParam param) {
        // 1. 获取数据集版本信息
        DataSetVersion datasetVersion = getDatasetVersion(param.getDatasetId(), param.getDatasetVersionId());

        // 2. 获取表头信息
        Map<String, FieldMappingDTO> fieldTypeMap = DatasetFieldUtils.convertHeadConfig2FieldMapping(datasetVersion.getHeadConfig());

        // 3. 分离需要内存过滤的条件和ES查询条件
        List<DataSetQueryCondition> memoryFilterConditions = new ArrayList<>();
        List<DataSetQueryCondition> esQueryConditions = new ArrayList<>();

        Set<String> commonFields = DatasetFieldUtils.getCommonFieldsFromLion();
        for (DataSetQueryCondition queryCondition : param.getConditionList()) {
            DatasetFieldPathInfo pathInfo = DatasetFieldUtils.buildFieldPathInfo(queryCondition.getColumnName(), fieldTypeMap, commonFields);

            // 判断是否为需要内存过滤的条件：CONTAIN类型且使用扁平化路径
            if (ConditionTypeEnum.CONTAIN.equals(ConditionTypeEnum.getByCode(queryCondition.getConditionType()))
                    && pathInfo != null
                    && DatasetQueryConditionUtils.isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
                memoryFilterConditions.add(queryCondition);
                log.info("检测到需要内存过滤的CONTAIN条件: 字段={}", queryCondition.getColumnName());
            } else {
                esQueryConditions.add(queryCondition);
            }
        }

        // 4. 构建ES查询条件（只包含可以在ES中处理的条件）
        DatasetEsQueryCondition condition = new DatasetEsQueryCondition();
        DatasetFilterPageQueryParam esQueryParam = new DatasetFilterPageQueryParam();
        esQueryParam.setDatasetId(param.getDatasetId());
        esQueryParam.setDatasetVersionId(param.getDatasetVersionId());
        esQueryParam.setConditionList(esQueryConditions);
        esQueryParam.setColumnList(param.getColumnList());

        // 如果有内存过滤条件，需要获取更多数据进行内存过滤
        if (CollectionUtils.isNotEmpty(memoryFilterConditions)) {
            // 设置更大的页面大小以获取足够的数据进行内存过滤
            esQueryParam.setPageSize(ES_MAX_RESULT_WINDOW);
            log.info("检测到内存过滤条件，调整ES查询页面大小为: {}", esQueryParam.getPageSize());
        } else {
            // 没有内存过滤条件，使用原始分页参数
            esQueryParam.setPageNum(param.getPageNum() - 1);
            esQueryParam.setPageSize(param.getPageSize());
        }

        // 4.1 设置基本查询条件
        buildDataFilterSearchCondition(esQueryParam, condition, fieldTypeMap, datasetVersion);

        // 4.2 设置是否启用滚动查询
        if (CollectionUtils.isNotEmpty(memoryFilterConditions)) {
            condition.setScrollTime("2m");
        }

        // 4.3 设置返回字段
        configureReturnFields(condition, param.getColumnList(), fieldTypeMap);

        // 5. 执行ES查询
        DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);

        // 6. 如果有内存过滤条件，进行内存过滤和分页
        if (CollectionUtils.isNotEmpty(memoryFilterConditions)) {
            pageResult = applyMemoryFilterAndPagination(pageResult, memoryFilterConditions, fieldTypeMap, param, condition);
        }

        // 7. 构建返回结果
        Map<String, DataContentFieldDTO> esColumnNameMap = DatasetFieldUtils.getEsFieldMapping(datasetVersion.getHeadConfig());
        DatasetFilterPageResponseDTO responseDTO = buildFilterPageResponse(pageResult, param, esColumnNameMap);

        log.info("数据集筛选分页查询完成, 参数: {}, 返回数据条数: {}, 内存过滤条件数: {}",
                param,
                responseDTO.getData() != null ? responseDTO.getData().size() : 0,
                memoryFilterConditions.size());
        return responseDTO;
    }

    @Override
    public void saveDataset(DatasetSaveParam param) {
        log.info("开始保存数据集, 参数: {}", param);

        try {
            // 1. 参数校验
            if (StringUtils.isBlank(param.getNewVersionName())) {
                log.error("保存数据集失败: 新版本名称为空, 参数: {}", param);
                throw new RuntimeException("新版本名称不能为空");
            }

            // 2. 获取历史版本信息，验证版本是否存在
            DataSetVersion dataSetVersion = getDatasetVersion(param.getDatasetId(), param.getHistoryVersionId());

            // 3. 构建新增版本版本信息
            DataSetCopyVersionParam copyVersionParam = new DataSetCopyVersionParam();
            copyVersionParam.setDataSetId(param.getDatasetId());
            copyVersionParam.setVersionId(param.getHistoryVersionId());
            copyVersionParam.setVersionName(param.getNewVersionName());
            copyVersionParam.setVersionType(DataSetVersionTypeEnum.INSIGHT_EXPORT.getCode());
            copyVersionParam.setVersionDescription("洞察与分析导出版本");

            // 4. 设置筛选条件
            if (CollectionUtils.isNotEmpty(param.getConditionList())) {
                copyVersionParam.setConditionList(param.getConditionList());
                copyVersionParam.setFieldTypeMap(DatasetFieldUtils.convertHeadConfig2FieldMapping(dataSetVersion.getHeadConfig()));
                log.info("设置筛选条件, 条件数量: {}", param.getConditionList().size());
            }

            // 5. 设置保存的列
            if (CollectionUtils.isNotEmpty(param.getColumnList())) {
                copyVersionParam.setSavedColumnList(param.getColumnList());
                log.info("设置保存列, 列数量: {}", param.getColumnList().size());
            }

            // 6. 执行复制版本操作
            Long newVersionId = dataSetVersionService.copyVersion(copyVersionParam);

            log.info("保存数据集成功, datasetId: {}, historyVersionId: {}, newVersionId: {}, newVersionName: {}",
                    param.getDatasetId(), param.getHistoryVersionId(), newVersionId, param.getNewVersionName());

        } catch (Exception e) {
            log.error("保存数据集失败, 参数: {}", param, e);
            throw new RuntimeException("保存数据集失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建数据筛选查询条件
     *
     * @param condition      查询条件对象
     * @param fieldTypeMap   字段类型映射
     * @param datasetVersion 数据集版本信息
     */
    private void buildDataFilterSearchCondition(DatasetFilterPageQueryParam param, DatasetEsQueryCondition condition, Map<String, FieldMappingDTO> fieldTypeMap, DataSetVersion datasetVersion) {
        if (Objects.isNull(datasetVersion) || MapUtils.isEmpty(fieldTypeMap)) {
            return;
        }
        // 设置基本查询参数
        condition.setPageNum(param.getPageNum());
        condition.setPageSize(param.getPageSize());
        condition.setIndexName(datasetVersion.getEsIndexName());
        condition.addTermCondition(CommonConstants.BaseEsIndex.VERSION_ID, CommonConstants.BaseEsIndex.VERSION_ID, datasetVersion.getId().toString());
        condition.addTermCondition(CommonConstants.BaseEsIndex.DATASET_ID, CommonConstants.BaseEsIndex.DATASET_ID, datasetVersion.getDataSetId().toString());
        condition.addTermCondition(CommonConstants.BaseEsIndex.DATA_SOURCE, CommonConstants.BaseEsIndex.DATA_SOURCE, String.valueOf(getDataSource(param.getDatasetId())));

        Set<String> commonFields = DatasetFieldUtils.getCommonFieldsFromLion();
        // 构建筛选条件
        for (DataSetQueryCondition queryCondition : param.getConditionList()) {
            DatasetFieldPathInfo pathInfo = DatasetFieldUtils.buildFieldPathInfo(queryCondition.getColumnName(), fieldTypeMap, commonFields);
            DatasetQueryConditionUtils.addQueryCondition(queryCondition, condition, pathInfo);
        }
    }


    /**
     * 获取数据集版本信息
     *
     * @param datasetId        数据集ID
     * @param datasetVersionId 数据集版本ID
     * @return 数据集版本信息
     */
    private DataSetVersion getDatasetVersion(Long datasetId, Long datasetVersionId) {
        if (Objects.isNull(datasetVersionId) || Objects.isNull(datasetId)) {
            log.error("getDatasetVersion-数据集版本信息为空");
            throw new RuntimeException("数据集信息为空");
        }

        DataSetVersion version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(datasetId, datasetVersionId);
        if (Objects.isNull(version) || StringUtils.isBlank(version.getHeadConfig())) {
            log.error("数据集版本信息为空, datasetId = {}, datasetVersionId = {}", datasetId, datasetVersionId);
            throw new RuntimeException("数据集版本信息为空");
        }
        return version;
    }

    /**
     * 获取数据集来源
     *
     * @param datasetId 数据集ID
     * @return 数据集来源
     */
    private Integer getDataSource(Long datasetId) {
        if (Objects.isNull(datasetId)) {
            log.error("getDataSource-数据集ID为空");
            throw new RuntimeException("数据集ID为空");
        }

        DataSet dataSet = dataSetRepository.getDataSetById(datasetId);
        if (Objects.isNull(dataSet)) {
            log.error("数据集信息为空, datasetId = {}", datasetId);
            throw new RuntimeException("数据集信息为空");
        }
        return dataSet.getDataSource();
    }

    /**
     * 解析ES聚合查询结果
     *
     * @param pageResult ES查询结果
     * @return 数据集分布响应DTO
     */
    private DatasetDistributeResponseDTO parseAggregationResult(DatasetPageResult pageResult, long totalDataCount) {
        DatasetDistributeResponseDTO responseDTO = new DatasetDistributeResponseDTO();
        List<DatasetDistributeResponseDTO.DistributeResultItem> resultList = new ArrayList<>();

        if (pageResult != null && pageResult.getTermsAggResult() != null && !pageResult.getTermsAggResult().isEmpty()) {
            Map<String, Map<String, Long>> termsAggResult = pageResult.getTermsAggResult();

            // 遍历聚合结果
            for (Map.Entry<String, Map<String, Long>> aggEntry : termsAggResult.entrySet()) {
                String aggKey = aggEntry.getKey();
                Map<String, Long> buckets = aggEntry.getValue();

                if (buckets != null && !buckets.isEmpty()) {
                    // 计算总数，用于计算占比
                    long termTotalCount = buckets.values().stream().mapToLong(Long::longValue).sum();

                    // 构建分组项列表
                    List<DatasetDistributeResponseDTO.DistributeGroupItem> groupList = buildDistributeGroupList(buckets, totalDataCount, termTotalCount);

                    // 构建分布结果项
                    DatasetDistributeResponseDTO.DistributeResultItem resultItem =
                            new DatasetDistributeResponseDTO.DistributeResultItem(aggKey, groupList);
                    resultList.add(resultItem);
                }
            }
        }

        responseDTO.setResultList(resultList);
        return responseDTO;
    }

    /**
     * 构建分布分组项列表
     *
     * @param buckets        ES聚合桶数据
     * @param totalDataCount 总数量
     * @param termTotalCount 聚合出的文档数量
     * @return 分组项列表
     */
    private List<DatasetDistributeResponseDTO.DistributeGroupItem> buildDistributeGroupList(Map<String, Long> buckets, long totalDataCount, long termTotalCount) {
        List<DatasetDistributeResponseDTO.DistributeGroupItem> normalValueItems = new ArrayList<>();
        List<DatasetDistributeResponseDTO.DistributeGroupItem> resultList = new ArrayList<>();

        // 用于记录buckets中的"信号缺失"项
        DatasetDistributeResponseDTO.DistributeGroupItem signalMissingFromBuckets = null;

        // 1. 先处理所有桶数据，分离正常值项和"信号缺失"项
        for (Map.Entry<String, Long> bucketEntry : buckets.entrySet()) {
            String value = bucketEntry.getKey();
            Long count = bucketEntry.getValue();
            // 如果值为空，则改为“信号值为空”
            if (StringUtils.isBlank(value)) {
                value = "信号值为空";
            }

            double ratio = calculatePercentage(count, totalDataCount);
            DatasetDistributeResponseDTO.DistributeGroupItem groupItem =
                    new DatasetDistributeResponseDTO.DistributeGroupItem(value, count, ratio);

            // 判断是否为"信号缺失"项
            if ("信号缺失".equals(value)) {
                signalMissingFromBuckets = groupItem;
                log.info("检测到buckets中的信号缺失项, 数量: {}", count);
            } else {
                // 只有非"信号缺失"的项才加入正常值列表
                normalValueItems.add(groupItem);
            }
        }

        // 2. 判断是否需要合并所有正常值项（不包括"信号缺失"）
        if (normalValueItems.size() > dataLion.getInsightEnumerableSize()) {
            log.info("正常值项超过枚举限制, 当前项数: {}, 枚举限制: {}, 将合并所有正常值项", normalValueItems.size(), dataLion.getInsightEnumerableSize());

            // 计算所有正常值项的总数量
            long allNormalValueCount = normalValueItems.stream()
                    .mapToLong(DatasetDistributeResponseDTO.DistributeGroupItem::getCount)
                    .sum();
            double allNormalValueRatio = calculatePercentage(allNormalValueCount, totalDataCount);

            // 创建"不可枚举—有值"项，包含所有正常值
            DatasetDistributeResponseDTO.DistributeGroupItem nonEnumerableItem =
                    new DatasetDistributeResponseDTO.DistributeGroupItem("不可枚举—有值", allNormalValueCount, allNormalValueRatio);
            resultList.add(nonEnumerableItem);

            log.info("完成合并操作, 合并项数: {}, 合并总数量: {}", normalValueItems.size(), allNormalValueCount);
        } else {
            // 不超过枚举限制，保持原有的正常值项
            resultList.addAll(normalValueItems);
            // 按count降序排序
            resultList.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));
        }

        // 3. 处理"信号缺失"项
        if (signalMissingFromBuckets != null) {
            // 如果buckets中有"信号缺失"项，直接使用
            resultList.add(signalMissingFromBuckets);
            log.info("使用buckets中的信号缺失项, 数量: {}", signalMissingFromBuckets.getCount());
        } else if (termTotalCount != totalDataCount) {
            // 如果buckets中没有"信号缺失"项，但总数不匹配，则计算添加
            String value = "信号缺失";
            long count = totalDataCount - termTotalCount;
            double ratio = calculatePercentage(count, totalDataCount);
            DatasetDistributeResponseDTO.DistributeGroupItem groupItem =
                    new DatasetDistributeResponseDTO.DistributeGroupItem(value, count, ratio);
            resultList.add(groupItem);
            log.info("计算添加信号缺失项, 数量: {}", count);
        }

        return resultList;
    }

    /**
     * 计算百分比
     *
     * @param count          当前数量
     * @param totalDataCount 总数量
     * @return 百分比值（0-100）
     */
    private double calculatePercentage(long count, long totalDataCount) {
        if (totalDataCount <= 0) {
            return 0.0;
        }
        return BigDecimal.valueOf(count)
                .divide(BigDecimal.valueOf(totalDataCount), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue();
    }

    /**
     * 构建基础查询条件
     *
     * @param condition      查询条件对象
     * @param dataSetVersion 数据集版本信息
     */
    private void buildBasicSearchCondition(DatasetEsQueryCondition condition, DataSetVersion dataSetVersion, DatasetFieldPathInfo fieldPathInfo) {
        if (Objects.isNull(dataSetVersion)) {
            return;
        }
        // 设置基本查询参数
        condition.setIndexName(dataSetVersion.getEsIndexName());
        condition.addTermCondition(CommonConstants.BaseEsIndex.VERSION_ID, CommonConstants.BaseEsIndex.VERSION_ID, dataSetVersion.getId().toString());
        condition.addTermCondition(CommonConstants.BaseEsIndex.DATASET_ID, CommonConstants.BaseEsIndex.DATASET_ID, dataSetVersion.getDataSetId().toString());
        condition.setPageSize(0);

        // 设置聚合查询参数
        if (fieldPathInfo.getFieldMappingDTO().getFieldType() != null && DatasetQueryConditionUtils.isUseFlattenPath(fieldPathInfo.getFieldMappingDTO())) {
            for (String path : fieldPathInfo.getFirstLevelJsonPath()) {
                condition.addTermsAggregation(path.replace("flattenedData.", "").replace(fieldPathInfo.getFieldName() + POINT, ""), fieldPathInfo.getFieldName(), path, dataLion.getInsightTermsBucketsSize(), null);
            }
        } else {
            condition.addTermsAggregationNested(fieldPathInfo.getOriginFieldName(), fieldPathInfo.getFieldName(), "textData", fieldPathInfo.getTextPath(), dataLion.getInsightTermsBucketsSize(), "信号缺失");
        }
    }

    /**
     * 配置返回字段
     */
    private void configureReturnFields(DatasetEsQueryCondition condition, List<String> columnList, Map<String, FieldMappingDTO> fieldTypeMap) {
        List<String> fieldPath = new ArrayList<>();
        Set<String> commonFields = DatasetFieldUtils.getCommonFieldsFromLion();
        // 构建筛选条件
        for (String column : columnList) {
            DatasetFieldPathInfo pathInfo = DatasetFieldUtils.buildFieldPathInfo(Collections.singletonList(column), fieldTypeMap, commonFields);
            if (Objects.isNull(pathInfo)) {
                break;
            }
            if (DatasetQueryConditionUtils.isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
                fieldPath.add(pathInfo.getFlattenPath());
            } else {
                fieldPath.add(pathInfo.getTextPath().replace(ES_KEYWORD_SUFFIX, ""));
            }
        }
        condition.setFieldPath(fieldPath);
    }

    /**
     * 构建筛选分页响应结果
     *
     * @param pageResult ES查询结果
     * @param param      查询参数
     * @return 筛选分页响应DTO
     */
    private DatasetFilterPageResponseDTO buildFilterPageResponse(DatasetPageResult pageResult,
                                                                 DatasetFilterPageQueryParam param,
                                                                 Map<String, DataContentFieldDTO> nameMap) {
        DatasetFilterPageResponseDTO responseDTO = new DatasetFilterPageResponseDTO();

        if (pageResult == null) {
            responseDTO.setTotalCount(0L);
            responseDTO.setData(new ArrayList<>());
            return responseDTO;
        }

        // 设置总记录数
        responseDTO.setTotalCount(pageResult.getTotal());

        // 转换数据列表
        List<DatasetFilterPageResponseDTO.DataItem> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
            for (DatasetEsIndex record : pageResult.getRecords()) {
                DatasetFilterPageResponseDTO.DataItem dataItem = convertToDataItem(record, param.getColumnList(), nameMap);
                dataList.add(dataItem);
            }
        }

        responseDTO.setData(dataList);
        return responseDTO;
    }

    /**
     * 转换ES记录为数据项
     *
     * @param record     ES索引记录
     * @param columnList 需要返回的列列表
     * @return 数据项
     */
    private DatasetFilterPageResponseDTO.DataItem convertToDataItem(DatasetEsIndex record, List<String> columnList, Map<String, DataContentFieldDTO> nameMap) {
        DatasetFilterPageResponseDTO.DataItem dataItem = new DatasetFilterPageResponseDTO.DataItem();
        List<DatasetFilterPageResponseDTO.FieldResult> resultList = new ArrayList<>();

        // 处理文本数据
        if (MapUtils.isNotEmpty(record.getTextData())) {
            addFieldsFromMap(resultList, record.getTextData(), ES_TEXT_PREFIX, nameMap, columnList);
        }
        // 处理扁平化数据
        if (MapUtils.isNotEmpty(record.getFlattenedData())) {
            addFieldsFromMap(resultList, record.getFlattenedData(), ES_FLATTEN_PREFIX, nameMap, columnList);
        }
        // 处理公共数据
        if (MapUtils.isNotEmpty(record.getCommonData())) {
            addFieldsFromMap(resultList, record.getCommonData(), ES_COMMON_DATA_PREFIX, nameMap, columnList);
        }

        dataItem.setResultList(resultList);
        return dataItem;
    }

    /**
     * 从Map中添加字段
     */
    private void addFieldsFromMap(List<DatasetFilterPageResponseDTO.FieldResult> resultList, Map<String, Object> dataMap,
                                  String fatherFieldName, Map<String, DataContentFieldDTO> nameMap, List<String> columnList) {
        if (dataMap == null) {
            return;
        }

        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            DatasetFilterPageResponseDTO.FieldResult fieldResult = new DatasetFilterPageResponseDTO.FieldResult();
            String esFieldName = "";
            if (ES_TEXT_PREFIX.equals(fatherFieldName)) {
                esFieldName = ES_TEXT_PREFIX + POINT + entry.getKey();
            } else if (ES_FLATTEN_PREFIX.equals(fatherFieldName)) {
                esFieldName = ES_FLATTEN_PREFIX + POINT + entry.getKey();
            } else {
                esFieldName = ES_COMMON_DATA_PREFIX + POINT + entry.getKey();
            }
            // 若指定了返回的列，过滤掉不需要的列
            if (CollectionUtils.isNotEmpty(columnList) && !columnList.contains(nameMap.get(esFieldName).getColumnName())) {
                break;
            }
            fieldResult.setName(nameMap.get(esFieldName).getColumnName());
            fieldResult.setFieldType(FieldTypeEnum.getDescriptionByCode(nameMap.get(esFieldName).getFieldType()));
            fieldResult.setValue(entry.getValue());
            resultList.add(fieldResult);
        }
    }

    /**
     * 应用内存过滤和分页
     *
     * @param pageResult             ES查询结果
     * @param memoryFilterConditions 需要内存过滤的条件列表
     * @param fieldTypeMap           字段类型映射
     * @param param                  原始查询参数
     * @return 过滤和分页后的结果
     */
    private DatasetPageResult applyMemoryFilterAndPagination(DatasetPageResult pageResult,
                                                             List<DataSetQueryCondition> memoryFilterConditions,
                                                             Map<String, FieldMappingDTO> fieldTypeMap,
                                                             DatasetFilterPageQueryParam param,
                                                             DatasetEsQueryCondition condition) {
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
            log.info("ES查询结果为空，无需进行内存过滤");
            return pageResult;
        }

        List<DatasetEsIndex> allRecords = pageResult.getRecords();
        log.info("开始内存过滤，原始数据条数: {}, 过滤条件数: {}", allRecords.size(), memoryFilterConditions.size());

        // 1. 应用内存过滤条件
        List<DatasetEsIndex> filteredRecords = allRecords.stream()
                .filter(record -> DatasetQueryConditionUtils.matchesMemoryFilterConditions(record, memoryFilterConditions, fieldTypeMap))
                .collect(Collectors.toList());
        while (pageResult.getHasNext() && filteredRecords.size() <= ES_MAX_RESULT_WINDOW) {
            condition.setScrollId(pageResult.getScrollId());
            pageResult = datasetEsIndexService.pageQuery(condition);
            allRecords = pageResult.getRecords();
            filteredRecords.addAll(allRecords.stream()
                    .filter(record -> DatasetQueryConditionUtils.matchesMemoryFilterConditions(record, memoryFilterConditions, fieldTypeMap))
                    .collect(Collectors.toList()));
        }

        if (pageResult.getHasNext()) {
            datasetEsIndexService.clearScrollSearch(pageResult.getScrollId());
        }

        log.info("内存过滤完成，过滤后数据条数: {}", filteredRecords.size());

        // 2. 计算分页信息
        int pageNum = param.getPageNum() != null ? param.getPageNum() : 1;
        int pageSize = param.getPageSize() != null ? param.getPageSize() : 20;
        long totalCount = filteredRecords.size();
        int totalPages = (int) Math.ceil((double) totalCount / pageSize);

        // 3. 应用分页
        int endIndex = Math.min(pageNum + pageSize, filteredRecords.size());

        List<DatasetEsIndex> pagedRecords;
        if (pageNum >= filteredRecords.size()) {
            pagedRecords = Collections.emptyList();
        } else {
            pagedRecords = filteredRecords.subList(pageNum, endIndex);
        }

        log.info("内存分页完成，页码: {}, 页大小: {}, 总条数: {}, 当前页条数: {}",
                pageNum, pageSize, totalCount, pagedRecords.size());

        // 4. 构建新的分页结果
        return DatasetPageResult.builder()
                .records(pagedRecords)
                .total(totalCount)
                .pageNum(pageNum - 1)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .hasNext(pageNum < totalPages)
                .hasPrevious(pageNum > 1)
                .took(pageResult.getTook())
                .scrollId(pageResult.getScrollId())
                .highlights(pageResult.getHighlights())
                .termsAggResult(pageResult.getTermsAggResult())
                .build();
    }
} 