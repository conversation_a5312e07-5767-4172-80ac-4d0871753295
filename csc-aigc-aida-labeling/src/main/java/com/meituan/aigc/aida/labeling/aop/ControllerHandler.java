package com.meituan.aigc.aida.labeling.aop;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingException;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Aspect
@Slf4j
@Component
public class ControllerHandler {

    @Around("execution(public * com.meituan.aigc.aida.labeling.controller..*.*(..)) || execution(public * com.meituan.aigc.aida.data.management.fetch.controller..*.*(..)) || execution(public * com.meituan.aigc.aida.data.management.dataset.controller..*.*(..))")
    public Object controllerPointcut(ProceedingJoinPoint joinPoint) {
        Map<String, Object> params = getRequestParams(joinPoint);
        String methodName = joinPoint.getSignature().getName();
        User user = UserUtils.getUser();
        String requestUrl = getRequestUrl(joinPoint);
        try {
            log.info("执行controller请求{}: {}({}), params-{}, {}.{}",
                    requestUrl,
                    user == null ? null : user.getName(),
                    user == null ? null : user.getLogin(),
                    JSONObject.toJSONString(params),
                    joinPoint.getTarget().getClass().getName(),
                    methodName);
            Object result = joinPoint.proceed();
            log.info("执行controller请求成功{}: {}({}), params-{}, result-{}, {}.{}",
                    requestUrl,
                    user == null ? null : user.getName(),
                    user == null ? null : user.getLogin(),
                    JSONObject.toJSONString(params),
                    JSONObject.toJSONString(result),
                    joinPoint.getTarget().getClass().getName(),
                    methodName);
            return result;
        } catch (Throwable t) {
            Cat.logError(t);
            log.info("执行controller请求异常{}: {}({}), params-{}, error-{}, {}.{}",
                    requestUrl,
                    user == null ? null : user.getName(),
                    user == null ? null : user.getLogin(),
                    JSONObject.toJSONString(params),
                    t.getMessage(),
                    joinPoint.getTarget().getClass().getName(),
                    methodName, t);
            String errorMsg = t instanceof AidaTrainingException ? t.getMessage() : "接口异常";
            return Result.fail(Result.ResultCode.SERVICE_EXCEPTION.getCode(), errorMsg);
        }
    }


    private String getRequestUrl(ProceedingJoinPoint joinPoint) {
        String prefix = getRequestUrl(joinPoint.getTarget().getClass().getAnnotations());
        String suffix = getRequestUrl(((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotations());
        if (prefix == null && suffix == null) {
            return null;
        }
        if (prefix == null) {
            return suffix;
        }
        if (suffix == null) {
            return prefix;
        }
        return (prefix + suffix).replace("//", "/");
    }

    /**
     * 解析出注解的url信息
     */
    private String getRequestUrl(Annotation[] annotations) {
        for (Annotation annotation : annotations) {
            Class<? extends Annotation> clazz = annotation.annotationType();
            if (GetMapping.class.equals(clazz)) {
                return StringUtils.join(((GetMapping) annotation).value());
            } else if (PostMapping.class.equals(clazz)) {
                return StringUtils.join(((PostMapping) annotation).value());
            } else if (PutMapping.class.equals(clazz)) {
                return StringUtils.join(((PutMapping) annotation).value());
            } else if (DeleteMapping.class.equals(clazz)) {
                return StringUtils.join(((DeleteMapping) annotation).value());
            } else if (RequestMapping.class.equals(clazz)) {
                return StringUtils.join(((RequestMapping) annotation).value());
            }
        }
        return null;
    }

    /**
     * 获取方法参数名和参数值
     */
    private Map<String, Object> getRequestParams(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        final String[] names = methodSignature.getParameterNames();
        final Object[] args = joinPoint.getArgs();

        if (null == names || null == args) {
            return Collections.emptyMap();
        }
        Map<String, Object> map = Maps.newHashMap();
        for (int i = 0; i < names.length; i++) {
            if (args[i] instanceof HttpServletRequest || args[i] instanceof HttpServletResponse) {
                continue;
            }
            map.put(names[i], args[i]);
        }
        return map;
    }
}
