package com.meituan.aigc.aida.labeling.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConditionFieldTypeEnum {

    MAPPED_FIELD(1, "映射字段"),
    TRAINING_SIGNAL_FIELD(2, "训练集信号字段"),
    SESSION_SIGNAL_FIELD(3, "会话信号字段"),
    LLM_FIELD(4, "大模型字段"),
    LABELING_ITEM_FIELD(5, "标注项字段"),
    ;

    private final int code;
    private final String description;
}
