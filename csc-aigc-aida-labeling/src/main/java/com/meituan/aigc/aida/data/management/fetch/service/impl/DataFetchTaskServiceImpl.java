package com.meituan.aigc.aida.data.management.fetch.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.util.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.fetch.common.convertor.DataFetchTaskConvertor;
import com.meituan.aigc.aida.data.management.fetch.common.enums.DataFetchTaskStatus;
import com.meituan.aigc.aida.data.management.fetch.common.enums.TrackingRulesChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchRecordRepository;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchTaskRepository;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.SignalTrackingRulesRepository;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchRecordDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskCreateFilterConditionsDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskCreateOutputFieldDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskDTO;
import com.meituan.aigc.aida.data.management.fetch.helper.SignalTrackingRulesHelper;
import com.meituan.aigc.aida.data.management.fetch.param.DataFetchTaskCreateParam;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;
import com.meituan.aigc.aida.data.management.fetch.service.DataFetchTaskService;
import com.meituan.aigc.aida.data.management.fetch.strategy.export.ExportDataFetch;
import com.meituan.aigc.aida.data.management.fetch.vo.RobotVO;
import com.meituan.aigc.aida.data.management.fetch.vo.SignalLogRecordVO;
import com.meituan.aigc.aida.data.management.proxy.HiveRequestServiceProxy;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingOperationException;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.meituan.aigc.aida.labeling.util.ThreadPoolUtil;
import com.meituan.csc.aigc.runtime.api.AidaConfigService;
import com.meituan.csc.aigc.runtime.dto.aida.AppConfigDTO;
import com.meituan.csc.aigc.runtime.dto.aida.param.AppConfigRequestParam;
import com.meituan.talos.commons.domain.Column;
import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.model.QueryResult;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: guowenhui
 * @Create: 2025/4/7 15:59
 * @Version: 1.0
 */
@Slf4j
@Service
public class DataFetchTaskServiceImpl implements DataFetchTaskService {

    @Resource
    private ExportDataFetch exportDataFetch;

    /**
     * 系统可用的CPU核心数
     */
    private static final int CPU_CORE_COUNT = 4;
    private static final ExecutorService CREATE_DATA_FETCH_TASK_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            1000,
            "create-data-fetch-task-%d");

    /**
     * 处理数据获取记录导出的线程池
     */
    private static final ExecutorService EXPORT_DATA_FETCH_RECORD_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "export-data-fetch-record-%d");

    @Resource
    private SignalTrackingRulesRepository signalTrackingRulesRepository;

    @Autowired
    private AidaConfigService aidaConfigService;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    @Resource
    private DataFetchRecordRepository dataFetchRecordRepository;

    @Resource
    private DataFetchTaskRepository dataFetchTaskRepository;

    @Resource
    private HiveRequestServiceProxy hiveRequestServiceProxy;

    @Resource
    private SignalTrackingRulesHelper signalTrackingRulesHelper;

    @Resource
    private PushElephantService pushElephantService;

    @Override
    public List<RobotVO> listRobotByParam(RobotListQuery query) {
        log.info("获取埋点机器人信息收到请求，入参：{}", JSON.toJSONString(query));
        if (StringUtils.isNotBlank(query.getTypicalQuestionIds())) {
            CheckUtil.paramCheck(isValidCommaDelimitedValues(query.getTypicalQuestionIds()), "请正确填写标准问ID");
        }
        CheckUtil.paramCheck(StringUtils.isNotBlank(query.getMisList()), "客服mis号不能为空");
        // 获取去重后机器人ID集合
        Set<String> robotIdSet = signalTrackingRulesHelper.getDistinctAidaAppIds(query);
        // 构建返回结果
        return buildResult(robotIdSet);
    }

    @Override
    public void createTask(DataFetchTaskCreateParam param) {
        if (!dataManagementLionConfig.getDataFetchTaskCreateSwitch()) {
            throw new AidaTrainingOperationException("功能维护中，请联系AI搭平台支持（大象mis：zb_aida）");
        }
        log.info("创建数据获取任务收到请求，参数：{}", JSON.toJSONString(param));
        // 校验创建数据获取任务参数
        validCreateTaskParam(param);
        // 构建任务实体
        DataFetchTask dataFetchTask = biuldDataFetchTask(param);
        dataFetchTaskRepository.addDataFetchTask(dataFetchTask);
        Long dataFetchTaskId = dataFetchTask.getId();
        String userMis = Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse("");
        // 提交异步拉取数据任务
        CREATE_DATA_FETCH_TASK_EXECUTOR.submit(() -> fetchData(param, dataFetchTaskId, userMis));
    }

    /**
     * 拉取数据
     *
     * @param param           请求参数
     * @param dataFetchTaskId 拉取数据任务Id
     */
    private void fetchData(DataFetchTaskCreateParam param, Long dataFetchTaskId, String userMis) {
        log.info("创建数据获取任务，异步拉取数据开始，任务Id:{}，任务参数：{}", dataFetchTaskId, JSON.toJSONString(param));
        long startTime = System.currentTimeMillis();
        String dataFetchPageUrl = dataManagementLionConfig.getDataFetchPageUrl();
        DataFetchTaskCreateFilterConditionsDTO filterConditions = param.getFilterConditions();
        String misList = filterConditions.getMisList();
        String typicalQuestionIds = filterConditions.getTypicalQuestionIds();
        String contactType = param.getContactType();
        Date triggerTimeStartDate = DateUtil.parseToDate(filterConditions.getTriggerTimeStart(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
        String triggerTimeStart = Objects.isNull(triggerTimeStartDate) ? ""
                : String.valueOf(triggerTimeStartDate.getTime());
        Date triggerTimeEndDate = DateUtil.parseToDate(filterConditions.getTriggerTimeEnd(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
        String triggerTimeEnd = Objects.isNull(triggerTimeEndDate) ? ""
                : String.valueOf(triggerTimeEndDate.getTime());
        List<DataFetchTaskCreateOutputFieldDTO> outputFields = param.getOutputFields();
        List<String> robotIds = outputFields.stream().map(DataFetchTaskCreateOutputFieldDTO::getRobotId).distinct()
                .collect(Collectors.toList());
        // 查询Hive库
        AsyncTalosClient client = null;
        try {
            client = hiveRequestServiceProxy.getClient();
            String sql = buildQueryHiveParam(robotIds, triggerTimeStart, triggerTimeEnd, misList, typicalQuestionIds, contactType);
            log.info("创建数据获取任务，任务Id:{}，参数：{}，查询Hive库SQL：{}", dataFetchTaskId, JSON.toJSONString(param), sql);
            QueryResult queryResult = hiveRequestServiceProxy.invoke(client, sql,
                    dataManagementLionConfig.getCreateDataFetchTaskTalosWaitingTime() * 60);
            // 处理查询结果，返回条数
            Integer dataCount = handleQueryResult(param, dataFetchTaskId, queryResult, sql);
            // 更新任务表状态
            updateDataFetchTask(dataFetchTaskId, dataCount);
            // 发送大象通知
            String message = String.format("数据获取任务「%s」处理完成，请前往[训练系统|%s]查看。", param.getName(), dataFetchPageUrl);
            pushElephantService.pushElephant(message, userMis);
            long timeConsuming = System.currentTimeMillis() - startTime;
            log.info("创建数据获取任务，异步拉取数据结束，任务Id:{}，耗时：{}ms，任务参数：{}", dataFetchTaskId, timeConsuming,
                    JSON.toJSONString(param));
        } catch (Exception e) {
            // 发生异常时将任务状态更新为失败,发送创建失败大象通知
            DataFetchTask dataFetchTask = new DataFetchTask();
            dataFetchTask.setId(dataFetchTaskId);
            dataFetchTask.setTaskStatus(DataFetchTaskStatus.FAILED.getCode());
            dataFetchTask.setUpdateTime(new Date());
            dataFetchTaskRepository.updateByIdSelective(dataFetchTask);
            log.error("创建数据获取任务，获取数据发生异常，任务ID：{}，参数：{}， 异常信息：", dataFetchTaskId, JSON.toJSONString(param), e);
            String message = String.format("数据获取任务「%s」处理失败，失败原因：%s，请前往[训练系统|%s]查看。", param.getName(), e.getMessage(),
                    dataFetchPageUrl);
            pushElephantService.pushElephant(message, userMis);
        } finally {
            hiveRequestServiceProxy.closeClient(client);
        }
    }

    private Integer handleQueryResult(DataFetchTaskCreateParam param, Long dataFetchTaskId, QueryResult queryResult,
                                      String sql) throws TalosException {
        // 新增数据量,查询出来的数据会根据 bizMessageId + "-" + popupAcId + "-" + inputs
        // 进行合并，新增的数据可能比查出来的少，所以要根据实际新增的数据量计数
        if (Objects.isNull(queryResult) || queryResult.getResultSize() <= 0
                || CollectionUtils.isEmpty(queryResult.getColumns())) {
            // 查询无数据时线下Mock测试 查询数据
            if (CollectionUtils.isNotEmpty(dataManagementLionConfig.getMockFetchDataFromHiveItem())) {
                log.info("创建数据获取任务，获取数据为空，MOCK数据测试，任务Id:{}，参数：{}", dataFetchTaskId, JSON.toJSONString(param));
                List<Map<String, String>> resultMapList = dataManagementLionConfig.getMockFetchDataFromHiveItem();
                // 构建数据并入库,返回入库条数
                addDataFetchRecordAndReturnCount(param, dataFetchTaskId,
                        resultMapList);
                return resultMapList.size();
            } else {
                // 无数据成功
                log.warn("创建数据获取任务，获取数据为空，任务Id:{}，参数：{}", dataFetchTaskId, JSON.toJSONString(param));
                return 0;
            }
        }
        log.info("创建数据获取任务，任务Id:{}，查询Hive库SQL：{}，查询结果条数：{}", dataFetchTaskId, sql,
                queryResult.getTotal());
        int offset = 0;
        // 每页记录数
        Integer pageSize = dataManagementLionConfig.getDataPullHiveMaxSize();
        // 字段名称
        List<Column> headList = queryResult.getColumns();
        while (queryResult.hasNext()) {
            List<List<Object>> dataList = queryResult.fetchResult(offset, pageSize);
            List<Map<String, String>> resultMapList = new ArrayList<>();
            for (List<Object> data : dataList) {
                Map<String, String> map = new HashMap<>();
                for (int index = 0; index < headList.size(); index++) {
                    map.put(headList.get(index).getName(), data.get(index).toString());
                }
                resultMapList.add(map);
            }

            // 构建数据并入库
            addDataFetchRecordAndReturnCount(param, dataFetchTaskId,
                    resultMapList);
            offset += pageSize;
        }
        return Math.toIntExact(queryResult.getTotal());
    }

    private void updateDataFetchTask(Long dataFetchTaskId, Integer totalNum) {
        if (Objects.isNull(dataFetchTaskId)) {
            return;
        }
        DataFetchTask dataFetchTask = new DataFetchTask();
        dataFetchTask.setId(dataFetchTaskId);
        dataFetchTask.setTaskStatus(DataFetchTaskStatus.COMPLETED.getCode());
        dataFetchTask.setUpdateTime(new Date());
        if (Objects.nonNull(totalNum)) {
            dataFetchTask.setDataCount(totalNum);
        }
        dataFetchTaskRepository.updateByIdSelective(dataFetchTask);
    }

    /**
     * 添加任务拉取数据表
     *
     * @param param           任务参数
     * @param dataFetchTaskId 任务id
     * @param resultMapList   分页查询give结果
     */
    private void addDataFetchRecordAndReturnCount(DataFetchTaskCreateParam param,
                                                  Long dataFetchTaskId, List<Map<String, String>> resultMapList) {
        if (CollectionUtils.isEmpty(resultMapList)) {
            log.info("创建数据获取任务，获取数据为空不做处理，参数：{}", JSON.toJSONString(param));
            return;
        }
        String jsonStrSubResultMapList = JSON.toJSONString(resultMapList);
        List<SignalLogRecordVO> signalLogRecordVos = JSON.parseObject(jsonStrSubResultMapList,
                new TypeReference<List<SignalLogRecordVO>>() {
                });
        if (CollectionUtils.isEmpty(signalLogRecordVos)) {
            return;
        }
        List<DataFetchRecordWithBLOBs> dataFetchRecordWithBLOBs = new ArrayList<>();
        signalLogRecordVos.forEach(recordVo -> {
            DataFetchRecordWithBLOBs dataFetchRecord = buildDataFetchRecordWithBLOBs(param, dataFetchTaskId, recordVo);
            dataFetchRecordWithBLOBs.add(dataFetchRecord);
        });
        dataFetchRecordRepository.batchInsert(dataFetchRecordWithBLOBs);
        dataFetchRecordWithBLOBs.clear();
        resultMapList.clear();
    }

    @NotNull
    private static DataFetchRecordWithBLOBs buildDataFetchRecordWithBLOBs(DataFetchTaskCreateParam param,
                                                                          Long dataFetchTaskId, SignalLogRecordVO recordVo) {
        DataFetchRecordWithBLOBs dataFetchRecord = new DataFetchRecordWithBLOBs();
        dataFetchRecord.setTaskId(dataFetchTaskId);
        dataFetchRecord.setSessionId(recordVo.getSessionid());
        dataFetchRecord.setContactId(recordVo.getContactid());
        if (StringUtils.isNotBlank(recordVo.getSignaldatacontent()) && Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getOutputFields())) {
            String signaldatacontent = recordVo.getSignaldatacontent();
            List<DataFetchTaskCreateOutputFieldDTO> outputFields = param.getOutputFields();
            for (DataFetchTaskCreateOutputFieldDTO outputField : outputFields) {
                signaldatacontent = signaldatacontent.replace(outputField.getRobotId(), outputField.getFieldName());
            }
            dataFetchRecord.setSignalDataContent(signaldatacontent);
        }
        dataFetchRecord.setStaffMis(recordVo.getStaffmis());
        dataFetchRecord.setStaffMessageContent(recordVo.getStaffmessagecontent());
        dataFetchRecord.setMessageId(recordVo.getBizmessageid());
        dataFetchRecord.setExtraInfo(recordVo.getCommoninfo());
        if (StringUtils.isNotBlank(recordVo.getMessageoccurredtime())
                && StringUtils.isNumeric(recordVo.getMessageoccurredtime())) {
            dataFetchRecord.setMessageOccurredTime(new Date(Long.parseLong(recordVo.getMessageoccurredtime())));
        } else {
            log.warn("创建数据获取任务,拉取hive消息发送时间为空或非数字");
            dataFetchRecord.setMessageOccurredTime(new Date());

        }
        dataFetchRecord.setCommonInfo(recordVo.getCommoninfo());
        dataFetchRecord.setCustomerMessageContent(recordVo.getCustomermessagecontent());
        dataFetchRecord.setChatMessageFromType(recordVo.getChatmessagefromtype());
        dataFetchRecord.setContactType(recordVo.getContacttype());
        dataFetchRecord.setTypicalQuestionIds(recordVo.getTypicalquestionids());
        dataFetchRecord.setPopupAcId(recordVo.getPopupacid());
        dataFetchRecord.setInputs(recordVo.getInputs());
        dataFetchRecord.setRobotInvokeInfo(recordVo.getRobotInvokeInfo());
        dataFetchRecord.setCreateTime(new Date());
        dataFetchRecord.setUpdateTime(new Date());
        return dataFetchRecord;
    }

    /**
     * 合并分页分割数据
     *
     * @param param          请求参数
     * @param lastId         最后一个Id
     * @param key            本次循环的key
     * @param groupedSignals 分组结果
     */
    private void mergeData(DataFetchTaskCreateParam param, Long lastId, String key,
                           Map<String, List<SignalLogRecordVO>> groupedSignals) {
        DataFetchRecordWithBLOBs dataFetchRecord = dataFetchRecordRepository.getById(lastId);
        if (Objects.isNull(dataFetchRecord)) {
            log.info("创建数据获取任务，处理分页分割数据根据lastId查询结果为空不做处理，参数：{}，lastId：{}", JSON.toJSONString(param), lastId);
            return;
        }
        String oldSignalDataContentStr = dataFetchRecord.getSignalDataContent();
        Map<String, String> signalDataContent = buildSignalDataContent(param, groupedSignals.get(key));
        if (StringUtils.isBlank(oldSignalDataContentStr)) {
            dataFetchRecord.setSignalDataContent(JSON.toJSONString(signalDataContent));
        } else {
            Map<String, String> oldSignalDataContent = JSON.parseObject(oldSignalDataContentStr,
                    new TypeReference<Map<String, String>>() {
                    });
            signalDataContent.putAll(oldSignalDataContent);
            dataFetchRecord.setSignalDataContent(JSON.toJSONString(signalDataContent));
        }
        dataFetchRecord.setUpdateTime(new Date());
        dataFetchRecordRepository.updateByIdSelective(dataFetchRecord);
    }

    /**
     * 构建信号内容
     *
     * @param param                    参数
     * @param groupSignalLogRecordList 分组后的数据
     * @return 结果
     */
    @NotNull
    private static Map<String, String> buildSignalDataContent(DataFetchTaskCreateParam param,
                                                              List<SignalLogRecordVO> groupSignalLogRecordList) {
        List<DataFetchTaskCreateOutputFieldDTO> outputFields = param.getOutputFields();
        Map<String, DataFetchTaskCreateOutputFieldDTO> outputFieldDTOMap = outputFields.stream()
                .collect(Collectors.toMap(DataFetchTaskCreateOutputFieldDTO::getRobotId, Function.identity()));
        // 处理信号数据内容，key为机器人输出字段名称 value为机器人执行结果
        Map<String, String> signalDataContent = new HashMap<>();
        groupSignalLogRecordList.stream()
                .filter(Objects::nonNull)
                .filter(groupSignalLogRecord -> StringUtils.isNotBlank(groupSignalLogRecord.getAppid()))
                .filter(groupSignalLogRecord -> {
                    DataFetchTaskCreateOutputFieldDTO dataFetchTaskCreateOutputFieldDTO = outputFieldDTOMap
                            .get(groupSignalLogRecord.getAppid());
                    if (Objects.isNull(dataFetchTaskCreateOutputFieldDTO)) {
                        log.info("创建数据获取任务，根据appId获取机器人输出配置为空, record = {}",
                                JSONObject.toJSONString(groupSignalLogRecord));
                        return false;
                    }
                    return true;
                })
                .forEach(groupSignalLogRecord -> {
                    DataFetchTaskCreateOutputFieldDTO dataFetchTaskCreateOutputFieldDTO = outputFieldDTOMap
                            .get(groupSignalLogRecord.getAppid());
                    signalDataContent.put(dataFetchTaskCreateOutputFieldDTO.getFieldName(),
                            groupSignalLogRecord.getOutputs());
                });
        return signalDataContent;
    }

    /**
     * 通过lastKey 校验是否存在分页分割数据
     *
     * @param lastKeyStr lastKey
     * @param key        本次分组数据key
     * @return 校验结果
     */
    private boolean checkLastKey(String lastKeyStr, String key) {
        if (StringUtils.isBlank(lastKeyStr)) {
            return false;
        }
        return Objects.equals(lastKeyStr, key);
    }

    /**
     * 构建查询条件
     *
     * @param robotIds           机器人ID
     * @param triggerTimeStart   开始时间
     * @param triggerTimeEnd     结束时间
     * @param misList            客服mis号集合
     * @param typicalQuestionIds 标问ID集合
     * @param contactType        联络类型，online: 在线渠道, call: 电话渠道
     * @return 构建结果
     */
    private String buildQueryHiveParam(List<String> robotIds, String triggerTimeStart, String triggerTimeEnd,
                                       String misList, String typicalQuestionIds, String contactType) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(dataManagementLionConfig.getQueryAidaTrainingSignalLoggingSqlPre());
        String robotIdsStr = robotIds.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
        String finalMisList = Arrays.stream(misList.split(",")).map(id -> "'" + id + "'")
                .collect(Collectors.joining(","));
        stringBuilder.append(" appid IN(").append(robotIdsStr).append(")");
        stringBuilder.append(" AND createdat BETWEEN '").append(triggerTimeStart).append("' AND '")
                .append(triggerTimeEnd);
        stringBuilder.append("' AND staffmis IN(").append(finalMisList).append(")");
        if (StringUtils.isNotBlank(typicalQuestionIds)) {
            String[] typicalQuestionIdArray = typicalQuestionIds.split(CommonConstants.FieldName.ENGLISH_COMMA);
            for (int i = 0; i < typicalQuestionIdArray.length; i++) {
                if (i == 0) {
                    stringBuilder.append(" AND (FIND_IN_SET('").append(typicalQuestionIdArray[i])
                            .append("', typicalquestionids) > 0");
                } else {
                    stringBuilder.append(" OR FIND_IN_SET('").append(typicalQuestionIdArray[i])
                            .append("', typicalquestionids) > 0");
                }
            }
            stringBuilder.append(")");
        }
        if (StringUtils.isNotBlank(contactType)) {
            // 直接使用contactType进行查询，contactType的值对应数据库中的contacttype字段
            stringBuilder.append(" AND contacttype = '").append(contactType).append("'");
        }
        stringBuilder.append(" GROUP BY bizmessageid, popupacid, inputs ORDER BY MAX(messageoccurredtime) DESC");
        return stringBuilder.toString();
    }

    @Override
    public PageData<DataFetchTaskDTO> pageDataFetchTaskList(String taskName, String creatorName, Integer pageNum, Integer pageSize) {
        log.info("开始查询数据获取列表,taskName:{},creatorName:{},pageNum:{},pageSize:{}", taskName, creatorName, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<DataFetchTask> dataFetchTasks = dataFetchTaskRepository.listTask(taskName, creatorName);
        if (CollectionUtils.isEmpty(dataFetchTasks)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<DataFetchTask> pageInfo = new PageInfo<>(dataFetchTasks);
        PageHelper.clearPage();
        List<DataFetchTaskDTO> dataFetchTaskList = dataFetchTasks.stream()
                .map(DataFetchTaskConvertor::buildDataFetchTaskDTO)
                .collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), dataFetchTaskList);
    }

    @Override
    public DataFetchTaskDTO getDataFetchTaskDetail(Long taskId) {
        log.info("查询数据获取任务,任务Id:{}", taskId);
        DataFetchTask dataFetchTask = dataFetchTaskRepository.getTaskById(taskId);
        if (Objects.isNull(dataFetchTask)) {
            return null;
        }
        return DataFetchTaskConvertor.buildDataFetchTaskDTO(dataFetchTask);
    }

    @Override
    public PageData<DataFetchRecordDTO> pageDataFetchRecordList(Long taskId, Integer pageNum, Integer pageSize, String inputs) {
        log.info("查询数据记录,taskId:{}", taskId);
        PageHelper.startPage(pageNum, pageSize);
        List<DataFetchRecordWithBLOBs> dataFetchRecords = dataFetchRecordRepository.listByTaskIdAndLikeByInputs(taskId, inputs);
        if (CollectionUtils.isEmpty(dataFetchRecords)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<DataFetchRecordWithBLOBs> pageInfo = new PageInfo<>(dataFetchRecords);
        PageHelper.clearPage();
        List<DataFetchRecordDTO> dataFetchRecordList = dataFetchRecords.stream()
                .map(DataFetchTaskConvertor::buildDataFetchRecordDTO)
                .collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), dataFetchRecordList);
    }

    @Override
    public void exportDataFetchRecord(Long taskId) {
        log.info("导出数据拉取任务记录，入参：taskId={}", taskId);
        DataFetchTask dataFetchTask = validateAndGetTask(taskId);
        submitExportTask(dataFetchTask);
    }

    @Override
    public void deleteDataFetchTask(Long taskId) {
        log.info("删除数据拉取任务，入参：taskId={}", taskId);
        DataFetchTask dataFetchTask = validateAndGetTask(taskId);
        dataFetchTask.setIsDelete(Boolean.TRUE);
        dataFetchTask.setUpdateTime(new Date());
        dataFetchTaskRepository.updateByIdSelective(dataFetchTask);
        // 删除任务下的所有记录
        dataFetchRecordRepository.deleteByTaskId(taskId);
        log.info("删除数据拉取任务成功，taskId={}", taskId);
    }

    /**
     * 校验任务是否存在
     *
     * @param taskId 任务Id
     * @return 任务实体
     */
    private DataFetchTask validateAndGetTask(Long taskId) {
        CheckUtil.paramCheck(Objects.nonNull(taskId), "任务Id不能为空");
        DataFetchTask dataFetchTask = dataFetchTaskRepository.getTaskById(taskId);
        if (Objects.isNull(dataFetchTask)) {
            throw new AidaTrainingCheckException("任务不存在");
        }
        return dataFetchTask;
    }

    /**
     * 提交导出任务
     *
     * @param dataFetchTask 数据获取任务
     */
    private void submitExportTask(DataFetchTask dataFetchTask) {
        String mis = UserUtils.getUser().getLogin();
        // 提交导出任务
        EXPORT_DATA_FETCH_RECORD_EXECUTOR.submit(() -> exportDataFetch.exportDataFetchRecord(dataFetchTask, mis));
    }

    @NotNull
    private static DataFetchTask biuldDataFetchTask(DataFetchTaskCreateParam param) {
        DataFetchTask dataFetchTask = new DataFetchTask();
        dataFetchTask.setName(param.getName());
        dataFetchTask.setDescription(param.getDescription());
        dataFetchTask.setDataType(param.getDataType());
        dataFetchTask.setFilterConditions(JSON.toJSONString(param.getFilterConditions()));
        dataFetchTask.setOutputFields(JSON.toJSONString(param.getOutputFields()));
        dataFetchTask.setTaskStatus(DataFetchTaskStatus.PROCESSING.getCode());
        dataFetchTask.setDataCount(0);
        User user = UserUtils.getUser();
        dataFetchTask.setCreateMis(Objects.nonNull(user) ? user.getLogin()
                : com.meituan.aigc.aida.labeling.common.constant.CommonConstants.FieldName.UNKNOWN);
        dataFetchTask.setCreateName(Objects.nonNull(user) ? user.getName()
                : com.meituan.aigc.aida.labeling.common.constant.CommonConstants.FieldName.UNKNOWN);
        dataFetchTask.setCreateTime(new Date());
        dataFetchTask.setUpdateTime(new Date());
        dataFetchTask.setIsDelete(false);
        dataFetchTask.setContactType(param.getContactType());
        return dataFetchTask;
    }

    /**
     * 校验创建数据拉取任务参数
     *
     * @param param 参数
     */
    private void validCreateTaskParam(DataFetchTaskCreateParam param) {
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getName()), "请输入任务名称");
        CheckUtil.paramCheck(Objects.nonNull(param.getDataType()), "请选择数据类型");
        CheckUtil.paramCheck(Objects.nonNull(param.getFilterConditions()), "请填写筛序条件");
        CheckUtil.paramCheck(Objects.nonNull(param.getFilterConditions().getDataCapturePoint()), "请选择数据采集时刻");
        CheckUtil.paramCheck(DateUtil.isValidDateTime(param.getFilterConditions().getTriggerTimeStart(),
                        DateUtil.DATE_PATTERN_YYYY_MM_DD)
                        && DateUtil.isValidDateTime(param.getFilterConditions().getTriggerTimeEnd(),
                        DateUtil.DATE_PATTERN_YYYY_MM_DD),
                "请选择埋点触发日期");
        // 标问Id非必填，有值时校验格式
        if (StringUtils.isNotBlank(param.getFilterConditions().getTypicalQuestionIds())) {
            CheckUtil.paramCheck(isValidCommaDelimitedValues(param.getFilterConditions().getTypicalQuestionIds()),
                    "请正确填写标准问ID");
            // 热线渠道不支持标问ID筛选
            if ("call".equals(param.getContactType())) {
                throw new AidaTrainingCheckException("热线渠道不支持标问ID筛选");
            }
        }
        CheckUtil.paramCheck(isValidCommaDelimitedValues(param.getFilterConditions().getMisList()), "请正确填写客服mis号");
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(param.getOutputFields()), "请填写机器人返回结果");
        param.getOutputFields().forEach(field -> {
            CheckUtil.paramCheck(StringUtils.isNotBlank(field.getRobotId()), "机器人ID不能为空");
            CheckUtil.paramCheck(StringUtils.isNotBlank(field.getRobotName()), "机器人名称不能为空");
            CheckUtil.paramCheck(StringUtils.isNotBlank(field.getFieldName()), "请填写机器人返回字段名称");
        });
    }

    /**
     * 校验字符串值是否符合英文逗号分割要求
     *
     * @param input 字符串
     * @return 结果
     */
    private boolean isValidCommaDelimitedValues(String input) {
        if (StringUtils.isBlank(input) || input.endsWith(CommonConstants.FieldName.ENGLISH_COMMA)) {
            return false;
        }
        // 分割逗号分隔的字符串
        String[] values = input.split(CommonConstants.FieldName.ENGLISH_COMMA);
        // 检查是否存在空值
        if (values.length == 0) {
            return false;
        }
        // 验证每个值是否符合要求
        for (String value : values) {
            String trimmedValue = value.trim();
            if (StringUtils.isBlank(trimmedValue) || trimmedValue.contains(CommonConstants.FieldName.CHINESE_COMMA)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取机器人信息
     *
     * @param robotIdSet 机器人ID集合
     * @return 机器人信息
     */
    private List<RobotVO> buildResult(Set<String> robotIdSet) {
        List<RobotVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(robotIdSet)) {
            return resultList;
        }
        // 调用aida接口查询机器人名称
        robotIdSet.forEach(robotId -> {
            try {
                AppConfigRequestParam param = new AppConfigRequestParam();
                param.setAppId(robotId);
                param.setAuthorization(String.format("Bearer %s", aidaConfigService.getAppToken(robotId)));
                param.setUser(Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse(""));
                log.info("获取埋点机器人信息,查询AidaConfigService.getAppConfigNoToken接口入参：{}", JSON.toJSONString(param));
                AppConfigDTO appConfigNoToken = aidaConfigService.getAppConfigNoToken(param);
                if (Objects.isNull(appConfigNoToken)) {
                    log.warn("获取埋点机器人信息查询Aida结果为空，robotId={}", robotId);
                    return;
                }
                String appId = appConfigNoToken.getAppId();
                String appName = appConfigNoToken.getAppName();
                if (StringUtils.isBlank(appId) || StringUtils.isBlank(appName)) {
                    log.warn("获取埋点机器人信息查询Aida机器人名称或ID为空，robotId={}", robotId);
                    return;
                }
                RobotVO robotVO = RobotVO.builder()
                        .robotId(appId)
                        .robotName(appName)
                        .build();
                resultList.add(robotVO);
            } catch (Exception e) {
                log.error("获取机器人信息失败，robotId={}，错误信息：{}", robotId, e.getMessage(), e);
                throw new AidaTrainingCheckException(e.getMessage() + ",appId=" + robotId);
            }
        });
        return resultList;
    }
}
