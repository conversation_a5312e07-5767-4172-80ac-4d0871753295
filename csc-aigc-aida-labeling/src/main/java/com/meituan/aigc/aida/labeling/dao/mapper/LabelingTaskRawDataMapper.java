package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawDataExample;
import com.meituan.aigc.aida.labeling.dao.po.LabelingRawDataCopyRelationPO;
import com.meituan.aigc.aida.labeling.dao.po.LabelingTaskRawDataPO;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingTaskCountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LabelingTaskRawDataMapper {
    long countByExample(LabelingTaskRawDataExample example);

    int deleteByExample(LabelingTaskRawDataExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LabelingTaskRawData record);

    int insertSelective(LabelingTaskRawData record);

    List<LabelingTaskRawData> selectByExample(LabelingTaskRawDataExample example);

    LabelingTaskRawData selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LabelingTaskRawData record, @Param("example") LabelingTaskRawDataExample example);

    int updateByExample(@Param("record") LabelingTaskRawData record, @Param("example") LabelingTaskRawDataExample example);

    int updateByPrimaryKeySelective(LabelingTaskRawData record);

    int updateByPrimaryKey(LabelingTaskRawData record);

    void batchInsert(@Param("list") List<LabelingTaskRawData> list);

    List<LabelingTaskRawData> listByIds(@Param("rawDataIds") List<Long> rawDataIds);

    List<LabelingTaskRawDataPO> getCountByTaskIds(@Param("taskIds") List<Long> taskIds);

    List<LabelingTaskRawData> pageQueryByTaskIdWithOffset(@Param("taskId") Long taskId,
                                                          @Param("offset") int offset,
                                                          @Param("limit") int limit);

    List<String> pageSessionByTaskIdWithOffset(@Param("taskId") Long taskId,
                                                          @Param("offset") int offset,
                                                          @Param("limit") int limit);

    LabelingTaskCountDTO listCountByTaskId(@Param("taskId") Long taskId);

    List<LabelingRawDataCopyRelationPO> listCopyRelationByIds(@Param("rawDataIds") List<Long> rawDataIds);
}