package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 检查项表DTO
 */
@Data
public class InspectionItemDTO {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 标注项名称
     */
    private String name;

    /**
     * 数据类型 1:文本/2:枚举
     */
    private Integer type;

    /**
     * 是否背靠背一致率标注项 1:是/2:否
     */
    private Integer isCompare;

    /**
     * 是否必填 1:是/2:否
     */
    private Integer isRequired;

    /**
     * 值列表
     */
    private List<InspectionItemValueDTO> value;
} 