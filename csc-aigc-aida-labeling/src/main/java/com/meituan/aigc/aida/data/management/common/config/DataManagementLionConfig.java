package com.meituan.aigc.aida.data.management.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/4/3 15:46
 * @Version: 1.0
 */
@Slf4j
@Data
@Component
public class DataManagementLionConfig {

    /**
     * talos Hive 查询账号
     */
    @MdpConfig("talos.username")
    private String talosUsername;
    /**
     * talos Hive 查询密码
     */
    @MdpConfig("talos.password")
    private String talosPassword;
    /**
     * talos Hive 查询等待时间
     */
    @MdpConfig("talos.waiting.time:4")
    private Integer talosWaitingTime;
    /**
     * talos Hive 一次查询条数
     */
    @MdpConfig("data.pull.hive.max.size:200")
    private Integer dataPullHiveMaxSize;
    /**
     * 创建数据拉取任务 talos Hive 查询等待时间
     */
    @MdpConfig("create.data.fetch.task.talos.waiting.time:10")
    private Integer createDataFetchTaskTalosWaitingTime;
    /**
     * 数据获取任务页面地址
     */
    @MdpConfig("data.fetch.page.url")
    private String dataFetchPageUrl;
    /**
     * ac执行记录消费开关
     */
    @MdpConfig("ac.invoke.record.consumer.switch:false")
    private Boolean acInvokeSwitch;
    /**
     * 创建数据拉取任务 MOCK 从Hive拉取数据的内容
     */
    @MdpConfig("mock.fetch.data.from.hive.item:null")
    private String mockFetchDataFromHiveItem;

    /**
     * 数据埋点规则权限列表
     */
    @MdpConfig("data.buried.points.permission.list: []")
    private String dataBuriedPointsPermissionList;

    /**
     * 消费海豚对话消息开关
     */
    @MdpConfig("chat.consumer.switch:false")
    private Boolean chatConsumerSwitch;

    /**
     * 消费木星镜像录音对话消息开关
     */
    @MdpConfig("voice.consumer.switch:false")
    private Boolean voiceConsumerSwitch;

    /**
     * 弹屏数据缓存过期时间
     */
    @MdpConfig("signal.mock.popup.cache.expireMinutes:10")
    private Integer popupCacheExpireMinutes;
    /**
     * 接口参数缓存过期时间
     */
    @MdpConfig("signal.mock.interface.param.cache.minutes:10")
    private Integer interfaceParamCacheMinutes;
    /**
     * 客服信息缓存过期时间
     */
    @MdpConfig("csc.foundation.signal.mock.staff.cache.minutes:30")
    private Integer staffCacheMinutes;
    /**
     * 查询hive埋点表sql前缀
     */
    @MdpConfig("query.aida.training.signal.logging.sql.pre")
    private String queryAidaTrainingSignalLoggingSqlPre;
    /**
     * 导出数据获取任务详情数据总量限制
     */
    @MdpConfig("export.data.fetch.item.total.num:10000")
    private Integer exportDataFetchItemTotalNum;
    /**
     * 创建数据获取任务开关 true 开启可创建任务 false 关闭，不可创建任务
     */
    @MdpConfig("data.fetch.task.create.switch")
    private Boolean dataFetchTaskCreateSwitch;
    /**
     * 信号埋点机器人配置
     */
    @MdpConfig("signal.tracking.robot.config")
    private String signalTrackingRobotConfig;
    /**
     * 调用AI搭是否启用传入太平洋随路参数
     */
    @MdpConfig("invoke.aida.with.butler.popup.param.switch:false")
    private Boolean invokeAidaWithButlerPopupParamSwitch;
    /**
     * 数据洞察—terms buckets 最大数量
     */
    @MdpConfig("insight.buckets.max.size:100")
    private Integer insightTermsBucketsSize;
    /**
     * 数据洞察——可枚举值最大数量
     */
    @MdpConfig("insight.buckets.enumerable.max.size:20")
    private Integer insightEnumerableSize;

    /**
     * 打开返回mock数据的机器人类型列表
     */
    @MdpConfig("mock.data.robot.signal.type.list:[]")
    private String mockDataRobotSignalTypeList;

    /**
     * 信号埋点规则MIS数量上限配置
     */
    @MdpConfig("signal.rule.upper.limit.config")
    private String signalRuleUpperLimitConfig;


    public List<String> getRobotList() {
        if(StringUtils.isBlank(mockDataRobotSignalTypeList)){
            return Collections.emptyList();
        }
        try {
            return JSON.parseObject(mockDataRobotSignalTypeList, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.error("获取{}配置异常：", mockDataRobotSignalTypeList, e);
        }
        return Collections.emptyList();
    }


    public List<Map<String, String>> getMockFetchDataFromHiveItem() {
        if (StringUtils.isBlank(mockFetchDataFromHiveItem) || Objects.equals("null", mockFetchDataFromHiveItem)) {
            return Collections.emptyList();
        }
        try {
            return JSON.parseObject(mockFetchDataFromHiveItem, new TypeReference<List<Map<String, String>>>() {
            });
        } catch (Exception e) {
            log.error("创建数据拉取任务 MOCK 从Hive拉取数据的内容,发生异常：", e);
            return Collections.emptyList();
        }
    }


    public List<String> getDataBuriedPointsPermissionList() {
        try {
            return JSON.parseObject(dataBuriedPointsPermissionList, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.error("获取dataBuriedPointsPermissionList配置异常：", e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取信号埋点规则MIS数量上限配置
     *
     * 设计说明：
     * 该配置支持动态调整MIS数量限制，当Lion配置解析异常时返回默认配置以保证系统稳定性
     */
    public SignalRuleUpperLimitConfig getSignalRuleUpperLimitConfig() {
        try {
            if (StringUtils.isBlank(signalRuleUpperLimitConfig)) {
                // 配置为空时返回默认配置
                return new SignalRuleUpperLimitConfig();
            }
            return JSON.parseObject(signalRuleUpperLimitConfig, SignalRuleUpperLimitConfig.class);
        } catch (Exception e) {
            log.error("获取signalRuleUpperLimitConfig配置异常：", e);
            // 配置解析异常时返回默认配置，确保系统正常运行
            return new SignalRuleUpperLimitConfig();
        }
    }

    /**
     * 信号埋点规则MIS数量上限配置
     *
     * 配置格式示例：
     * {
     *   "limitNumber": 570,
     *   "limitMsg": "当前配置的mis号已到达上限，如需扩量，请联系平台侧同学解决。"
     * }
     *
     * 业务说明：
     * - limitNumber: MIS数量上限，设置为0或负数表示不限制
     * - limitMsg: 超限时的友好提示信息，支持动态配置
     */
    @Data
    public static class SignalRuleUpperLimitConfig {

        /**
         * 默认MIS数量限制
         * 设计考虑：570是基于6.25日配置的mis去重后的数量给出的上限值
         */
        private static final Integer DEFAULT_LIMIT_NUMBER = 570;

        /**
         * 默认超限错误信息
         * 用户友好的提示信息，引导用户联系平台侧解决
         */
        private static final String DEFAULT_LIMIT_MSG = "当前配置的mis号已到达上限，如需扩量，请联系平台侧同学解决。";

        /**
         * MIS数量限制
         */
        private Integer limitNumber = DEFAULT_LIMIT_NUMBER;

        /**
         * 超过限制时的错误信息
         */
        private String limitMsg = DEFAULT_LIMIT_MSG;
    }
}
