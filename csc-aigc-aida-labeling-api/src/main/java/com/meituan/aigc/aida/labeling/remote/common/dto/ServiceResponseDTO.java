package com.meituan.aigc.aida.labeling.remote.common.dto;

import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

import com.meituan.aigc.aida.labeling.remote.common.enums.ResultCode;

/**
 * 统一API响应结果封装
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceResponseDTO<T> implements Serializable {
    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 状态码
     */
    private int code;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 数据
     */
    private T data;

    /**
     * 成功返回结果
     *
     * @param data 获取的数据
     */
    public static <T> ServiceResponseDTO<T> success(T data) {
        return new ServiceResponseDTO<T>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }
    
    /**
     * 成功返回结果
     *
     * @param data 获取的数据
     * @param message 提示信息
     */
    public static <T> ServiceResponseDTO<T> success(T data, String message) {
        return new ServiceResponseDTO<T>(ResultCode.SUCCESS.getCode(), message, data);
    }
    
    /**
     * 失败返回结果
     * @param errorCode 错误码
     */
    public static <T> ServiceResponseDTO<T> failed(ResultCode errorCode) {
        return new ServiceResponseDTO<T>(errorCode.getCode(), errorCode.getMessage(), null);
    }
    
    /**
     * 失败返回结果
     * @param errorCode 错误码
     * @param message 错误信息
     */
    public static <T> ServiceResponseDTO<T> failed(ResultCode errorCode, String message) {
        return new ServiceResponseDTO<T>(errorCode.getCode(), message, null);
    }
}