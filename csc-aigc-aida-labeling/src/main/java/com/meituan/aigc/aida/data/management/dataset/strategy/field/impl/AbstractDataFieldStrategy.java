package com.meituan.aigc.aida.data.management.dataset.strategy.field.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategy;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 数据字段处理抽象基类
 */
@Slf4j
public abstract class AbstractDataFieldStrategy implements DataFieldStrategy {

    protected static final String LION_FIELD_TYPE_REGEX_MAPPING_KEY = "aida.data.field.type.regex.mapping";

    @Override
    public void validateValue(String value) throws Exception {
        // 默认不做任何校验
    }

    /**
     * 通过Lion配置的正则表达式匹配数据类型
     *
     * @param value 字段值
     * @return 匹配到的字段类型，如果未匹配到则返回null
     */
    protected Integer matchByLionRegex(String value) {
        try {
            String configValue = Lion.getString(ConfigUtil.getAppkey(), LION_FIELD_TYPE_REGEX_MAPPING_KEY);
            if (StringUtils.isBlank(configValue)) {
                return null;
            }

            Map<String, String> regexMapping = JSON.parseObject(configValue, Map.class);
            if (MapUtils.isEmpty(regexMapping)) {
                return null;
            }

            for (Map.Entry<String, String> entry : regexMapping.entrySet()) {
                String typeCode = entry.getKey();
                String regex = entry.getValue();

                if (StringUtils.isNotBlank(regex) && Pattern.matches(regex, value)) {
                    return Integer.parseInt(typeCode);
                }
            }
        } catch (Exception e) {
            log.warn("Lion配置正则匹配失败", e);
        }

        return null;
    }
} 