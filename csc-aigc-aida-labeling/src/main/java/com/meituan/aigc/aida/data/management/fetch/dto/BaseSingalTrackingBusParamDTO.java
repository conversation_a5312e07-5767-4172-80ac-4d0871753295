package com.meituan.aigc.aida.data.management.fetch.dto;

import com.meituan.aigc.aida.data.management.fetch.common.enums.ChatMessageFromEnum;
import com.meituan.aigc.aida.data.management.fetch.dto.pacific.PacificButlerContextParamDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 信号埋点业务参数BaseDTO
 *
 * <AUTHOR> wangyibo
 * @date : 2025/4/9
 */
@Data
public class BaseSingalTrackingBusParamDTO implements Serializable {

    /**
     * UUID
     */
    private String uuid;

    /**
     * 智能侧SessionId
     */
    private String sessionId;

    /**
     * 标问ID列表
     */
    private List<String> typicalQuestionIds;

    /**
     * 联系类型
     */
    private String contactType;

    /**
     * 联系ID
     */
    private String contactId;

    /**
     * 客服ChatId, 当联系类型为在线时, chatId == contactId
     */
    private String chatId;

    /**
     * 消息发送时间, 毫秒时间戳
     */
    private String messageOccurredTime;

    /**
     * 客服MIS
     */
    private String staffMis;

    /**
     * 消息来源: STAFF: 客服发送的消息, CUSTOMER: 用户发送的消息
     * {@link ChatMessageFromEnum#code}
     */
    private String chatMessageFromType;

    /**
     * 客服发送的消息内容
     */
    private String staffMessageContent;

    /**
     * 用户发送的消息内容
     */
    private String customerMessageContent;

    /**
     * 业务消息ID，用于标识一条消息的唯一业务消息ID，比如在线的海豚消息ID
     */
    private String bizMessageId;

    /**
     * 随路信息-太平洋管家
     */
    private PacificButlerContextParamDTO pathParamsButler;

    /**
     * 随路信息-太平洋弹屏
     */
    private Map<String, Object> pathParamsPopup;
}
