package com.meituan.aigc.aida.labeling.param;

import com.meituan.aigc.aida.labeling.pojo.dto.LabelingResultSaveDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/3/4 10:15
 * @Version: 1.0
 * 标注结果保存参数
 */
@Data
public class LabelingResultSaveParam implements Serializable {
    /**
     * 子任务ID
     */
    private Long subTaskId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * session标注结果
     */
    private LabelingResultSaveDTO sessionDimension;

    /**
     * query标注结果
     */
    private List<LabelingResultSaveDTO> conversations;

}
