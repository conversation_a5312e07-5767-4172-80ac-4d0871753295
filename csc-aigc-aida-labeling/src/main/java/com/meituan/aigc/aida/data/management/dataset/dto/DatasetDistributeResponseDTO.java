package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 数据集分布情况查询响应
 *
 * <AUTHOR>
 * @date 2025-06-03
 * @description 数据集分析 - 分布查询响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetDistributeResponseDTO implements Serializable {

    /**
     * 分布结果列表
     */
    private List<DistributeResultItem> resultList;

    /**
     * 分布结果项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DistributeResultItem implements Serializable {

        /**
         * 分组key
         */
        private String key;

        /**
         * 分组列表
         */
        private List<DistributeGroupItem> groupList;
    }

    /**
     * 分组项数据
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DistributeGroupItem implements Serializable {

        /**
         * 值
         */
        private String value;

        /**
         * 数量
         */
        private Long count;

        /**
         * 占比
         */
        private Double ratio;
    }
} 