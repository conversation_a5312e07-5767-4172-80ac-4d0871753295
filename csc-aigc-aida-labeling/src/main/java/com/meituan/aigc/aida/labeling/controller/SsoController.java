package com.meituan.aigc.aida.labeling.controller;

import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.pojo.dto.UserDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class SsoController {

    /**
     * 未获取到用户信息code码
     */
    private static final Integer NOT_LOGIN_CODE = CommonConstants.HttpStatus.UNAUTHORIZED;

    /**
     * 获取到用户信息code码
     */
    private static final Integer LOGIN_CODE = CommonConstants.HttpStatus.SUCCESS;

    @ApiOperation(value = "获取用户登录信息", notes = "前端获取用户登录信息，sso接入使用", httpMethod = "GET", response = Result.class)
    @GetMapping("/user")
    public Result<UserDTO> getUser() {
        Result<UserDTO> result = Result.create();
        User user = UserUtils.getUser();
        if (user == null) {
            log.warn("未获取到用户信息");
            result.setCode(NOT_LOGIN_CODE);
            result.setMessage("未获取到用户信息");
            return result;
        }
        result.setCode(LOGIN_CODE);
        result.setData(convertToUserDTO(user));
        return result;
    }

    private UserDTO convertToUserDTO(User user) {
        return UserDTO.builder()
                .mis(user.getLogin())
                .name(user.getName())
                .id(user.getId())
                .build();
    }
}
