package com.meituan.aigc.aida.labeling.param.task;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/6/9 19:15
 * @Version: 1.0
 */
@Data
public class TaskReLabelingParam implements Serializable {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 复标分组
     */
    private List<LabelingGroupParam> reLabelingGroupIds;


    @Data
    public static class LabelingGroupParam {

        /**
         * 分组ID
         */
        private Long groupId;

        /**
         * 复标子任务ID
         */
        private List<Long> reLabelingSubTaskIds;

    }

}
