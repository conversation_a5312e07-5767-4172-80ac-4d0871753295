package com.meituan.aigc.aida.data.management.fetch.dao.model;

public class DataFetchRecordWithBLOBs extends DataFetchRecord {

    /**
     * 客服发送的消息内容
     */
    private String staffMessageContent;
    /**
     * 业务传递的信息
     */
    private String extraInfo;
    /**
     * 埋点通用信息
     */
    private String commonInfo;
    /**
     * 用户发送的消息内容
     */
    private String customerMessageContent;
    /**
     * 应用入参
     */
    private String inputs;

    /**
     * 机器人请求信息
     */
    private String robotInvokeInfo;

    public String getStaffMessageContent() {
        return staffMessageContent;
    }

    public void setStaffMessageContent(String staffMessageContent) {
        this.staffMessageContent = staffMessageContent == null ? null : staffMessageContent.trim();
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo == null ? null : extraInfo.trim();
    }

    public String getCommonInfo() {
        return commonInfo;
    }

    public void setCommonInfo(String commonInfo) {
        this.commonInfo = commonInfo == null ? null : commonInfo.trim();
    }

    public String getCustomerMessageContent() {
        return customerMessageContent;
    }

    public void setCustomerMessageContent(String customerMessageContent) {
        this.customerMessageContent = customerMessageContent == null ? null : customerMessageContent.trim();
    }

    public String getInputs() {
        return inputs;
    }

    public void setInputs(String inputs) {
        this.inputs = inputs == null ? null : inputs.trim();
    }

    public String getRobotInvokeInfo() {
        return robotInvokeInfo;
    }

    public void setRobotInvokeInfo(String robotInvokeInfo) {
        this.robotInvokeInfo = robotInvokeInfo == null ? null : robotInvokeInfo.trim();
    }
}