package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.Data;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/3/11 20:00
 * @Version: 1.0
 * 任务数据导入模版
 */
@Data
public class TaskDataImportTemplateVO {

    /**
     * 任务类型编码
     */
    private Integer taskTypeCode;

    /**
     * 任务类型名称
     */
    private String taskTypeName;

    /**
     * 数据模板配置列表
     */
    private List<DataTemplateConfig> dataTemplateConfig;

    /**
     * 数据模板配置实体类
     */
    @Data
    public static class DataTemplateConfig {

        /**
         * 数据类型编码
         */
        private Integer dataTypeCode;

        /**
         * 数据类型名称
         */
        private String dataTypeName;

        /**
         * 模板URL
         */
        private String templateUrl;
    }
}
