package com.meituan.aigc.aida.common.enums;

import com.meituan.aigc.aida.common.enums.base.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Elasticsearch操作类型枚举
 * 用于标识不同类型的ES操作，主要用于异常处理和日志记录
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
@Getter
@AllArgsConstructor
public enum EsOperationEnum implements BaseEnum<String> {

    /**
     * 插入操作
     * 包括单条文档插入和批量文档插入
     */
    INSERT("INSERT", "插入操作"),

    /**
     * 更新操作
     * 包括单条文档更新、批量文档更新和根据查询条件更新
     */
    UPDATE("UPDATE", "更新操作"),

    /**
     * 删除操作
     * 包括单条文档删除、批量文档删除和根据查询条件删除
     */
    DELETE("DELETE", "删除操作"),

    /**
     * 查询操作
     * 包括精确查询、模糊查询、范围查询等
     */
    QUERY("QUERY", "查询操作"),

    /**
     * 聚合操作
     * 用于数据统计和分析
     */
    AGGREGATION("AGGREGATION", "聚合操作"),

    /**
     * 批量操作
     * 多个操作的组合，如批量插入、批量更新等
     */
    BULK("BULK", "批量操作"),

    /**
     * 索引管理操作
     * 包括创建索引、删除索引、更新映射等
     */
    INDEX_ADMIN("INDEX_ADMIN", "索引管理操作"),

    /**
     * 别名操作
     * 包括添加别名、删除别名等
     */
    ALIAS("ALIAS", "别名操作"),

    /**
     * 刷新操作
     * 强制刷新索引，使最近的更改对搜索可见
     */
    REFRESH("REFRESH", "刷新操作"),

    /**
     * 滚动查询操作
     * 用于处理大结果集的分批查询
     */
    SCROLL("SCROLL", "滚动查询操作"),

    /**
     * 模板操作
     * 包括索引模板的创建、更新和删除
     */
    TEMPLATE("TEMPLATE", "模板操作"),

    /**
     * 分析操作
     * 包括文本分析、分词等操作
     */
    ANALYZE("ANALYZE", "分析操作");

    /**
     * 操作类型代码
     */
    private final String code;

    /**
     * 操作类型描述
     */
    private final String value;
}