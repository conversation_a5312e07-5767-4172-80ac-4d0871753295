package com.meituan.aigc.aida.data.management.fetch.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.meituan.csc.aigc.runtime.dto.aida.MockConfigDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.csc.aircraft.api.dto.InterfacePolymericResponse;
import com.dianping.csc.aircraft.api.dto.inner.InputParamDTO;
import com.dianping.csc.aircraft.api.service.inner.IFaceRemoteService;
import com.dianping.csc.haitun.kefu.dto.ResponseDTO;
import com.dianping.csc.haitun.kefu.dto.StaffInfoFacadeDTO;
import com.dianping.csc.haitun.kefu.service.StaffFacadeService;
import com.dianping.csc.pacific.domain.common.RespDTO;
import com.dianping.haitun.cases.dto.SessionBriefDTO;
import com.dianping.haitun.cases.service.ManualDialogInfoService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.meituan.aigc.aida.common.util.BizThreadUtils;
import com.meituan.aigc.aida.common.util.ObjectConverter;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.fetch.biz.pacific.butler.ContextUtils;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.TrackingRulesChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs;
import com.meituan.aigc.aida.data.management.fetch.dto.BaseSingalTrackingBusParamDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.PopupAcInterfaceDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.DelayTriggerRobotDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.SignalMockTriggerMessageDTO;
import com.meituan.aigc.aida.data.management.fetch.helper.AcInputDistinctHelper;
import com.meituan.aigc.aida.data.management.fetch.helper.AidaSignalLoggingAppInvokeHelper;
import com.meituan.aigc.aida.data.management.fetch.helper.SignalTrackingRulesHelper;
import com.meituan.aigc.aida.data.management.fetch.helper.TypicalQuestionIdQueryHelper;
import com.meituan.aigc.aida.data.management.fetch.mq.producer.DelayTriggerRobotToMafkaProducer;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;
import com.meituan.aigc.aida.data.management.fetch.service.PacificAcInterfaceLogService;
import com.meituan.aigc.aida.data.management.fetch.service.SignalLoggingTriggerService;
import com.meituan.mtrace.Tracer;
import com.sankuai.csc.workbench.bff.api.common.enums.ContactTypeEnum;
import com.sankuai.pacific.cscpacific.contact.chat.api.dto.PopupDataRequestDTO;
import com.sankuai.pacific.cscpacific.contact.chat.api.remote.PopupRemoteService;

import lombok.extern.slf4j.Slf4j;

/**
 * 信号模拟触发服务抽象基类
 * <p>
 * 该抽象类包含在线和热线埋点触发的公共逻辑，主要功能包括:
 * 1. 管理缓存(弹窗数据、接口参数、客服信息)
 * 2. 多种接口的参数校验和处理
 * 3. 执行AI搭埋点Workflow机器人
 * 4. 公共的业务逻辑处理
 *
 * <AUTHOR>
 * @date 2025/06/06
 */
@Slf4j
public abstract class AbstractSignalLoggingTriggerService implements SignalLoggingTriggerService {

    protected static final String LOG_PREFIX = "[AbstractSignalLoggingTriggerService]";

    protected final ExecutorService signalLoggingInvokeAidaExecuteThreadPool = BizThreadUtils.createRhinoThreadPool(
            "signal-logging-invoke-aida-execute", 10, 20, 1000, 60, TimeUnit.MINUTES,
            "signal-logging-invoke-aida-execute-id-%d");

    @Resource
    protected ContextUtils contextUtils;

    @Autowired
    protected StaffFacadeService staffFacadeService;

    @Autowired
    protected PopupRemoteService popupRemoteService;

    @Autowired
    protected IFaceRemoteService faceRemoteService;

    @Autowired
    protected ManualDialogInfoService manualDialogInfoService;

    @Resource
    protected SignalTrackingRulesHelper signalTrackingRulesHelper;

    @Resource
    protected AidaSignalLoggingAppInvokeHelper aidaAppInvokeHelper;

    @Resource
    protected PacificAcInterfaceLogService pacificAcInterfaceLogService;

    @Resource
    protected AcInputDistinctHelper acInputDistinctHelper;

    @Resource
    protected TypicalQuestionIdQueryHelper typicalQuestionIdQueryHelper;

    @Resource
    protected DataManagementLionConfig dataManagementLionConfig;

    @Resource
    protected DelayTriggerRobotToMafkaProducer delayTriggerRobotToMafkaProducer;

    /**
     * 接口参数缓存
     */
    protected Cache<String, Map<String, Object>> popupDataCache;
    /**
     * 接口参数缓存
     */
    protected LoadingCache<Long, Set<String>> validParamsCache;
    /**
     * 客服信息缓存
     */
    protected LoadingCache<Long, StaffInfoFacadeDTO> staffInfoCache;

    /**
     * 初始化缓存组件
     * <p>
     * 初始化三个主要缓存:
     * 1. 弹窗数据缓存(popupDataCache): 缓存客户弹窗信息
     * 2. 接口参数缓存(validParamsCache): 缓存接口的有效参数集合
     * 3. 客服信息缓存(staffInfoCache): 缓存客服基本信息
     */
    @PostConstruct
    public void init() {
        // 初始化弹窗数据缓存
        popupDataCache = CacheBuilder.newBuilder()
                .expireAfterWrite(Optional.ofNullable(dataManagementLionConfig.getPopupCacheExpireMinutes()).orElse(10), TimeUnit.MINUTES)
                .maximumSize(1000) // 最多缓存1000个key
                .build();
        log.info("{}, init popupDataCache with expireMinutes = [{}]", LOG_PREFIX, dataManagementLionConfig.getPopupCacheExpireMinutes());

        // 初始化接口参数缓存
        validParamsCache = CacheBuilder.newBuilder()
                .expireAfterWrite(Optional.ofNullable(dataManagementLionConfig.getInterfaceParamCacheMinutes()).orElse(10), TimeUnit.MINUTES)
                .maximumSize(1000)
                .build(new CacheLoader<Long, Set<String>>() {
                    @Override
                    public Set<String> load(@NotNull Long interfaceId) {
                        InterfacePolymericResponse response = faceRemoteService.getPolymericResponse(interfaceId);
                        return response.getInputParamDTOList().stream()
                                .map(InputParamDTO::getCode)
                                .collect(Collectors.toSet());
                    }
                });
        log.info("{}, init validParamsCache with expireMinutes = [{}]", LOG_PREFIX, dataManagementLionConfig.getInterfaceParamCacheMinutes());

        staffInfoCache = CacheBuilder.newBuilder()
                .expireAfterWrite(Optional.ofNullable(dataManagementLionConfig.getStaffCacheMinutes()).orElse(30), TimeUnit.MINUTES)
                .maximumSize(1000)
                .build(new CacheLoader<Long, StaffInfoFacadeDTO>() {
                    @Override
                    public StaffInfoFacadeDTO load(@NotNull Long chatStaffId) {
                        ResponseDTO<StaffInfoFacadeDTO> chatStaffInfoResp = staffFacadeService
                                .getStaffInfoById(chatStaffId.intValue());
                        log.info("{}, 加载客服信息缓存响应, resp = [{}]", LOG_PREFIX,
                                ObjectConverter.convertToJson(chatStaffInfoResp));

                        if (null == chatStaffInfoResp || !chatStaffInfoResp.isSuccess()
                                || null == chatStaffInfoResp.getData()) {
                            log.warn("{}, 加载客服信息缓存失败, chatStaffId = [{}]", LOG_PREFIX, chatStaffId);
                            return null;
                        }
                        return chatStaffInfoResp.getData();
                    }
                });
        log.info("{}, init staffInfoCache with expireMinutes = [{}]", LOG_PREFIX, dataManagementLionConfig.getStaffCacheMinutes());
    }

    /**
     * 处理信号Mock触发消息
     * <p>
     * 执行流程:
     * 1. 校验消息合法性
     * 2. 构建全局参数，包括会话信息和弹窗数据
     * 3. 获取并执行所有Mock脚本
     * 4. 并行处理每个脚本的前置接口记录
     *
     * @param messageDTO 触发消息DTO，包含会话信息和用户消息
     */
    protected void processSignalLoggingTrigger(SignalMockTriggerMessageDTO messageDTO) {
        // 1. 解析消息
        if (Objects.isNull(messageDTO)) {
            String errorMsg = String.format("%s, 消息体解析为空", LOG_PREFIX);
            log.warn(errorMsg);
            Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "EmptyMessage", errorMsg);
            return;
        }
        log.info("{}, 消费消息, 触发AI搭埋点机器人, 渠道: {}, msg:{}", LOG_PREFIX, messageDTO.getContactType(), messageDTO);

        // 2. 构建AI搭随路参数信息
        BaseSingalTrackingBusParamDTO busParamDTO = buildBaseSingalTrackingBusParamDTO(messageDTO);
        if (Objects.isNull(busParamDTO)) {
            return;
        }

        // 3. 根据标问列表、客服mis, 获取弹屏AC接口与机器人ID的映射关系
        Triple<Map<String, List<SignalTrackingRulesHelper.RobotConfig>>, Map<String, List<String>>, Map<String, Integer>> acInterfaceToAidaAppIdsMap = signalTrackingRulesHelper
                .getAcInterfaceToAidaAppIdsMap(buildRobotListQuery(busParamDTO));
        // 所有机器人id
        Map<String, List<SignalTrackingRulesHelper.RobotConfig>> acToRobotIdsMap = acInterfaceToAidaAppIdsMap.getLeft();
        if (MapUtils.isEmpty(acToRobotIdsMap)) {
            log.warn("{}, 没有可用的机器人列表", LOG_PREFIX);
            return;
        }
        // 延迟执行机器人id
        Map<String, List<String>> acInterfaceToDelayRobotIdsMap = acInterfaceToAidaAppIdsMap.getMiddle();

        // 4. 收集所有配置的弹屏AC ID
        Set<String> cfgPopupAcIds = acToRobotIdsMap.keySet();

        // 5. 根据 contactId, contactType, staffMis 查找星脉埋点表中所有的AC input
        List<PacificAcInterfaceLogWithBlobs> acLogs = pacificAcInterfaceLogService
                .listByContactIdContactTypeAndStaffMis(busParamDTO.getContactId(), busParamDTO.getContactType(),
                        busParamDTO.getStaffMis());
        // 获取一个包含acId和acInput的列表
        List<PopupAcInterfaceDTO> toTriggerAcInputList = getToTriggerAcInputList(acLogs, cfgPopupAcIds);

        // 没有找到需要触发的AC接口记录，直接返回
        if (CollectionUtils.isEmpty(toTriggerAcInputList)) {
            log.warn("{}, 没有找到需要触发的AC接口记录, Message:{}", LOG_PREFIX, JSONObject.toJSONString(messageDTO));
            return;
        }

        // 6. 根据input字段去重，使用辅助类处理去重逻辑
        List<PopupAcInterfaceDTO> distinctToTriggerAcInputList = acInputDistinctHelper
                .getDistinctAcInputs(toTriggerAcInputList, getValidParams(toTriggerAcInputList));

        // 7. 根据去重后的input, 依次调用从需要执行的AI搭机器人ID, 并根据AI搭应用返回进行埋点
        doTriggerAidaSignalLoggingApp(distinctToTriggerAcInputList, acToRobotIdsMap, busParamDTO, acInterfaceToDelayRobotIdsMap, acInterfaceToAidaAppIdsMap.getRight());
    }

    /**
     * 执行AI搭机器人调用逻辑
     *
     * @param distinctToTriggerAcInputList  去重后的AC接口输入列表
     * @param acToRobotIdsMap               AC接口到机器人ID的映射
     * @param busParamDTO                   业务参数DTO
     * @param acInterfaceToDelayRobotIdsMap AC接口到延迟机器人ID的映射
     * @param acInterfaceToDelayTimeMap     AC接口到延迟时间的映射
     */
    protected void doTriggerAidaSignalLoggingApp(List<PopupAcInterfaceDTO> distinctToTriggerAcInputList,
                                                 Map<String, List<SignalTrackingRulesHelper.RobotConfig>> acToRobotIdsMap,
                                                 BaseSingalTrackingBusParamDTO busParamDTO,
                                                 Map<String, List<String>> acInterfaceToDelayRobotIdsMap,
                                                 Map<String, Integer> acInterfaceToDelayTimeMap) {
        if (CollectionUtils.isEmpty(distinctToTriggerAcInputList)) {
            log.warn("{}, 去重后没有可用的AC接口输入数据", LOG_PREFIX);
            return;
        }
        for (PopupAcInterfaceDTO acInterface : distinctToTriggerAcInputList) {
            String acIdStr = String.valueOf(acInterface.getInterfaceId());
            List<SignalTrackingRulesHelper.RobotConfig> robotConfigList = acToRobotIdsMap.get(acIdStr);
            List<String> delayRobotIds = acInterfaceToDelayRobotIdsMap.get(acIdStr);

            if (CollectionUtils.isEmpty(robotConfigList)) {
                log.warn("{}, AC接口[{}]没有对应的AI搭机器人ID", LOG_PREFIX, acIdStr);
                continue;
            }

            log.info("{}, 开始处理AC接口[{}], 找到对应的AI搭机器人ID: {}", LOG_PREFIX, acIdStr, JSONObject.toJSONString(robotConfigList));

            // 对每个机器人ID进行调用
            for (SignalTrackingRulesHelper.RobotConfig robotConfig : robotConfigList) {
                try {
                    // 准备调用数据
                    Map inputData = ObjectConverter.convert(acInterface.getInput(), Map.class);
                    if (MapUtils.isEmpty(inputData)) {
                        log.warn("{}, AC接口[{}]输入数据解析失败, 跳过机器人[{}]调用", LOG_PREFIX, acIdStr, robotConfig.getAppId());
                        continue;
                    }
                    // 延迟执行机器人，不直接调用aidaAppInvokeHelper.invokeAidaAppAndLogging
                    if (CollectionUtils.isNotEmpty(delayRobotIds) && delayRobotIds.contains(robotConfig.getAppId())) {
                        executeDelayRobot(busParamDTO, acInterfaceToDelayTimeMap, robotConfig.getAppId(), robotConfig.getSignalType(), acIdStr, inputData);
                        continue;
                    }

                    // 准备机器人调用参数
                    log.info("{}, 调用AI搭机器人, robotId: {}, acId: {}", LOG_PREFIX, robotConfig.getAppId(), acIdStr);
                    signalLoggingInvokeAidaExecuteThreadPool
                            .submit(() -> aidaAppInvokeHelper.invokeAidaAppAndLogging(robotConfig.getAppId(), robotConfig.getSignalType(), inputData, busParamDTO,
                                    acIdStr));
                } catch (Exception e) {
                    log.error("{}, 处理AC接口[{}]的AI搭机器人[{}]调用准备时发生异常", LOG_PREFIX, acIdStr, robotConfig.getAppId(), e);
                    Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "PrepareRobotCallException",
                            e.getMessage());
                }
            }
        }
    }

    /**
     * 执行延迟机器人
     *
     * @param busParamDTO               业务参数DTO
     * @param acInterfaceToDelayTimeMap AC接口到延迟时间的映射
     * @param robotId                   机器人ID
     * @param acIdStr                   AC接口ID字符串
     * @param inputData                 输入数据
     */
    protected void executeDelayRobot(BaseSingalTrackingBusParamDTO busParamDTO,
                                     Map<String, Integer> acInterfaceToDelayTimeMap,
                                     String robotId, String signalType, String acIdStr, Map inputData) {
        if (MapUtils.isNotEmpty(acInterfaceToDelayTimeMap)
                && Objects.nonNull(acInterfaceToDelayTimeMap.get(acIdStr))) {
            DelayTriggerRobotDTO delayTriggerRobot = DelayTriggerRobotDTO.builder()
                    .robotId(robotId)
                    .signalType(signalType)
                    .acIdStr(acIdStr)
                    .traceId(Tracer.id())
                    .inputData(inputData)
                    .busParamDTO(busParamDTO)
                    .build();
            String message = JSONObject.toJSONString(delayTriggerRobot);
            Long delayTime = (long) (acInterfaceToDelayTimeMap.get(acIdStr) * 1000);
            delayTriggerRobotToMafkaProducer.sendDelayMessage(message, delayTime);
        }
    }

    /**
     * 获取接口的有效参数列表，用于后续去重
     *
     * @param toTriggerAcInputList AC接口输入列表
     * @return 有效参数集合
     */
    protected Set<String> getValidParams(List<PopupAcInterfaceDTO> toTriggerAcInputList) {
        // 获取接口的有效参数列表，用于后续去重
        Set<String> validParams = Collections.emptySet();
        try {
            PopupAcInterfaceDTO firstRecord = toTriggerAcInputList.get(0);
            Long interfaceId = firstRecord.getInterfaceId();
            if (interfaceId != null) {
                log.info("{}, 获取接口有效参数, interfaceId:{}", LOG_PREFIX, interfaceId);
                validParams = validParamsCache.get(interfaceId);
            } else {
                log.warn("{}, 无法获取接口ID, 将使用空参数集合进行去重", LOG_PREFIX);
            }
        } catch (Exception e) {
            log.error("{}, 获取接口参数失败, 将使用空参数集合进行去重", LOG_PREFIX, e);
        }
        return validParams;
    }

    /**
     * 获取需要执行的AC接口与机器人ID的映射关系
     *
     * @param acLogs        前置接口日志列表
     * @param cfgPopupAcIds 配置的弹窗AC ID集合
     * @return 需要触发的AC接口列表
     */
    protected static List<PopupAcInterfaceDTO> getToTriggerAcInputList(List<PacificAcInterfaceLogWithBlobs> acLogs,
                                                                       Set<String> cfgPopupAcIds) {
        List<PopupAcInterfaceDTO> acInterfaces = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(acLogs)) {
            for (PacificAcInterfaceLogWithBlobs acLog : acLogs) {
                Integer acId = acLog.getInterfaceId();
                String acInput = acLog.getInput();
                // 检查AC ID是否为空
                if (acId == null || StringUtils.isBlank(acInput)) {
                    String errorMsg = String.format("%s, AC ID OR Input为空", LOG_PREFIX);
                    log.error(errorMsg);
                    Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "EmptyAcIdOrInput", errorMsg);
                    continue;
                }

                String acIdStr = String.valueOf(acId);
                if (cfgPopupAcIds.contains(acIdStr)) {
                    // 创建PopupAcInterfaceDTO对象
                    PopupAcInterfaceDTO dto = new PopupAcInterfaceDTO();
                    dto.setId(acLog.getId());
                    dto.setInterfaceId(Long.valueOf(acId));
                    dto.setInput(acInput);
                    dto.setSendTime(acLog.getSendTime() != null ? Long.valueOf(acLog.getSendTime()) : null);
                    acInterfaces.add(dto);
                }
            }
        }
        return acInterfaces;
    }

    /**
     * 构建信号埋点业务参数DTO
     *
     * @param messageDTO 触发消息DTO
     * @return 业务参数DTO
     */
    protected BaseSingalTrackingBusParamDTO buildBaseSingalTrackingBusParamDTO(SignalMockTriggerMessageDTO messageDTO) {
        BaseSingalTrackingBusParamDTO busParamDTO = new BaseSingalTrackingBusParamDTO();
        // 生成随机32位字符串作为uuid
        busParamDTO.setUuid(UUID.randomUUID().toString().replace("-", ""));
        busParamDTO.setContactType(messageDTO.getContactType());
        String contactId = getContactId(messageDTO);
        if (StringUtils.isBlank(contactId)) {
            return null;
        }
        busParamDTO.setContactId(contactId);
        busParamDTO.setOriContactId(messageDTO.getOriContactId());

        if (Objects.equals(ChannelEnum.ONLINE.getCode(), messageDTO.getContactType())) {
            // 在线渠道需要设置chatId和sessionId
            busParamDTO.setChatId(contactId);
            Long sessionId = getSessionId(contactId);
            if (sessionId == null) {
                return null;
            }
            busParamDTO.setSessionId(String.valueOf(sessionId));
            List<String> typicalIds = typicalQuestionIdQueryHelper.getTypicalQuestionIds(String.valueOf(sessionId));
            if (CollectionUtils.isEmpty(typicalIds)) {
                log.info("{}, 未找到标问ID列表, sessionId:{}", LOG_PREFIX, sessionId);
            }
            busParamDTO.setTypicalQuestionIds(typicalIds);
        } else if (Objects.equals(ChannelEnum.CALL.getCode(), messageDTO.getContactType())) {
            // 电话渠道无需sessionId，直接使用contactId
            log.debug("{}, 电话渠道无需获取sessionId, contactId:{}", LOG_PREFIX, contactId);
        }
        busParamDTO.setChatMessageFromType(messageDTO.getChatMessageFromType());
        busParamDTO.setBizMessageId(messageDTO.getMessageId());
        busParamDTO.setMessageOccurredTime(messageDTO.getMessageOccurredTime());
        busParamDTO.setStaffMis(messageDTO.getStaffMis());
        busParamDTO.setStaffMessageContent(messageDTO.getStaffMessageContent());
        busParamDTO.setPathParamsButler(messageDTO.getContextParam());
        busParamDTO.setPathParamsPopup(
                getPopupData(Objects.equals(ChannelEnum.ONLINE.getCode(), messageDTO.getContactType())
                        ? ContactTypeEnum.CHAT.getCode()
                        : ContactTypeEnum.CALL.getCode(), contactId));
        return busParamDTO;
    }

    /**
     * 获取会话ID
     *
     * @param contactId 联系ID
     * @return 会话ID
     */
    @Nullable
    protected Long getSessionId(String contactId) {
        SessionBriefDTO sessionByManualDialogId = manualDialogInfoService
                .getSessionByManualDialogId(Long.valueOf(contactId));
        Long sessionId = sessionByManualDialogId.getSessionId();
        if (Objects.isNull(sessionId)) {
            log.error("{}, 查询太平洋会话ID失败, contactId:{}", LOG_PREFIX, contactId);
            Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "GetSessionByManualDialogIdFAIL", "");
            return null;
        }
        return sessionId;
    }

    /**
     * 获取ContactId
     *
     * @param messageDTO 触发消息DTO
     * @return 联系ID
     */
    @Nullable
    protected static String getContactId(SignalMockTriggerMessageDTO messageDTO) {
        String contactId = messageDTO.getContactId();
        if (StringUtils.isBlank(contactId)) {
            log.error("{}, contactId为空", LOG_PREFIX);
            Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "EmptyContactId", "");
            return null;
        }

        // 在线渠道contactId必须是数字，电话渠道contactId是UUID格式
        if (Objects.equals(ChannelEnum.ONLINE.getCode(), messageDTO.getContactType())) {
            if (!NumberUtils.isDigits(contactId)) {
                log.error("{}, 在线渠道contactId:{} 不是数字", LOG_PREFIX, contactId);
                Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "InvalidOnlineContactId", "");
                return null;
            }
        }
        // 电话渠道的contactId不需要格式校验，只要不为空即可

        return contactId;
    }

    /**
     * 获取弹窗数据
     * <p>
     * 先从缓存中获取，如果缓存不存在则远程调用服务获取
     * 获取到的数据会被缓存一定时间(默认10分钟)
     *
     * @param type      弹窗类型，1=电话，2=在线
     * @param contactId 会话标识
     * @return 弹窗数据映射，如果获取失败则返回空映射
     */
    protected Map<String, Object> getPopupData(Integer type, String contactId) {
        String cacheKey = String.format("%s:%s", type, contactId);

        // 尝试从缓存获取
        Map<String, Object> cachedData = popupDataCache.getIfPresent(cacheKey);
        if (cachedData != null) {
            return cachedData;
        }

        PopupDataRequestDTO requestDTO = new PopupDataRequestDTO();
        requestDTO.setType(type);
        requestDTO.setContactId(contactId);
        try {
            RespDTO<Map<String, Object>> popupData = popupRemoteService.getPopupData(requestDTO);
            log.info("{}, getPopupData, result = [{}]", LOG_PREFIX, ObjectConverter.convertToJson(popupData));

            Map<String, Object> result = Collections.emptyMap();
            if (Objects.nonNull(popupData) && popupData.isSuccess()) {
                result = popupData.getData();
            }

            // 如果结果不为空，则缓存
            if (!result.isEmpty()) {
                popupDataCache.put(cacheKey, result);
            }
            return result;
        } catch (Exception e) {
            log.error("{}, getPopupData, type = [{}], contactId = [{}]", LOG_PREFIX, type, contactId, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 通过客服员ID获取Pacific客服信息
     * <p>
     * 从缓存中获取客服信息，如果缓存不存在则远程调用服务获取
     *
     * @param chatStaffId 客服员ID
     * @return 客服信息DTO，如果获取失败则返回null
     */
    protected StaffInfoFacadeDTO getPacificStaffIdByChatStaffId(Long chatStaffId) {
        if (null == chatStaffId) {
            return null;
        }
        try {
            return staffInfoCache.get(chatStaffId);
        } catch (Exception e) {
            log.error("{}, 获取客服信息异常, chatStaffId = [{}]", LOG_PREFIX, chatStaffId, e);
            return null;
        }
    }

    /**
     * 构建机器人列表查询参数
     * 子类需要实现此方法来设置不同的渠道类型
     *
     * @param busParamDTO 业务参数DTO
     * @return 机器人列表查询参数
     */
    protected abstract RobotListQuery buildRobotListQuery(BaseSingalTrackingBusParamDTO busParamDTO);

    /**
     * 获取渠道类型
     * 子类需要实现此方法来返回对应的渠道类型
     *
     * @return 渠道类型枚举
     */
    protected abstract TrackingRulesChannelEnum getChannelType();
} 