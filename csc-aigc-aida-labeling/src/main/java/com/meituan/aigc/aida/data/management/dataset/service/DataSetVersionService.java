package com.meituan.aigc.aida.data.management.dataset.service;

import com.meituan.aigc.aida.data.management.dataset.dto.DataSetVersionDTO;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCopyVersionParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetVersionParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-30 16:59
 * @description 数据集版本服务
 */
public interface DataSetVersionService {

    /**
     * 新增版本
     */
    void addVersion(DataSetVersionParam dataSetVersionParam);

    /**
     * 复制新版本的数据
     *
     * @param dataSetCopyVersionParam 数据集版本参数
     * @return 返回复制后的版本ID
     */
    Long copyVersion(DataSetCopyVersionParam dataSetCopyVersionParam);

    /**
     * 删除版本
     */
    void deleteVersion(Long dataSetId, Long versionId);

    /**
     * 修改版本名称
     */
    void updateVersionName(Long dataSetId, Long versionId, String versionName);

    /**
     * 获取版本信息列表
     */
    List<DataSetVersionDTO> listVersion(Long dataSetId);

    /**
     * 根据数据集Id和版本Id查询数据
     */
    DataSetVersionDTO getByDataSetIdAndVersionId(Long dataSetId, Long versionId);
}
