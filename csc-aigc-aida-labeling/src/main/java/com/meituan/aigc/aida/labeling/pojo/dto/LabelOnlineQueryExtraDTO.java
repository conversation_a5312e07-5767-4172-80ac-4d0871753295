package com.meituan.aigc.aida.labeling.pojo.dto;

import com.meituan.aigc.aida.labeling.param.Context;
import com.meituan.aigc.aida.labeling.param.Signal;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/6/27 15:33
 * @Version: 1.0
 */
@Data
public class LabelOnlineQueryExtraDTO implements Serializable {

    /**
     * 大模型消息内容
     */
    private String messageContent;
    /**
     * 上下文
     */
    private List<Context> context;
    /**
     * 信号
     */
    private List<Signal> signalList;
    /**
     * 原始数据JSON
     */
    private String rawDataContent;

}
