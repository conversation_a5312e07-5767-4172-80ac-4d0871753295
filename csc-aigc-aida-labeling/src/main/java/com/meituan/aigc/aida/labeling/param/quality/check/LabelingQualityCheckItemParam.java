package com.meituan.aigc.aida.labeling.param.quality.check;

import com.meituan.aigc.aida.labeling.param.AdvancedFilter;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description 质检任务详情查询参数param
 * <AUTHOR>
 **/
@Data
public class LabelingQualityCheckItemParam {

    /**
     * 质检任务id
     */
    private Long qualityCheckTaskId;

    /**
     * 质检状态
     */
    private Integer qualityCheckStatus;

    /**
     * 标注人mis
     */
    private String labelerMis;
    /**
     * 质检人mis
     */
    private String qualityChecker;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 质检结果
     */
    private Integer qualityCheckResult;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 高级筛选项
     */
    private List<AdvancedFilter> advancedFilters;
    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
}
