package com.meituan.aigc.aida.labeling.remote.dataset.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 数据集处理参数
 */
@Data
public class DataSetProcessParam implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 数据集ID
     */
    private Long dataSetId;
    
    /**
     * 版本ID
     */
    private Long versionId;
    
    /**
     * 数据源类型
     */
    private String dataSource;
    
    /**
     * 数据源类型代码
     */
    private Integer dataSourceCode;
    
    /**
     * 数据记录列表
     */
    private List<Map<String, Object>> dataRecords;
    
    /**
     * 额外参数
     */
    private Map<String, Object> extraParams;
} 