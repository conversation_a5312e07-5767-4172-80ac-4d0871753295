package com.meituan.aigc.aida.data.management.fetch.dto;

import lombok.Data;

/**
 * @Author: g<PERSON><PERSON><PERSON>
 * @Create: 2025/4/8 10:38
 * @Version: 1.0
 */
@Data
public class DataFetchTaskCreateOutputFieldDTO {

    /**
     * 机器人ID
     */
    private String robotId;
    /**
     * 机器人名称
     */
    private String robotName;
    /**
     * 输出字段名称
     */
    private String fieldName;

}
