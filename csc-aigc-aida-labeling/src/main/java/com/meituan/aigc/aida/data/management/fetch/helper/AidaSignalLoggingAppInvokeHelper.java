package com.meituan.aigc.aida.data.management.fetch.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.aigc.aida.common.constants.TrainingCommonConstants;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.fetch.dto.BaseSingalTrackingBusParamDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.aida.AidaWorkflowBusParamDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.hive.signal.SignalLoggingCommonInfoDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.hive.signal.SignalLoggingToHiveDTO;
import com.meituan.aigc.aida.data.management.fetch.param.AidaRobotInfoParam;
import com.meituan.aigc.aida.data.management.fetch.service.DataToHiveService;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingException;
import com.meituan.csc.aigc.runtime.api.AidaConfigService;
import com.meituan.csc.aigc.runtime.api.AidaGptService;
import com.meituan.csc.aigc.runtime.dto.aida.*;
import com.meituan.csc.aigc.runtime.inner.api.AidaConfigInnerRemoteService;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppConfigDTO;
import com.meituan.csc.aigc.runtime.inner.param.InnerAppConfigRequestParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.meituan.aigc.aida.data.management.common.constant.CommonConstants.EMPTY_STRING;

/**
 * AI搭埋点机器人调用Helper
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AidaSignalLoggingAppInvokeHelper {

    private static final String LOG_PREFIX = "【AidaAppInvokeHelper】";

    /**
     * 来源类型常量
     */
    private static final String SOURCE_TYPE_API = "API";

    /**
     * Mock接口来源
     */
    private static final String MOCK_SOURCE = "signal_logging";

    /**
     * 渠道枚举常量
     */
    public static final class AidaChannelEnum {
        public static final String SIGNAL_LOGGING = "signalLogging";
    }

    /**
     * Cat错误类型常量
     */
    private static final String AIDA_SIGNAL_PARAM_VALIDATE_ERROR = "aida.signal.param.validate.error";
    public static final String AIDA_RESP_ERROR = "aida.resp.error";

    @Autowired
    private AidaGptService aidaGptPigeonService;

    @Autowired
    private AidaConfigInnerRemoteService aidaConfigInnerRemoteService;

    @Autowired
    private AidaConfigService aidaConfigService;

    @Resource
    private DataToHiveService dataToHiveService;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    /**
     * 调用AI搭机器人
     *
     * @param aidaAppid   Aida机器人ID
     * @param inputData   输入数据
     * @param busParamDTO 业务参数
     * @param acId        弹屏AC ID
     */
    public void invokeAidaAppAndLogging(String aidaAppid, String signalType, Map inputData, BaseSingalTrackingBusParamDTO busParamDTO,
                                        String acId) {
        log.info("{} 执行invokeAidaApp, aidaAppid:{}, acId:{}, sessionId:{}", LOG_PREFIX, aidaAppid, acId,
                busParamDTO.getSessionId());

        try {
            if (StringUtils.isBlank(aidaAppid)) {
                log.error("{}, aidaAppid为空, 调用AI搭机器人失败", LOG_PREFIX);
            }
            // 组装参数
            GptRequestParam param = buildGptRequestParam(inputData, busParamDTO, aidaAppid, acId);
            if (Objects.isNull(param)) {
                log.info("{} 无法构建有效AI搭参数, 无需调用AI搭机器人", LOG_PREFIX);
                return;
            }
            log.info("{} 组装调用AI搭参数:{}", LOG_PREFIX, JSON.toJSONString(param));

            // 调用AI搭
            AidaFinalRes aidaRes = aidaGptPigeonService.chat(param);
            if (Objects.isNull(aidaRes)
                    || !Objects.equals(aidaRes.getCode(), TrainingCommonConstants.AIDA_SUCC_RESP_CODE)) {
                Cat.logErrorWithCategory(AIDA_RESP_ERROR, new AidaTrainingException(JSONObject.toJSONString(aidaRes)));
                log.info("{} 调用AI搭返回码异常, aidaAppid={}, acId={}, sessionId={}, aidaRes={}", LOG_PREFIX, aidaAppid, acId,
                        busParamDTO.getSessionId(), JSON.toJSONString(aidaRes));
                return;
            }
            log.info("{} 调用AI搭成功, aidaAppid={}, acId={}, sessionId={}, aidaRes={}", LOG_PREFIX, aidaAppid, acId,
                    busParamDTO.getSessionId(), JSON.toJSONString(aidaRes));
            dataToHiveService.pushSignalLoggingData(buildSignalLoggingToHiveDTO(aidaRes, param, busParamDTO, acId, aidaAppid, signalType));
        } catch (Exception e) {
            Cat.logErrorWithCategory(AIDA_RESP_ERROR, e);
            log.error("{} 调用AI搭失败, aidaAppid={}, acId={}, sessionId={}, msg={}", LOG_PREFIX, aidaAppid, acId,
                    busParamDTO.getSessionId(), e.getMessage(), e);
        }
    }

    public AidaFinalRes invokeAida(String aidaAppId, String versionId, String apiToken, Map<String, Object> paramMap, String extraInfo, MockConfigDTO mockConfigDTO) {
        log.info("{} 执行invokeAidaApp, aidaAppid:{}", LOG_PREFIX, aidaAppId);

        try {
            if (StringUtils.isBlank(aidaAppId)) {
                log.error("{}, aidaAppid为空, 调用AI搭机器人失败", LOG_PREFIX);
            }
            // 组装参数
            GptRequestParam param = buildGptRequestParam(paramMap, aidaAppId, versionId, apiToken, extraInfo, mockConfigDTO);
            if (Objects.isNull(param)) {
                log.info("{} 无法构建有效AI搭参数, 无需调用AI搭机器人", LOG_PREFIX);
                return null;
            }
            log.info("{} 组装调用AI搭参数:{}", LOG_PREFIX, JSON.toJSONString(param));

            // 调用AI搭
            AidaFinalRes aidaRes = aidaGptPigeonService.chat(param);
            if (Objects.isNull(aidaRes)
                    || !Objects.equals(aidaRes.getCode(), TrainingCommonConstants.AIDA_SUCC_RESP_CODE)) {
                Cat.logErrorWithCategory(AIDA_RESP_ERROR, new AidaTrainingException(JSONObject.toJSONString(aidaRes)));
                log.info("{} 调用AI搭返回码异常, aidaAppid={}, aidaRes={}", LOG_PREFIX, aidaAppId, JSON.toJSONString(aidaRes));
                return null;
            }
            log.info("{} 调用AI搭成功, aidaAppid={}, aidaRes={}", LOG_PREFIX, aidaAppId, JSON.toJSONString(aidaRes));
            return aidaRes;
        } catch (Exception e) {
            Cat.logErrorWithCategory(AIDA_RESP_ERROR, e);
            log.error("{} 调用AI搭失败, aidaAppid={}, msg={}", LOG_PREFIX, aidaAppId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建埋点数据
     *
     * @param aidaRes     AI搭响应结果
     * @param param       请求参数
     * @param busParamDTO 业务参数
     * @param acId        弹屏AC ID
     * @param aidaAppid
     * @return SignalLoggingToHiveDTO
     */
    private SignalLoggingToHiveDTO buildSignalLoggingToHiveDTO(AidaFinalRes aidaRes, GptRequestParam param,
                                                               BaseSingalTrackingBusParamDTO busParamDTO, String acId, String aidaAppid, String signalType) {
        // 生成UUID作为埋点数据ID
        String id = UUID.randomUUID().toString().replace("-", "");

        // 获取当前时间作为创建时间和更新时间
        String currentTime = String.valueOf(System.currentTimeMillis());

        // 构建并返回SignalLoggingToHiveDTO对象
        return SignalLoggingToHiveDTO.builder()
                .id(id)
                .appId(aidaAppid)
                .popupAcId(acId)
                .inputs(JSON.toJSONString(param.getInputs()))
                .outputs(aidaRes.getAnswer())
                .messageOccurredTime(busParamDTO.getMessageOccurredTime())
                .createdAt(currentTime)
                .updatedAt(currentTime)
                .sessionId(Objects.nonNull(busParamDTO.getSessionId()) ? busParamDTO.getSessionId() : "")
                .typicalQuestionIds(CollectionUtils.isEmpty(busParamDTO.getTypicalQuestionIds()) ? "" : String.join(",", busParamDTO.getTypicalQuestionIds()))
                .contactType(busParamDTO.getContactType())
                .contactId(busParamDTO.getContactId())
                .staffMis(busParamDTO.getStaffMis())
                .chatMessageFromType(busParamDTO.getChatMessageFromType())
                .staffMessageContent(busParamDTO.getStaffMessageContent())
                .customerMessageContent(busParamDTO.getCustomerMessageContent())
                .bizMessageId(busParamDTO.getBizMessageId())
                .commonInfo(buildCommonInfoString(busParamDTO))
                .aidaInfo(buildAidaInfo(param, aidaRes, signalType))
                .build();
    }

    /**
     * 构建工具节点输出
     *
     * @param aidaRes AI搭响应结果
     * @return 工具节点输出
     */
    private String buildAidaInfo(GptRequestParam param, AidaFinalRes aidaRes, String signalType) {
        AidaRobotInfoParam aidaRobotInfoParam = new AidaRobotInfoParam();
        aidaRobotInfoParam.setExtraInfo(param.getExtra_info());
        AidaFinalRes.ExtraInfo extraInfo = aidaRes.getExtraInfo();
        if (Objects.nonNull(extraInfo) && CollectionUtils.isNotEmpty(extraInfo.getToolOutput())) {
            aidaRobotInfoParam.setToolOutput(extraInfo.getToolOutput());
        }
        aidaRobotInfoParam.setSignalType(signalType);
        return JSONObject.toJSONString(aidaRobotInfoParam);
    }

    /**
     * 构建埋点通用信息
     *
     * @param busParamDTO
     * @return
     */
    private String buildCommonInfoString(BaseSingalTrackingBusParamDTO busParamDTO) {
        SignalLoggingCommonInfoDTO commonInfoDTO = new SignalLoggingCommonInfoDTO();
        commonInfoDTO.setPathParamsButler(JSONObject.toJSONString(busParamDTO.getPathParamsButler()));
        commonInfoDTO.setPathParamsPopup(JSONObject.toJSONString(busParamDTO.getPathParamsPopup()));
        return JSONObject.toJSONString(commonInfoDTO);
    }

    /**
     * 构建GptRequestParam参数
     *
     * @param paramMap  输入参数
     * @param aidaAppId aida机器人ID
     * @return GptRequestParam
     */
    private GptRequestParam buildGptRequestParam(Map<String, Object> paramMap, String aidaAppId, String versionId, String apiToken, String extraInfo, MockConfigDTO mockConfigDTO) {
        log.info("{}, buildGptRequestParam, aidaAppId = {}", LOG_PREFIX, aidaAppId);
        GptRequestParam param = new GptRequestParam();

        InnerAppConfigDTO appConfig;
        Map<String, String> validatedInputs;

        // 设置基本参数
        Map<String, String> inputs = convertInputs(paramMap);

        // 查询 AIDA 配置，检查入参是否合法
        try {
            InnerAppConfigRequestParam configParam = new InnerAppConfigRequestParam();
            configParam.setAppId(aidaAppId);
            configParam.setSecret(TrainingCommonConstants.AIDA_SECRET);

            AidaBaseResponse<InnerAppConfigDTO> configResponse = aidaConfigInnerRemoteService
                    .getRobotConfigById(configParam);

            if (configResponse == null
                    || !Objects.equals(configResponse.getCode(), TrainingCommonConstants.AIDA_SUCC_CODE)
                    || configResponse.getData() == null) {
                log.error("{} 获取AIDA配置失败, aidaAppId={}", LOG_PREFIX, aidaAppId);
                Cat.logErrorWithCategory(AIDA_SIGNAL_PARAM_VALIDATE_ERROR, new RuntimeException(
                        String.format("获取AIDA配置失败, code=%s, msg=%s",
                                configResponse != null ? configResponse.getCode() : "null",
                                configResponse != null ? configResponse.getMessage() : "null")));
                return null;
            }
            appConfig = configResponse.getData();

            // 验证AIDA配置并处理输入参数
            validatedInputs = validateAidaConfig(appConfig, inputs, aidaAppId);
            if (validatedInputs == null) {
                // 验证失败（必填项缺失），中止执行
                return null;
            }
        } catch (Exception e) {
            log.error("{} 校验AIDA参数异常, aidaAppId={}, error={}", LOG_PREFIX, aidaAppId, e.getMessage(), e);
            Cat.logErrorWithCategory(AIDA_SIGNAL_PARAM_VALIDATE_ERROR, e);
            return null;
        }

        param.setInputs(validatedInputs);
        param.setQuery(null);
        param.setAuthorization(String.format("Bearer %s", StringUtils.isNotBlank(apiToken) ? apiToken : aidaConfigService.getAppToken(aidaAppId)));
        param.setApp_model_version_id(versionId);
        param.setSource(MOCK_SOURCE);

        // 设置extra_info
        param.setExtra_info(extraInfo);
        // 设置mock配置信息
        param.setMockConfig(mockConfigDTO);
        log.info("{}, buildGptRequestParam, param = {}", LOG_PREFIX, JSONObject.toJSONString(param));
        return param;
    }

    /**
     * 构建GptRequestParam参数
     *
     * @param inputData   输入数据
     * @param busParamDTO 业务参数
     * @param aidaAppId   aida机器人ID
     * @param acId        弹屏AC ID
     * @return GptRequestParam
     */
    private GptRequestParam buildGptRequestParam(Map<String, Object> inputData,
                                                 BaseSingalTrackingBusParamDTO busParamDTO,
                                                 String aidaAppId, String acId) {
        log.info("{}, buildGptRequestParam, acId = {}", LOG_PREFIX, acId);
        GptRequestParam param = new GptRequestParam();

        InnerAppConfigDTO appConfig = null;
        Map<String, String> validatedInputs = null;

        // 设置基本参数
        Map<String, String> inputs = convertInputs(inputData);

        // 查询 AIDA 配置，检查入参是否合法
        try {
            InnerAppConfigRequestParam configParam = new InnerAppConfigRequestParam();
            configParam.setAppId(aidaAppId);
            configParam.setSecret(TrainingCommonConstants.AIDA_SECRET);

            AidaBaseResponse<InnerAppConfigDTO> configResponse = aidaConfigInnerRemoteService
                    .getRobotConfigById(configParam);

            if (configResponse == null
                    || !Objects.equals(configResponse.getCode(), TrainingCommonConstants.AIDA_SUCC_CODE)
                    || configResponse.getData() == null) {
                log.error("{} 获取AIDA配置失败, aidaAppId={}", LOG_PREFIX, aidaAppId);
                Cat.logErrorWithCategory(AIDA_SIGNAL_PARAM_VALIDATE_ERROR, new RuntimeException(
                        String.format("获取AIDA配置失败, code=%s, msg=%s",
                                configResponse != null ? configResponse.getCode() : "null",
                                configResponse != null ? configResponse.getMessage() : "null")));
                return null;
            }
            appConfig = configResponse.getData();

            // 验证AIDA配置并处理输入参数
            validatedInputs = validateAidaConfig(appConfig, inputs, aidaAppId);
            if (validatedInputs == null) {
                // 验证失败（必填项缺失），中止执行
                return null;
            }
        } catch (Exception e) {
            log.error("{} 校验AIDA参数异常, aidaAppId={}, error={}", LOG_PREFIX, aidaAppId, e.getMessage(), e);
            Cat.logErrorWithCategory(AIDA_SIGNAL_PARAM_VALIDATE_ERROR, e);
            return null;
        }

        param.setInputs(validatedInputs);
        param.setQuery(null);
        param.setAuthorization(String.format("Bearer %s", aidaConfigService.getAppToken(aidaAppId)));
        param.setSource(MOCK_SOURCE);

        // 设置extra_info
        AidaWorkflowBusParamDTO aidaWorkflowBusParamDTO = buildAidaWorkflowBusParamDTO(busParamDTO);

        // 将DTO转换为JSON字符串设置到extra_info
        param.setExtra_info(JSON.toJSONString(aidaWorkflowBusParamDTO));
        // 从lion中获取能返回mock数据的机器人列表
        MockConfigDTO mockConfigDTO = new MockConfigDTO();
        mockConfigDTO.setMockReturnOpen(true);
        mockConfigDTO.setMockRunOpen(false);
        mockConfigDTO.setMockRunParams(new ArrayList<>());
        param.setMockConfig(mockConfigDTO);
        log.info("{}, buildGptRequestParam, param = {}", LOG_PREFIX, JSONObject.toJSONString(param));
        return param;
    }

    /**
     * 验证AIDA配置并处理输入参数
     *
     * @param appConfig AIDA应用配置
     * @param inputs    输入参数
     * @param aidaAppId AIDA应用ID
     * @return 处理后的输入参数，如果验证失败返回null
     */
    private Map<String, String> validateAidaConfig(InnerAppConfigDTO appConfig, Map<String, String> inputs,
                                                   String aidaAppId) {
        // 检查是否包含userInputFormList
        if (CollectionUtils.isEmpty(appConfig.getUserInputFormList())) {
            // 配置中没有定义输入表单，直接返回原始输入，调用方决定是否传入 AIDA
            log.warn("{} AIDA配置不包含userInputFormList, aidaAppId={}", LOG_PREFIX, aidaAppId);
            return inputs;
        }

        // 检查是否包含text_input
        boolean hasTextInput = false;
        for (InnerAppConfigDTO.UserInputForm form : appConfig.getUserInputFormList()) {
            if (form.getText() != null) {
                hasTextInput = true;
                break;
            }
        }

        if (!hasTextInput) {
            // 配置中没有定义 TextInput，无法进行必填校验，直接返回原始输入
            log.warn("{} AIDA配置不包含text_input, aidaAppId={}", LOG_PREFIX, aidaAppId);
            return inputs;
        }

        // 检查必填参数
        for (InnerAppConfigDTO.UserInputForm form : appConfig.getUserInputFormList()) {
            InnerAppConfigDTO.Text textInput = form.getText();
            if (textInput != null && Boolean.TRUE.equals(textInput.getRequired())) {
                String paramValue = inputs.get(textInput.getVariable());
                if (StringUtils.isBlank(paramValue)) {
                    String errorMsg = String.format("%s AIDA必填参数 %s 为空, aidaAppId=%s",
                            LOG_PREFIX, textInput.getVariable(), aidaAppId);
                    log.error(errorMsg);
                    // 使用exception.getMessage()作为type参数
                    RuntimeException runtimeException = new RuntimeException(errorMsg);
                    Cat.logErrorWithCategory(AIDA_SIGNAL_PARAM_VALIDATE_ERROR, runtimeException);
                    return null;
                }
            }
        }

        return inputs;
    }

    /**
     * 构建AidaWorkflowBusParamDTO
     *
     * @param busParamDTO
     * @return
     */
    @NotNull
    private AidaWorkflowBusParamDTO buildAidaWorkflowBusParamDTO(BaseSingalTrackingBusParamDTO busParamDTO) {
        AidaWorkflowBusParamDTO aidaWorkflowBusParamDTO = new AidaWorkflowBusParamDTO();
        // 复制基本参数, 此处不可以使用BeanUtils, 防止过多嵌套随路JSON参数传入AI搭, 导致工作流解析异常
        aidaWorkflowBusParamDTO.setUuid(
                busParamDTO.getUuid() != null ? busParamDTO.getUuid() : UUID.randomUUID().toString().replace("-", ""));
        aidaWorkflowBusParamDTO.setSessionId(busParamDTO.getSessionId());
        aidaWorkflowBusParamDTO.setTypicalQuestionIds(busParamDTO.getTypicalQuestionIds());
        aidaWorkflowBusParamDTO.setContactType(busParamDTO.getContactType());
        aidaWorkflowBusParamDTO.setContactId(busParamDTO.getContactId());
        aidaWorkflowBusParamDTO.setOriContactId(busParamDTO.getOriContactId());
        aidaWorkflowBusParamDTO.setChatId(busParamDTO.getChatId());
        aidaWorkflowBusParamDTO.setStaffMis(busParamDTO.getStaffMis());
        aidaWorkflowBusParamDTO.setChatMessageFromType(busParamDTO.getChatMessageFromType());
        aidaWorkflowBusParamDTO.setStaffMessageContent(busParamDTO.getStaffMessageContent());
        aidaWorkflowBusParamDTO.setCustomerMessageContent(busParamDTO.getCustomerMessageContent());
        aidaWorkflowBusParamDTO.setBizMessageId(busParamDTO.getBizMessageId());
        // 设置渠道为信号埋点
        aidaWorkflowBusParamDTO.setChannel(AidaChannelEnum.SIGNAL_LOGGING);
        if (BooleanUtils.isTrue(dataManagementLionConfig.getInvokeAidaWithButlerPopupParamSwitch())) {
            // 设置太平洋随路参数
            aidaWorkflowBusParamDTO.setPathParamsButler(busParamDTO.getPathParamsButler());
            aidaWorkflowBusParamDTO.setPathParamsPopup(busParamDTO.getPathParamsPopup());
        }
        return aidaWorkflowBusParamDTO;
    }

    /**
     * 转换输入数据为字符串Map
     *
     * @param inputData 输入数据
     * @return 字符串Map
     */
    private Map<String, String> convertInputs(Map<String, Object> inputData) {
        Map<String, String> inputs = new HashMap<>();
        if (MapUtils.isNotEmpty(inputData)) {
            for (Map.Entry<String, Object> entry : inputData.entrySet()) {
                if (entry.getValue() != null) {
                    inputs.put(entry.getKey(), String.valueOf(entry.getValue()));
                } else {
                    inputs.put(entry.getKey(), EMPTY_STRING);
                }
            }
        }
        return inputs;
    }

}