package com.meituan.aigc.aida.labeling.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.common.squirrel.SquirrelClient;
import com.meituan.aigc.aida.labeling.common.constant.RedisConstant;
import com.meituan.aigc.aida.labeling.common.enums.*;
import com.meituan.aigc.aida.labeling.common.utils.JsonCompareUtil;
import com.meituan.aigc.aida.labeling.common.utils.LabelResultCompareUtil;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.dao.model.*;
import com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckItemPO;
import com.meituan.aigc.aida.labeling.dao.po.LabelingTaskRawDataPO;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckSampleCountPO;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO;
import com.meituan.aigc.aida.labeling.dao.repository.*;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingOperationException;
import com.meituan.aigc.aida.labeling.helper.LabelingTaskConditionHelper;
import com.meituan.aigc.aida.labeling.param.LabelingQualityDetailPageQuery;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.param.QualityCheckResultSaveParam;
import com.meituan.aigc.aida.labeling.param.RawDataExtraInfo;
import com.meituan.aigc.aida.labeling.param.quality.check.LabelingQualityCheckItemParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckViewParam;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingQualityCheckItemDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.PageQueryDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.QualityCheckResultSaveDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.QualityCheckTaskListDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.quality.check.TaskConditionDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingQualityCheckItemVO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingQualityCheckRawCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.QualityCheckedItemVO;
import com.meituan.aigc.aida.labeling.service.QualityCheckService;
import com.meituan.aigc.aida.labeling.service.common.PermissionService;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.meituan.aigc.aida.labeling.util.labeling.task.LabelingTaskUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 质检服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class QualityCheckServiceImpl implements QualityCheckService {

    @Autowired
    private LabelingQualityCheckItemRepository labelingQualityCheckItemRepository;

    @Autowired
    private LabelingQualityCheckTaskRepository labelingQualityCheckTaskRepository;

    @Autowired
    private LabelingTaskSessionDataRepository labelingTaskSessionDataRepository;

    @Autowired
    private LabelingTaskRepository labelingTaskRepository;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private LabelingDetailRepository labelingDetailRepository;

    @Autowired
    private LabelingTaskRawDataRepository labelingTaskRawDataRepository;

    @Autowired
    private PushElephantService pushElephantService;

    @Autowired
    private LionConfig lionConfig;

    @Autowired
    private LabelingSubTaskRepository labelingSubTaskRepository;

    @Autowired
    private SquirrelClient squirrelClient;


    /**
     * 分页查询质检任务列表详情
     *
     * @param param 质检任务列表查询参数
     * @return 质检任务列表详情
     */
    @Override
    public PageQueryDTO<LabelingQualityCheckItemDTO> page(LabelingQualityCheckItemParam param) {
        CheckUtil.paramCheck(param != null && ObjectUtil.isNotNull(param.getQualityCheckTaskId()), "查询质检任务参数错误!");
        PageQueryDTO<?> pageCount = labelingQualityCheckItemRepository.countByTaskIdAndLabelerMis(param.getQualityCheckTaskId(), param.getLabelerMis());
        // 没有高级筛选条件时，采用数据库分页，有高级筛选时只能使用内存分页
        if (CollectionUtils.isEmpty(param.getAdvancedFilters())) {
            PageHelper.startPage(param.getPageNum(), param.getPageSize());
            // 调用mapper方法进行查询，质检过的排在前面
            List<LabelingQualityCheckItemPO> qualityCheckItemList = labelingQualityCheckItemRepository.listByQualityCheckTaskIdAndQualityCheckStatus(param);
            PageHelper.clearPage();
            if (CollectionUtils.isEmpty(qualityCheckItemList)) {
                return new PageQueryDTO<>(0, pageCount.getAllDataTotalCount(), pageCount.getQualityCheckedCount(), pageCount.getNoQualityCheckedCount(), Collections.emptyList());
            }
            PageInfo<LabelingQualityCheckItemPO> res = new PageInfo<>(qualityCheckItemList);

            List<LabelingQualityCheckItemDTO> qualityCheckItemDTOList = qualityCheckItemList.stream()
                    .map(this::buildLabelingQualityCheckItemDTO)
                    .collect(Collectors.toList());
            // 返回分页查询结果
            return new PageQueryDTO<>((int) res.getTotal(), pageCount.getAllDataTotalCount(), pageCount.getQualityCheckedCount(), pageCount.getNoQualityCheckedCount(), qualityCheckItemDTOList);
        }
        // 有高级筛选条件时，查全部数据，过滤后进行内容分页
        List<LabelingQualityCheckItemPO> qualityCheckItemList = labelingQualityCheckItemRepository.listByQualityCheckTaskIdAndQualityCheckStatus(param);
        if (CollectionUtils.isEmpty(qualityCheckItemList)) {
            return new PageQueryDTO<>(0, pageCount.getAllDataTotalCount(), pageCount.getQualityCheckedCount(), pageCount.getNoQualityCheckedCount(), Collections.emptyList());
        }

        Map<String, List<LabelingTaskSessionData>> sessionDataMap = new HashMap<>();
        boolean isModelContextCondition = LabelingTaskConditionHelper.hasModelContextCondition(param.getAdvancedFilters());
        // 如果条件中有模型上下文，则查找会话的上下文信息
        if (isModelContextCondition) {
            LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(param.getQualityCheckTaskId());
            List<String> sessionIdList = qualityCheckItemList.stream().map(LabelingQualityCheckItemPO::getSessionId).collect(Collectors.toList());
            List<LabelingTaskSessionData> labelingTaskSessionDataList = labelingTaskSessionDataRepository.listByTaskIdAndSessionIdListWithBlobs(labelingQualityCheckTask.getTaskId(), sessionIdList);
            if (CollectionUtils.isNotEmpty(labelingTaskSessionDataList)) {
                sessionDataMap = labelingTaskSessionDataList.stream().collect(Collectors.groupingBy(LabelingTaskSessionData::getSessionId));
            }
        }
        // 先过滤数据
        List<LabelingQualityCheckItemPO> filterQualityCheckItemList = LabelingTaskConditionHelper.filterQualityCheckCondition(qualityCheckItemList, sessionDataMap, param.getAdvancedFilters());
        int totalNum = filterQualityCheckItemList.size();
        // 内存分页
        List<LabelingQualityCheckItemPO> pageFilterQualityCheckItemList = filterQualityCheckItemList.stream()
                .skip((long) (param.getPageNum() - 1) * param.getPageSize())
                .limit(param.getPageSize())
                .collect(Collectors.toList());
        // 转换数据
        List<LabelingQualityCheckItemDTO> qualityCheckItemDTOList = pageFilterQualityCheckItemList.stream()
                .map(this::buildLabelingQualityCheckItemDTO)
                .collect(Collectors.toList());
        // 返回分页查询结果
        return new PageQueryDTO<>(totalNum, pageCount.getAllDataTotalCount(), pageCount.getQualityCheckedCount(), pageCount.getNoQualityCheckedCount(), qualityCheckItemDTOList);
    }

    @Override
    public PageData<LabelingQualityCheckItemVO> getLabelingQualityDetail(LabelingQualityDetailPageQuery query) {
        // 检查参数是否有效，质检任务ID不能为空
        CheckUtil.paramCheck(Objects.nonNull(query.getQualityCheckTaskId()), "质检任务id不能为空");
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(query.getQualityCheckTaskId());
        CheckUtil.paramCheck(Objects.nonNull(labelingQualityCheckTask), "质检任务不存在或已被删除");
        LabelingTask labelingTask = labelingTaskRepository.getById(labelingQualityCheckTask.getTaskId());
        if (labelingTask.getDataType() == LabelTaskDataType.QUERY_ANNOTATION.getCode()) {
            return getQueryDataTypePageData(query);
        }

        // 其他类型的数据使用一维数组存储
        return getOtherDataTypePageData(query);
    }

    /**
     * 质检任务列表
     *
     * @param param 质检任务列表查询参数
     * @return 质检任务列表
     */
    @Override
    public PageData<QualityCheckTaskListDTO> qualityChecks(QualityCheckParam param) {
        // 获取当前用户信息
        User user = UserUtils.getUser();
        String mis = user != null ? user.getLogin() : null;
        param.setQualityCheckerMis(mis);
        // 判断是否为管理员，管理员不限制查询结果
        boolean isAdmin = permissionService.isAdminUser();
        param.setAdmin(isAdmin);
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        //根据质检任务ID、质检任务名称和质检人查询质检任务列表
        List<QualityCheckTaskPO> qualityCheckTaskList = labelingQualityCheckTaskRepository.listByTaskIdAndNameAndQualityCheckerMis(param);
        if (CollectionUtils.isEmpty(qualityCheckTaskList)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<QualityCheckTaskPO> pageInfo = new PageInfo<>(qualityCheckTaskList);
        PageHelper.clearPage();
        // 统计每个主任务下的质检任务的总样本数量
        List<Long> taskIdList = qualityCheckTaskList.stream().map(QualityCheckTaskPO::getTaskId).collect(Collectors.toList());
        List<LabelingTask> labelingTaskList = labelingTaskRepository.listByIdList(taskIdList);
        Map<Long, LabelingTask> labelingTaskMap = labelingTaskList.stream().collect(Collectors.toMap(LabelingTask::getId, Function.identity()));
        List<LabelingTaskRawDataPO> rawDataList = labelingTaskRawDataRepository.getCountByTaskIds(taskIdList);
        // 现在的质检逻辑改为同一份数据的多个标注人都可以进行质检，因此，统计数据需要进行去重
        Map<Long, LabelingTaskRawDataPO> totalCountMap = rawDataList.stream()
                .collect(Collectors.toMap(LabelingTaskRawDataPO::getTaskId, Function.identity()));
        //根据质检任务id去详情表统计标注进度
        List<Long> qualityCheckTaskIds = qualityCheckTaskList.stream().map(QualityCheckTaskPO::getId).collect(Collectors.toList());
        //获取标注进度
        List<QualityCheckTaskPO> qualityCheckItemList = labelingQualityCheckItemRepository.listByQualityCheckTaskId(qualityCheckTaskIds);
        //根据质检任务ID转map
        Map<Long, QualityCheckTaskPO> map = qualityCheckItemList.stream().collect(Collectors.toMap(QualityCheckTaskPO::getId, Function.identity()));
        //构建返回结果
        List<QualityCheckTaskListDTO> labelingTaskDTOList = qualityCheckTaskList.stream()
                .map(item -> buildQualityCheckTaskListDTO(item, map, labelingTaskMap, totalCountMap))
                .collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), labelingTaskDTOList);
    }

    /**
     * 构建质检任务DTO
     *
     * @param qualityCheckTask 质检任务PO
     * @param map              质检任务PO的映射
     * @return 质检任务DTO
     */
    private QualityCheckTaskListDTO buildQualityCheckTaskListDTO(QualityCheckTaskPO qualityCheckTask, Map<Long, QualityCheckTaskPO> map,
                                                                 Map<Long, LabelingTask> labelingTaskMap, Map<Long, LabelingTaskRawDataPO> totalCountMap) {
        TaskSourceEnum taskSourceEnum = TaskSourceEnum.getByCode(qualityCheckTask.getDataSetSource());
        QualityCheckTaskStatus qualityCheckTaskStatus = QualityCheckTaskStatus.getByCode(qualityCheckTask.getQualityCheckStatus());
        QualityCheckTaskPO checkTask = map.get(qualityCheckTask.getId());
        LabelingTask labelingTask = labelingTaskMap.get(qualityCheckTask.getTaskId());
        LabelingTaskRawDataPO labelingTaskCount = totalCountMap.get(qualityCheckTask.getTaskId());
        // qualityCheckProgress的统计逻辑改为总数据量减去未完成数据量，因为所有标注的信息都完成质检才算完成，而只要有一个标注未完成质检，就需要按照未完成计算，因此统计的完成项是不准的，而统计的未完成项是准确的
        return QualityCheckTaskListDTO.builder()
                .id(qualityCheckTask.getId())
                .taskId(qualityCheckTask.getTaskId())
                .taskName(qualityCheckTask.getTaskName())
                .dataSetSource(Objects.isNull(taskSourceEnum) ? "未知" : taskSourceEnum.getValue())
                .totalCount(labelingTask.getAssignType() != null && labelingTask.getAssignType() == AssignTypeEnum.SESSION.getCode() ? labelingTaskCount.getTotalSessionNum() : labelingTaskCount.getTotalNum())
                .sampleCount(labelingTask.getAssignType() != null && labelingTask.getAssignType() == AssignTypeEnum.SESSION.getCode()
                        ? Objects.isNull(checkTask) ? 0 : checkTask.getSampleSessionCount()
                        : Objects.isNull(checkTask) ? 0 : checkTask.getSampleCount())
                .qualityCheckProgress(labelingTask.getAssignType() != null && labelingTask.getAssignType() == AssignTypeEnum.SESSION.getCode()
                        ? (Objects.isNull(checkTask) ? 0 : checkTask.getSampleSessionCount()) - (Objects.isNull(checkTask) ? 0 : checkTask.getUnFinishedSessionCount())
                        : (Objects.isNull(checkTask) ? 0 : checkTask.getSampleCount()) - (Objects.isNull(checkTask) ? 0 : checkTask.getUnFinishedCount()))
                .qualityCheckStatus(Objects.isNull(qualityCheckTaskStatus) ? "未知" : qualityCheckTaskStatus.getValue())
                .qualityCheckStatusCode(Objects.isNull(qualityCheckTask.getQualityCheckStatus()) ? 0 : qualityCheckTask.getQualityCheckStatus())
                .qualityCheckerMis(qualityCheckTask.getQualityCheckMis())
                .qualityCheckerName(qualityCheckTask.getQualityCheckName())
                .taskType(qualityCheckTask.getTaskType())
                .dataType(labelingTask.getDataType())
                .build();
    }

    /**
     * 质检结果保存
     *
     * @param param 参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveResult(QualityCheckResultSaveParam param) {
        //参数校验
        verifyQualityResultParam(param);
        //构建修改内容
        List<LabelingQualityCheckItem> labelingQualityCheckItems = buildUpdateQualityCheckItem(param);
        //更新质检结果
        labelingQualityCheckItems.forEach(labelingQualityCheckItem -> labelingQualityCheckItemRepository.updateById(labelingQualityCheckItem));
        //修改任务状态到已质检  强制走主库，避免主从延迟
        ZebraForceMasterHelper.forceMasterInLocalContext();
        updateCheckTaskStatusToCompleted(labelingQualityCheckItems.get(0).getQualityCheckTaskId());
        ZebraForceMasterHelper.clearLocalContext();
    }

    @Override
    public LabelingQualityCheckRawCountVO countCheckTaskRawDataNum(Long checkTaskId) {
        CheckUtil.paramCheck(Objects.nonNull(checkTaskId), "请选择一个质检任务");
        //查询质检任务原数据数量
        Integer rawDataTotalNum = labelingQualityCheckItemRepository.countRawDataIdsByTaskAndStatus(checkTaskId,
                null, null);
        if (Objects.equals(0, rawDataTotalNum)) {
            return new LabelingQualityCheckRawCountVO(0, 0, 0, 0);
        }
        //查询质检任务不可检原数据数量
        Integer undetectableRawDataNum = labelingQualityCheckItemRepository.countRawDataIdsByTaskAndStatus(checkTaskId,
                QualityCheckAbleStatus.UNDETECTABLE.getCode(), null);
        //查询质检任务质检完成原数据数量
        Integer waitingCheckRawDataNum = labelingQualityCheckItemRepository.countRawDataIdsByTaskAndStatus(checkTaskId,
                null, QualityCheckDataStatus.WAITING_QUALITY_CHECK.getCode());
        //计算质检任务未质检的原数据数量
        Integer checkedRawDataNum = rawDataTotalNum - waitingCheckRawDataNum;
        return new LabelingQualityCheckRawCountVO(rawDataTotalNum, checkedRawDataNum, waitingCheckRawDataNum, undetectableRawDataNum);
    }

    @Override
    public void view(QualityCheckViewParam param) {
        List<LabelingQualityCheckItem> labelingQualityCheckItemList;
        // 检测当前登录人是不是质检人，如果不是，则不记录
        String mis = UserUtils.getUser().getLogin();
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(param.getQualityCheckTaskId());
        if (labelingQualityCheckTask == null || !mis.equals(labelingQualityCheckTask.getQualityCheckMis())) {
            return;
        }
        // query维度标记整个sessionId
        if (LabelTaskDataType.QUERY_ANNOTATION.getCode() == param.getType()) {
            labelingQualityCheckItemList = labelingQualityCheckItemRepository.listDetailIdByTaskIdAndSessionId(param.getQualityCheckTaskId(), param.getSessionId());
        } else {
            // 其他场景标记当前页面展示的数据
            labelingQualityCheckItemList = labelingQualityCheckItemRepository.listDetailIdByTaskIdAndRawDataId(param.getQualityCheckTaskId(), param.getRawDataId());
        }
        if (CollectionUtils.isNotEmpty(labelingQualityCheckItemList)) {
            List<Long> labelingDetailIdList = labelingQualityCheckItemList.stream().map(LabelingQualityCheckItem::getLabelingDataId).collect(Collectors.toList());
            labelingDetailRepository.updateViewStatusByIdList(labelingDetailIdList);
        }
    }

    @Override
    public List<TaskConditionDTO> getQualityCheckCondition(Long qualityCheckId) {
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(qualityCheckId);
        CheckUtil.operationCheck(Objects.nonNull(labelingQualityCheckTask), "质检任务不存在");
        LabelingTask labelingTask = labelingTaskRepository.getById(labelingQualityCheckTask.getTaskId());
        List<TaskConditionDTO> allCondition = new ArrayList<>();
        // 大模型消息检索条件
        List<TaskConditionDTO> llmCondition = LabelingTaskConditionHelper.getLlmCondition(labelingTask.getDataType());
        if (CollectionUtils.isNotEmpty(llmCondition)) {
            allCondition.addAll(llmCondition);
        }
        // 映射数据查询条件
        List<TaskConditionDTO> mappedCondition = LabelingTaskConditionHelper.getMappedCondition(labelingTask.getDataType());
        if (CollectionUtils.isNotEmpty(mappedCondition)) {
            allCondition.addAll(mappedCondition);
        }
        // 信号查询条件
        List<String> signalList = getSignalList(labelingQualityCheckTask, labelingTask.getDataType());
        List<TaskConditionDTO> signalCondition = LabelingTaskConditionHelper.getSignalCondition(labelingTask.getDataType(), signalList);
        if (CollectionUtils.isNotEmpty(signalCondition)) {
            allCondition.addAll(signalCondition);
        }
        return allCondition;
    }

    @Override
    public void checkTemporaryStorage(QualityCheckResultSaveParam param) {
        List<QualityCheckResultSaveDTO> checkItems = param.getCheckItems();
        if (CollectionUtils.isEmpty(checkItems)) {
            log.warn("质检信息暂存，存储数据为空，不处理");
            return;
        }
        checkItems.forEach(checkItem -> {
            if (Objects.isNull(checkItem.getId())){
                log.warn("质检信息暂存，存储数据Id为空，不处理");
                return;
            }
            CheckUtil.paramCheck(Objects.nonNull(checkItem.getQualityCheckResult()), "质检结果不能为空");
            if (Objects.isNull(checkItem.getQualityCheckResult())
                    && StringUtils.isBlank(checkItem.getQualityCheckRemark())
                    && StringUtils.isBlank(checkItem.getRawDataMappedContent())
                    && CollectionUtils.isEmpty(checkItem.getModifiedLabelingItems())) {
                log.warn("质检信息暂存，没有要存储的数据，不处理");
                return;
            }
            LabelingQualityCheckItem labelingQualityCheckItem = labelingQualityCheckItemRepository.getById(checkItem.getId());
            if (Objects.isNull(labelingQualityCheckItem) || Objects.equals(QualityCheckDataStatus.QUALITY_CHECKED.getCode(), labelingQualityCheckItem.getStatus())) {
                log.warn("质检信息暂存，已完成质检，不需要暂存");
                return;
            }
            QualityCheckResultSaveDTO checkTemporaryStorage = getCheckTemporaryStorage(checkItem.getId());
            // 质检反馈特殊处理
            List<List<LabelResult>> itemsResult = handleQualityCheckRemark(checkItem);
            // 没有暂存信息直接存
            if (Objects.isNull(checkTemporaryStorage)) {
                checkItem.setQualityCheckRemark(JSON.toJSONString(itemsResult));
                saveCheckTemporaryStorage(checkItem);
                return;
            }
            if (Objects.nonNull(checkItem.getQualityCheckResult())){
                checkTemporaryStorage.setQualityCheckResult(checkItem.getQualityCheckResult());
            }
            if (CollectionUtils.isNotEmpty(itemsResult)){
                checkTemporaryStorage.setQualityCheckRemark(JSON.toJSONString(itemsResult));
            }
            if (StringUtils.isNotBlank(checkItem.getRawDataMappedContent())){
                checkTemporaryStorage.setRawDataMappedContent(checkItem.getRawDataMappedContent());
            }
            if (CollectionUtils.isNotEmpty(checkItem.getModifiedLabelingItems())){
                checkTemporaryStorage.setModifiedLabelingItems(checkItem.getModifiedLabelingItems());
            }
            saveCheckTemporaryStorage(checkTemporaryStorage);
        });
    }

    public List<List<LabelResult>> handleQualityCheckRemark(QualityCheckResultSaveDTO checkItem) {
        List<List<LabelResult>> itemsResult = new ArrayList<>();
        LabelResult labelResult = LabelResult.builder()
                .name("质检反馈")
                .itemType(LabelingItemType.QUALITY.getCode())
                .value(checkItem.getQualityCheckRemark())
                .enumList(null)
                .isCompare(2)
                .parentEnumValue(null)
                .parentId(null)
                .dataType(1)
                .id(38L)
                .build();
        itemsResult.add(Collections.singletonList(labelResult));
        return itemsResult;
    }

    /**
     * 获取质检暂存信息
     * @param checkItemId 质检详情ID
     * @return 结果
     */
    private QualityCheckResultSaveDTO getCheckTemporaryStorage(Long checkItemId){
       String checkTemporaryStorageStr = squirrelClient.get(RedisConstant.CHECK_TEMPORARY_STORAGE, String.valueOf(checkItemId));
       if (StringUtils.isBlank(checkTemporaryStorageStr)) {
           return null;
       }
       return JSON.parseObject(checkTemporaryStorageStr, new TypeReference<QualityCheckResultSaveDTO>(){});
    }

    /**
     * 暂存质检详情信息
     * @param qualityCheckResultSave 质检详情
     */
    private void saveCheckTemporaryStorage(QualityCheckResultSaveDTO qualityCheckResultSave){
        if (Objects.isNull(qualityCheckResultSave)){
            return;
        }
        squirrelClient.set(RedisConstant.CHECK_TEMPORARY_STORAGE, String.valueOf(qualityCheckResultSave.getId()), JSON.toJSONString(qualityCheckResultSave));
    }

    /**
     * 删除质检详情信息
     * @param checkItemId 质检详情ID
     */
    private void deleteCheckTemporaryStorage(Long checkItemId){
        if (Objects.isNull(checkItemId)){
            return;
        }
        squirrelClient.delete(RedisConstant.CHECK_TEMPORARY_STORAGE, String.valueOf(checkItemId));
    }
    
    /**
     * 获取信号列表
     *
     * @param labelingQualityCheckTask 质检任务
     * @param dataType                 数据类型
     * @return 信号列表
     */
    private List<String> getSignalList(LabelingQualityCheckTask labelingQualityCheckTask, Integer dataType) {
        if (dataType != LabelTaskDataType.SFT_FINE_TUNING.getCode() && dataType != LabelTaskDataType.DPO_ALIGNMENT.getCode()) {
            return new ArrayList<>();
        }
        List<Long> rawDataIdList = labelingQualityCheckItemRepository.listRawDataIdsByTaskAndRawDataIdAndStatus(labelingQualityCheckTask.getId(), null, null, null);
        if (CollectionUtils.isEmpty(rawDataIdList)) {
            return new ArrayList<>();
        }
        List<LabelingTaskRawData> rawDataList = labelingTaskRawDataRepository.listByIds(rawDataIdList);
        Set<String> signalSet = new HashSet<>();
        // 训练数据信号是excel上传，取表头字段
        rawDataList.forEach(rawData -> {
            if (StringUtils.isNotBlank(rawData.getRawDataHeaders())) {
                List<String> headers = JSONObject.parseArray(rawData.getRawDataHeaders(), String.class);
                if (CollectionUtils.isNotEmpty(headers)) {
                    signalSet.addAll(headers);
                }
            }
        });
        return new ArrayList<>(signalSet);
    }

    /**
     * 校验保存质检结果参数
     *
     * @param param 参数
     */
    public void verifyQualityResultParam(QualityCheckResultSaveParam param) {
        List<QualityCheckResultSaveDTO> checkItems = param.getCheckItems();
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(checkItems), "质检详情不能为空");
        checkItems.forEach(checkItem -> {
            CheckUtil.paramCheck(Objects.nonNull(checkItem.getId()), "请选择要保存的数据");
            CheckUtil.paramCheck(Objects.nonNull(checkItem.getQualityCheckResult()), "质检结果不能为空");
        });
        //获取本次保存的质检详情ID
        List<Long> checkItemIds = checkItems.stream().map(QualityCheckResultSaveDTO::getId).collect(Collectors.toList());
        //校验本次提交的数据是否属于同一个质检任务
        Integer qualityCheckTaskNum = labelingQualityCheckItemRepository.countCheckTaskNumByQualityCheckItemIds(checkItemIds);
        CheckUtil.paramCheck(qualityCheckTaskNum <= 1, "一次只支持保存同一个质检任务内的数据");
        //检查本次提交的质检结果是否属于同一个分组(分配时候同一分组的数据是一条原数据)
        Integer groupNum = labelingSubTaskRepository.countGroupNumByQualityCheckItemIds(checkItemIds);
        CheckUtil.paramCheck(groupNum <= 1, "一次只支持保存同一个分组内的数据");
        // 质检任务和复标任务锁校验
        checkQualityCheckTaskAndStatus(checkItemIds.get(0));
    }

    /**
     * 构建修改内容
     *
     * @param param 参数
     * @return 结果
     */
    public List<LabelingQualityCheckItem> buildUpdateQualityCheckItem(QualityCheckResultSaveParam param) {
        List<QualityCheckResultSaveDTO> checkItems = param.getCheckItems();
        List<LabelingQualityCheckItem> labelingQualityCheckItems = new ArrayList<>();
        checkItems.forEach(item -> {
            //查询质检详情
            LabelingQualityCheckItem checkItem = labelingQualityCheckItemRepository.getById(item.getId());
            if (Objects.isNull(checkItem)) {
                throw new AidaTrainingCheckException("质检数据不存在!");
            }
            User user = UserUtils.getUser();
            checkItem.setQualityCheckMis(user != null ? user.getLogin() : "unknown");
            checkItem.setQualityCheckName(user != null ? user.getName() : "unknown");
            checkItem.setUpdateTime(new Date());
            checkItem.setQualityCheckResult(item.getQualityCheckResult());
            // 加逻辑 当质检状态从待质检变成已质检时才给首次质检时间赋值
            if (QualityCheckDataStatus.WAITING_QUALITY_CHECK.getCode() == checkItem.getStatus()){
                // 赋值
                checkItem.setFirstCheckTime(new Date());
                // 删除暂存记录
                deleteCheckTemporaryStorage(checkItem.getId());
            }
            checkItem.setStatus(QualityCheckDataStatus.QUALITY_CHECKED.getCode());
            List<List<LabelResult>> itemsResult = handleQualityCheckRemark(item);
            checkItem.setQualityCheckItemsResult(JSON.toJSONString(itemsResult));
            // 处理标注内容
            handleLabelingData(checkItem, item);
            labelingQualityCheckItems.add(checkItem);
        });
        return labelingQualityCheckItems;
    }

    public void handleLabelingData(LabelingQualityCheckItem checkItem, QualityCheckResultSaveDTO qualityCheckResultSaveDTO) {
        if (Objects.isNull(checkItem) || Objects.isNull(qualityCheckResultSaveDTO)) {
            return;
        }
        // 查询标注详情
        LabelingDetail labelingDetail = labelingDetailRepository.getById(checkItem.getLabelingDataId());
        // 查询原数据
        LabelingTaskRawData labelingTaskRawData = labelingTaskRawDataRepository.getById(Objects.isNull(labelingDetail) ? null : labelingDetail.getRawDataId());
        // 比较原数据内容映射内容是否被修改, 不一致时需要更新表
        if (!LabelResultCompareUtil.compareRawDataMappedContent(qualityCheckResultSaveDTO.getRawDataMappedContent(), labelingTaskRawData, labelingDetail)) {
            checkItem.setModifiedRawDataMappedContent(qualityCheckResultSaveDTO.getRawDataMappedContent());
        }
        // 质检结果为错误，修改了标注结果时需要处理标注结果
        if (Objects.equals(QualityCheckResultEnum.ERROR.getCode(), qualityCheckResultSaveDTO.getQualityCheckResult())
                && CollectionUtils.isNotEmpty(qualityCheckResultSaveDTO.getModifiedLabelingItems()) && Objects.nonNull(labelingDetail) &&
                !JsonCompareUtil.areEqual(labelingDetail.getLabelingItemsResult(), JSON.toJSONString(qualityCheckResultSaveDTO.getModifiedLabelingItems()))) {
            checkItem.setModifiedLabelingItemsResult(JSON.toJSONString(qualityCheckResultSaveDTO.getModifiedLabelingItems()));
        }
        // 正确时置空
        if (Objects.equals(QualityCheckResultEnum.CORRECT.getCode(), qualityCheckResultSaveDTO.getQualityCheckResult())) {
            checkItem.setModifiedLabelingItemsResult(null);
        }
    }

    public void updateCheckTaskStatusToCompleted(Long qualityCheckTaskId) {
        if (Objects.isNull(qualityCheckTaskId)) {
            log.warn("质检任务id为空不做处理");
            return;
        }
        //修改质检任务状态
        Integer waitingCheckNum = labelingQualityCheckItemRepository.countByCheckTaskIdAndStatus(qualityCheckTaskId, QualityCheckDataStatus.WAITING_QUALITY_CHECK.getCode());
        if (waitingCheckNum > 0) {
            return;
        }
        log.info("当前质检任务完成，开始修改状态，质检任务id：{}", qualityCheckTaskId);
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(qualityCheckTaskId);
        if (Objects.isNull(labelingQualityCheckTask)) {
            log.warn("修改质检任务状态，任务不存在，质检任务id：{}", qualityCheckTaskId);
            return;
        }
        //只有质检中状态可以到已质检
        if (QualityCheckTaskStatus.CHECKING.getCode() == labelingQualityCheckTask.getStatus()) {
            labelingQualityCheckTaskRepository.updateStatusById(labelingQualityCheckTask.getId(), QualityCheckTaskStatus.COMPLETED.getCode());
        }
        //查看主任务下所有质检任务是否都已完成
        Integer waitingCheckTaskNum = labelingQualityCheckTaskRepository.countByTaskIdAndStatus(
                labelingQualityCheckTask.getTaskId(),
                Arrays.asList(QualityCheckTaskStatus.CREATING.getCode(), QualityCheckTaskStatus.CHECKING.getCode()));
        if (waitingCheckTaskNum > 0) {
            return;
        }
        log.info("所以质检任务完成，开始修改主任务质检状态，质检任务id：{}", qualityCheckTaskId);
        LabelingTask labelingTask = labelingTaskRepository.getById(labelingQualityCheckTask.getTaskId());
        if (Objects.isNull(labelingTask)) {
            log.warn("修改主任务质检状态，主任务不存在，质检任务id：{}", qualityCheckTaskId);
            return;
        }
        List<LabelingTaskRawDataPO> rawDataList = labelingTaskRawDataRepository.getCountByTaskIds(Collections.singletonList(labelingTask.getId()));
        QualityCheckSampleCountPO countSampleSizeByTaskId = labelingQualityCheckTaskRepository.countSampleSizeByTaskId(labelingTask.getId());
        int checkTaskStatus = QualityCheckTaskStatus.COMPLETED.getCode();
        // 部分质检
        if (CollectionUtils.isNotEmpty(rawDataList) && countSampleSizeByTaskId != null) {
            Integer rawDataNum = labelingTask.getAssignType() != null && labelingTask.getAssignType() == AssignTypeEnum.SESSION.getCode() ? rawDataList.get(0).getTotalSessionNum() : rawDataList.get(0).getTotalNum();
            Integer totalSampleSize = labelingTask.getAssignType() != null && labelingTask.getAssignType() == AssignTypeEnum.SESSION.getCode() ? countSampleSizeByTaskId.getTotalSessionCount() : countSampleSizeByTaskId.getTotalCount();
            if (rawDataNum != null && totalSampleSize != null && rawDataNum > totalSampleSize) {
                checkTaskStatus = QualityCheckTaskStatus.PARTIAL.getCode();
            }
        }
        // 只有质检中和部分质检可以修改质检状态
        if (Objects.equals(QualityCheckTaskStatus.CHECKING.getCode(), labelingTask.getQualityCheckStatus())
                || Objects.equals(QualityCheckTaskStatus.PARTIAL.getCode(), labelingTask.getQualityCheckStatus())) {
            labelingTaskRepository.updateTaskQualityCheckStatusById(labelingTask.getId(), checkTaskStatus, new Date());
            //发送大象通知
            pushElephantService.pushElephant(String.format("任务「%s」质检完成，请前往[训练系统|%s]查看。", labelingTask.getTaskName(),
                    lionConfig.getCreateDataSetNotifyElephantUrl()), labelingTask.getLabelingManager());
        }
    }

    /**
     * 构建质检详情DTO
     *
     * @param qualityCheckItem 质检详情PO
     * @return 质检详情DTO
     */
    private LabelingQualityCheckItemDTO buildLabelingQualityCheckItemDTO(LabelingQualityCheckItemPO qualityCheckItem) {
        String messageId = null;
        String messageContent = null;
        if (StringUtils.isNotBlank(qualityCheckItem.getExtraInfo())) {
            RawDataExtraInfo extraInfo = JSON.parseObject(qualityCheckItem.getExtraInfo(), RawDataExtraInfo.class);
            if (Objects.nonNull(extraInfo)) {
                messageId = extraInfo.getLlmMessageId();
                messageContent = extraInfo.getMessageContent();
            }
        }
        LabelingQualityCheckItemDTO qualityCheckItemDTO = LabelingQualityCheckItemDTO.builder()
                .id(qualityCheckItem.getId())
                .dataId(qualityCheckItem.getRawDataId())
                .rawDataContent(qualityCheckItem.getRawDataContent())
                .rawDataMappedContent(qualityCheckItem.getRawDataMappedContent())
                .labelingItems(qualityCheckItem.getLabelingItems())
                .qualityCheckResult(qualityCheckItem.getQualityCheckResult())
                .qualityCheckRemark(qualityCheckItem.getQualityCheckRemark())
                .qualityChecker(qualityCheckItem.getQualityChecker())
                .qualityCheckTime(qualityCheckItem.getQualityCheckTime())
                .qualityCheckStatus(qualityCheckItem.getQualityCheckStatus())
                .labelerMis(qualityCheckItem.getLabelerMis())
                .labelerName(qualityCheckItem.getLabelerName())
                .labelTime(qualityCheckItem.getLabelTime())
                .sessionId(qualityCheckItem.getSessionId())
                .sessionTime(qualityCheckItem.getSessionTime())
                .messageId(messageId)
                .thirdMessageId(qualityCheckItem.getMessageId())
                .messageContent(messageContent)
                .modifiedLabelingItems(qualityCheckItem.getModifiedLabelingItems())
                .build();
        if (StringUtils.isNotBlank(qualityCheckItem.getQualityCheckRemark())) {
            List<List<LabelResult>> labelResults = JSON.parseObject(qualityCheckItem.getQualityCheckRemark(), new TypeReference<List<List<LabelResult>>>() {
            });
            LabelResult labelResult = Optional.ofNullable(labelResults.get(0)).map(list -> list.get(0)).orElse(new LabelResult());
            qualityCheckItemDTO.setQualityCheckRemark(labelResult.getValue());
        }
        return qualityCheckItemDTO;

    }

    /**
     * 构建质检详情VO
     *
     * @param labelingQualityCheckItemPos 质检详情PO列表
     * @return 质检详情VO列表
     */
    private List<QualityCheckedItemVO> buildQualityCheckedItemVos(List<LabelingQualityCheckItemPO> labelingQualityCheckItemPos) {
        if (CollectionUtils.isEmpty(labelingQualityCheckItemPos)) {
            return Collections.emptyList();
        }
        List<QualityCheckedItemVO> qualityCheckedItemVos = new ArrayList<>();
        labelingQualityCheckItemPos.forEach(qualityCheckItem -> {
            QualityCheckedItemVO qualityCheckedItemVO = QualityCheckedItemVO.builder()
                    .qualityCheckedItemId(qualityCheckItem.getId())
                    .labelingDataId(qualityCheckItem.getLabelingDataId())
                    .rawDataContent(LabelingTaskUtil.filterNullHeadValue(qualityCheckItem.getRawDataContent()))
                    .rawDataHeaders(LabelingTaskUtil.filterNullHeader(qualityCheckItem.getRawDataHeaders()))
                    .rawDataMappedContent(qualityCheckItem.getRawDataMappedContent())
                    .labelingItems(qualityCheckItem.getLabelingItems())
                    .qualityCheckResult(qualityCheckItem.getQualityCheckResult())
                    .qualityChecker(qualityCheckItem.getQualityChecker())
                    .qualityCheckTime(Objects.nonNull(qualityCheckItem.getQualityCheckTime())
                            ? DateUtil.formatDate(qualityCheckItem.getQualityCheckTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD) : "")
                    .qualityCheckStatus(qualityCheckItem.getQualityCheckStatus())
                    .labelerMis(qualityCheckItem.getLabelerMis())
                    .labelerName(qualityCheckItem.getLabelerName())
                    .labelTime(Objects.nonNull(qualityCheckItem.getLabelTime())
                            ? DateUtil.formatDate(qualityCheckItem.getLabelTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD) : "")
                    .modifiedLabelingItems(qualityCheckItem.getModifiedLabelingItems())
                    .build();
            if (StringUtils.isNotBlank(qualityCheckItem.getQualityCheckRemark())) {
                List<List<LabelResult>> labelResults = JSON.parseObject(qualityCheckItem.getQualityCheckRemark(), new TypeReference<List<List<LabelResult>>>() {
                });
                if (CollectionUtils.isNotEmpty(labelResults)) {
                    LabelResult labelResult = Optional.ofNullable(labelResults.get(0)).map(list -> list.get(0)).orElse(new LabelResult());
                    qualityCheckedItemVO.setQualityCheckRemark(labelResult.getValue());
                }
            }
            // 处理暂存数据(只有在未质检时候才需要处理并展示暂存数据)
            handleCheckTemporaryStorage(qualityCheckItem, qualityCheckedItemVO);
            qualityCheckedItemVos.add(qualityCheckedItemVO);
        });
        return qualityCheckedItemVos;

    }

    /**
     * 处理暂存数据，替换qualityCheckedItemVO中的一些值
     * @param qualityCheckItem 质检详情
     * @param qualityCheckedItemVO 质检详情展示vo
     */
    private void handleCheckTemporaryStorage(LabelingQualityCheckItemPO qualityCheckItem, QualityCheckedItemVO qualityCheckedItemVO) {
        // 只有未质检的时候才展示暂存数据
        if (Objects.isNull(qualityCheckItem) || Objects.isNull(qualityCheckedItemVO) || !Objects.equals(QualityCheckDataStatus.WAITING_QUALITY_CHECK.getCode(), qualityCheckItem.getQualityCheckStatus())){
            return;
        }
        QualityCheckResultSaveDTO checkTemporaryStorage = getCheckTemporaryStorage(qualityCheckItem.getId());
        if (Objects.isNull(checkTemporaryStorage)){
            log.warn("缓存中未查询到质检暂存信息，详情ID={}", qualityCheckItem.getId());
            return;
        }
        // 只有暂存信息有值的时候才替换qualityCheckedItemVO中的数据
        Integer qualityCheckResult = checkTemporaryStorage.getQualityCheckResult();
        String qualityCheckRemark = checkTemporaryStorage.getQualityCheckRemark();
        String rawDataMappedContent = checkTemporaryStorage.getRawDataMappedContent();
        List<List<LabelResult>> modifiedLabelingItems = checkTemporaryStorage.getModifiedLabelingItems();
        if (Objects.nonNull(qualityCheckResult)) {
            qualityCheckedItemVO.setQualityCheckResult(qualityCheckResult);
        }
        if (StringUtils.isNotBlank(qualityCheckRemark)){
            List<List<LabelResult>> labelResults = JSON.parseObject(qualityCheckRemark, new TypeReference<List<List<LabelResult>>>() {
            });
            if (CollectionUtils.isNotEmpty(labelResults)){
                LabelResult labelResult = Optional.ofNullable(labelResults.get(0)).map(list -> list.get(0)).orElse(new LabelResult());
                qualityCheckedItemVO.setQualityCheckRemark(labelResult.getValue());
            }
        }
        if (StringUtils.isNotBlank(rawDataMappedContent)){
            qualityCheckedItemVO.setRawDataMappedContent(rawDataMappedContent);
        }
        if (CollectionUtils.isNotEmpty(modifiedLabelingItems)){
            qualityCheckedItemVO.setModifiedLabelingItems(JSON.toJSONString(modifiedLabelingItems));
        }
    }

    /**
     * 获取质检详情分页数据
     *
     * @param query 质检详情查询参数
     * @return 质检详情分页数据
     */
    private PageData<LabelingQualityCheckItemVO> getQueryDataTypePageData(LabelingQualityDetailPageQuery query) {
        // 设置分页参数，默认页码为1，默认每页大小为1
        int pageNum = Objects.isNull(query.getPageNum()) ? 1 : query.getPageNum();
        int pageSize = Objects.isNull(query.getPageSize()) ? 1 : query.getPageSize();
        ZebraForceMasterHelper.forceMasterInLocalContext();
        PageHelper.startPage(pageNum, pageSize);
        List<String> sessionIdList = labelingQualityCheckItemRepository.listSessionIdByTaskAndSessionIdAndStatus(query.getQualityCheckTaskId(), query.getSessionId(), query.getCheckableStatus(), query.getQualityStatus());
        if (CollectionUtils.isEmpty(sessionIdList)) {
            log.warn("质检任务：{}，质检详情展示查询到的原数据ID为空！", query.getQualityCheckTaskId());
            ZebraForceMasterHelper.clearLocalContext();
            return new PageData<>(0L, Collections.emptyList());
        }

        // 获取分页信息
        PageInfo<String> pageInfo = new PageInfo<>(sessionIdList);
        PageHelper.clearPage();

        // 获取当前页的原数据ID列表
        List<String> pageSessionIdList = pageInfo.getList();

        List<LabelingQualityCheckItemPO> labelingQualityCheckItems = labelingQualityCheckItemRepository.listQualityCheckItemByTaskIdAndSessionIdList(query.getQualityCheckTaskId(), sessionIdList);
        ZebraForceMasterHelper.clearLocalContext();
        if (CollectionUtils.isEmpty(labelingQualityCheckItems)) {
            log.warn("质检任务：{}，质检详情查询为空！", query.getQualityCheckTaskId());
            return new PageData<>(pageInfo.getTotal(), Collections.emptyList());
        }
        Map<String, List<LabelingQualityCheckItemPO>> sessionCheckItemMap = labelingQualityCheckItems.stream().collect(Collectors.groupingBy(LabelingQualityCheckItemPO::getSessionId));
        List<LabelingQualityCheckItemVO> data = new ArrayList<>();
        pageSessionIdList.forEach(sessionId -> {
            // 获取当前原数据ID对应的质检详情列表
            List<LabelingQualityCheckItemPO> labelingQualityCheckItemPos = sessionCheckItemMap.get(sessionId);
            if (CollectionUtils.isEmpty(labelingQualityCheckItemPos)) {
                return;
            }
            // 只要有一个状态是已查看，则整个会话都是已查看，后续不用再处理查看逻辑
            boolean viewed = labelingQualityCheckItemPos.stream().anyMatch(item -> item.getViewStatus() != null && item.getViewStatus() == DataViewStatusEnum.VIEWED.getCode());

            // 构建质检详情VO对象
            LabelingQualityCheckItemVO qualityCheckItemVO = new LabelingQualityCheckItemVO();
            qualityCheckItemVO.setViewed(viewed);
            qualityCheckItemVO.setSessionId(sessionId);
            qualityCheckItemVO.setSessionTime(Objects.nonNull(labelingQualityCheckItemPos.get(0)) && Objects.nonNull(labelingQualityCheckItemPos.get(0).getSessionTime())
                    ? DateUtil.formatDate(labelingQualityCheckItemPos.get(0).getSessionTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD) : "");
            // 在每个会话中按照messageId分组，每个消息再按标注数据分组，因此是一个二维数组，可以供前端选择不同的消息
            // 没有messageId的分为一组，可能是因为还没标注，query维度下没有获取到messageId
            List<LabelingQualityCheckItemPO> noMessageIdCheckItems = labelingQualityCheckItemPos.stream().filter(labelingQualityCheckItem -> StringUtils.isBlank(labelingQualityCheckItem.getMessageId())).collect(Collectors.toList());
            List<LabelingQualityCheckItemPO> hasMessageIdCheckItems = labelingQualityCheckItemPos.stream().filter(labelingQualityCheckItem -> StringUtils.isNotBlank(labelingQualityCheckItem.getMessageId())).collect(Collectors.toList());
            List<LabelingQualityCheckItemVO.QueryQualityCheckedItem> queryQualityCheckedItemList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(hasMessageIdCheckItems)) {
                Map<String, List<LabelingQualityCheckItemPO>> messageCheckItemMap = labelingQualityCheckItemPos.stream().collect(Collectors.groupingBy(LabelingQualityCheckItemPO::getMessageId));
                // 构建每个query的数据
                messageCheckItemMap.forEach((messageId, checkItemList) -> {
                    LabelingQualityCheckItemVO.QueryQualityCheckedItem queryQualityCheckedItem = new LabelingQualityCheckItemVO.QueryQualityCheckedItem();
                    queryQualityCheckedItem.setMessageId(messageId);
                    queryQualityCheckedItem.setRowDataId(checkItemList.get(0).getRawDataId());
                    queryQualityCheckedItem.setQualityCheckedItemList(buildQualityCheckedItemVos(checkItemList));
                    queryQualityCheckedItemList.add(queryQualityCheckedItem);
                });
            }
            // 没有大模型消息ID的单独处理
            if (CollectionUtils.isNotEmpty(noMessageIdCheckItems)) {
                LabelingQualityCheckItemVO.QueryQualityCheckedItem queryQualityCheckedItem = new LabelingQualityCheckItemVO.QueryQualityCheckedItem();
                queryQualityCheckedItem.setRowDataId(noMessageIdCheckItems.get(0).getRawDataId());
                queryQualityCheckedItem.setQualityCheckedItemList(buildQualityCheckedItemVos(noMessageIdCheckItems));
                queryQualityCheckedItemList.add(queryQualityCheckedItem);
            }
            qualityCheckItemVO.setQueryQualityCheckedItemList(queryQualityCheckedItemList);
            data.add(qualityCheckItemVO);
        });

        // 返回分页数据
        return new PageData<>(pageInfo.getTotal(), data);
    }


    /**
     * 获取除了query维度数据类型的质检数据，其他类型的数据使用一维数组存储
     *
     * @param query 查询参数
     * @return 分页数据
     */
    private PageData<LabelingQualityCheckItemVO> getOtherDataTypePageData(LabelingQualityDetailPageQuery query) {
        // 设置分页参数，默认页码为1，默认每页大小为1
        int pageNum = Objects.isNull(query.getPageNum()) ? 1 : query.getPageNum();
        int pageSize = Objects.isNull(query.getPageSize()) ? 1 : query.getPageSize();
        ZebraForceMasterHelper.forceMasterInLocalContext();
        PageHelper.startPage(pageNum, pageSize);
        List<Long> rawDataIds = labelingQualityCheckItemRepository.listRawDataIdsByTaskAndRawDataIdAndStatus(query.getQualityCheckTaskId(), query.getDataId(), query.getCheckableStatus(), query.getQualityStatus());
        if (CollectionUtils.isEmpty(rawDataIds)) {
            log.warn("质检任务：{}，质检详情展示查询到的原数据ID为空！", query.getQualityCheckTaskId());
            ZebraForceMasterHelper.clearLocalContext();
            return new PageData<>(0L, Collections.emptyList());
        }

        // 获取分页信息
        PageInfo<Long> pageInfo = new PageInfo<>(rawDataIds);
        PageHelper.clearPage();

        // 获取当前页的原数据ID列表
        List<Long> pageRawDataIds = pageInfo.getList();
        List<LabelingQualityCheckItemPO> labelingQualityCheckItems = labelingQualityCheckItemRepository.listQualityCheckItemByTaskIdAndRawDataIds(query.getQualityCheckTaskId(), pageRawDataIds);
        ZebraForceMasterHelper.clearLocalContext();
        if (CollectionUtils.isEmpty(labelingQualityCheckItems)) {
            log.warn("质检任务：{}，质检详情查询为空！", query.getQualityCheckTaskId());
            return new PageData<>(pageInfo.getTotal(), Collections.emptyList());
        }
        Map<Long, List<LabelingQualityCheckItemPO>> rawDataCheekItemMap = labelingQualityCheckItems.stream().collect(Collectors.groupingBy(LabelingQualityCheckItemPO::getRawDataId));
        List<LabelingQualityCheckItemVO> data = new ArrayList<>();
        pageRawDataIds.forEach(rawDataId -> {
            // 获取当前原数据ID对应的质检详情列表
            List<LabelingQualityCheckItemPO> labelingQualityCheckItemPos = rawDataCheekItemMap.get(rawDataId);
            if (CollectionUtils.isEmpty(labelingQualityCheckItemPos)) {
                return;
            }

            // 只要有一个状态是已查看，则当前数据对应的标注结果都是已查看，后续不用再处理查看逻辑
            boolean viewed = labelingQualityCheckItemPos.stream().anyMatch(item -> item.getViewStatus() != null && item.getViewStatus() == DataViewStatusEnum.VIEWED.getCode());

            // 构建质检详情VO对象
            LabelingQualityCheckItemVO qualityCheckItemVO = new LabelingQualityCheckItemVO();
            qualityCheckItemVO.setViewed(viewed);
            qualityCheckItemVO.setRowDataId(rawDataId);
            qualityCheckItemVO.setSessionId(Objects.nonNull(labelingQualityCheckItemPos.get(0)) ? rawDataCheekItemMap.get(rawDataId).get(0).getSessionId() : "");
            qualityCheckItemVO.setSessionTime(Objects.nonNull(labelingQualityCheckItemPos.get(0)) && Objects.nonNull(labelingQualityCheckItemPos.get(0).getSessionTime())
                    ? DateUtil.formatDate(labelingQualityCheckItemPos.get(0).getSessionTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD) : "");
            qualityCheckItemVO.setQualityCheckedItemList(buildQualityCheckedItemVos(labelingQualityCheckItemPos));
            data.add(qualityCheckItemVO);
        });

        // 返回分页数据
        return new PageData<>(pageInfo.getTotal(), data);
    }

    private void checkQualityCheckTaskAndStatus(Long checkItemId){
        if(Objects.isNull(checkItemId)){
            return;
        }
        // 查询质检详情对应的质检任务
        ZebraForceMasterHelper.forceMasterInLocalContext();
        LabelingQualityCheckItem labelingQualityCheckItem = labelingQualityCheckItemRepository.getById(checkItemId);
        CheckUtil.paramCheck(Objects.nonNull(labelingQualityCheckItem), "质检数据不存在");
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(labelingQualityCheckItem.getQualityCheckTaskId());
        CheckUtil.paramCheck(Objects.nonNull(labelingQualityCheckTask), "质检任务不存在");
        // 校验质检任务状态
        if(Objects.equals(QualityCheckTaskStatus.ARCHIVED.getCode(), labelingQualityCheckTask.getStatus())){
            throw new AidaTrainingOperationException("当前质检任务已失效，不可质检");
        }
        // 检查任务锁
        checkReLabelingLock(labelingQualityCheckTask.getTaskId());
        // 校验主任务状态
        LabelingTask labelingTask = labelingTaskRepository.getById(labelingQualityCheckTask.getTaskId());
        if(Objects.equals(LabelingTaskStatus.ARCHIVED.getCode(), labelingTask.getQualityCheckStatus())){
            throw new AidaTrainingOperationException("当前任务已失效，不可质检");
        }
        // 清除上下文
        ZebraForceMasterHelper.clearLocalContext();
    }

    /**
     * 校验复标锁是否存在
     * @param taskId 任务ID
     */
    private void checkReLabelingLock(Long taskId){
        if (BooleanUtils.isTrue(squirrelClient.exists(RedisConstant.RE_LABELING_TASK_ID, String.valueOf(taskId)))) {
            throw new AidaTrainingCheckException("当前任务复标中，请稍后前往新任务操作");
        }
    }
}
