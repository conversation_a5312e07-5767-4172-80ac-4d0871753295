package com.meituan.aigc.aida.benchmark.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * Benchmark版本管理实体类
 * 对应数据库表：benchmark_version
 *
 * <AUTHOR>
 */
@Data
public class BenchmarkVersion {

    /**
     * 版本ID - 主键，自增长
     */
    private Long id;

    /**
     * 版本名称 - 不能为空，最大长度100
     */
    private String versionName;

    /**
     * 版本描述 - 可为空，最大长度200
     */
    private String description;

    /**
     * 评测对象类型
     * 0 - 基础模型
     */
    private Integer evalObject;

    /**
     * 版本状态
     * 0 - 未发布
     * 1 - 已发布
     */
    private Integer status;

    /**
     * 创建人MIS - 不能为空，最大长度20
     */
    private String createdMis;

    /**
     * 更新人MIS - 可为空，最大长度20
     */
    private String updatedMis;

    /**
     * 创建时间 - 默认当前时间
     */
    private Date createdTime;

    /**
     * 更新时间 - 默认当前时间，更新时自动修改
     */
    private Date updatedTime;
}