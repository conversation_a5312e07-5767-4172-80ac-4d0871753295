package com.meituan.aigc.aida.labeling.config;

import com.meituan.csc.aigc.eval.remote.autoanalysistool.api.AutoAnalysisToolRemoteService;
import com.meituan.csc.aigc.eval.remote.training.api.TrainingCascadeMetricService;
import com.meituan.csc.aigc.runtime.api.AidaRobotXmMessageRemoteService;
import com.meituan.csc.aigc.runtime.api.UserRemoteService;
import com.meituan.csc.aigc.runtime.inner.api.AidaUserInfoRemoteService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.csccratos.csc.aida.label.client.api.AidaSceneRemoteService;
import org.springframework.stereotype.Component;

@Component
public class RpcConfig {

    /**
     * mis信息查询
     */
    @MdpPigeonClient(timeout = 3000L, remoteAppKey = "com.sankuai.csccratos.prompt.runtime")
    private AidaUserInfoRemoteService aidaUserInfoRemoteService;

    /**
     * 大象消息推送
     */
    @MdpPigeonClient(timeout = 3000L, remoteAppKey = "com.sankuai.csccratos.prompt.runtime")
    private AidaRobotXmMessageRemoteService aidaRobotXmMessageRemoteService;

    /**
     * 训练级联指标
     */
    @MdpPigeonClient(timeout = 3000L)
    private TrainingCascadeMetricService trainingCascadeMetricService;
    /**
     * 用户信息查询
     */
    @MdpPigeonClient(timeout = 3000L)
    private UserRemoteService userRemoteService;

    /**
     * 自动分析工具远程服务
     */
    @MdpPigeonClient(timeout = 3000L)
    private AutoAnalysisToolRemoteService autoAnalysisToolRemoteService;

    /**
     * 业务场景相关接口
     */
    @MdpPigeonClient(timeout = 3000L, remoteAppKey = "com.sankuai.csccratos.aida.label")
    private AidaSceneRemoteService aidaSceneRemoteService;

}
