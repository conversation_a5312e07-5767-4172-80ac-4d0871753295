package com.meituan.aigc.aida.benchmark.dao.repository.impl;

import com.meituan.aigc.aida.benchmark.dao.mapper.BenchmarkVersionMapper;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersionExample;
import com.meituan.aigc.aida.benchmark.dao.repository.BenchmarkVersionRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Benchmark版本Repository实现类
 *
 * <AUTHOR>
 * @description Benchmark版本数据访问层实现
 * @date 2025/6/24
 */
@Repository
public class BenchmarkVersionRepositoryImpl implements BenchmarkVersionRepository {

    @Resource
    private BenchmarkVersionMapper benchmarkVersionMapper;

    @Override
    public List<BenchmarkVersion> listAllBenchmarkVersions() {
        BenchmarkVersionExample example = new BenchmarkVersionExample();
        // 按创建时间倒序排列
        example.setOrderByClause("created_time desc");
        return benchmarkVersionMapper.selectByExample(example);
    }

    @Override
    public List<BenchmarkVersion> listBenchmarkVersionsByStatus(Integer status) {
        if (Objects.isNull(status)) {
            return Collections.emptyList();
        }

        BenchmarkVersionExample example = new BenchmarkVersionExample();
        BenchmarkVersionExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(status);
        example.setOrderByClause("created_time desc");

        return benchmarkVersionMapper.selectByExample(example);
    }

    @Override
    public List<BenchmarkVersion> listBenchmarkVersionsByName(String versionName) {
        BenchmarkVersionExample example = new BenchmarkVersionExample();
        BenchmarkVersionExample.Criteria criteria = example.createCriteria();

        if (StringUtils.isNotBlank(versionName)) {
            criteria.andVersionNameLike("%" + versionName + "%");
        }

        example.setOrderByClause("created_time desc");
        return benchmarkVersionMapper.selectByExample(example);
    }

    @Override
    public BenchmarkVersion getBenchmarkVersionById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return benchmarkVersionMapper.selectByPrimaryKey(id);
    }
}
