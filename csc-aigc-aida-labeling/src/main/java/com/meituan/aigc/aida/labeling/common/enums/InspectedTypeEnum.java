package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

@Getter
public enum InspectedTypeEnum {
    LABELING(1, "标注"),
    QUALITY_INSPECTION(2, "质检");

    private final Integer code;
    private final String value;

    InspectedTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public String toString() {
        return "InspectedTypeEnum{" +
                "code=" + code +
                ", value='" + value + '\'' +
                '}';
    }

    /**
     * 根据编码获取枚举
     * 
     * @param code 编码
     * @return 枚举值
     */
    public static InspectedTypeEnum getByCode(Integer code) {
        for (InspectedTypeEnum inspectedType : values()) {
            if (inspectedType.getCode() == code) {
                return inspectedType;
            }
        }
        return null;
    }
}
