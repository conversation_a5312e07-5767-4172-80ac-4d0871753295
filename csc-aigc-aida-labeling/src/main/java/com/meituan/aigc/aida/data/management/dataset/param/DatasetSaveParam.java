package com.meituan.aigc.aida.data.management.dataset.param;

import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetQueryCondition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 数据集保存参数
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集分析 - 保存数据集参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetSaveParam implements Serializable {

    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long datasetId;

    /**
     * 历史版本ID
     */
    @NotNull(message = "历史版本ID不能为空")
    private Long historyVersionId;

    /**
     * 新版本名称
     */
    @NotEmpty(message = "新版本名称不能为空")
    private String newVersionName;

    /**
     * 需要保存的列列表
     */
    private List<String> columnList;

    /**
     * 筛选条件列表
     */
    private List<DataSetQueryCondition> conditionList;
} 