package com.meituan.aigc.aida.labeling.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * 标注子任务表实体类
 */
@Data
public class LabelingSubTask {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 分组id
     */
    private Long groupId;
    
    /**
     * 子任务名称
     */
    private String name;
    
    /**
     * 标注员ID
     */
    private String labelerMis;
    
    /**
     * 状态(1:创建中 2:标注中 3:待回收 4:已回收 5:失败)
     * {@link com.meituan.aigc.aida.labeling.common.enums.LabelingSubTaskStatus}
     */
    private Integer status;
    
    /**
     * 总数据量
     */
    private Integer totalDataCount;
    
    /**
     * 是否删除: 0-否, 1-是
     */
    private Boolean isDeleted;
    
    /**
     * 租户id
     */
    private String tenantId;
    
    /**
     * 创建人ID
     */
    private String creatorId;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 标注员名称
     */
    private String labelerName;

    /**
     * 分配方式 1:session维度分配 2:query维度分配'
     */
    private Integer assignType;
} 