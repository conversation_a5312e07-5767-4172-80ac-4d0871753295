package com.meituan.aigc.aida.data.management.fetch.mq.consumer;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.aigc.aida.common.squirrel.SquirrelClient;
import com.meituan.aigc.aida.data.management.common.client.ManualDialogInfoClient;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;
import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.PacificAcInterfaceLogRepository;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.SignalTrackingRulesRepository;
import com.meituan.aigc.aida.data.management.fetch.helper.SignalTrackingRulesHelper;
import com.meituan.aigc.aida.data.management.fetch.helper.TypicalQuestionIdQueryHelper;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;
import com.meituan.aigc.aida.labeling.common.constant.RedisConstant;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guowenhui
 * @Create: 2025/4/3 10:40
 * @Version: 1.0 监听白名单mis太平洋AC调用通知
 */
@Slf4j
@Service("tpyWhiteListMisCallAcNotice")
public class WhiteListMisCallAcNoticeListener {

    private static final String LOG_PRE = "[AI搭训练系统监听白名单mis太平洋AC调用通知]";

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    @Resource
    private SignalTrackingRulesRepository signalTrackingRulesRepository;

    @Resource
    private PacificAcInterfaceLogRepository pacificAcInterfaceLogRepository;

    @Resource
    private ManualDialogInfoClient manualDialogInfoClient;

    @Resource
    private TypicalQuestionIdQueryHelper typicalQuestionIdQueryHelper;

    @Resource
    private SquirrelClient squirrelClient;

    @Resource
    private SignalTrackingRulesHelper signalTrackingRulesHelper;

    /**
     * 接收白名单mis太平洋AC调用通知
     *
     * @param msgBody 消息体
     * @return 消费结果
     *         msgBody格式如下：
     *         {
     *         "output":
     *         "{\"dataMap\":{\"userCommentLog\":\"2901532944134259425\",\"safeFoodClaimStatus\":null...}",
     *         "input":
     *         "{\"misId\":2847514,\"operatorMis\":\"zhaozongran\",\"operatorOrgId\":2847514,\"id\":2901532944134259425,\"__global_key__\":\"{\\\"uid\\\":2847514,\\\"mis\\\":\\\"zhaozongran\\\",\\\"appKey\\\":\\\"csc-pacific-outer-service\\\"}\",\"misName\":\"zhaozongran\"}",
     *         "contact_type": "online",
     *         "staff_mis": "xiongliu",
     *         "interface_id": 2712,
     *         "send_time": 1743387446002,
     *         "parse_type": "PACIFIC_CODE_OBJECT_RESULT_PARSER",
     *         "contact_id": "546849335"
     *         }
     */
    @MdpMafkaMsgReceive
    public ConsumeStatus receive(String msgBody) {

        if (isConsumeSwitchOff() || StringUtils.isBlank(msgBody)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            // 1. 解析消息并获取必要参数
            JSONObject jsonObject = JSON.parseObject(msgBody);
            if (!verifyExistPopupAcId(jsonObject)){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            MqMessageParams mqParams = extractMessageParams(jsonObject);
            log.info(LOG_PRE + "收到有效mq消息, msg:{}", JSON.toJSONString(mqParams));

            // 1.1 获取会话ID
            String sessionId = getSessionId(mqParams.getContactId());
            if (StringUtils.isBlank(sessionId)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 1.2 获取客服Mis号
            if (StringUtils.isBlank(mqParams.getStaffMis())) {
                log.warn(LOG_PRE + "staffMis为空");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 1.3 获取标准问IDs
            List<String> typicalQuestionIds = getTypicalQuestionIds(sessionId);

            // 2. 查询并匹配埋点规则
            List<LlmSignalTrackingRules> matchedRules = getMatchedTrackingRules(mqParams.getStaffMis(),
                    typicalQuestionIds, mqParams.getInterfaceId());
            if (CollectionUtils.isEmpty(matchedRules)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 3. 保存AC接口调用日志
            saveAcInterfaceLog(mqParams);
            log.info(LOG_PRE + "消费成功");
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error(LOG_PRE + "发生异常：", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

    /**
     * 校验ac弹屏Id
     * @param jsonObject 对象
     * @return false  无需消费  true 需要消费
     */
    private boolean verifyExistPopupAcId(JSONObject jsonObject) {
        if (!ChannelEnum.ONLINE.getCode().equals(jsonObject.getString(CommonConstants.FieldName.CONTACT_TYPE))) {
            return false;
        }
        if (!NumberUtil.isNumber(jsonObject.getString(CommonConstants.FieldName.CONTACT_ID))){
            return false;
        }
        // 从请求中获取接口ID
        String interfaceId = jsonObject.getString(CommonConstants.FieldName.INTERFACE_ID);
        if (StringUtils.isBlank(interfaceId)) {
            return false;
        }
        // 先从缓存中获取接口ID列表
        String cachedAcIds = squirrelClient.get(RedisConstant.VERIFY_EXIST_POPUP_AC_ID, "");
        // 缓存存在，直接检查是否包含当前接口ID
        if (StringUtils.isNotBlank(cachedAcIds)) {
            Set<String> cachedAcIdSet = JSON.parseObject(cachedAcIds, new TypeReference<Set<String>>() {
            });
            if (CollectionUtils.isNotEmpty(cachedAcIdSet)) {
                return cachedAcIdSet.contains(interfaceId);
            }
        }
        // 缓存不存在，查询数据库并重建缓存
        List<LlmSignalTrackingRules> enabledRules = signalTrackingRulesRepository
                .listEnableByMisIdsAndTypicalQuestionIds(null, null);
        // 无有效规则时直接返回false
        if (CollectionUtils.isEmpty(enabledRules)) {
            return false;
        }
        // 提取所有有效的接口ID并过滤空值
        Set<String> popupAcIds = enabledRules.stream()
                .map(LlmSignalTrackingRules::getAcInterface)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        // 更新缓存
        if (CollectionUtils.isNotEmpty(popupAcIds)) {
            squirrelClient.add(RedisConstant.VERIFY_EXIST_POPUP_AC_ID, "", JSON.toJSONString(popupAcIds));
        }
        // 检查是否包含当前接口ID
        return popupAcIds.contains(interfaceId);
    }

    /**
     * 检查消费开关是否关闭
     */
    private boolean isConsumeSwitchOff() {
        return BooleanUtils.isFalse(dataManagementLionConfig.getAcInvokeSwitch());
    }

    /**
     * 从JSON对象中提取消息参数
     */
    private MqMessageParams extractMessageParams(JSONObject jsonObject) {
        MqMessageParams params = new MqMessageParams();
        params.setInput(jsonObject.getString(CommonConstants.FieldName.INPUT));
        params.setStaffMis(jsonObject.getString(CommonConstants.FieldName.STAFF_MIS));
        params.setSendTime(jsonObject.getString(CommonConstants.FieldName.SEND_TIME));
        params.setContactId(jsonObject.getLong(CommonConstants.FieldName.CONTACT_ID));
        params.setParseType(jsonObject.getString(CommonConstants.FieldName.PARSE_TYPE));
        params.setInterfaceId(jsonObject.getInteger(CommonConstants.FieldName.INTERFACE_ID));
        params.setContactType(jsonObject.getString(CommonConstants.FieldName.CONTACT_TYPE));
        return params;
    }

    /**
     * 获取会话ID
     */
    private String getSessionId(Long contactId) {
        String sessionId = manualDialogInfoClient.getPacificSessionIdByContactId(contactId);
        if (StringUtils.isBlank(sessionId)) {
            log.warn(LOG_PRE + "未查询到sessionId，contactId={}", contactId);
            Cat.newTransaction("aida.training.tpyWhiteListMisCallAcNotice", "未查询到sessionId，contactId=" + contactId);
        }
        return sessionId;
    }

    /**
     * 获取标准问IDs
     */
    private List<String> getTypicalQuestionIds(String sessionId) {
        return typicalQuestionIdQueryHelper.getTypicalQuestionIds(sessionId);
    }

    /**
     * 获取匹配的埋点规则
     */
    private List<LlmSignalTrackingRules> getMatchedTrackingRules(String staffMis, List<String> typicalQuestionIds,
            Integer interfaceId) {
        RobotListQuery query = new RobotListQuery();
        query.setMisList(staffMis);
        if (CollectionUtils.isNotEmpty(typicalQuestionIds)) {
            query.setTypicalQuestionIds(String.join(",", typicalQuestionIds));
        }
        // 获取所有埋点规则
        List<LlmSignalTrackingRules> allRules = signalTrackingRulesHelper.getAllRulesByQuery(query);
        if (CollectionUtils.isEmpty(allRules)) {
            log.info(LOG_PRE + "未配置规则无需消费");
            return new ArrayList<>();
        }

        List<LlmSignalTrackingRules> matchedRules = filterRulesByInterfaceId(allRules, interfaceId);
        if (CollectionUtils.isEmpty(matchedRules)) {
            log.info(LOG_PRE + "未配置太平洋弹屏AC无需消费");
            return new ArrayList<>();
        }

        return matchedRules;
    }

    /**
     * 根据接口ID过滤规则
     */
    private List<LlmSignalTrackingRules> filterRulesByInterfaceId(List<LlmSignalTrackingRules> rules,
            Integer interfaceId) {
        return rules.stream()
                .filter(rule -> Objects.equals(rule.getAcInterface(), String.valueOf(interfaceId)))
                .collect(Collectors.toList());
    }

    /**
     * 保存AC接口调用日志
     */
    private void saveAcInterfaceLog(MqMessageParams params) {
        PacificAcInterfaceLogWithBlobs pacificAcInterfaceLog = buildPacificAcInterfaceLogWithBlobs(
                params.getInput(), params.getStaffMis(), params.getSendTime(), params.getContactId(),
                params.getParseType(), params.getInterfaceId(), params.getContactType());
        pacificAcInterfaceLogRepository.addSelective(pacificAcInterfaceLog);
    }

    /**
     * MQ消息参数类
     */
    @Data
    private static class MqMessageParams {
        @JsonProperty("input")
        private String input;
        @JsonProperty("staff_mis")
        private String staffMis;
        @JsonProperty("send_time")
        private String sendTime;
        @JsonProperty("contact_id")
        private Long contactId;
        @JsonProperty("parse_type")
        private String parseType;
        @JsonProperty("interface_id")
        private Integer interfaceId;
        @JsonProperty("contact_type")
        private String contactType;
    }

    @NotNull
    private static PacificAcInterfaceLogWithBlobs buildPacificAcInterfaceLogWithBlobs(String input, String staffMis,
            String sendTime, Long contactId, String parseType, Integer interfaceId, String contactType) {
        PacificAcInterfaceLogWithBlobs pacificAcInterfaceLog = new PacificAcInterfaceLogWithBlobs();
        pacificAcInterfaceLog.setInput(input);
        pacificAcInterfaceLog.setStaffMis(staffMis);
        pacificAcInterfaceLog.setSendTime(sendTime);
        pacificAcInterfaceLog.setContactId(String.valueOf(contactId));
        pacificAcInterfaceLog.setParseType(parseType);
        pacificAcInterfaceLog.setInterfaceId(interfaceId);
        pacificAcInterfaceLog.setContactType(contactType);
        pacificAcInterfaceLog.setIsDelete(false);
        pacificAcInterfaceLog.setCreateTime(new Date());
        pacificAcInterfaceLog.setUpdateTime(new Date());
        return pacificAcInterfaceLog;
    }
}
