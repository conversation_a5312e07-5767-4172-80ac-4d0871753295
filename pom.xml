<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.meituan.csc.aigc.aida</groupId>
    <artifactId>csc-aigc-aida-training</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>csc-aigc-aida-parent</name>
    <description>训练平台后端系统</description>

    <modules>
        <module>csc-aigc-aida-labeling</module>
        <module>csc-aigc-aida-labeling-api</module>
    </modules>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-parent</artifactId>
        <version>********</version>
    </parent>

    <properties>
        <service.api.version>1.3</service.api.version>
        <aida.labeling.api.version>1.4</aida.labeling.api.version>
        <fastjson.version>1.2.83_noneautotype</fastjson.version>
        <lombok.version>1.18.32</lombok.version>
        <easyexcel.version>3.2.1</easyexcel.version>
        <prompt.common.version>1.0.32</prompt.common.version>
        <aida-workflow-client.version>0.0.12</aida-workflow-client.version>
        <aida-config-client.version>0.0.8</aida-config-client.version>
        <aida-common-dao.version>0.0.13</aida-common-dao.version>
        <prompt.runtime.api.version>0.1.44</prompt.runtime.api.version>
        <csc-eval-service-api.version>1.10</csc-eval-service-api.version>
        <poi.version>4.1.2</poi.version>
        <commons-io.version>2.11.0</commons-io.version>
        <distribute-thrift-client.version>2.1.8_stand_alone</distribute-thrift-client.version>
        <mss-java-sdk-s3.version>1.9.16</mss-java-sdk-s3.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <mafka-client.version>3.9.1</mafka-client.version>
        <talostwo-sdk-java.version>1.8.1-RELEASE</talostwo-sdk-java.version>
        <csc-haitun-case-api.version>0.0.55</csc-haitun-case-api.version>
        <csc-aida-label-client.version>0.0.6-RELEASE</csc-aida-label-client.version>
        <crane-client.version>1.7.0</crane-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.it.sso</groupId>
                <artifactId>sso-java-sdk</artifactId>
                <version>2.5.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>mss-java-sdk-s3</artifactId>
                <version>1.9.16</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.csc.aigc.aida</groupId>
                <artifactId>csc-aigc-aida-labeling-api</artifactId>
                <version>${aida.labeling.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.csccratos</groupId>
                <artifactId>aida-common-utils</artifactId>
                <version>0.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mdp.boot</groupId>
                <artifactId>mdp-boot-starter-squirrel</artifactId>
                <version>********</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mdp.boot</groupId>
                <artifactId>mdp-boot-starter-zebra</artifactId>
                <version>********</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis-spring</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>rhino-client</artifactId>
                        <groupId>com.dianping.rhino</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bcpkix-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.lion</groupId>
                        <artifactId>lion-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- Mybatis -->
            <dependency>
                <artifactId>mybatis</artifactId>
                <groupId>org.mybatis</groupId>
                <version>3.5.16</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>2.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.meituan.mdp.boot</groupId>
                <artifactId>mdp-boot-starter-crane</artifactId>
            </dependency>
            <dependency>
                <groupId>com.meituan.mdp.boot</groupId>
                <artifactId>mdp-boot-starter-thrift</artifactId>
                <version>********</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.csc.aigc</groupId>
                <artifactId>runtime-service-api</artifactId>
                <version>${prompt.runtime.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.csc.aigc.eval</groupId>
                <artifactId>csc-eval-service-api</artifactId>
                <version>${csc-eval-service-api.version}</version>
            </dependency>
            <!-- lion依赖 -->
            <dependency>
                <groupId>com.dianping.lion</groupId>
                <artifactId>lion-client</artifactId>
                <version>0.13.0</version>
            </dependency>
            <!-- 添加 Apache Commons IO 依赖 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <!-- Excel Processing -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!--文枢-->
            <dependency>
                <groupId>com.meituan.sec</groupId>
                <artifactId>distribute-thrift-client</artifactId>
                <version>${distribute-thrift-client.version}</version>
            </dependency>
            <!--S3-->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>mss-java-sdk-s3</artifactId>
                <version>${mss-java-sdk-s3.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--mapstruct-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <!--mafka-->
            <dependency>
                <groupId>com.meituan.mafka</groupId>
                <artifactId>mafka-client_2.10</artifactId>
                <version>${mafka-client.version}</version>
            </dependency>
            <!--Hive-->
            <dependency>
                <groupId>com.meituan.talostwo</groupId>
                <artifactId>talostwo-sdk-java</artifactId>
                <version>${talostwo-sdk-java.version}</version>
            </dependency>
            <!--星脉-->
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>csc-aircraft-sdk</artifactId>
                <version>0.3.10</version>
                <exclusions>
                    <exclusion>
                        <artifactId>validation-api</artifactId>
                        <groupId>javax.validation</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--海豚客服接口-->
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>csc-haitun-kefu-api</artifactId>
                <version>1.6.41</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--太平洋contact接口-->
            <dependency>
                <groupId>com.sankuai.pacific</groupId>
                <artifactId>csc-pacific-contact-chat-api</artifactId>
                <version>0.0.47</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--太平洋新工作台(管家)-->
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>csc-workbench-bff-api</artifactId>
                <version>1.0.37</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mdp-boot-starter-log</artifactId>
                        <groupId>com.meituan.mdp.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-slf4j-impl</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>csc-pacific-efficiency-api</artifactId>
                <version>0.0.38</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>csc-workbench-bff-common</artifactId>
                <version>1.0.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mdp</groupId>
                <artifactId>csc-pacific-context-api</artifactId>
                <version>1.0.11</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.csc</groupId>
                <artifactId>csc-pacific-common-tools</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
                <version>1.0.9</version>
            </dependency>
            <!--海豚caseApi-->
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>csc-haitun-case-api</artifactId>
                <version>${csc-haitun-case-api.version}</version>
            </dependency>
            <!-- 智能侧分析API -->
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>csc-analtics-ctx-api</artifactId>
                <version>0.0.76</version>
            </dependency>
            <!--业务场景相关接口-->
            <dependency>
                <groupId>com.sankuai.csccratos</groupId>
                <artifactId>csc-aida-label-client</artifactId>
                <version>${csc-aida-label-client.version}</version>
            </dependency>
            <!--定时任务-->
            <dependency>
                <groupId>com.cip.crane</groupId>
                <artifactId>crane-client</artifactId>
                <version>${crane-client.version}</version>
            </dependency>
            <!--木星镜像录音-->
            <dependency>
                <groupId>com.sankuai.vcfaqr</groupId>
                <artifactId>voice-well-dto</artifactId>
                <version>1.0.7</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>