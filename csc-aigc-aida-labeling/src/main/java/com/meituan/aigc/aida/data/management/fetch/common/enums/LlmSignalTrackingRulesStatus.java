package com.meituan.aigc.aida.data.management.fetch.common.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON>
 * @Create: 2025/4/11 16:05
 * @Version: 1.0
 */
@Getter
public enum LlmSignalTrackingRulesStatus {

    DISABLED(1, "禁用"),
    ENABLE(2, "启用"),
    ;

    private final int code;
    private final String value;

    LlmSignalTrackingRulesStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LlmSignalTrackingRulesStatus getByCode(int code) {
        for (LlmSignalTrackingRulesStatus status : LlmSignalTrackingRulesStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
