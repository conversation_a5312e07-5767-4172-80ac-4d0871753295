package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskSessionData;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskSessionDataExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LabelingTaskSessionDataMapper {
    long countByExample(LabelingTaskSessionDataExample example);

    int deleteByExample(LabelingTaskSessionDataExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LabelingTaskSessionData record);

    int insertSelective(LabelingTaskSessionData record);

    List<LabelingTaskSessionData> selectByExampleWithBLOBs(LabelingTaskSessionDataExample example);

    List<LabelingTaskSessionData> selectByExample(LabelingTaskSessionDataExample example);

    LabelingTaskSessionData selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LabelingTaskSessionData record, @Param("example") LabelingTaskSessionDataExample example);

    int updateByExampleWithBLOBs(@Param("record") LabelingTaskSessionData record, @Param("example") LabelingTaskSessionDataExample example);

    int updateByExample(@Param("record") LabelingTaskSessionData record, @Param("example") LabelingTaskSessionDataExample example);

    int updateByPrimaryKeySelective(LabelingTaskSessionData record);

    int updateByPrimaryKeyWithBLOBs(LabelingTaskSessionData record);

    int updateByPrimaryKey(LabelingTaskSessionData record);
}