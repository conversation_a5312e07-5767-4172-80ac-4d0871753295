package com.meituan.aigc.aida.data.management.fetch.param;

import com.meituan.csc.aigc.runtime.dto.aida.MockRunParamDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AidaRobotInfoParam implements Serializable {

    /**
     * 机器人请求入参extra_info
     */
    private String extraInfo;

    /**
     * 工具节点输出信息
     */
    private List<MockRunParamDTO> toolOutput;

    /**
     * 加工信号类型
     */
    private String signalType;
}
