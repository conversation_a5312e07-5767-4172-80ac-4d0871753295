package com.meituan.aigc.aida.labeling.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.meituan.aigc.aida.labeling.common.constant.LionConstants;
import com.meituan.aigc.aida.labeling.common.enums.ConditionFieldEnum;
import com.meituan.aigc.aida.labeling.common.enums.ConditionFieldTypeEnum;
import com.meituan.aigc.aida.labeling.common.enums.ConditionOperateEnum;
import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskSessionData;
import com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckItemPO;
import com.meituan.aigc.aida.labeling.param.AdvancedFilter;
import com.meituan.aigc.aida.labeling.param.Context;
import com.meituan.aigc.aida.labeling.param.RawDataExtraInfo;
import com.meituan.aigc.aida.labeling.pojo.dto.DetailDataItemDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.quality.check.TaskConditionDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 检索条件辅助类
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
public class LabelingTaskConditionHelper {

    /**
     * 获取大模型数据的检索条件
     *
     * @param dataType 数据类型
     * @return 检索条件列表
     */
    public static List<TaskConditionDTO> getLlmCondition(int dataType) {
        List<TaskConditionDTO> conditions = new ArrayList<>();
        // 线上会话标注支持大模型上下文检索
        if (dataType == LabelTaskDataType.SESSION_ANNOTATION.getCode() || dataType == LabelTaskDataType.QUERY_ANNOTATION.getCode()) {
            conditions.add(new TaskConditionDTO(ConditionFieldEnum.LLM_CONTEXT.getDescription(), ConditionFieldTypeEnum.LLM_FIELD.getCode()));
        }
        return conditions;
    }

    /**
     * 获取映射数据的检索条件
     *
     * @param dataType 数据类型
     */
    public static List<TaskConditionDTO> getMappedCondition(int dataType) {
        List<TaskConditionDTO> conditions = new ArrayList<>();
        if (dataType == LabelTaskDataType.SFT_FINE_TUNING.getCode()) {
            conditions.add(new TaskConditionDTO(ConditionFieldEnum.PROMPT.getDescription(), ConditionFieldTypeEnum.MAPPED_FIELD.getCode()));
            conditions.add(new TaskConditionDTO(ConditionFieldEnum.OUTPUT.getDescription(), ConditionFieldTypeEnum.MAPPED_FIELD.getCode()));
        } else if (dataType == LabelTaskDataType.DPO_ALIGNMENT.getCode()) {
            conditions.add(new TaskConditionDTO(ConditionFieldEnum.PROMPT.getDescription(), ConditionFieldTypeEnum.MAPPED_FIELD.getCode()));
            conditions.add(new TaskConditionDTO(ConditionFieldEnum.REJECTED.getDescription(), ConditionFieldTypeEnum.MAPPED_FIELD.getCode()));
            conditions.add(new TaskConditionDTO(ConditionFieldEnum.CHOSEN.getDescription(), ConditionFieldTypeEnum.MAPPED_FIELD.getCode()));
        }
        return conditions;
    }

    /**
     * 获取信号数据的检索条件
     *
     * @param signalList 信号列表
     * @return 检索条件列表
     */
    public static List<TaskConditionDTO> getSignalCondition(int dataType, List<String> signalList) {
        List<TaskConditionDTO> conditions = new ArrayList<>();
        if (dataType == LabelTaskDataType.QUERY_ANNOTATION.getCode()) {
            conditions.add(new TaskConditionDTO(ConditionFieldEnum.SIGNAL.getDescription(), ConditionFieldTypeEnum.SESSION_SIGNAL_FIELD.getCode()));
        } else if (CollectionUtils.isNotEmpty(signalList)) {
            signalList.forEach(signal -> {
                conditions.add(new TaskConditionDTO(signal, ConditionFieldTypeEnum.TRAINING_SIGNAL_FIELD.getCode()));
            });
        }
        return conditions;
    }

    /**
     * 获取标注项的检索条件
     *
     * @param labelItemList 标注项列表
     * @return 检索条件列表
     */
    public static List<TaskConditionDTO> getLabelItemCondition(List<String> labelItemList) {
        List<TaskConditionDTO> conditions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(labelItemList)) {
            labelItemList.forEach(labelItem -> {
                conditions.add(new TaskConditionDTO(labelItem, ConditionFieldTypeEnum.LABELING_ITEM_FIELD.getCode()));
            });
        }
        return conditions;
    }

    /**
     * 判断是否有大模型上下文的条件
     *
     * @param advancedFilters 高级筛选条件
     * @return 是否有大模型上下文的条件
     */
    public static boolean hasModelContextCondition(List<AdvancedFilter> advancedFilters) {
        if (CollectionUtils.isEmpty(advancedFilters)) {
            return false;
        }
        return advancedFilters.stream().flatMap(advancedFilter -> advancedFilter.getFieldList().stream()).anyMatch(field -> field.getFieldType() == ConditionFieldTypeEnum.LLM_FIELD.getCode());
    }

    /**
     * 过滤条件
     *
     * @param dataItems       标注项列表
     * @param advancedFilters 高级筛选条件
     * @return 过滤后的质检项列表
     */
    public static List<DetailDataItemDTO> filterLabelingCondition(List<DetailDataItemDTO> dataItems, Map<String, List<LabelingTaskSessionData>> sessionDataMap, List<AdvancedFilter> advancedFilters) {
        return dataItems.stream().filter(dataItem -> {
            if (CollectionUtils.isEmpty(advancedFilters)) {
                return true;
            }
            AtomicBoolean isFilter = new AtomicBoolean(false);
            advancedFilters.forEach(advancedFilter -> {
                if (CollectionUtils.isNotEmpty(advancedFilter.getFieldList())) {
                    boolean isAllFilter = true;
                    for (AdvancedFilter.Field field : advancedFilter.getFieldList()) {
                        boolean filterResult = doFilter(dataItem.getSessionId(), sessionDataMap, dataItem.getRawDataMappedContent(), dataItem.getRawDataContent(), dataItem.getExtraInfo(), dataItem.getLabelingItemsResult(), advancedFilter, field);
                        if (!filterResult) {
                            isAllFilter = false;
                        }
                    }
                    if (isAllFilter) {
                        isFilter.set(true);
                    }
                }
            });
            return !isFilter.get();
        }).collect(Collectors.toList());
    }

    /**
     * 过滤条件
     *
     * @param qualityCheckItemList 质检项列表
     * @param advancedFilters      高级筛选条件
     * @return 过滤后的质检项列表
     */
    public static List<LabelingQualityCheckItemPO> filterQualityCheckCondition(List<LabelingQualityCheckItemPO> qualityCheckItemList, Map<String, List<LabelingTaskSessionData>> sessionDataMap, List<AdvancedFilter> advancedFilters) {
        return qualityCheckItemList.stream().filter(labelingQualityCheckItem -> {
            if (CollectionUtils.isEmpty(advancedFilters)) {
                return true;
            }
            AtomicBoolean isFilter = new AtomicBoolean(false);
            advancedFilters.forEach(advancedFilter -> {
                if (CollectionUtils.isNotEmpty(advancedFilter.getFieldList())) {
                    boolean isAllFilter = true;
                    for (AdvancedFilter.Field field : advancedFilter.getFieldList()) {
                        boolean filterResult = doFilter(labelingQualityCheckItem.getSessionId(), sessionDataMap, labelingQualityCheckItem.getRawDataMappedContent(), labelingQualityCheckItem.getRawDataContent(), labelingQualityCheckItem.getExtraInfo(), null, advancedFilter, field);
                        if (!filterResult) {
                            isAllFilter = false;
                        }
                    }
                    if (isAllFilter) {
                        isFilter.set(true);
                    }
                }
            });
            return !isFilter.get();
        }).collect(Collectors.toList());
    }

    /**
     * 执行过滤
     *
     * @param advancedFilter 高级筛选条件
     * @return 是否过滤
     */
    private static boolean doFilter(String sessionId, Map<String, List<LabelingTaskSessionData>> sessionDataMap, String rawDataMappedContent, String rawDataContent, String extraInfoStr, String labelingResult, AdvancedFilter advancedFilter, AdvancedFilter.Field field) {
        boolean isContain = false;
        if (field.getFieldType() == ConditionFieldTypeEnum.MAPPED_FIELD.getCode()) {
            if (StringUtils.isNotBlank(rawDataMappedContent)) {
                Map<String, String> contentMap = JSON.parseObject(rawDataMappedContent, new TypeReference<Map<String, String>>() {
                });
                if (contentMap.containsKey(field.getField()) && contentMap.get(field.getField()).contains(advancedFilter.getValue())) {
                    isContain = true;
                }
            }
        } else if (field.getFieldType() == ConditionFieldTypeEnum.TRAINING_SIGNAL_FIELD.getCode()) {
            if (StringUtils.isNotBlank(rawDataContent)) {
                Map<String, String> contentMap = JSON.parseObject(rawDataContent, new TypeReference<Map<String, String>>() {
                });
                if (contentMap.containsKey(field.getField()) && contentMap.get(field.getField()).contains(advancedFilter.getValue())) {
                    isContain = true;
                }
            }
        } else if (field.getFieldType() == ConditionFieldTypeEnum.SESSION_SIGNAL_FIELD.getCode()) {
            if (StringUtils.isNotBlank(extraInfoStr)) {
                RawDataExtraInfo extraInfo = JSON.parseObject(extraInfoStr, RawDataExtraInfo.class);
                if (extraInfo != null && CollectionUtils.isNotEmpty(extraInfo.getSignalList())) {
                    // 如果输入的查询值有冒号，则认为指定信号的key查询，否则认为是分别在key和value中查询
                    if (advancedFilter.getValue().contains(":") || advancedFilter.getValue().contains("：")) {
                        // 为了防止本身value中有冒号，指定key查不到数据后，尝试在key和value中分别查询
                        int index = advancedFilter.getValue().indexOf(":");
                        if (index == -1) {
                            index = advancedFilter.getValue().indexOf("：");
                        }
                        String signalKey = advancedFilter.getValue().substring(0, index).trim();
                        String signalValue = advancedFilter.getValue().substring(index + 1).trim();
                        if (StringUtils.isNotBlank(signalKey)) {
                            long hitCount = extraInfo.getSignalList().stream().filter(signal -> StringUtils.isNotBlank(signal.getSignalName()) && signal.getSignalName().equals(signalKey) && StringUtils.isNotBlank(signal.getSignalValue()) && signal.getSignalValue().contains(signalValue)).count();
                            if (hitCount != 0) {
                                isContain = true;
                            }
                        }
                    }
                    if (!isContain) {
                        // 只要有一个信号命中，就算包含
                        long hitCount = extraInfo.getSignalList().stream().filter(signal -> (StringUtils.isNotBlank(signal.getSignalName()) && signal.getSignalName().contains(advancedFilter.getValue())) || (StringUtils.isNotBlank(signal.getSignalValue()) && signal.getSignalValue().contains(advancedFilter.getValue()))).count();
                        if (hitCount != 0) {
                            isContain = true;
                        }
                    }
                }
            }
        } else if (field.getFieldType() == ConditionFieldTypeEnum.LLM_FIELD.getCode()) {
            if (sessionDataMap.containsKey(sessionId)) {
                List<LabelingTaskSessionData> sessionDataList = sessionDataMap.get(sessionId);
                if (CollectionUtils.isNotEmpty(sessionDataList)) {
                    // 只要有一个会话数据命中，就算包含
                    LabelingTaskSessionData sessionData = sessionDataList.get(0);
                    if (StringUtils.isNotBlank(sessionData.getContext())) {
                        List<Context> contextList = JSON.parseArray(sessionData.getContext(), Context.class);
                        for (Context context : contextList) {
                            if (isFilterContext(context)) {
                                continue;
                            }
                            if (context.getMessage().contains(advancedFilter.getValue())) {
                                isContain = true;
                                break;
                            }
                        }
                    }
                }
            }
        } else if (field.getFieldType() == ConditionFieldTypeEnum.LABELING_ITEM_FIELD.getCode()) {
            if (StringUtils.isNotBlank(labelingResult)) {
                List<List<LabelResult>> labelingResultList = JSON.parseObject(labelingResult, new TypeReference<List<List<LabelResult>>>() {
                });
                if (CollectionUtils.isNotEmpty(labelingResultList)) {
                    // 对应标注项命中，就算包含
                    long hitCount = labelingResultList.stream().flatMap(Collection::stream).filter(labelResult -> labelResult.getName().equals(field.getField()) && StringUtils.isNotBlank(labelResult.getValue()) && labelResult.getValue().contains(advancedFilter.getValue())).count();
                    if (hitCount != 0) {
                        isContain = true;
                    }
                }
            }
        }
        return ConditionOperateEnum.CONTAIN.getName().equals(advancedFilter.getOperator()) == !isContain;
    }

    /**
     * 过滤上下文
     *
     * @param context 上下文
     * @return 是否过滤
     */
    private static boolean isFilterContext(Context context) {
        if ("无法解析的消息类型".equals(context.getMessage())) {
            return true;
        }
        List<String> needMessageType = Lion.getList(ConfigUtil.getAppkey(), LionConstants.NEED_MESSAGE_TYPE, String.class);
        if (CollectionUtils.isEmpty(needMessageType)) {
            return false;
        }
        return !needMessageType.contains(context.getMessageType());
    }

}
