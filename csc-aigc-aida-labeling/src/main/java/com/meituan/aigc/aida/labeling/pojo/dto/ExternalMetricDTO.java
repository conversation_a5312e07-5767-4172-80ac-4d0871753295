package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-21 16:06
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExternalMetricDTO {
    /**
     * 指标id
     */
    private Long id;
    /**
     * 指标名称
     */
    private String metricName;
    /**
     * 标注类型
     */
    private String annotationType;
    /**
     * 是否树形标注
     */
    private Boolean isTreeAnnotation;
    /**
     * 枚举值列表
     */
    private List<String> enumValueList;
}
