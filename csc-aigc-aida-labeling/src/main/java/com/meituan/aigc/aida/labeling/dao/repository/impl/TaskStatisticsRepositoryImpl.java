package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.meituan.aigc.aida.labeling.dao.mapper.TaskStatisticsMapper;
import com.meituan.aigc.aida.labeling.dao.model.TaskStatistics;
import com.meituan.aigc.aida.labeling.dao.repository.TaskStatisticsRepository;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DashboardTaskListQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DashboardTaskItemVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.TaskStatisticalVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 14:54
 * @Version: 1.0
 */
@Repository
public class TaskStatisticsRepositoryImpl implements TaskStatisticsRepository {

    @Resource
    private TaskStatisticsMapper taskStatisticsMapper;


    @Override
    public List<DashboardTaskItemVO> listTasks(DashboardTaskListQuery query) {
        return taskStatisticsMapper.listTasks(query);
    }

    @Override
    public TaskStatisticalVO statisticalTaskData(DataStatisticalQuery query) {
        return taskStatisticsMapper.statisticalTaskData(query);
    }

    @Override
    public void batchInsert(List<TaskStatistics> taskStatisticsList) {
        if (CollectionUtils.isEmpty(taskStatisticsList)) {
            return;
        }
        taskStatisticsMapper.batchInsert(taskStatisticsList);
    }
}
