<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.InspectionItemTemplateMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="template_desc" jdbcType="VARCHAR" property="templateDesc" />
    <result column="template_type" jdbcType="TINYINT" property="templateType" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="create_mis" jdbcType="VARCHAR" property="createMis" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_mis" jdbcType="VARCHAR" property="updateMis" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_name, template_desc, template_type, is_deleted, tenant_id, create_mis, 
    create_time, update_mis, update_time
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from inspection_item_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from inspection_item_template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from inspection_item_template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplateExample">
    delete from inspection_item_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate">
    insert into inspection_item_template (id, template_name, template_desc, 
      template_type, is_deleted, tenant_id, 
      create_mis, create_time, update_mis, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{templateName,jdbcType=VARCHAR}, #{templateDesc,jdbcType=VARCHAR}, 
      #{templateType,jdbcType=TINYINT}, #{isDeleted,jdbcType=BIT}, #{tenantId,jdbcType=VARCHAR}, 
      #{createMis,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateMis,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate">
    insert into inspection_item_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="templateDesc != null">
        template_desc,
      </if>
      <if test="templateType != null">
        template_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="createMis != null">
        create_mis,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateMis != null">
        update_mis,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateDesc != null">
        #{templateDesc,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null">
        #{templateType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="createMis != null">
        #{createMis,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateMis != null">
        #{updateMis,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplateExample" resultType="java.lang.Long">
    select count(*) from inspection_item_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update inspection_item_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.templateDesc != null">
        template_desc = #{record.templateDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.templateType != null">
        template_type = #{record.templateType,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.createMis != null">
        create_mis = #{record.createMis,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateMis != null">
        update_mis = #{record.updateMis,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update inspection_item_template
    set id = #{record.id,jdbcType=BIGINT},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      template_desc = #{record.templateDesc,jdbcType=VARCHAR},
      template_type = #{record.templateType,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      create_mis = #{record.createMis,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_mis = #{record.updateMis,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate">
    update inspection_item_template
    <set>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateDesc != null">
        template_desc = #{templateDesc,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null">
        template_type = #{templateType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="createMis != null">
        create_mis = #{createMis,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateMis != null">
        update_mis = #{updateMis,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate">
    update inspection_item_template
    set template_name = #{templateName,jdbcType=VARCHAR},
      template_desc = #{templateDesc,jdbcType=VARCHAR},
      template_type = #{templateType,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      create_mis = #{createMis,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_mis = #{updateMis,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listByTemplateType" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from inspection_item_template
    where is_deleted = 0 and template_type = 1
  </select>

  <insert id="insertInspectionItem" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO inspection_item_template (
    template_name,
    template_desc,
    template_type,
    is_deleted,
    create_mis,
    update_mis
    ) VALUES (
    #{templateName},
    #{templateDesc},
    #{templateType},
    #{isDeleted},
    #{createMis},
    #{updateMis}
    )
  </insert>

</mapper>