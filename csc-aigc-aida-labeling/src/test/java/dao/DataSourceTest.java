package dao;

import com.meituan.aigc.aida.labeling.Application;
import com.meituan.aigc.aida.labeling.dao.mapper.*;
import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.param.LabelingDetailBySessionQueryParam;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingDetailBySessionVO;
import com.meituan.aigc.aida.labeling.service.LabelingSubTaskService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class DataSourceTest {

    @Autowired
    private InspectionItemMapper inspectionItemMapper;

    @Autowired
    private LabelingTaskMapper labelingTaskMapper;

    @Autowired
    private LabelingTaskGroupMapper labelingTaskGroupMapper;

    @Autowired
    private LabelingDetailMapper labelingDetailMapper;

    @Autowired
    private LabelingSubTaskMapper labelingSubTaskMapper;

    @Autowired
    private LabelingSubTaskService labelingSubTaskService;

    @Test
    public void testPageSession() {
        LabelingDetailBySessionQueryParam param = new LabelingDetailBySessionQueryParam();
        param.setSubTaskId(31L);
        param.setPageNum(1);
        param.setPageSize(10);
        PageData<LabelingDetailBySessionVO> result = labelingSubTaskService.pageBySession(param);
        System.out.println(result);
    }


    @Test
    public void testDataSource() {

//        InspectionItem item = new InspectionItem();
//        item.setName("test");
//        item.setDataType(1);
//        item.setItemType(1);
//        item.setRankNum(1);
//        item.setCreateMis("yanxiao07");
//        inspectionItemMapper.insertSelective(item);

//        inspectionItemMapper.deleteByPrimaryKey(2L);
    }

    @Test
    public void insertTest() {
    }
}
