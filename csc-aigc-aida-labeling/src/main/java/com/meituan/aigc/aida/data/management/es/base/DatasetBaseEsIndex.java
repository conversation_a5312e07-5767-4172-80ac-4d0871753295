package com.meituan.aigc.aida.data.management.es.base;

import com.meituan.aigc.aida.es.EsEntityBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * Aida训练平台-数据集管理-基础字段
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetBaseEsIndex extends EsEntityBase implements Serializable {

    /**
     * 数据集ID
     * ES映射类型: keyword
     * ignore_above: 64
     * doc_values: true
     * 用于唯一标识数据集
     */
    private String datasetId;

    /**
     * 数据集版本ID
     * ES映射类型: keyword
     * index: true
     * ignore_above: 64
     * 用于追踪数据集的不同版本
     */
    private String versionId;

    /**
     * 会话ID
     * ES映射类型: keyword
     * ignore_above: 64
     * 用于关联同一会话中的数据集操作
     */
    private String sessionId;

    /**
     * 数据来源
     * ES映射类型: keyword
     * ignore_above: 64
     * 用于标识着数据的来源
     */
    private String dataSource;

    /**
     * 字段映射
     * ES映射类型: keyword
     * 用于存储字段的映射关系配置
     */
    private String fieldMapping;

    /**
     * 创建时间
     * ES映射类型: date
     * 格式: strict_date_optional_time||epoch_millis
     * 记录数据创建的时间戳
     */
    private Date createTime;

    /**
     * 更新时间
     * ES映射类型: date
     * 格式: strict_date_optional_time||epoch_millis
     * 记录数据最后更新的时间戳
     */
    private Date updateTime;

    /**
     * 通用数据
     */
    private Map<String, Object> commonData;
}
