package com.meituan.aigc.aida.labeling.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3KmsClient;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.exceptions.GetS3CredentialFailedAfterRetryException;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MssConfiguration {

    @Autowired
    private LionConfig lionConfig;


    @Bean
    public AmazonS3 initAmazonS3Con() throws GetS3CredentialFailedAfterRetryException {
        String appKey = MdpContextUtils.getAppKey();
        return createAmazonS3Conn(appKey, lionConfig.getEvalServiceS3Endpoint());
    }

    //hostname:MSS的endpoint服务地址
    public AmazonS3 createAmazonS3Conn(String appkey, String hostname) throws GetS3CredentialFailedAfterRetryException {
        ClientConfiguration configuration = new ClientConfiguration();
        // 默认协议为HTTPS。将这个值设置为Protocol.HTTP，则使用的是HTTP协议
        configuration.setProtocol(Protocol.HTTPS);

        //设置客户端生成的http请求hos格式，目前只支持path type的格式，不支持bucket域名的格式
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        // 目前s3只支持path style,下面选项需要设置为true
        s3ClientOptions.setPathStyleAccess(true);

        //生成云存储api client
        return new AmazonS3KmsClient(hostname, appkey, configuration, s3ClientOptions);
    }
}
