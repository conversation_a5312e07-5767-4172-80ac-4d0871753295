package com.meituan.aigc.aida.data.management.es.query;

import lombok.*;
import org.elasticsearch.search.sort.SortOrder;

import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * 数据集查询条件
 *
 * <AUTHOR>
 * @date 2025/5/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DatasetEsQueryCondition {

    // ========== 分页参数 ==========
    /**
     * 分页参数 - 页码（从0开始）
     */
    @Builder.Default
    private Integer pageNum = 0;

    /**
     * 分页参数 - 每页大小
     */
    @Builder.Default
    private Integer pageSize = 20;

    /**
     * 滚动时间
     */
    private String scrollTime;

    /**
     * 滚动Id
     */
    private String scrollId;

    /**
     * 数据来源
     * 依据数据源查询不同的索引-必须指定
     */
    @NotNull
    private String dataSource;

    /**
     * 查询索引名称
     */
    private String indexName;

    /**
     * 查询保留字段路径
     */
    private List<String> fieldPath;

    /**
     * 精确匹配查询
     * 适用keyword字段 or flattened类型叶子字段
     * text字段查询有误差
     * 不支持通配符
     * key: 字段名, value: 查询值
     */
    @Builder.Default
    private Map<EsSearchProperty, String> termCondition = new HashMap<>();

    /**
     * 多值查询,查询包含数据(List)
     * 仅适用keyword字段 or flattened类型叶子字段
     * text字段查询有误差
     * 不支持通配符
     * key: 字段名, value: 包含列表
     */
    @Builder.Default
    private Map<EsSearchProperty, List<String>> termsCondition = new HashMap<>();

    /**
     * 全文搜索,对查询文本进行分词后匹配
     * 适用text字段
     * 将查询进行分词后,匹配text的分词字段
     * key: 字段名, value: 查询值
     */
    @Builder.Default
    private Map<EsSearchProperty, String> matchCondition = new HashMap<>();

    /**
     * 短语匹配查询
     * 要求词序一致的分词短语匹配
     * 适用text字段
     * key: 字段名, value: 短语查询值
     */
    @Builder.Default
    private Map<EsSearchProperty, String> matchPhraseCondition = new HashMap<>();

    /**
     * 通配符查询，查找符合通配符模式的精确匹配
     * 支持 * 和 ? 通配符
     * 适用keyword字段、text字段、flattened类型叶子字段
     * 不建议使用，查询效率低，容易OOM
     * key: 字段名, value: 通配符查询值
     */
    @Builder.Default
    private Map<EsSearchProperty, String> wildcardCondition = new HashMap<>();

    /**
     * 前缀查询
     * 查找以指定前缀开头的文档
     * 适用keyword字段、flattened类型叶子字段
     * text字段查询有误差
     * key: 字段名, value: 前缀值
     */
    @Builder.Default
    private Map<EsSearchProperty, String> prefixCondition = new HashMap<>();

    /**
     * 正则表达式查询
     * 使用正则表达式匹配
     * 适用keyword字段
     * key: 字段名, value: 正则表达式
     */
    @Builder.Default
    private Map<EsSearchProperty, String> regexpCondition = new HashMap<>();

    /**
     * 范围查询
     * 数值或日期范围查询
     * key: 字段名, value: 范围条件
     */
    @Builder.Default
    private Map<EsSearchProperty, RangeCondition> rangeCondition = new HashMap<>();

    /**
     * 存在性查询
     * 检查字段是否存在
     * key: 字段名, value: true表示必须存在，false表示必须不存在
     */
    @Builder.Default
    private Map<EsSearchProperty, Boolean> existsCondition = new HashMap<>();

    /**
     * 模糊查询
     * 支持编辑距离的模糊匹配
     * key: 字段名, value: 模糊查询配置
     */
    @Builder.Default
    private Map<EsSearchProperty, FuzzyCondition> fuzzyCondition = new HashMap<>();

    /**
     * 聚合查询条件
     * key: 聚合名称(EsSearchProperty), value: 聚合配置
     */
    @Builder.Default
    private Map<EsSearchProperty, AggregationCondition> termsAggConditions = new HashMap<>();

    /**
     * 是否只返回聚合结果（不返回文档内容）
     */
    @Builder.Default
    private Boolean aggregationOnly = false;

    /**
     * 查询字符串
     * 支持复杂的查询语法，如布尔操作符、通配符等
     */
    private String queryString;

    @Builder.Default
    private Map<EsSearchProperty, SortOrder> sortCondition = new HashMap<>();

    /**
     * 排序字段
     */
    @Builder.Default
    private String sortField = "createTime";

    /**
     * 排序方向
     */
    @Builder.Default
    private SortOrder sortOrder = SortOrder.DESC;

    /**
     * 是否包含高亮
     */
    @Builder.Default
    private Boolean includeHighlight = false;

    /**
     * 高亮字段列表
     */
    private List<String> highlightFields = new ArrayList<>();

    /**
     * 日期范围查询条件
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DateRangeCondition {
        /**
         * 开始日期
         */
        private Date startDate;

        /**
         * 结束日期
         */
        private Date endDate;
    }

    /**
     * 范围查询条件
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RangeCondition {
        /**
         * 起始值（不包含）
         */
        private Object from;

        /**
         * 结束值（不包含）
         */
        private Object to;

        /**
         * 是否包含下限
         */
        private boolean includeLower = false;

        /**
         * 是否包含上限
         */
        private boolean includeUpper = false;

        // 便捷构造方法
        public static RangeCondition between(Object from, Object to) {
            return RangeCondition.builder().from(from).to(to).build();
        }

        public static RangeCondition greaterThan(Object value) {
            return RangeCondition.builder().from(value).build();
        }

        public static RangeCondition greaterThanOrEqual(Object value) {
            return RangeCondition.builder().from(value).includeLower(true).build();
        }

        public static RangeCondition lessThan(Object value) {
            return RangeCondition.builder().to(value).build();
        }

        public static RangeCondition lessThanOrEqual(Object value) {
            return RangeCondition.builder().to(value).includeUpper(true).build();
        }
    }

    /**
     * 模糊查询条件
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FuzzyCondition {
        /**
         * 查询值
         */
        private String value;

        /**
         * 编辑距离（默认AUTO）
         */
        @Builder.Default
        private String fuzziness = "AUTO";

        /**
         * 前缀长度（不参与模糊匹配的前缀字符数）
         */
        @Builder.Default
        private Integer prefixLength = 0;

        /**
         * 最大扩展数（模糊匹配的最大项数）
         */
        @Builder.Default
        private Integer maxExpansions = 50;

        /**
         * 是否转置（允许相邻字符转置）
         */
        @Builder.Default
        private Boolean transpositions = true;
    }

    /**
     * 验证分页参数
     * 最大为1000每页
     */
    public void validatePagination() {
        if (pageNum == null || pageNum < 0) {
            pageNum = 0;
        }
        if (pageSize == null || pageSize <= 0 || pageSize > 1000) {
            pageSize = 20;
        }
    }

    /**
     * 获取ES查询的from参数
     */
    public int getFrom() {
        validatePagination();
        return pageNum * pageSize;
    }

    /**
     * 获取ES查询的size参数
     */
    public int getSize() {
        validatePagination();
        return pageSize;
    }

    // ========== 便捷构建方法 ==========

    /**
     * 添加精确匹配条件
     */
    public void addTermCondition(EsSearchProperty field, String value) {
        if (this.termCondition == null) {
            this.termCondition = new HashMap<>();
        }
        this.termCondition.put(field, value);
    }

    /**
     * 添加精确匹配条件（字符串字段名）
     */
    public void addTermCondition(String fieldName, String path, String value) {
        addTermCondition(EsSearchProperty.of(fieldName, path), value);
    }

    /**
     * 添加Nested嵌套查询匹配条件
     */
    public void addTermConditionNested(String fieldName, String nestedPath, String path, String value) {
        addTermCondition(EsSearchProperty.nested(fieldName, nestedPath, path), value);
    }

    /**
     * 添加多值匹配条件
     */
    public void addTermsCondition(EsSearchProperty field, List<String> values) {
        if (this.termsCondition == null) {
            this.termsCondition = new java.util.HashMap<>();
        }
        this.termsCondition.put(field, values);
    }

    /**
     * 添加多值匹配条件（字符串字段名）
     */
    public void addTermsCondition(String fieldName, String path, List<String> values) {
        addTermsCondition(EsSearchProperty.of(fieldName, path), values);
    }

    /**
     * 添加Nested嵌套多值查询匹配条件
     */
    public void addTermsConditionNested(String fieldName, String nestedPath, String path, List<String> values) {
        addTermsCondition(EsSearchProperty.nested(fieldName, nestedPath, path), values);
    }

    /**
     * 添加全文搜索条件
     */
    public void addMatchCondition(EsSearchProperty field, String value) {
        if (this.matchCondition == null) {
            this.matchCondition = new HashMap<>();
        }
        this.matchCondition.put(field, value);
    }

    /**
     * 添加全文搜索条件（字符串字段名）
     */
    public void addMatchCondition(String fieldName, String path, String value) {
        addMatchCondition(EsSearchProperty.of(fieldName, path), value);
    }

    /**
     * 添加Nested嵌套全文搜索条件
     */
    public void addMatchConditionNested(String fieldName, String nestedPath, String path, String value) {
        addMatchCondition(EsSearchProperty.nested(fieldName, nestedPath, path), value);
    }

    /**
     * 添加短语匹配条件
     */
    public void addMatchPhraseCondition(EsSearchProperty field, String value) {
        if (this.matchPhraseCondition == null) {
            this.matchPhraseCondition = new java.util.HashMap<>();
        }
        this.matchPhraseCondition.put(field, value);
    }

    /**
     * 添加短语匹配条件（字符串字段名）
     */
    public void addMatchPhraseCondition(String fieldName, String path, String value) {
        addMatchPhraseCondition(EsSearchProperty.of(fieldName, path), value);
    }

    /**
     * 添加Nested嵌套短语匹配条件
     */
    public void addMatchPhraseConditionNested(String fieldName, String nestedPath, String path, String value) {
        addMatchPhraseCondition(EsSearchProperty.nested(fieldName, nestedPath, path), value);
    }

    /**
     * 添加通配符查询条件
     */
    public void addWildcardCondition(EsSearchProperty field, String value) {
        if (this.wildcardCondition == null) {
            this.wildcardCondition = new java.util.HashMap<>();
        }
        this.wildcardCondition.put(field, value);
    }

    /**
     * 添加通配符查询条件（字符串字段名）
     */
    public void addWildcardCondition(String fieldName, String path, String value) {
        addWildcardCondition(EsSearchProperty.of(fieldName, path), value);
    }

    /**
     * 添加Nested嵌套通配符查询条件
     */
    public void addWildcardConditionNested(String fieldName, String nestedPath, String path, String value) {
        addWildcardCondition(EsSearchProperty.nested(fieldName, nestedPath, path), value);
    }

    /**
     * 添加前缀查询条件
     */
    public void addPrefixCondition(EsSearchProperty field, String value) {
        if (this.prefixCondition == null) {
            this.prefixCondition = new java.util.HashMap<>();
        }
        this.prefixCondition.put(field, value);
    }

    /**
     * 添加前缀查询条件（字符串字段名）
     */
    public void addPrefixCondition(String fieldName, String path, String value) {
        addPrefixCondition(EsSearchProperty.of(fieldName, path), value);
    }

    /**
     * 添加Nested嵌套前缀查询条件
     */
    public void addPrefixConditionNested(String fieldName, String nestedPath, String path, String value) {
        addPrefixCondition(EsSearchProperty.nested(fieldName, nestedPath, path), value);
    }

    /**
     * 添加正则表达式查询条件
     */
    public void addRegexpCondition(EsSearchProperty field, String value) {
        if (this.regexpCondition == null) {
            this.regexpCondition = new java.util.HashMap<>();
        }
        this.regexpCondition.put(field, value);
    }

    /**
     * 添加正则表达式查询条件（字符串字段名）
     */
    public void addRegexpCondition(String fieldName, String path, String value) {
        addRegexpCondition(EsSearchProperty.of(fieldName, path), value);
    }

    /**
     * 添加Nested嵌套正则表达式查询条件
     */
    public void addRegexpConditionNested(String fieldName, String nestedPath, String path, String value) {
        addRegexpCondition(EsSearchProperty.nested(fieldName, nestedPath, path), value);
    }

    /**
     * 添加排序条件
     */
    public void addSortCondition(EsSearchProperty field, SortOrder order) {
        if (this.sortCondition == null) {
            this.sortCondition = new HashMap<>();
        }
        this.sortCondition.put(field, order);
    }

    /**
     * 添加排序条件-字符串字段名
     */
    public void addSortCondition(String fieldName, String path, SortOrder order) {
        addSortCondition(EsSearchProperty.of(fieldName, path), order);
    }

    /**
     * 添加Nested嵌套排序条件
     */
    public void addSortConditionNested(String fieldName, String nestedPath, String path, SortOrder order) {
        addSortCondition(EsSearchProperty.nested(fieldName, nestedPath, path), order);
    }

    /**
     * 添加范围查询条件
     */
    public void addRangeCondition(EsSearchProperty field, RangeCondition condition) {
        if (this.rangeCondition == null) {
            this.rangeCondition = new HashMap<>();
        }
        if (this.rangeCondition.containsKey(field)) {
            RangeCondition oldRangeCondition = this.rangeCondition.get(field);
            if (oldRangeCondition.getFrom() == null) {
                oldRangeCondition.setFrom(condition.getFrom());
            }
            if (oldRangeCondition.getTo() == null) {
                oldRangeCondition.setTo(condition.getTo());
            }
        } else {
            this.rangeCondition.put(field, condition);
        }
    }

    /**
     * 添加范围查询条件（字符串字段名）
     */
    public void addRangeCondition(String fieldName, String path, RangeCondition condition) {
        addRangeCondition(EsSearchProperty.of(fieldName, path), condition);
    }

    /**
     * 添加Nested嵌套范围查询条件
     */
    public void addRangeConditionNested(String fieldName, String nestedPath, String path, RangeCondition condition) {
        addRangeCondition(EsSearchProperty.nested(fieldName, nestedPath, path), condition);
    }

    /**
     * 添加存在性查询条件
     */
    public void addExistsCondition(EsSearchProperty field, boolean exists) {
        if (this.existsCondition == null) {
            this.existsCondition = new java.util.HashMap<>();
        }
        this.existsCondition.put(field, exists);
    }

    /**
     * 添加存在性查询条件（字符串字段名）
     */
    public void addExistsCondition(String fieldName, String path, boolean exists) {
        addExistsCondition(EsSearchProperty.of(fieldName, path), exists);
    }

    /**
     * 添加Nested嵌套存在性查询条件
     */
    public void addExistsConditionNested(String fieldName, String nestedPath, String path, boolean exists) {
        addExistsCondition(EsSearchProperty.nested(fieldName, nestedPath, path), exists);
    }

    /**
     * 添加模糊查询条件
     */
    public void addFuzzyCondition(EsSearchProperty field, FuzzyCondition condition) {
        if (this.fuzzyCondition == null) {
            this.fuzzyCondition = new java.util.HashMap<>();
        }
        this.fuzzyCondition.put(field, condition);
    }

    /**
     * 添加模糊查询条件（字符串字段名）
     */
    public void addFuzzyCondition(String fieldName, String path, FuzzyCondition condition) {
        addFuzzyCondition(EsSearchProperty.of(fieldName, path), condition);
    }

    /**
     * 添加Nested嵌套模糊查询条件
     */
    public void addFuzzyConditionNested(String fieldName, String nestedPath, String path, FuzzyCondition condition) {
        addFuzzyCondition(EsSearchProperty.nested(fieldName, nestedPath, path), condition);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建基础查询条件
     */
    public static DatasetEsQueryCondition create() {
        return new DatasetEsQueryCondition();
    }

    /**
     * 为指定数据源创建查询条件
     */
    public static DatasetEsQueryCondition forDataSource(String dataSource) {
        return DatasetEsQueryCondition.builder()
                .dataSource(dataSource)
                .build();
    }

    /**
     * 创建分页查询条件
     */
    public static DatasetEsQueryCondition withPagination(int pageNum, int pageSize) {
        return DatasetEsQueryCondition.builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();
    }

    // ========== 常用查询模板 ==========

    /**
     * 检查是否有任何查询条件
     */
    public boolean hasAnyCondition() {
        return (termCondition != null && !termCondition.isEmpty()) ||
                (termsCondition != null && !termsCondition.isEmpty()) ||
                (matchCondition != null && !matchCondition.isEmpty()) ||
                (matchPhraseCondition != null && !matchPhraseCondition.isEmpty()) ||
                (wildcardCondition != null && !wildcardCondition.isEmpty()) ||
                (prefixCondition != null && !prefixCondition.isEmpty()) ||
                (regexpCondition != null && !regexpCondition.isEmpty()) ||
                (rangeCondition != null && !rangeCondition.isEmpty()) ||
                (existsCondition != null && !existsCondition.isEmpty()) ||
                (fuzzyCondition != null && !fuzzyCondition.isEmpty()) ||
                (queryString != null && !queryString.trim().isEmpty());
    }

    /**
     * 聚合查询条件配置
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AggregationCondition {
        /**
         * 聚合类型
         */
        private AggregationType type;

        /**
         * 聚合字段
         */
        private String field;

        /**
         * 聚合大小（适用于terms聚合等）
         */
        private Integer size;

        /**
         * 缺失值处理（当字段不存在时的默认值）
         */
        private String missing;

        /**
         * 最小文档数量（bucket最小文档数阈值）
         */
        private Long minDocCount;

        /**
         * 排序配置
         */
        private List<AggregationSort> sorts;

        /**
         * 时间间隔（适用于date_histogram）
         */
        private String interval;

        /**
         * 时间格式（适用于date_histogram）
         */
        private String format;

        /**
         * 时区（适用于date_histogram）
         */
        private String timeZone;

        /**
         * 范围配置（适用于range聚合）
         */
        private List<AggregationRange> ranges;

        /**
         * 子聚合配置
         */
        private Map<String, AggregationCondition> subAggregations;

        /**
         * 过滤条件（适用于filter聚合）
         */
        private Map<String, Object> filterCondition;

        /**
         * 包含模式（适用于terms聚合）
         */
        private String includePattern;

        /**
         * 排除模式（适用于terms聚合）
         */
        private String excludePattern;

        /**
         * 精度阈值（适用于cardinality聚合）
         */
        private Long precisionThreshold;
    }

    /**
     * 聚合类型枚举
     */
    @Getter
    public enum AggregationType {
        /**
         * 词条聚合 - 统计每个值的文档数量
         */
        TERMS("terms"),

        /**
         * 统计聚合 - 计算数值字段的统计信息（min, max, avg, sum, count）
         */
        STATS("stats"),

        /**
         * 扩展统计聚合 - 包含方差、标准差等
         */
        EXTENDED_STATS("extended_stats"),

        /**
         * 日期直方图聚合 - 按时间间隔分组
         */
        DATE_HISTOGRAM("date_histogram"),

        /**
         * 直方图聚合 - 按数值间隔分组
         */
        HISTOGRAM("histogram"),

        /**
         * 范围聚合 - 按指定范围分组
         */
        RANGE("range"),

        /**
         * 日期范围聚合
         */
        DATE_RANGE("date_range"),

        /**
         * 最小值聚合
         */
        MIN("min"),

        /**
         * 最大值聚合
         */
        MAX("max"),

        /**
         * 平均值聚合
         */
        AVG("avg"),

        /**
         * 求和聚合
         */
        SUM("sum"),

        /**
         * 计数聚合
         */
        VALUE_COUNT("value_count"),

        /**
         * 基数聚合 - 统计唯一值数量
         */
        CARDINALITY("cardinality"),

        /**
         * 百分位聚合
         */
        PERCENTILES("percentiles"),

        /**
         * 百分位排名聚合
         */
        PERCENTILE_RANKS("percentile_ranks"),

        /**
         * 过滤聚合
         */
        FILTER("filter"),

        /**
         * 多过滤聚合
         */
        FILTERS("filters"),

        /**
         * 嵌套聚合
         */
        NESTED("nested"),

        /**
         * 反向嵌套聚合
         */
        REVERSE_NESTED("reverse_nested"),

        /**
         * TopHits聚合 - 获取每个bucket中的顶级文档
         */
        TOP_HITS("top_hits");

        private final String value;

        AggregationType(String value) {
            this.value = value;
        }
    }

    /**
     * 聚合排序配置
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AggregationSort {
        /**
         * 排序字段或聚合名称
         */
        private String field;

        /**
         * 排序方向
         */
        private SortOrder order;

        /**
         * 创建按计数排序
         */
        public static AggregationSort byCount(SortOrder order) {
            return AggregationSort.builder()
                    .field("_count")
                    .order(order)
                    .build();
        }

        /**
         * 创建按键值排序
         */
        public static AggregationSort byKey(SortOrder order) {
            return AggregationSort.builder()
                    .field("_key")
                    .order(order)
                    .build();
        }

        /**
         * 创建按子聚合排序
         */
        public static AggregationSort bySubAggregation(String subAggName, SortOrder order) {
            return AggregationSort.builder()
                    .field(subAggName)
                    .order(order)
                    .build();
        }
    }

    /**
     * 聚合范围配置
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AggregationRange {
        /**
         * 范围标识
         */
        private String key;

        /**
         * 起始值
         */
        private Object from;

        /**
         * 结束值
         */
        private Object to;

        /**
         * 创建数值范围
         */
        public static AggregationRange of(String key, Object from, Object to) {
            return AggregationRange.builder()
                    .key(key)
                    .from(from)
                    .to(to)
                    .build();
        }

        /**
         * 创建开放式范围（只有起始值）
         */
        public static AggregationRange from(String key, Object from) {
            return AggregationRange.builder()
                    .key(key)
                    .from(from)
                    .build();
        }

        /**
         * 创建开放式范围（只有结束值）
         */
        public static AggregationRange to(String key, Object to) {
            return AggregationRange.builder()
                    .key(key)
                    .to(to)
                    .build();
        }
    }

    // ========== 聚合查询便捷构建方法 ==========

    /**
     * 添加词条聚合
     *
     * @param aggName 聚合名称
     * @param path    字段路径
     * @param size    返回的bucket数量
     */
    public void addTermsAggregation(String aggName, String path, int size) {
        addAggregation(EsSearchProperty.of(aggName, path), AggregationCondition.builder()
                .type(AggregationType.TERMS)
                .field(path)
                .size(size)
                .build());
    }

    /**
     * 添加嵌套聚合
     */
    public void addTermsAggregationNested(String fieldName, String nestedPath, String path, int size) {
        addAggregation(EsSearchProperty.nested(fieldName, nestedPath, path), AggregationCondition.builder()
                .type(AggregationType.TERMS)
                .field(fieldName)
                .size(size)
                .build());
    }

    /**
     * 添加聚合条件（使用聚合名称）
     */
    public void addAggregation(String fieldName, String path, AggregationCondition condition) {
        addAggregation(EsSearchProperty.of(fieldName, path), condition);
    }

    /**
     * 添加聚合条件（使用EsSearchProperty）
     */
    public void addAggregation(EsSearchProperty field, AggregationCondition condition) {
        if (this.termsAggConditions == null) {
            this.termsAggConditions = new HashMap<>();
        }
        this.termsAggConditions.put(field, condition);
    }

    /**
     * 设置只返回聚合结果
     */
    public void setAggregationOnly() {
        this.aggregationOnly = true;
        this.pageSize = 0; // 不返回文档内容
    }

    /**
     * 创建分组统计查询（常用模板）
     * 按指定字段分组，统计每组的数量和占比
     */
    public static DatasetEsQueryCondition createFieldDistributionQuery(String dataSource,
                                                                       String field,
                                                                       int maxResults) {
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource(dataSource)
                .aggregationOnly(true)
                .build();

        condition.addTermsAggregation("field_distribution", field, maxResults);
        return condition;
    }

    /**
     * 创建多字段分组查询（常用模板）
     * 对多个字段进行分组统计
     */
    public static DatasetEsQueryCondition createMultiFieldDistributionQuery(String dataSource,
                                                                            Map<String, String> fieldAggNames,
                                                                            int maxResults) {
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource(dataSource)
                .aggregationOnly(true)
                .build();

        fieldAggNames.forEach((aggName, field) -> {
            condition.addTermsAggregation(aggName, field, maxResults);
        });

        return condition;
    }
} 