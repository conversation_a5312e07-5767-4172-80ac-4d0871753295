package com.meituan.aigc.aida.labeling.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * 标注详情表实体类
 */
@Data
public class LabelingDetail {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 子任务ID
     */
    private Long subTaskId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 会话时间
     */
    private Date sessionTime;
    
    /**
     * 原始数据ID
     */
    private Long rawDataId;
    
    /**
     * 被修改的原始数据内容映射内容, 单独存储字段, 查看详情、导出时覆盖原始数据表中的raw_data_mapped_content
     */
    private String modifiedRawDataMappedContent;
    
    /**
     * 对比项是否一致 0:否 1:是
     * @see com.meituan.aigc.aida.labeling.common.enums.CompareItemsConsistent
     */
    private Integer comparItemsConsistent;
    
    /**
     * 自定义标注项标注结果
     */
    private String labelingItemsResult;
    
    /**
     * 标注人mis号
     */
    private String labelerMis;

    /**
     * 标注人姓名
     */
    private String labelerName;
    
    /**
     * 状态(1:标注中 2:已标注)
     * {@link com.meituan.aigc.aida.labeling.common.enums.LabelingDetailStatus}
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 首次标注时间
     */
    private Date firstLabelingTime;

    /**
     * 抽样状态
     */
    private Integer sampleStatus;

    /**
     * 大模型消息ID
     */
    private String messageId;

    /**
     * 质检人查看状态 1-未查看 2-已查看
     */
    private Integer viewStatus;

    /**
     * 扩展信息(json格式)
     */
    private String extraInfo;
} 