package com.meituan.aigc.aida.data.management.dataset.strategy.task.impl;

import java.util.Arrays;
import java.util.List;

/**
 * 数据集字段名称常量类
 */
public class DataSetFieldConstants {

    /**
     * 智能测SessionId字段名
     */
    public static final String FIELD_SESSION_ID = "智能测SessionId";

    /**
     * 联络Id字段名
     */
    public static final String FIELD_CONTACT_ID = "联络Id";

    /**
     * 客服mis字段名
     */
    public static final String FIELD_STAFF_MIS = "客服mis";

    /**
     * 消息ID字段名
     */
    public static final String FIELD_MESSAGE_ID = "消息ID";

    /**
     * 消息来源字段名
     */
    public static final String FIELD_MESSAGE_SOURCE = "消息来源";

    /**
     * 联系类型字段名
     */
    public static final String FIELD_CONTACT_TYPE = "联系类型";

    /**
     * 标问ID列表字段名
     */
    public static final String FIELD_TYPICAL_QUESTION_IDS = "标问ID列表";

    /**
     * 弹屏ACid字段名
     */
    public static final String FIELD_POPUP_AC_ID = "弹屏ACid";

    /**
     * 客服发送的消息内容字段名
     */
    public static final String FIELD_STAFF_MESSAGE_CONTENT = "客服发送的消息内容";

    /**
     * 用户发送的消息内容字段名
     */
    public static final String FIELD_CUSTOMER_MESSAGE_CONTENT = "用户发送的消息内容";

    /**
     * 应用入参字段名
     */
    public static final String FIELD_INPUTS = "应用入参";

    /**
     * 扩展字段名
     */
    public static final String FIELD_EXTENSION = "扩展字段";

    /**
     * extra_info
     */
    public static final String FIELD_EXTRA_INFO = "extra_info";


    /**
     * 所有字段名称列表
     */
    public static final List<String> ALL_FIELD_NAMES = Arrays.asList(
            FIELD_SESSION_ID,
            FIELD_CONTACT_ID,
            FIELD_STAFF_MIS,
            FIELD_MESSAGE_ID,
            FIELD_MESSAGE_SOURCE,
            FIELD_CONTACT_TYPE,
            FIELD_TYPICAL_QUESTION_IDS,
            FIELD_POPUP_AC_ID,
            FIELD_STAFF_MESSAGE_CONTENT,
            FIELD_CUSTOMER_MESSAGE_CONTENT,
            FIELD_INPUTS,
            FIELD_EXTENSION
    );
} 