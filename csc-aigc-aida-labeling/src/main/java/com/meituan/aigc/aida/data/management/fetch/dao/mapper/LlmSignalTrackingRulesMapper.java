package com.meituan.aigc.aida.data.management.fetch.dao.mapper;

import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRulesExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LlmSignalTrackingRulesMapper {
    long countByExample(LlmSignalTrackingRulesExample example);

    int deleteByExample(LlmSignalTrackingRulesExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LlmSignalTrackingRules record);

    int insertSelective(LlmSignalTrackingRules record);

    List<LlmSignalTrackingRules> selectByExample(LlmSignalTrackingRulesExample example);

    LlmSignalTrackingRules selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LlmSignalTrackingRules record, @Param("example") LlmSignalTrackingRulesExample example);

    int updateByExample(@Param("record") LlmSignalTrackingRules record, @Param("example") LlmSignalTrackingRulesExample example);

    int updateByPrimaryKeySelective(LlmSignalTrackingRules record);

    int updateByPrimaryKey(LlmSignalTrackingRules record);

    List<LlmSignalTrackingRules> listEnableByMisIdsAndTypicalQuestionIds(@Param("staffMis") String staffMis, @Param("typicalQuestionId") String typicalQuestionId);

    List<LlmSignalTrackingRules> listEnableByMisIdsAndTypicalQuestionIdsIsBlank(@Param("staffMis") String staffMis);
}