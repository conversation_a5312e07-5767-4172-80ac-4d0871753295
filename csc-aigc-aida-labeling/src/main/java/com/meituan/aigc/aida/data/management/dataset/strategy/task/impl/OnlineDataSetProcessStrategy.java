package com.meituan.aigc.aida.data.management.dataset.strategy.task.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.JSONObject;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchRecordRepository;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchTaskRepository;
import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import com.meituan.aigc.aida.data.management.dataset.dto.DataSourceField;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;

import java.util.Collections;

/**
 * 线上拉取数据集任务处理策略
 */
@Slf4j
@Component
public class OnlineDataSetProcessStrategy extends AbstractDataSetProcessStrategy {

    @Resource
    private DataFetchTaskRepository dataFetchTaskRepository;

    @Resource
    private DataFetchRecordRepository dataFetchRecordRepository;

    @Override
    public Integer getStrategyType() {
        return DataSourceEnum.ONLINE.getCode();
    }

    @Override
    public DataSetCreateResponseParam createDataSet(DataSetCreateParam param) {
        log.info("开始处理线上拉取数据集任务, param: {}", JSON.toJSONString(param));

        // 参数校验
        CheckUtil.paramCheck(param.getFetchId() != null, "数据获取任务ID不能为空");
        // 获取数据获取任务
        DataFetchTask dataFetchTask = dataFetchTaskRepository.getTaskById(param.getFetchId());
        CheckUtil.paramCheck(dataFetchTask != null, "找不到指定的数据获取任务");

        // 创建数据集
        DataSet dataSet = createNewDataSet(param);
        Long dataSetId = dataSet.getId();

        // 创建数据集版本
        DataSetVersion dataSetVersion = createDataSetVersion(dataSetId, param);

        // 获取数据获取任务的记录
        List<DataFetchRecordWithBLOBs> fetchRecords = dataFetchRecordRepository.listRecordByTaskId(param.getFetchId());
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(fetchRecords), "数据获取任务没有数据记录");

        // 调用通用的保存方法
        commonSaveDataInBatches(param, dataSet, dataSetVersion);

        // 返回结果
        return new DataSetCreateResponseParam(dataSetId, null);
    }

    @Override
    protected List<DataSetRecordParam> getNextBatchDataList(DataSetPageParam param, int pageNum, int pageSize) {
        // 从数据获取任务中分页获取记录
        List<DataFetchRecordWithBLOBs> fetchRecords = dataFetchRecordRepository.pageByTaskId(param.getFetchId(), (pageNum - 1) * pageSize, pageSize);
        if (CollectionUtils.isEmpty(fetchRecords)) {
            return new ArrayList<>();
        }

        // 构建数据集记录
        return buildDataSetRecords(fetchRecords, param.getHeadList());
    }

    /**
     * 解析表头
     *
     * @param param 解析参数
     * @return 表头字段列表
     */
    @Override
    public List<DataFieldParam> parseHeader(DataSetParseHeadParam param) {
        CheckUtil.paramCheck(param.getFetchId() != null, "数据获取任务ID不能为空");

        // 获取数据获取任务
        DataFetchTask dataFetchTask = dataFetchTaskRepository.getTaskById(param.getFetchId());
        CheckUtil.paramCheck(dataFetchTask != null, "找不到指定的数据获取任务");

        // 创建结果集
        List<DataFieldParam> headerList = new ArrayList<>();

        // 1. 添加通用字段
        for (String fieldName : DataSetFieldConstants.ALL_FIELD_NAMES) {
            DataFieldParam field = new DataFieldParam();
            field.setColumnName(fieldName);
            headerList.add(field);
        }

        // 2. 添加信号字段
        List<DataFetchRecordWithBLOBs> dataFetchRecordList = dataFetchRecordRepository.pageByTaskId(param.getFetchId(), 0, 10);
        Set<String> signalList = dataFetchRecordList.stream()
                .filter(record -> StringUtils.isNotBlank(record.getSignalDataContent()))
                .flatMap(record -> JSONObject.parseObject(record.getSignalDataContent()).keySet().stream())
                .collect(Collectors.toSet());

        for (String signal : signalList) {
            DataFieldParam field = new DataFieldParam();
            field.setColumnName(signal);
            headerList.add(field);
        }

        // 推断每个表头的数据类型
        headerList = inferHeadTypeInBatches(null, headerList, param.getFetchId());
        return headerList;
    }

    /**
     * 构建数据集记录
     *
     * @param fetchRecords 数据获取记录
     * @param headList     表头列表
     * @return 数据集记录列表
     */
    private List<DataSetRecordParam> buildDataSetRecords(List<DataFetchRecordWithBLOBs> fetchRecords, List<DataFieldParam> headList) {
        List<DataSetRecordParam> dataList = new ArrayList<>();

        // 构建字段名称列表，用于映射数据
        Set<String> fieldNames = headList.stream()
                .map(DataFieldParam::getColumnName)
                .collect(Collectors.toSet());

        for (DataFetchRecordWithBLOBs record : fetchRecords) {
            DataSetRecordParam dataRecord = new DataSetRecordParam();
            Map<String, Object> content = new HashMap<>();

            // 基础字段映射
            if (fieldNames.contains(DataSetFieldConstants.FIELD_SESSION_ID)) {
                content.put(DataSetFieldConstants.FIELD_SESSION_ID, record.getSessionId());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_CONTACT_ID)) {
                content.put(DataSetFieldConstants.FIELD_CONTACT_ID, record.getContactId());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_STAFF_MIS)) {
                content.put(DataSetFieldConstants.FIELD_STAFF_MIS, record.getStaffMis());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_MESSAGE_ID)) {
                content.put(DataSetFieldConstants.FIELD_MESSAGE_ID, record.getMessageId());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_MESSAGE_SOURCE)) {
                content.put(DataSetFieldConstants.FIELD_MESSAGE_SOURCE, record.getChatMessageFromType());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_CONTACT_TYPE)) {
                content.put(DataSetFieldConstants.FIELD_CONTACT_TYPE, record.getContactType());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_TYPICAL_QUESTION_IDS)) {
                content.put(DataSetFieldConstants.FIELD_TYPICAL_QUESTION_IDS, record.getTypicalQuestionIds());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_POPUP_AC_ID)) {
                content.put(DataSetFieldConstants.FIELD_POPUP_AC_ID, record.getPopupAcId());
            }

            // BLOB 类型字段
            if (fieldNames.contains(DataSetFieldConstants.FIELD_STAFF_MESSAGE_CONTENT)) {
                content.put(DataSetFieldConstants.FIELD_STAFF_MESSAGE_CONTENT, record.getStaffMessageContent());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_CUSTOMER_MESSAGE_CONTENT)) {
                content.put(DataSetFieldConstants.FIELD_CUSTOMER_MESSAGE_CONTENT, record.getCustomerMessageContent());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_INPUTS)) {
                content.put(DataSetFieldConstants.FIELD_INPUTS, record.getInputs());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_EXTENSION)) {
                content.put(DataSetFieldConstants.FIELD_EXTENSION, record.getCommonInfo());
            }
            if (fieldNames.contains(DataSetFieldConstants.FIELD_EXTRA_INFO)) {
                content.put(DataSetFieldConstants.FIELD_EXTRA_INFO, record.getExtraInfo());
            }

            // 处理信号数据内容，这是一个JSON字符串，需要解析后添加到内容中
            if (StringUtils.isNotBlank(record.getSignalDataContent())) {
                try {
                    Map<String, String> signalDataMap = JSON.parseObject(record.getSignalDataContent(), new TypeReference<Map<String, String>>() {
                    });
                    for (Map.Entry<String, String> entry : signalDataMap.entrySet()) {
                        if (fieldNames.contains(entry.getKey())) {
                            content.put(entry.getKey(), entry.getValue());
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析信号数据内容失败：{}", e.getMessage());
                }
            }

            dataRecord.setContent(content);
            dataList.add(dataRecord);
        }

        return dataList;
    }
} 