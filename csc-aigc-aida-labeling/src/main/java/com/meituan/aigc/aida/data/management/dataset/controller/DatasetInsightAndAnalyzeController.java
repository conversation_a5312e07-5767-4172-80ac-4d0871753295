package com.meituan.aigc.aida.data.management.dataset.controller;

import com.meituan.aigc.aida.data.management.dataset.dto.DatasetDistributeResponseDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFieldListResponseDTO;
import com.meituan.aigc.aida.data.management.dataset.param.DatasetDistributeQueryParam;
import com.meituan.aigc.aida.data.management.dataset.service.DatasetAnalysisService;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 数据集洞察和分析控制器
 * @date 2025/6/3
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/data/analysis")
public class DatasetInsightAndAnalyzeController {

    @Autowired
    private DatasetAnalysisService datasetAnalysisService;

    /**
     * 查询数据集分布情况
     *
     * @param datasetId        数据集ID
     * @param datasetVersionId 数据集版本ID
     * @param columnName       列名
     * @return 数据集分布结果
     */
    @GetMapping("/distribute")
    public Result<DatasetDistributeResponseDTO> queryDatasetDistribute(
            @RequestParam("datasetId") Long datasetId,
            @RequestParam("datasetVersionId") Long datasetVersionId,
            @RequestParam("columnName") String columnName) {

        log.info("接收到数据集分布查询请求, datasetId: {}, datasetVersionId: {}, columnName: {}",
                datasetId, datasetVersionId, columnName);

        try {
            // 构建查询参数
            DatasetDistributeQueryParam param = new DatasetDistributeQueryParam();
            param.setDatasetId(datasetId);
            param.setDatasetVersionId(datasetVersionId);
            param.setColumnName(columnName);

            // 执行查询
            DatasetDistributeResponseDTO response = datasetAnalysisService.queryDatasetDistribute(param);

            log.info("数据集分布查询成功, datasetId: {}", datasetId);
            return Result.ok(response);

        } catch (Exception e) {
            log.error("数据集分布查询失败, datasetId: {}, error: {}", datasetId, e.getMessage(), e);
            return Result.fail(500, "查询数据集分布失败: " + e.getMessage());
        }
    }

    /**
     * 查询一级字段下的二级字段列表
     *
     * @param datasetId        数据集ID
     * @param datasetVersionId 数据集版本ID
     * @param columnName       一级字段名
     * @return 字段列表结果
     */
    @GetMapping("/field")
    public Result<DatasetFieldListResponseDTO> querySubFields(
            @RequestParam("datasetId") Long datasetId,
            @RequestParam("datasetVersionId") Long datasetVersionId,
            @RequestParam("columnName") String columnName) {
        try {
            // 构建查询参数
            DatasetDistributeQueryParam param = new DatasetDistributeQueryParam();
            param.setDatasetId(datasetId);
            param.setDatasetVersionId(datasetVersionId);
            param.setColumnName(columnName);

            // 执行查询
            DatasetFieldListResponseDTO response = datasetAnalysisService.querySubFields(param);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("查询二级字段列表失败, datasetId: {}, error: {}", datasetId, e.getMessage(), e);
            return Result.fail(500, "查询字段列表失败: " + e.getMessage());
        }
    }
}
