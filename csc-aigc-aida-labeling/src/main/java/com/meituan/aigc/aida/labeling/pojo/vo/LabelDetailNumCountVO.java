package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: guowenhui
 * @Create: 2025/3/10 15:12
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelDetailNumCountVO implements Serializable {

    /**
     * 总数量
     */
    private Integer totalNum;
    /**
     * 待标注数量
     */
    private Integer waitLabelingNum;
    /**
     * 已标注数量
     */
    private Integer doneLabelingNum;


}
