package com.meituan.aigc.aida.benchmark.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Benchmark排行榜排名响应VO
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BenchmarkLeaderboardRankingVO implements Serializable {

    /**
     * 表头列表
     */
    private List<String> headList;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 排行榜数据列表
     */
    private List<RankingDataItem> rankingDataList;

    /**
     * 排行榜数据项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RankingDataItem implements Serializable {

        /**
         * 数据ID
         */
        private String id;

        /**
         * 模型名称
         */
        private String modelName;

        /**
         * 排名数据
         */
        private List<RankingData> rankingData;
    }

    /**
     * 排名数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RankingData implements Serializable {

        /**
         * 列名
         */
        private String dataName;

        /**
         * 数值
         */
        private Double dataValue;

        /**
         * 字符数据
         */
        private String dataString;
    }
} 