package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:03
 * @Version: 1.0
 */
public interface InspectionItemTemplateRepository {

    /**
     * 获取质检模板列表
     * @return 质检模板列表
     */
    List<InspectionItemTemplate> listByTemplateType();

    /**
     * 新增模板
     * @param template
     */
    void insertTemplate(InspectionItemTemplate template);

    /**
     * 更新模版
     * @param template
     */
    void updateTemplate(InspectionItemTemplate template);

    /**
     * 获取模板
     * @param id
     */
    InspectionItemTemplate getTemplateById(Long id);
}
