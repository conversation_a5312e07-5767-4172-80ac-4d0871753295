package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics;
import com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatisticsExample;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonnelDetailQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonLabelingStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingAndCheckCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonLabelingQueryVO;
import org.apache.ibatis.annotations.Param;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DailyStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DailyBizStatisticsVO;

import java.util.List;

public interface PersonLabelingStatisticsMapper {
    long countByExample(PersonLabelingStatisticsExample example);

    int deleteByExample(PersonLabelingStatisticsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PersonLabelingStatistics record);

    int insertSelective(PersonLabelingStatistics record);

    List<PersonLabelingStatistics> selectByExample(PersonLabelingStatisticsExample example);

    PersonLabelingStatistics selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") PersonLabelingStatistics record, @Param("example") PersonLabelingStatisticsExample example);

    int updateByExample(@Param("record") PersonLabelingStatistics record, @Param("example") PersonLabelingStatisticsExample example);

    int updateByPrimaryKeySelective(PersonLabelingStatistics record);

    int updateByPrimaryKey(PersonLabelingStatistics record);

    /**
     * 查询人员标注统计数据
     * @param param
     * @return
     */
    List<PersonLabelingQueryVO> queryPersonLabelingStats(@Param("param") PersonLabelingStatisticsDTO param);

    LabelingAndCheckCountVO statisticalLabelingData(@Param("query") DataStatisticalQuery query);

    /**
     * 查询人员指定业务的标注统计数据
     * @param param
     * @return
     */
    List<DailyBizStatisticsVO> queryDailyTrends(@Param("param") DailyStatisticsDTO param);

    /**
     * 查询人员详情
     * @param query
     * @return
     */
    List<PersonnelDetailVO> queryPersonnelDetail(@Param("query") PersonnelDetailQuery query);

    void batchInsert(@Param("personLabelingStatisticsList") List<PersonLabelingStatistics> personLabelingStatisticsList);

}