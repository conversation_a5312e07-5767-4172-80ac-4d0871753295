package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskPO;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubtaskCountPo;
import com.meituan.aigc.aida.labeling.param.LabelingListParam;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelSubTaskDTO;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:08
 * @Version: 1.0
 */
public interface LabelingSubTaskRepository {

    /**
     * 根据分组ID查询剩余子任务ID列表
     *
     * @param groupId
     * @param subTaskId
     * @return
     */
    List<Long> listSubTaskIdByGroupId(Long groupId, Long subTaskId);

    /**
     * 子任务转交
     *
     * @param subTaskId
     * @param mis
     * @param name
     * @return
     */
    int transferSubTask(Long subTaskId, String mis, String name);

    /**
     * 根据主任务Id和分组id查询子任务
     *
     * @param taskId
     * @param groupId
     * @return
     */
    List<LabelSubTaskDTO> listSubTaskByGroupId(Long taskId, Long groupId);


    /**
     * 根据ID查询子任务详情
     *
     * @param subTaskId 子任务ID
     * @return 子任务详情
     */
    LabelingSubTask getById(Long subTaskId);

    /**
     * 根据主键修改子任务
     *
     * @param labelingSubTask 子任务对象
     */
    void updateById(LabelingSubTask labelingSubTask);

    /**
     * 查询分组下子任务列表
     *
     * @param groupId 分组ID
     * @return 子任务列表
     */
    List<LabelingSubTask> listByGroupId(Long groupId);

    /**
     * 查询子任务列表
     *
     * @param param 查询参数
     * @return 子任务列表
     */
    List<LabelingSubTaskPO> listByTaskIdAndNameAndLabelerMis(LabelingListParam param);

    /**
     * 批量插入子任务
     *
     * @param subTaskList 子任务列表
     */
    void batchInsertSubTask(List<LabelingSubTask> subTaskList);

    /**
     * 根据任务id查询所有子任务
     *
     * @param taskId
     * @return
     */
    List<LabelingSubTask> listAllByTaskId(Long taskId);

    /**
     * 根据任务id更新状态
     *
     * @param taskId 任务id
     * @param status 子任务状态
     */
    void updateStatusByTaskId(Long taskId, Integer status);

    /**
     * 子任务统计信息
     *
     * @param taskId 任务id
     * @return 子任务统计信息
     */
    List<LabelingSubtaskCountPo> countSubTask(Long taskId);

    /**
     * 根据质检详情ID查询分组数量
     *
     * @param checkItemIds 质检详情ID集合
     * @return 分组数量
     */
    Integer countGroupNumByQualityCheckItemIds(List<Long> checkItemIds);

    /**
     * 根据分组ID列表查询子任务列表
     *
     * @param groupIdList 分组ID列表
     * @return 子任务列表
     */
    List<LabelingSubTask> listByGroupIdList(List<Long> groupIdList);

    /**
     * 插入子任务
     *
     * @param labelingSubTask 子任务对象
     */
    void insert(LabelingSubTask labelingSubTask);

    /**
     * 根据任务id和分组id查询子任务
     *
     * @param taskId  任务id
     * @param groupId 分组id
     * @return 子任务列表
     */
    List<LabelingSubTask> listByTaskIdAndGroupId(Long taskId, Long groupId);
}
