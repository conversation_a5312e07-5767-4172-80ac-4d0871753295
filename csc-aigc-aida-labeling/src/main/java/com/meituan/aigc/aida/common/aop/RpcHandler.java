package com.meituan.aigc.aida.common.aop;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingException;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.meituan.aigc.aida.labeling.remote.common.dto.ServiceResponseDTO;
import com.meituan.aigc.aida.labeling.remote.common.enums.ResultCode;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

@Aspect
@Slf4j
@Component
public class RpcHandler {

    @Around("execution(public * com.meituan.aigc.aida.data.management.dataset.remote..*.*(..))")
    public Object rpcPointcut(ProceedingJoinPoint joinPoint) {
        Map<String, Object> params = getRequestParams(joinPoint);
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();

        try {
            Object result = joinPoint.proceed();
            log.info("执行Pigeon RPC请求成功: params-{}, result-{}, {}.{}",
                    JSONObject.toJSONString(params),
                    JSONObject.toJSONString(result),
                    className,
                    methodName);
            return result;
        } catch (Throwable t) {
            Cat.logError(t);
            log.info("执行Pigeon RPC请求异常: params-{}, error-{}, {}.{}",
                    JSONObject.toJSONString(params),
                    t.getMessage(),
                    className,
                    methodName, t);
            if (t instanceof AidaTrainingException) {
                return ServiceResponseDTO.failed(ResultCode.FAIL, t.getMessage());
            }
            return ServiceResponseDTO.failed(ResultCode.FAIL, "接口异常");
        }
    }

    /**
     * 获取方法参数名和参数值
     */
    private Map<String, Object> getRequestParams(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        final String[] names = methodSignature.getParameterNames();
        final Object[] args = joinPoint.getArgs();

        if (null == names || null == args) {
            return Collections.emptyMap();
        }
        Map<String, Object> map = Maps.newHashMap();
        for (int i = 0; i < names.length; i++) {
            map.put(names[i], args[i]);
        }
        return map;
    }
}
