package com.meituan.aigc.aida.data.management.fetch.dao.mapper;

import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecord;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordExample;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataFetchRecordMapper {
    long countByExample(DataFetchRecordExample example);

    int deleteByExample(DataFetchRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DataFetchRecordWithBLOBs record);

    int insertSelective(DataFetchRecordWithBLOBs record);

    List<DataFetchRecordWithBLOBs> selectByExampleWithBLOBs(DataFetchRecordExample example);

    List<DataFetchRecord> selectByExample(DataFetchRecordExample example);

    DataFetchRecordWithBLOBs selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DataFetchRecordWithBLOBs record, @Param("example") DataFetchRecordExample example);

    int updateByExampleWithBLOBs(@Param("record") DataFetchRecordWithBLOBs record, @Param("example") DataFetchRecordExample example);

    int updateByExample(@Param("record") DataFetchRecord record, @Param("example") DataFetchRecordExample example);

    int updateByPrimaryKeySelective(DataFetchRecordWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(DataFetchRecordWithBLOBs record);

    int updateByPrimaryKey(DataFetchRecord record);

    int batchInsertDataFetchRecord(@Param("dataFetchRecordList") List<DataFetchRecordWithBLOBs> dataFetchRecordList);

    Integer countByTaskId(@Param("taskId") Long taskId);

    List<DataFetchRecordWithBLOBs> pageByTaskId(@Param("taskId") Long taskId, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    List<DataFetchRecordWithBLOBs> listByTaskIdAndLikeByInputs(@Param("taskId") Long taskId,@Param("inputs") String inputs);
}