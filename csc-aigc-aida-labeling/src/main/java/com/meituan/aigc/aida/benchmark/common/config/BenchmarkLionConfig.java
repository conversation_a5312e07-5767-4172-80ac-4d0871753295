package com.meituan.aigc.aida.benchmark.common.config;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.csc.pacific.common.tools.utils.json.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * benchmark lion配置
 *
 * <AUTHOR>
 * @date 2025/6/25
 */

@Component
@Data
@Slf4j
public class BenchmarkLionConfig {
    /**
     * Benchmark指标配置Lion
     */
    @MdpConfig("benchmark.indicator.config")
    private String indicatorConfig;

    /**
     * 根据版本名称获取指标配置列表
     *
     * @param versionName 版本名称，如 "Benchmark-基础模型-v1.0"
     * @return 指标列表，如 ["综合得分","可用率","满分率","时延","价格"]
     */
    public List<String> getIndicatorConfigList(String versionName) {
        if (StringUtils.isBlank(indicatorConfig) || StringUtils.isBlank(versionName)) {
            return Collections.emptyList();
        }

        try {
            // 解析 JSON 配置
            Map<String, Object> configMap = JsonUtils.toMap(indicatorConfig);

            if (MapUtils.isEmpty(configMap)) {
                log.warn("指标配置解析结果为空，原始配置: {}", indicatorConfig);
                return Collections.emptyList();
            }

            // 根据版本名称获取对应的指标列表
            Object indicatorObj = configMap.get(versionName);

            if (indicatorObj == null) {
                log.info("未找到版本 {} 的指标配置，可用版本: {}", versionName, configMap.keySet());
                return Collections.emptyList();
            }

            // 将Object转换为List<String>
            List<?> indicatorList = (List<?>) indicatorObj;

            if (indicatorList.isEmpty()) {
                log.warn("版本 {} 的指标配置为空或转换失败", versionName);
                return Collections.emptyList();
            }

            log.info("成功获取版本 {} 的指标配置，指标数量: {}", versionName, indicatorList.size());
            return indicatorList.stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(java.util.stream.Collectors.toList());

        } catch (Exception e) {
            log.error("解析指标配置失败，版本: {}, 配置: {}, 错误信息: {}", versionName, indicatorConfig, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
