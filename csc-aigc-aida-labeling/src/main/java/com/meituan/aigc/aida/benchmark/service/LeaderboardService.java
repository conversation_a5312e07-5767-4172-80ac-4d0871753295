package com.meituan.aigc.aida.benchmark.service;

import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkLeaderboardRankingVO;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkModelDetailVO;

/**
 * <AUTHOR>
 * @description 类描述.
 * @date 2025/6/24
 */
public interface LeaderboardService {

    /**
     * 获取排行榜排名列表
     *
     * @param versionId benchmark版本ID
     * @return 排行榜排名数据
     */
    BenchmarkLeaderboardRankingVO getRankingList(Long versionId);

    /**
     * 获取模型详细评测结果
     *
     * @param versionId benchmark版本ID
     * @param modelName 模型名称
     * @return 模型详细评测结果
     */
    BenchmarkModelDetailVO getModelDetail(Long versionId, String modelName);
}
