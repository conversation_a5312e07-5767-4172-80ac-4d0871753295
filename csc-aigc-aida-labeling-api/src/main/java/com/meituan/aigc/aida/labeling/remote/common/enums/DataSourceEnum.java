package com.meituan.aigc.aida.labeling.remote.common.enums;

import lombok.Getter;

/**
 * 数据来源枚举
 *
 * <AUTHOR>
 * @date 2025/5/24
 */
@Getter
public enum DataSourceEnum {
    /**
     * case分析平台来源数据
     */
    CASE("case", 3, "Case分析平台"),

    /**
     * 线上拉取数据
     */
    ONLINE("online", 1, "线上拉取数据"),

    /**
     * 用户上传数据
     */
    UPLOAD("upload", 2, "用户上传数据");

    /**
     * 数据来源值
     */
    private final String value;

    /**
     * 枚举值
     */
    private final int code;

    /**
     * 数据来源描述
     */
    private final String description;

    DataSourceEnum(String value, int code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 数据来源值
     * @return 对应的枚举
     * @throws IllegalArgumentException 当value不匹配任何枚举时抛出
     */
    public static DataSourceEnum fromValue(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Data source value cannot be null");
        }

        for (DataSourceEnum source : DataSourceEnum.values()) {
            if (source.value.equalsIgnoreCase(value)) {
                return source;
            }
        }
        throw new IllegalArgumentException("Unknown data source: " + value);
    }

    /**
     * 判断value是否为有效的数据来源
     *
     * @param value 数据来源值
     * @return true如果是有效的数据来源，否则false
     */
    public static boolean isValidDataSource(String value) {
        if (value == null) {
            return false;
        }

        for (DataSourceEnum source : DataSourceEnum.values()) {
            if (source.value.equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }

    public static String getValueByCode(int code) {
        for (DataSourceEnum source : DataSourceEnum.values()) {
            if (source.code == code) {
                return source.value;
            }
        }
        return null;
    }
} 