package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-03-05 11:15
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelSubTaskDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 子任务名称
     */
    private String name;

    /**
     * 标注员ID
     */
    private String labelerMis;

    /**
     * 标注员名称
     */
    private String labelerName;

    /**
     * 状态(1:创建中 2:标注中 3:待回收 4:已回收 5:失败)
     */
    private Integer status;

    /**
     * 总数据量
     */
    private Integer totalDataCount;

    /**
     * 是否删除: 0-否, 1-是
     */
    private Boolean isDeleted;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
