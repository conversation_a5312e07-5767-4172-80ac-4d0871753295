package com.meituan.aigc.aida.benchmark.service.impl;

import com.meituan.aigc.aida.benchmark.common.config.BenchmarkLionConfig;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion;
import com.meituan.aigc.aida.benchmark.dao.repository.BenchmarkVersionRepository;
import com.meituan.aigc.aida.benchmark.dao.repository.LeaderboardDetailsRepository;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkLeaderboardRankingVO;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkModelDetailVO;
import com.meituan.aigc.aida.benchmark.service.LeaderboardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Precision;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.aigc.aida.benchmark.common.constant.BenchmarkConstant.*;

/**
 * Benchmark排行榜Service实现类
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
@Slf4j
@Service
public class LeaderboardServiceImpl implements LeaderboardService {

    @Resource
    private LeaderboardDetailsRepository leaderboardDetailsRepository;

    @Resource
    private BenchmarkLionConfig benchmarkLionConfig;

    @Resource
    private BenchmarkVersionRepository benchmarkVersionRepository;

    @Override
    public BenchmarkLeaderboardRankingVO getRankingList(Long versionId) {
        log.info("开始获取排行榜数据，版本ID: {}", versionId);

        BenchmarkVersion benchmarkVersion = benchmarkVersionRepository.getBenchmarkVersionById(versionId);
        if (Objects.isNull(benchmarkVersion)) {
            log.warn("版本ID为空，返回空结果");
            return BenchmarkLeaderboardRankingVO.builder()
                    .headList(Collections.emptyList())
                    .total(0)
                    .rankingDataList(Collections.emptyList())
                    .build();
        }

        // 查询排行榜详情数据
        List<BenchmarkLeaderboardDetails> detailsList = leaderboardDetailsRepository.listByVersionId(versionId);

        if (CollectionUtils.isEmpty(detailsList)) {
            log.info("未查询到版本{}的排行榜数据", versionId);
            return BenchmarkLeaderboardRankingVO.builder()
                    .headList(Collections.emptyList())
                    .total(0)
                    .rankingDataList(Collections.emptyList())
                    .build();
        }

        // 按模型名称分组
        Map<String, List<BenchmarkLeaderboardDetails>> modelGroupMap = detailsList.stream()
                .filter(detail -> Objects.nonNull(detail.getModelName()))
                .collect(Collectors.groupingBy(BenchmarkLeaderboardDetails::getModelName));

        // 构建表头列表
        List<String> headList = benchmarkLionConfig.getIndicatorConfigList(benchmarkVersion.getVersionName());

        // 构建排行榜数据列表
        List<BenchmarkLeaderboardRankingVO.RankingDataItem> rankingDataList = new ArrayList<>();

        for (Map.Entry<String, List<BenchmarkLeaderboardDetails>> entry : modelGroupMap.entrySet()) {
            String modelName = entry.getKey();
            List<BenchmarkLeaderboardDetails> modelDetails = entry.getValue();

            // 构建该模型的排名数据
            List<BenchmarkLeaderboardRankingVO.RankingData> rankingData = buildRankingData(modelDetails, headList);

            BenchmarkLeaderboardRankingVO.RankingDataItem dataItem = BenchmarkLeaderboardRankingVO.RankingDataItem.builder()
                    .id(modelName)
                    .modelName(modelName)
                    .rankingData(rankingData)
                    .build();

            rankingDataList.add(dataItem);
        }

        log.info("成功获取排行榜数据，模型数量: {}", rankingDataList.size());

        return BenchmarkLeaderboardRankingVO.builder()
                .headList(headList)
                .total(rankingDataList.size())
                .rankingDataList(rankingDataList)
                .build();
    }

    /**
     * 构建模型的排名数据
     *
     * @param modelDetails 模型详情列表
     * @param headList     表头列表
     * @return 排名数据列表
     */
    private List<BenchmarkLeaderboardRankingVO.RankingData> buildRankingData(
            List<BenchmarkLeaderboardDetails> modelDetails, List<String> headList) {

        // 将详情数据转换为Map，便于查找
        Map<String, BenchmarkLeaderboardDetails> detailMap = modelDetails.stream()
                .collect(Collectors.toMap(
                        BenchmarkLeaderboardDetails::getMetricName,
                        detail -> detail,
                        (existing, replacement) -> existing
                ));

        List<BenchmarkLeaderboardRankingVO.RankingData> rankingData = new ArrayList<>();

        for (String metricName : headList) {
            BenchmarkLeaderboardDetails detail = detailMap.get(metricName);
            Double dataValue = null;
            String dataValueStr = "";

            if (Objects.nonNull(detail) && Objects.nonNull(detail.getMetricValue())) {
                // 根据指标类型格式化数值
                // 对于百分比类型的指标，保留2位小数并加上%
                if (AVAILABILITY_RATE.equals(metricName) || FULL_SCORE_RATE.equals(metricName)) {
                    dataValue = detail.getMetricValue().doubleValue();
                    dataValueStr = dataValue.toString() + "%";
                } else if (TOTAL_SCORES.equals(metricName)) {
                    dataValue = detail.getMetricValue().doubleValue();
                    dataValueStr = dataValue.toString();
                }
            }

            BenchmarkLeaderboardRankingVO.RankingData data = BenchmarkLeaderboardRankingVO.RankingData.builder()
                    .dataName(metricName)
                    .dataValue(dataValue)
                    .dataString(dataValueStr)
                    .build();

            rankingData.add(data);
        }

        return rankingData;
    }

    @Override
    public BenchmarkModelDetailVO getModelDetail(Long versionId, String modelName) {
        log.info("开始获取模型详细评测结果，版本ID: {}, 模型名称: {}", versionId, modelName);

        if (Objects.isNull(versionId) || Objects.isNull(modelName) || modelName.trim().isEmpty()) {
            log.warn("参数不完整，versionId: {}, modelName: {}", versionId, modelName);
            return buildEmptyModelDetail();
        }

        // 查询指定版本和模型的详情数据
        List<BenchmarkLeaderboardDetails> modelDetails = leaderboardDetailsRepository
                .listByVersionIdAndModelName(versionId, modelName);

        if (CollectionUtils.isEmpty(modelDetails)) {
            log.info("未查询到模型{}在版本{}的评测数据", modelName, versionId);
            return buildEmptyModelDetail();
        }

        // 将详情数据转换为Map，便于查找
        Map<String, BenchmarkLeaderboardDetails> detailMap = modelDetails.stream()
                .collect(Collectors.toMap(
                        BenchmarkLeaderboardDetails::getMetricName,
                        detail -> detail,
                        (existing, replacement) -> existing
                ));

        // 获取综合分数
        Double totalScore = getMetricValue(detailMap, TOTAL_SCORES);

        // 获取可用率和满分率
        Double availability = getMetricValue(detailMap, AVAILABILITY_RATE);
        Double fullScoreRatio = getMetricValue(detailMap, FULL_SCORE_RATE);

        // 计算排名
        Integer rank = calculateModelRank(versionId, modelName, totalScore);

        // 构建表头
        List<String> headList = Arrays.asList("考点类别", "客观得分", "主观得分", "总分");

        // 构建数据列表
        List<List<String>> dataList = buildDetailDataList(detailMap);

        log.info("成功获取模型详细评测结果，模型: {}, 综合得分: {}, 排名: {}", modelName, totalScore, rank);

        return BenchmarkModelDetailVO.builder()
                .totalScore(totalScore)
                .rank(rank)
                .availability(availability)
                .fullScoreRatio(fullScoreRatio)
                .headList(headList)
                .dataList(dataList)
                .build();
    }

    /**
     * 构建空的模型详情结果
     */
    private BenchmarkModelDetailVO buildEmptyModelDetail() {
        return BenchmarkModelDetailVO.builder()
                .totalScore(0.0)
                .rank(0)
                .availability(0.0)
                .fullScoreRatio(0.0)
                .headList(Arrays.asList("考点类别", "客观得分", "主观得分", "总分"))
                .dataList(Collections.emptyList())
                .build();
    }

    /**
     * 从详情Map中获取指标值
     */
    private Double getMetricValue(Map<String, BenchmarkLeaderboardDetails> detailMap, String metricName) {
        BenchmarkLeaderboardDetails detail = detailMap.get(metricName);
        if (Objects.nonNull(detail) && Objects.nonNull(detail.getMetricValue())) {
            return detail.getMetricValue().doubleValue();
        }
        return 0.0;
    }

    /**
     * 计算模型排名
     */
    private Integer calculateModelRank(Long versionId, String modelName, Double totalScore) {
        if (Objects.isNull(totalScore) || Precision.equals(totalScore, 0.0, 1e-6)) {
            return 0;
        }

        try {
            // 查询该版本下所有模型的综合得分
            List<BenchmarkLeaderboardDetails> allVersionDetails = leaderboardDetailsRepository.listByVersionId(versionId);

            // 按模型分组，获取每个模型的综合得分
            Map<String, Double> modelScoreMap = allVersionDetails.stream()
                    .filter(detail -> TOTAL_SCORES.equals(detail.getMetricName()))
                    .filter(detail -> Objects.nonNull(detail.getMetricValue()))
                    .collect(Collectors.toMap(
                            BenchmarkLeaderboardDetails::getModelName,
                            detail -> detail.getMetricValue().doubleValue(),
                            (existing, replacement) -> existing
                    ));

            // 统计比当前模型分数高的模型数量
            long higherScoreCount = modelScoreMap.values().stream()
                    .filter(score -> score > totalScore)
                    .count();

            return (int) higherScoreCount + 1;
        } catch (Exception e) {
            log.error("计算模型排名失败，版本ID: {}, 模型: {}, 错误: {}", versionId, modelName, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 构建详细数据列表
     */
    private List<List<String>> buildDetailDataList(
            Map<String, BenchmarkLeaderboardDetails> detailMap) {

        List<List<String>> dataList = new ArrayList<>();

        // 排除系统指标，只处理考点数据
        Set<String> systemMetrics = new HashSet<>(Arrays.asList("可用率", "满分率"));

        // 获取所有考点数据
        List<BenchmarkLeaderboardDetails> examDetails = detailMap.values().stream()
                .filter(detail -> !systemMetrics.contains(detail.getMetricName()))
                .collect(Collectors.toList());

        // 按考点分组（这里简化处理，将每个考点作为一行数据）
        for (BenchmarkLeaderboardDetails detail : examDetails) {
            List<String> rowData = new ArrayList<>();

            // 考点类别（使用考点名称）
            rowData.add(detail.getMetricName());

            // 客观得分（使用实际分数）
            Double objectiveScore = Objects.nonNull(detail.getObjectiveScore()) ? detail.getObjectiveScore().doubleValue() : 0.0;
            rowData.add(String.format("%.0f", objectiveScore));

            // 主观得分（暂时设为0，可能需要其他数据源）
            Double subjectiveScore = Objects.nonNull(detail.getSubjectiveScore()) ? detail.getSubjectiveScore().doubleValue() : 0.0;
            rowData.add(String.format("%.0f", subjectiveScore));

            // 总分（客观得分 + 主观得分，这里暂时等于客观得分）
            rowData.add(String.format("%.0f", detail.getMetricValue()));

            dataList.add(rowData);
        }

        dataList.sort((row1, row2) -> {
            String name1 = row1.get(0);
            String name2 = row2.get(0);

            // 如果是"综合得分"，排在最前面
            if (TOTAL_SCORES.equals(name1)) {
                return -1;
            }
            if (TOTAL_SCORES.equals(name2)) {
                return 1;
            }

            // 其他情况按字符串自然排序
            return name1.compareTo(name2);
        });
        dataList.sort(Comparator.comparing(row -> row.get(0)));

        return dataList;
    }
} 