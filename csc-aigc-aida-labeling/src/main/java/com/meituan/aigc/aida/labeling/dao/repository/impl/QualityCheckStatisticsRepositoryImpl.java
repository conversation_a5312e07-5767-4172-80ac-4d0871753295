package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.meituan.aigc.aida.labeling.dao.mapper.QualityCheckStatisticsMapper;
import com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics;
import com.meituan.aigc.aida.labeling.dao.repository.QualityCheckStatisticsRepository;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonnelDetailQuery;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingAndCheckCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.BizQualityCheckQueryVO;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.BaseStatisticsDTO;
import org.apache.commons.collections4.CollectionUtils;

import org.springframework.stereotype.Repository;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DailyStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DailyBizStatisticsVO;
import java.util.List;

import javax.annotation.Resource;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 14:56
 * @Version: 1.0
 */
@Repository
public class QualityCheckStatisticsRepositoryImpl implements QualityCheckStatisticsRepository {

    @Resource
    private QualityCheckStatisticsMapper qualityCheckStatisticsMapper;

    @Override
    public List<DailyBizStatisticsVO> queryDailyTrends(DailyStatisticsDTO param) {
        return qualityCheckStatisticsMapper.queryDailyTrends(param);
    }


    @Override
    public LabelingAndCheckCountVO statisticalQualityCheckData(DataStatisticalQuery query) {
        return qualityCheckStatisticsMapper.statisticalQualityCheckData(query);
    }

    @Override
    public List<PersonnelDetailVO> queryPersonnelDetail(PersonnelDetailQuery query){
        return qualityCheckStatisticsMapper.queryPersonnelDetail(query);
    }

    @Override
    public List<BizQualityCheckQueryVO> querySingleBizQualityCheckRateTrends(BaseStatisticsDTO query) {
        return qualityCheckStatisticsMapper.querySingleBizQualityCheckRateTrends(query);
    }

    @Override
    public List<BizQualityCheckQueryVO> queryBizQualityCheckRate(BaseStatisticsDTO query) {
        return qualityCheckStatisticsMapper.queryBizQualityCheckRate(query);
    }

    @Override
    public void batchInsert(List<QualityCheckStatistics> qualityCheckStatisticsList) {
        if (CollectionUtils.isEmpty(qualityCheckStatisticsList)) {
            return;
        }
        qualityCheckStatisticsMapper.batchInsert(qualityCheckStatisticsList);
    }

}
