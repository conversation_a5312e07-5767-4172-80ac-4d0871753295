package com.meituan.aigc.aida.data.management.fetch.dao.repository.impl;

import com.dianping.zebra.util.StringUtils;
import com.meituan.aigc.aida.data.management.fetch.dao.mapper.DataFetchTaskMapper;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTaskExample;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchTaskRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/4/7 15:51
 * @Version: 1.0
 */
@Repository
public class DataFetchTaskRepositoryImpl implements DataFetchTaskRepository {

    @Resource
    private DataFetchTaskMapper dataFetchTaskMapper;

    @Override
    public void addDataFetchTask(DataFetchTask dataFetchTask) {
        if (Objects.isNull(dataFetchTask)) {
            return;
        }
        dataFetchTaskMapper.insert(dataFetchTask);
    }

    @Override
    public List<DataFetchTask> listTask(String taskName) {
        DataFetchTaskExample example = new DataFetchTaskExample();
        DataFetchTaskExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(taskName)) {
            criteria.andNameLike("%" + taskName + "%");
        }
        criteria.andIsDeleteEqualTo(Boolean.FALSE);
        example.setOrderByClause("update_time desc");
        return dataFetchTaskMapper.selectByExample(example);
    }

    @Override
    public DataFetchTask getTaskById(Long taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }
        return dataFetchTaskMapper.selectByPrimaryKey(taskId);
    }

    @Override
    public void updateByIdSelective(DataFetchTask dataFetchTask) {
        if (Objects.isNull(dataFetchTask)) {
            return;
        }
        dataFetchTaskMapper.updateByPrimaryKeySelective(dataFetchTask);
    }


}
