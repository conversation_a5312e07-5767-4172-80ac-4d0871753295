package com.meituan.aigc.aida.data.management.es.query;

import com.meituan.aigc.aida.common.enums.EsPropertyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.NestedSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;

import java.util.Map;
import java.util.Objects;

/**
 * ES查询转化类
 * 将查询条件对象转化为ES查询构建器
 *
 * <AUTHOR>
 * @date 2025/5/27
 */

@Slf4j
public class EsQueryTransform {

    /**
     * 构建查询条件
     *
     * @param condition 查询条件对象
     * @return ES查询构建器
     */
    public static QueryBuilder buildQueryBuilder(DatasetEsQueryCondition condition) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 处理精确匹配查询（term）
        if (MapUtils.isNotEmpty(condition.getTermCondition())) {
            condition.getTermCondition().forEach((field, value) -> {
                if (isNotNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.termQuery(field.getPath(), value));
                } else if (isNestedField(field) && StringUtils.isNotBlank(value)) {
                    BoolQueryBuilder nestedBoolQuery = QueryBuilders.boolQuery();
                    nestedBoolQuery.must(QueryBuilders.termQuery(field.getPath(), value));
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), nestedBoolQuery, ScoreMode.None));
                }
            });
        }

        // 处理多值查询（terms）
        if (MapUtils.isNotEmpty(condition.getTermsCondition())) {
            condition.getTermsCondition().forEach((field, values) -> {
                if (isNotNestedField(field) && CollectionUtils.isNotEmpty(values)) {
                    boolQuery.must(QueryBuilders.termsQuery(field.getPath(), values));
                } else if (isNestedField(field) && CollectionUtils.isNotEmpty(values)) {
                    BoolQueryBuilder nestedBoolQuery = QueryBuilders.boolQuery();
                    nestedBoolQuery.must(QueryBuilders.termsQuery(field.getPath(), values));
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), nestedBoolQuery, ScoreMode.None));
                }
            });
        }

        // 处理全文搜索查询（match）
        if (MapUtils.isNotEmpty(condition.getMatchCondition())) {
            condition.getMatchCondition().forEach((field, value) -> {
                if (isNotNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.matchQuery(field.getPath(), value));
                } else if (isNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), QueryBuilders.matchQuery(field.getPath(), value), ScoreMode.None));
                }
            });
        }

        // 处理短语匹配查询（match_phrase）
        if (MapUtils.isNotEmpty(condition.getMatchPhraseCondition())) {
            condition.getMatchPhraseCondition().forEach((field, value) -> {
                if (isNotNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.matchPhraseQuery(field.getPath(), value));
                } else if (isNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), QueryBuilders.matchPhraseQuery(field.getPath(), value), ScoreMode.None));
                }
            });
        }

        // 处理通配符查询（wildcard）
        if (MapUtils.isNotEmpty(condition.getWildcardCondition())) {
            condition.getWildcardCondition().forEach((field, value) -> {
                if (isNotNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.wildcardQuery(field.getPath(), value));
                } else if (isNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), QueryBuilders.wildcardQuery(field.getPath(), value), ScoreMode.None));
                }
            });
        }

        // 处理前缀查询（prefix）
        if (MapUtils.isNotEmpty(condition.getPrefixCondition())) {
            condition.getPrefixCondition().forEach((field, value) -> {
                if (isNotNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.prefixQuery(field.getPath(), value));
                } else if (isNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), QueryBuilders.prefixQuery(field.getPath(), value), ScoreMode.None));
                }
            });
        }

        // 处理正则表达式查询（regexp）
        if (MapUtils.isNotEmpty(condition.getRegexpCondition())) {
            condition.getRegexpCondition().forEach((field, value) -> {
                if (isNotNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.regexpQuery(field.getPath(), value));
                } else if (isNestedField(field) && StringUtils.isNotBlank(value)) {
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), QueryBuilders.regexpQuery(field.getPath(), value), ScoreMode.None));
                }
            });
        }

        // 处理范围查询（range）
        if (MapUtils.isNotEmpty(condition.getRangeCondition())) {
            condition.getRangeCondition().forEach((field, rangeCondition) -> {
                if (isNotNestedField(field) && rangeCondition != null) {
                    boolQuery.must(buildRangeQuery(field.getPath(), rangeCondition));
                } else if (isNestedField(field) && rangeCondition != null) {
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), buildRangeQuery(field.getPath(), rangeCondition), ScoreMode.None));
                }
            });
        }

        // 处理存在性查询（exists）
        if (MapUtils.isNotEmpty(condition.getExistsCondition())) {
            condition.getExistsCondition().forEach((field, exists) -> {
                if (isNotNestedField(field) && exists != null) {
                    if (exists) {
                        boolQuery.must(QueryBuilders.existsQuery(field.getPath()));
                    } else {
                        boolQuery.mustNot(QueryBuilders.existsQuery(field.getPath()));
                    }
                } else if (isNestedField(field) && exists != null) {
                    if (exists) {
                        boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), QueryBuilders.existsQuery(field.getPath()), ScoreMode.None));
                    } else {
                        boolQuery.mustNot(QueryBuilders.nestedQuery(field.getNestedPath(), QueryBuilders.existsQuery(field.getPath()), ScoreMode.None));
                    }
                }
            });
        }

        // 处理模糊查询（fuzzy）
        if (MapUtils.isNotEmpty(condition.getFuzzyCondition())) {
            condition.getFuzzyCondition().forEach((field, fuzzyCondition) -> {
                if (isNotNestedField(field) && fuzzyCondition != null && StringUtils.isNotBlank(fuzzyCondition.getValue())) {
                    boolQuery.must(buildFuzzyQuery(field.getPath(), fuzzyCondition));
                } else if (isNestedField(field) && fuzzyCondition != null && StringUtils.isNotBlank(fuzzyCondition.getValue())) {
                    boolQuery.must(QueryBuilders.nestedQuery(field.getNestedPath(), buildFuzzyQuery(field.getPath(), fuzzyCondition), ScoreMode.None));
                }
            });
        }

        // 处理查询字符串（query_string）
        if (StringUtils.isNotBlank(condition.getQueryString())) {
            boolQuery.must(buildQueryStringQuery(condition.getQueryString()));
        }

        // 如果没有任何查询条件，返回match_all查询
        if (!boolQuery.hasClauses()) {
            return QueryBuilders.matchAllQuery();
        }

        return boolQuery;
    }

    /**
     * 构建聚合查询
     *
     * @param searchSourceBuilder 搜索源构建器
     * @param condition           查询条件对象
     */
    public static void buildAggregationSearch(SearchSourceBuilder searchSourceBuilder, DatasetEsQueryCondition condition) {
        // 处理Terms聚合统计
        if (MapUtils.isNotEmpty(condition.getTermsAggConditions())) {
            condition.getTermsAggConditions().forEach((field, aggregationCondition) -> {
                if (isNotNestedField(field) && aggregationCondition != null && DatasetEsQueryCondition.AggregationType.TERMS.equals(aggregationCondition.getType())) {
                    searchSourceBuilder.aggregation(buildAggregation(field.getFieldName(), field.getPath(), aggregationCondition));
                } else if (isNestedField(field) && aggregationCondition != null && DatasetEsQueryCondition.AggregationType.TERMS.equals(aggregationCondition.getType())) {
                    searchSourceBuilder.aggregation(buildNestedAggregation(field.getNestedPath(), field.getPath(), aggregationCondition));
                }
            });
        }
    }

    /**
     * 构建Term查询
     *
     * @param field 字段名
     * @param value 查询值
     * @return Term查询构建器
     */
    public static TermQueryBuilder buildTermQuery(String field, String value) {
        return QueryBuilders.termQuery(field, value);
    }

    /**
     * 构建Terms查询
     *
     * @param field  字段名
     * @param values 查询值列表
     * @return Terms查询构建器
     */
    public static TermsQueryBuilder buildTermsQuery(String field, java.util.List<String> values) {
        return QueryBuilders.termsQuery(field, values);
    }

    /**
     * 构建Match查询
     *
     * @param field 字段名
     * @param value 查询值
     * @return Match查询构建器
     */
    public static MatchQueryBuilder buildMatchQuery(String field, String value) {
        return QueryBuilders.matchQuery(field, value);
    }

    /**
     * 构建MatchPhrase查询
     *
     * @param field 字段名
     * @param value 查询值
     * @return MatchPhrase查询构建器
     */
    public static MatchPhraseQueryBuilder buildMatchPhraseQuery(String field, String value) {
        return QueryBuilders.matchPhraseQuery(field, value);
    }

    /**
     * 构建Wildcard查询
     *
     * @param field 字段名
     * @param value 查询值（包含通配符）
     * @return Wildcard查询构建器
     */
    public static WildcardQueryBuilder buildWildcardQuery(String field, String value) {
        return QueryBuilders.wildcardQuery(field, value);
    }

    /**
     * 构建Prefix查询
     *
     * @param field 字段名
     * @param value 前缀值
     * @return Prefix查询构建器
     */
    public static PrefixQueryBuilder buildPrefixQuery(String field, String value) {
        return QueryBuilders.prefixQuery(field, value);
    }

    /**
     * 构建Regexp查询
     *
     * @param field 字段名
     * @param value 正则表达式
     * @return Regexp查询构建器
     */
    public static RegexpQueryBuilder buildRegexpQuery(String field, String value) {
        return QueryBuilders.regexpQuery(field, value);
    }

    /**
     * 构建Range查询
     *
     * @param field          字段名
     * @param rangeCondition 范围条件
     * @return Range查询构建器
     */
    public static RangeQueryBuilder buildRangeQuery(String field, DatasetEsQueryCondition.RangeCondition rangeCondition) {
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(field);

        if (rangeCondition.getFrom() != null) {
            rangeQuery.from(rangeCondition.getFrom());
        }
        if (rangeCondition.getTo() != null) {
            rangeQuery.to(rangeCondition.getTo());
        }

        rangeQuery.includeLower(rangeCondition.isIncludeLower());
        rangeQuery.includeUpper(rangeCondition.isIncludeUpper());

        return rangeQuery;
    }

    /**
     * 构建Exists查询
     *
     * @param field 字段名
     * @return Exists查询构建器
     */
    public static ExistsQueryBuilder buildExistsQuery(String field) {
        return QueryBuilders.existsQuery(field);
    }

    /**
     * 构建Fuzzy查询
     *
     * @param field          字段名
     * @param fuzzyCondition 模糊查询条件
     * @return Fuzzy查询构建器
     */
    public static FuzzyQueryBuilder buildFuzzyQuery(String field, DatasetEsQueryCondition.FuzzyCondition fuzzyCondition) {
        FuzzyQueryBuilder fuzzyQuery = QueryBuilders.fuzzyQuery(field, fuzzyCondition.getValue());

        if (StringUtils.isNotBlank(fuzzyCondition.getFuzziness())) {
            fuzzyQuery.fuzziness(Fuzziness.AUTO);
        }
        if (fuzzyCondition.getPrefixLength() != null) {
            fuzzyQuery.prefixLength(fuzzyCondition.getPrefixLength());
        }
        if (fuzzyCondition.getMaxExpansions() != null) {
            fuzzyQuery.maxExpansions(fuzzyCondition.getMaxExpansions());
        }
        if (fuzzyCondition.getTranspositions() != null) {
            fuzzyQuery.transpositions(fuzzyCondition.getTranspositions());
        }

        return fuzzyQuery;
    }

    /**
     * 构建聚合查询
     *
     * @param field                聚合字段名
     * @param path                 聚合路径
     * @param aggregationCondition 聚合条件
     * @return 聚合查询构建器
     */
    private static AggregationBuilder buildAggregation(String field, String path, DatasetEsQueryCondition.AggregationCondition aggregationCondition) {
        return AggregationBuilders.terms(field).field(path).size(aggregationCondition.getSize());
    }

    /**
     * 构建Nested聚合查询 - 用于nested字段的terms聚合
     *
     * @param nestedPath           嵌套路径
     * @param fieldPath            聚合字段路径
     * @param aggregationCondition 聚合条件
     * @return Nested聚合查询构建器
     */
    private static AggregationBuilder buildNestedAggregation(String nestedPath, String fieldPath, DatasetEsQueryCondition.AggregationCondition aggregationCondition) {
        // 创建nested聚合，使用nestedPath作为聚合名称
        String nestedAggName = nestedPath + "_nested";

        // 构建子聚合（terms聚合）
        String termsAggName = nestedPath + "_terms";
        TermsAggregationBuilder termsAgg = AggregationBuilders.terms(termsAggName)
                .field(fieldPath)
                .size(1000);

        // 创建nested聚合并添加子聚合
        return AggregationBuilders.nested(nestedAggName, nestedPath)
                .subAggregation(termsAgg);
    }

    /**
     * 构建QueryString查询
     *
     * @param queryString 查询字符串
     * @return QueryString查询构建器
     */
    public static QueryStringQueryBuilder buildQueryStringQuery(String queryString) {
        return QueryBuilders.queryStringQuery(queryString);
    }

    /**
     * 构建排序条件
     *
     * @param condition 查询条件对象
     * @return ES排序构建器数组
     */
    public static org.elasticsearch.search.sort.SortBuilder<?>[] buildSortBuilders(DatasetEsQueryCondition condition) {
        java.util.List<org.elasticsearch.search.sort.SortBuilder<?>> sortBuilders = new java.util.ArrayList<>();

        // 优先处理sortCondition中的多字段排序
        if (MapUtils.isNotEmpty(condition.getSortCondition())) {
            condition.getSortCondition().forEach((field, sortOrder) -> {
                if (field != null && sortOrder != null) {
                    org.elasticsearch.search.sort.SortBuilder<?> sortBuilder = buildSortBuilder(field, sortOrder);
                    if (sortBuilder != null) {
                        sortBuilders.add(sortBuilder);
                    }
                }
            });
        }

        // 如果没有sortCondition，使用默认的sortField和sortOrder
        if (sortBuilders.isEmpty() && StringUtils.isNotBlank(condition.getSortField())) {
            org.elasticsearch.search.sort.SortOrder sortOrder = condition.getSortOrder() == org.elasticsearch.search.sort.SortOrder.ASC
                    ? org.elasticsearch.search.sort.SortOrder.ASC
                    : org.elasticsearch.search.sort.SortOrder.DESC;

            sortBuilders.add(org.elasticsearch.search.sort.SortBuilders.fieldSort(condition.getSortField()).order(sortOrder));
        }

        return sortBuilders.toArray(new org.elasticsearch.search.sort.SortBuilder[0]);
    }

    /**
     * 构建单个字段的排序条件
     *
     * @param field     ES搜索属性
     * @param sortOrder 排序方向
     * @return ES排序构建器
     */
    private static org.elasticsearch.search.sort.SortBuilder<?> buildSortBuilder(EsSearchProperty field, SortOrder sortOrder) {
        if (Objects.isNull(field) || StringUtils.isBlank(field.getPath())) {
            return null;
        }

        SortOrder esSortOrder = sortOrder == SortOrder.ASC ? SortOrder.ASC : SortOrder.DESC;

        // 检查是否为nested字段
        if (isNestedField(field) && StringUtils.isNotBlank(field.getNestedPath())) {
            // 创建nested排序
            NestedSortBuilder nestedSort =
                    new NestedSortBuilder(field.getNestedPath());

            return SortBuilders.fieldSort(field.getPath())
                    .order(esSortOrder)
                    .setNestedSort(nestedSort);
        } else {
            // 普通字段排序
            return SortBuilders.fieldSort(field.getPath())
                    .order(esSortOrder);
        }
    }

    /**
     * 构建高亮条件
     *
     * @param condition 查询条件对象
     * @return ES高亮构建器
     */
    public static org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder buildHighlightBuilder(DatasetEsQueryCondition condition) {
        if (!Boolean.TRUE.equals(condition.getIncludeHighlight()) || CollectionUtils.isEmpty(condition.getHighlightFields())) {
            return null;
        }

        org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder highlightBuilder =
                new org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder();

        // 设置高亮标签
        highlightBuilder.preTags("<em>");
        highlightBuilder.postTags("</em>");

        // 添加高亮字段
        for (String field : condition.getHighlightFields()) {
            highlightBuilder.field(field);
        }

        return highlightBuilder;
    }

    /**
     * 记录查询信息（用于调试）
     *
     * @param condition 查询条件对象
     */
    public static void logQueryInfo(DatasetEsQueryCondition condition) {
        if (!log.isDebugEnabled()) {
            return;
        }

        StringBuilder logBuilder = new StringBuilder("ES查询条件信息:\n");
        logBuilder.append("数据源: ").append(condition.getDataSource()).append("\n");
        logBuilder.append("分页: ").append(condition.getPageNum()).append("/").append(condition.getPageSize()).append("\n");
        logBuilder.append("排序: ").append(condition.getSortField()).append(" ").append(condition.getSortOrder()).append("\n");

        if (MapUtils.isNotEmpty(condition.getTermCondition())) {
            logBuilder.append("精确匹配条件: ").append(condition.getTermCondition()).append("\n");
        }
        if (MapUtils.isNotEmpty(condition.getMatchCondition())) {
            logBuilder.append("全文搜索条件: ").append(condition.getMatchCondition()).append("\n");
        }
        if (MapUtils.isNotEmpty(condition.getRangeCondition())) {
            logBuilder.append("范围查询条件: ").append(condition.getRangeCondition()).append("\n");
        }
        if (StringUtils.isNotBlank(condition.getQueryString())) {
            logBuilder.append("查询字符串: ").append(condition.getQueryString()).append("\n");
        }

        log.debug(logBuilder.toString());
    }

    /**
     * 检查字段是否为嵌套字段
     *
     * @param field ES搜索属性
     * @return true if nested field
     */
    private static boolean isNestedField(EsSearchProperty field) {
        return field != null &&
                field.getFieldType() != null &&
                EsPropertyTypeEnum.NESTED.getCode().equals(field.getFieldType().getCode());
    }

    /**
     * 检查字段是否为非嵌套字段
     *
     * @param field ES搜索属性
     * @return true if not nested field
     */
    private static boolean isNotNestedField(EsSearchProperty field) {
        return field == null ||
                field.getFieldType() == null ||
                !EsPropertyTypeEnum.NESTED.getCode().equals(field.getFieldType().getCode());
    }

    /**
     * 构建Nested查询
     *
     * @param path        嵌套路径
     * @param nestedQuery 嵌套查询条件
     * @return Nested查询构建器
     */
    public static NestedQueryBuilder buildNestedQuery(String path, java.util.Map<String, Object> nestedQuery) {
        BoolQueryBuilder nestedBoolQuery = QueryBuilders.boolQuery();

        nestedQuery.forEach((field, value) -> {
            if (value != null) {
                String nestedField = path + "." + field;
                if (value instanceof String && ((String) value).contains("*")) {
                    nestedBoolQuery.must(QueryBuilders.wildcardQuery(nestedField, value.toString()));
                } else {
                    nestedBoolQuery.must(QueryBuilders.termQuery(nestedField, value));
                }
            }
        });

        return QueryBuilders.nestedQuery(path, nestedBoolQuery, org.apache.lucene.search.join.ScoreMode.Avg);
    }
}
