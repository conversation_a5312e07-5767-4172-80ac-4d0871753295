package com.meituan.aigc.aida.labeling.remote.dataset.result;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 数据集处理结果
 */
@Data
public class DataSetProcessResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 处理消息
     */
    private String message;
    
    /**
     * 数据集ID
     */
    private Long dataSetId;
    
    /**
     * 版本ID
     */
    private Long versionId;
    
    /**
     * 索引名称
     */
    private String indexName;
    
    /**
     * 处理数据总量
     */
    private Integer totalCount;
    
    /**
     * 成功处理数量
     */
    private Integer successCount;
    
    /**
     * 失败处理数量
     */
    private Integer failCount;
    
    /**
     * 额外结果信息
     */
    private Map<String, Object> extraData;
} 