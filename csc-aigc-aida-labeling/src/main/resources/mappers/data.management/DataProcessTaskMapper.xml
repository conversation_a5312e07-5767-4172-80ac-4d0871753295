<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.data.management.dataset.dao.mapper.DataProcessTaskMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="dataset_id" jdbcType="BIGINT" property="datasetId" />
    <result column="orignal_dataset_version" jdbcType="VARCHAR" property="orignalDatasetVersion" />
    <result column="cleaned_dataset_version" jdbcType="VARCHAR" property="cleanedDatasetVersion" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask">
    <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_name, dataset_id, orignal_dataset_version, cleaned_dataset_version, status, 
    total_count, creator_mis, creator_name, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    extra_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from data_process_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from data_process_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from data_process_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_process_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTaskExample">
    delete from data_process_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask">
    insert into data_process_task (id, task_name, dataset_id, 
      orignal_dataset_version, cleaned_dataset_version, 
      status, total_count, creator_mis, 
      creator_name, create_time, update_time, 
      extra_info)
    values (#{id,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{datasetId,jdbcType=BIGINT}, 
      #{orignalDatasetVersion,jdbcType=VARCHAR}, #{cleanedDatasetVersion,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{totalCount,jdbcType=INTEGER}, #{creatorMis,jdbcType=VARCHAR}, 
      #{creatorName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{extraInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask">
    insert into data_process_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="datasetId != null">
        dataset_id,
      </if>
      <if test="orignalDatasetVersion != null">
        orignal_dataset_version,
      </if>
      <if test="cleanedDatasetVersion != null">
        cleaned_dataset_version,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="totalCount != null">
        total_count,
      </if>
      <if test="creatorMis != null">
        creator_mis,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="datasetId != null">
        #{datasetId,jdbcType=BIGINT},
      </if>
      <if test="orignalDatasetVersion != null">
        #{orignalDatasetVersion,jdbcType=VARCHAR},
      </if>
      <if test="cleanedDatasetVersion != null">
        #{cleanedDatasetVersion,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="totalCount != null">
        #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="creatorMis != null">
        #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTaskExample" resultType="java.lang.Long">
    select count(*) from data_process_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update data_process_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.datasetId != null">
        dataset_id = #{record.datasetId,jdbcType=BIGINT},
      </if>
      <if test="record.orignalDatasetVersion != null">
        orignal_dataset_version = #{record.orignalDatasetVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.cleanedDatasetVersion != null">
        cleaned_dataset_version = #{record.cleanedDatasetVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.totalCount != null">
        total_count = #{record.totalCount,jdbcType=INTEGER},
      </if>
      <if test="record.creatorMis != null">
        creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update data_process_task
    set id = #{record.id,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      dataset_id = #{record.datasetId,jdbcType=BIGINT},
      orignal_dataset_version = #{record.orignalDatasetVersion,jdbcType=VARCHAR},
      cleaned_dataset_version = #{record.cleanedDatasetVersion,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      total_count = #{record.totalCount,jdbcType=INTEGER},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      creator_name = #{record.creatorName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update data_process_task
    set id = #{record.id,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      dataset_id = #{record.datasetId,jdbcType=BIGINT},
      orignal_dataset_version = #{record.orignalDatasetVersion,jdbcType=VARCHAR},
      cleaned_dataset_version = #{record.cleanedDatasetVersion,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      total_count = #{record.totalCount,jdbcType=INTEGER},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      creator_name = #{record.creatorName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask">
    update data_process_task
    <set>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="datasetId != null">
        dataset_id = #{datasetId,jdbcType=BIGINT},
      </if>
      <if test="orignalDatasetVersion != null">
        orignal_dataset_version = #{orignalDatasetVersion,jdbcType=VARCHAR},
      </if>
      <if test="cleanedDatasetVersion != null">
        cleaned_dataset_version = #{cleanedDatasetVersion,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="totalCount != null">
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="creatorMis != null">
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask">
    update data_process_task
    set task_name = #{taskName,jdbcType=VARCHAR},
      dataset_id = #{datasetId,jdbcType=BIGINT},
      orignal_dataset_version = #{orignalDatasetVersion,jdbcType=VARCHAR},
      cleaned_dataset_version = #{cleanedDatasetVersion,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      total_count = #{totalCount,jdbcType=INTEGER},
      creator_mis = #{creatorMis,jdbcType=VARCHAR},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      extra_info = #{extraInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask">
    update data_process_task
    set task_name = #{taskName,jdbcType=VARCHAR},
      dataset_id = #{datasetId,jdbcType=BIGINT},
      orignal_dataset_version = #{orignalDatasetVersion,jdbcType=VARCHAR},
      cleaned_dataset_version = #{cleanedDatasetVersion,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      total_count = #{totalCount,jdbcType=INTEGER},
      creator_mis = #{creatorMis,jdbcType=VARCHAR},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>