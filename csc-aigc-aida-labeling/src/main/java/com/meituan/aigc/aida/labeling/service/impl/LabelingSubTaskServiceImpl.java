package com.meituan.aigc.aida.labeling.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.common.squirrel.SquirrelClient;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.common.constant.RedisConstant;
import com.meituan.aigc.aida.labeling.common.enums.*;
import com.meituan.aigc.aida.labeling.common.enums.sub.task.LabelingDetailSampleStatusEnum;
import com.meituan.aigc.aida.labeling.common.utils.LabelResultCompareUtil;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.dao.model.*;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskPO;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskUnLabelPO;
import com.meituan.aigc.aida.labeling.dao.repository.*;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingOperationException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingServiceException;
import com.meituan.aigc.aida.labeling.helper.LabelingTaskConditionHelper;
import com.meituan.aigc.aida.labeling.param.*;
import com.meituan.aigc.aida.labeling.pojo.dto.DetailDataItemDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingDetailDataDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingResultSaveDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingSubTaskListDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.quality.check.TaskConditionDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.*;
import com.meituan.aigc.aida.labeling.service.AutoAnalysisToolManualLabelService;
import com.meituan.aigc.aida.labeling.service.LabelingSubTaskService;
import com.meituan.aigc.aida.labeling.service.common.PermissionService;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.meituan.aigc.aida.labeling.util.ThreadPoolUtil;
import com.meituan.aigc.aida.labeling.util.labeling.task.LabelingTaskUtil;
import com.meituan.csc.aigc.eval.remote.autoanalysistool.api.AutoAnalysisToolRemoteService;
import com.meituan.csc.aigc.eval.remote.autoanalysistool.dto.AutoAnalysisResultRequest;
import com.meituan.csc.aigc.eval.remote.common.dto.ServiceResponse;
import com.meituan.csc.aigc.eval.remote.common.enums.BizErrCodeEnum;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-03 16:32
 * @description 数据标注服务
 */
@Service
@Slf4j
public class LabelingSubTaskServiceImpl implements LabelingSubTaskService {

    private static final List<Integer> DONE_LABEL_STATUS = Arrays.asList(LabelingSubTaskStatus.WAITING_RECYCLED.getCode(),
            LabelingSubTaskStatus.RECYCLED.getCode(), LabelingSubTaskStatus.FAILED.getCode());

    @Autowired
    private SquirrelClient squirrelClient;

    @Autowired
    private LabelingSubTaskRepository labelingSubTaskRepository;

    @Autowired
    private LabelingDetailRepository labelingDetailRepository;

    @Autowired
    private LabelingQualityCheckItemRepository labelingQualityCheckItemRepository;

    @Autowired
    private LabelingTaskRawDataRepository labelingTaskRawDataRepository;

    @Autowired
    private LabelingTaskGroupRepository labelingTaskGroupRepository;

    @Autowired
    private LabelingTaskRepository labelingTaskRepository;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private PushElephantService pushElephantService;

    @Autowired
    private LionConfig lionConfig;

    @Autowired
    private LabelingTaskSessionDataRepository labelingTaskSessionDataRepository;

    @Autowired
    private AutoAnalysisToolManualLabelService autoAnalysisToolManualLabelService;

    @Autowired
    private AutoAnalysisToolRemoteService autoAnalysisToolRemoteService;

    /**
     * 自动标注结果问题标签标注项
     */
    private static final String QUESTION_LABEL = "问题标签";

    /**
     * 系统可用的CPU核心数
     */
    private static final int CPU_CORE_COUNT = 4;

    /**
     * 处理标注任务导出的线程池
     */
    private static final ExecutorService SEND_MANUAL_LABEL_RESULT_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "send-manual-label-result-%d");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transfer(LabelingSubTaskTransferParam param) {
        CheckUtil.paramCheck(Objects.nonNull(param), "参数不能为空");
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(param.getSubTaskId());
        CheckUtil.paramCheck(Objects.nonNull(labelingSubTask), "子任务不存在");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getTransferLabelerMis()), "转交人mis不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getTransferLabelerName()), "转交人名字不能为空");
        CheckUtil.paramCheck(labelingSubTask.getStatus() == LabelingSubTaskStatus.IN_LABELING.getCode(), "子任务状态不是标注中,无法转交");
        LabelingTask labelingTask = labelingTaskRepository.getById(labelingSubTask.getTaskId());
        CheckUtil.paramCheck(Objects.nonNull(labelingTask), "主任务不存在");
        String labelingManager = labelingTask.getLabelingManager();
        List<String> administratorsList = lionConfig.getAdministratorsList();
        CheckUtil.operationCheck(CollectionUtils.isNotEmpty(administratorsList), "管理员列表为空");
        String userMis = Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse(CommonConstants.FieldName.UNKNOWN);
        CheckUtil.operationCheck(Objects.equals(userMis, labelingManager) || administratorsList.contains(userMis), "无操作权限，仅标注负责人可进行转交操作");
        labelingSubTaskRepository.transferSubTask(param.getSubTaskId(), param.getTransferLabelerMis(), param.getTransferLabelerName());
        //更新待标注详情默认标注人
        labelingDetailRepository.batchUpdateLabelerBySubTaskIdAndStatus(param.getSubTaskId(), param.getTransferLabelerMis(), param.getTransferLabelerName(), LabelingDetailStatus.WAITING_LABELING.getCode());
        //更新分组名称
        List<LabelingSubTask> labelingSubTasks = labelingSubTaskRepository.listByGroupId(labelingSubTask.getGroupId());
        String groupName = getGroupName(labelingSubTasks);
        labelingTaskGroupRepository.updateGroupNameById(labelingSubTask.getGroupId(), groupName);
        //更新子任务名称
        labelingSubTasks.forEach(subTask -> {
            subTask.setName(labelingTask.getTaskName() + "-" + groupName);
            labelingSubTaskRepository.updateById(subTask);
        });
        //发送大象消息
        String message = String.format("系统已将标注任务：「%s」转交给您，请及时前往[训练系统|%s]进行处理。", labelingTask.getTaskName(), lionConfig.getSubTaskUrl());
        pushElephantService.pushElephant(message, param.getTransferLabelerMis());
        log.info("转交成功,子任务Id:{},mis:{},name:{}", param.getSubTaskId(), param.getTransferLabelerMis(), param.getTransferLabelerName());
    }

    @Override
    public LabelingDetailDataDTO pageDetailData(LabelingDetailParam param) {
        CheckUtil.paramCheck(Objects.nonNull(param) && Objects.nonNull(param.getSubTaskId()), "缺少必传参数");
        LabelDetailNumCountVO labelDetailNumCountVO = labelingDetailRepository.countNumBySubTask(param.getSubTaskId());
        LabelingDetailDataDTO labelingDetailDataDto = new LabelingDetailDataDTO();
        labelingDetailDataDto.setTotalCount(labelDetailNumCountVO.getTotalNum());
        labelingDetailDataDto.setLabeledCount(labelDetailNumCountVO.getDoneLabelingNum());
        labelingDetailDataDto.setUnLabelCount(labelDetailNumCountVO.getWaitLabelingNum());

        long total;
        List<DetailDataItemDTO> filterDataItems;
        // 没有高级筛选时使用数据库分页，有高级筛选时必须内存分页
        if (CollectionUtils.isEmpty(param.getAdvancedFilters())) {
            PageHelper.startPage(param.getPageNum(), param.getPageSize());
            filterDataItems = labelingDetailRepository.pageDetailDataBySubTaskId(param);
            if (CollectionUtils.isEmpty(filterDataItems)) {
                labelingDetailDataDto.setPageData(new PageData<>());
                return labelingDetailDataDto;
            }
            PageInfo<DetailDataItemDTO> pageInfo = new PageInfo<>(filterDataItems);
            total = pageInfo.getTotal();
            PageHelper.clearPage();
        } else {
            // 有高级筛选条件时，查询所有数据，内存分页
            List<DetailDataItemDTO> dataItems = labelingDetailRepository.pageDetailDataBySubTaskId(param);
            if (CollectionUtils.isEmpty(dataItems)) {
                labelingDetailDataDto.setPageData(new PageData<>());
                return labelingDetailDataDto;
            }
            Map<String, List<LabelingTaskSessionData>> sessionDataMap = new HashMap<>();
            boolean isModelContextCondition = LabelingTaskConditionHelper.hasModelContextCondition(param.getAdvancedFilters());
            // 如果条件中有模型上下文，则查找会话的上下文信息
            if (isModelContextCondition) {
                LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(param.getSubTaskId());
                List<String> sessionIdList = dataItems.stream().map(DetailDataItemDTO::getSessionId).collect(Collectors.toList());
                List<LabelingTaskSessionData> labelingTaskSessionDataList = labelingTaskSessionDataRepository.listByTaskIdAndSessionIdListWithBlobs(labelingSubTask.getTaskId(), sessionIdList);
                if (CollectionUtils.isNotEmpty(labelingTaskSessionDataList)) {
                    sessionDataMap = labelingTaskSessionDataList.stream().collect(Collectors.groupingBy(LabelingTaskSessionData::getSessionId));
                }
            }
            // 使用高级筛选条件进行过滤
            filterDataItems = LabelingTaskConditionHelper.filterLabelingCondition(dataItems, sessionDataMap, param.getAdvancedFilters());
            total = filterDataItems.size();
            // 进行内存分页
            filterDataItems = filterDataItems.stream()
                    .skip((long) (param.getPageNum() - 1) * param.getPageSize())
                    .limit(param.getPageSize())
                    .collect(Collectors.toList());
        }

        // 解析extra_info
        filterDataItems.forEach(detailDataItem -> {
            if (StringUtils.isNotBlank(detailDataItem.getExtraInfo())) {
                RawDataExtraInfo rawDataExtraInfo = JSONObject.parseObject(detailDataItem.getExtraInfo(), RawDataExtraInfo.class);
                if (Objects.nonNull(rawDataExtraInfo)) {
                    detailDataItem.setMessageId(rawDataExtraInfo.getLlmMessageId());
                    detailDataItem.setMessageContent(rawDataExtraInfo.getMessageContent());
                }
            }
        });

        labelingDetailDataDto.setPageData(new PageData<>(total, filterDataItems));
        return labelingDetailDataDto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveLabelingResult(LabelingResultSaveParam param) {
        List<LabelingResultSaveDTO> dataItems = param.getConversations();
        LabelingResultSaveDTO sessionDimension = param.getSessionDimension();
        // 参数校验
        checkLabelingResultSaveParam(dataItems, sessionDimension);
        // session维度数据先按照query处理
        if (CollectionUtils.isEmpty(dataItems)) {
            dataItems = new ArrayList<>();
        }
        if (Objects.nonNull(sessionDimension)) {
            dataItems.add(sessionDimension);
        }

        // 提前处理id为空的数据，因为query维度标注的数据是动态生成的
        dataItems = handleNullIdData(param, dataItems);
        // 查询本次要保存的标注详情内容
        List<Long> detailDataIds = dataItems.stream().map(LabelingResultSaveDTO::getId).collect(Collectors.toList());
        ZebraForceMasterHelper.forceMasterInLocalContext();
        List<LabelingDetail> labelingDetailList = labelingDetailRepository.listByIds(detailDataIds);
        if (CollectionUtils.isEmpty(labelingDetailList)) {
            throw new AidaTrainingServiceException("查询到标注详情为空");
        }
        // 校验是否已经被质检人查看，如果是，则不允许修改
        checkLabelingDetailStatus(labelingDetailList);

        ZebraForceMasterHelper.forceMasterInLocalContext();
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(labelingDetailList.get(0).getSubTaskId());
        if (Objects.isNull(labelingSubTask) || !LabelingSubTaskStatus.getLabelingStatus().contains(labelingSubTask.getStatus())) {
            throw new AidaTrainingOperationException("当前任务状态不可标注");
        }
        Map<Long, LabelingDetail> labelingDetailMap = labelingDetailList.stream()
                .collect(Collectors.toMap(LabelingDetail::getId, Function.identity()));
        // 获取原数据ID
        List<Long> rawDataIds = labelingDetailList.stream().map(LabelingDetail::getRawDataId).collect(Collectors.toList());
        List<LabelingTaskRawData> labelingTaskRawDataList = labelingTaskRawDataRepository.listByIds(rawDataIds);
        Map<Long, LabelingTaskRawData> rawDataMap = labelingTaskRawDataList.stream()
                .collect(Collectors.toMap(LabelingTaskRawData::getId, Function.identity()));
        // 处理标注结果
        dataItems.forEach(item -> labelingResultHandle(item, labelingDetailMap, rawDataMap));
        // 获取处理后的内容
        List<LabelingDetail> updateLabelingDetails = new ArrayList<>(labelingDetailMap.values());
        // 修改标注内容
        labelingDetailRepository.batchUpdateLabelingDetail(updateLabelingDetails);
        //更新任务状态到待回收  强制走主库，避免主从延迟
        ZebraForceMasterHelper.forceMasterInLocalContext();
        updateSubTaskStatusToWaitingRecycled(updateLabelingDetails.get(0).getSubTaskId());
        ZebraForceMasterHelper.clearLocalContext();
    }

    /**
     * 校验标注详情状态
     *
     * @param labelingDetailList 标注详情列表
     */
    private void checkLabelingDetailStatus(List<LabelingDetail> labelingDetailList) {
        labelingDetailList.forEach(detail -> {
            CheckUtil.operationCheck(detail.getViewStatus() == null || detail.getViewStatus() == DataViewStatusEnum.NOT_VIEW.getCode(), "质检人已查看该数据，不允许修改标注结果");
        });
    }

    private List<LabelingResultSaveDTO> handleNullIdData(LabelingResultSaveParam param, List<LabelingResultSaveDTO> dataItems) {
        List<LabelingResultSaveDTO> nullIdDataItems = dataItems.stream().filter(item -> item.getId() == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nullIdDataItems)) {
            return dataItems;
        }
        LabelingSubTask subTask = labelingSubTaskRepository.getById(param.getSubTaskId());
        Long taskId = subTask.getTaskId();
        boolean lock = tryLock(taskId);
        while (!lock) {
            try {
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("handleNullIdData error", e);
            }
            lock = tryLock(taskId);
        }
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<LabelingTaskRawData> rawDataList = labelingTaskRawDataRepository.listByTaskIdAndSessionIdList(taskId, Collections.singletonList(param.getSessionId()));
            ZebraForceMasterHelper.clearLocalContext();
            List<LabelingDetail> labelingDetailList = labelingDetailRepository.listBySubTaskIdAndSessionIdList(param.getSubTaskId(), Collections.singletonList(param.getSessionId()), null);
            // 取一条记录来复制新的query，优先取messageId为空的
            LabelingDetail labelingDetail = labelingDetailList.stream().filter(item -> StringUtils.isBlank(item.getMessageId())).findFirst().orElse(null);
            if (Objects.isNull(labelingDetail)) {
                labelingDetail = labelingDetailList.get(0);
            }
            LabelingTaskRawData rawData = labelingTaskRawDataRepository.getById(labelingDetail.getRawDataId());
            List<LabelingQualityCheckItem> labelingQualityCheckItemList = labelingQualityCheckItemRepository.listByDetailIdList(Collections.singletonList(labelingDetail.getId()));
            LabelingQualityCheckItem labelingQualityCheckItem = null;
            if (CollectionUtils.isNotEmpty(labelingQualityCheckItemList)) {
                labelingQualityCheckItem = labelingQualityCheckItemList.get(0);
            }
            for (LabelingResultSaveDTO item : nullIdDataItems) {
                Long rawDataId;
                // 统计当前消息ID的信息是否已经被写入到数据库，如果已经写入，则无需再写一次
                List<LabelingTaskRawData> messageList = rawDataList.stream().filter(data -> StringUtils.isNotBlank(data.getMessageId()) && data.getMessageId().equals(item.getThirdMessageId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(messageList)) {
                    // 如果大模型消息ID为空，优先覆盖，如果不为空，新建数据
                    LabelingTaskRawData newRawData = new LabelingTaskRawData();
                    if (Objects.isNull(rawData.getMessageId())) {
                        newRawData = rawData;
                    } else {
                        BeanUtils.copyProperties(rawData, newRawData);
                        newRawData.setId(null);
                    }
                    newRawData.setMessageId(item.getThirdMessageId());
                    RawDataExtraInfo rawDataExtraInfo;
                    if (StringUtils.isNotBlank(newRawData.getExtraInfo())) {
                        rawDataExtraInfo = JSON.parseObject(newRawData.getExtraInfo(), RawDataExtraInfo.class);
                    } else {
                        rawDataExtraInfo = new RawDataExtraInfo();
                    }
                    rawDataExtraInfo.setLlmMessageId(item.getMessageId());
                    rawDataExtraInfo.setMessageContent(item.getMessageContent());
                    if (CollectionUtils.isNotEmpty(item.getContext())) {
                        rawDataExtraInfo.setContext(item.getContext());
                    }
                    if (CollectionUtils.isNotEmpty(item.getSignalList())) {
                        rawDataExtraInfo.setSignalList(item.getSignalList());
                    }
                    newRawData.setExtraInfo(JSON.toJSONString(rawDataExtraInfo));
                    if (Objects.isNull(newRawData.getId())) {
                        labelingTaskRawDataRepository.batchInsert(Collections.singletonList(newRawData));
                    } else {
                        labelingTaskRawDataRepository.updateByPrimaryKeySelective(newRawData);
                    }
                    rawDataId = newRawData.getId();
                } else {
                    rawDataId = messageList.get(0).getId();
                }

                // 更新标注信息
                LabelingDetail newLabelingDetail = new LabelingDetail();
                if (Objects.isNull(labelingDetail.getMessageId())) {
                    newLabelingDetail = labelingDetail;
                } else {
                    BeanUtils.copyProperties(labelingDetail, newLabelingDetail);
                    newLabelingDetail.setId(null);
                    newLabelingDetail.setRawDataId(rawDataId);
                }
                newLabelingDetail.setMessageId(item.getThirdMessageId());
                if (Objects.isNull(newLabelingDetail.getId())) {
                    labelingDetailRepository.batchInsertDetail(Collections.singletonList(newLabelingDetail));
                } else {
                    labelingDetailRepository.updateById(newLabelingDetail);
                }
                item.setId(newLabelingDetail.getId());

                // 检查质检信息，如果已经分配，需要添加质检信息，已备后续质检
                if (Objects.nonNull(labelingQualityCheckItem)) {
                    LabelingQualityCheckItem newLabelingQualityCheckItem = new LabelingQualityCheckItem();
                    if (Objects.isNull(labelingQualityCheckItem.getMessageId())) {
                        newLabelingQualityCheckItem = labelingQualityCheckItem;
                    } else {
                        BeanUtils.copyProperties(labelingQualityCheckItem, newLabelingQualityCheckItem);
                        newLabelingQualityCheckItem.setId(null);
                        newLabelingQualityCheckItem.setRawDataId(rawDataId);
                        newLabelingQualityCheckItem.setLabelingDataId(newLabelingDetail.getId());
                    }
                    newLabelingQualityCheckItem.setMessageId(item.getThirdMessageId());
                    if (Objects.isNull(newLabelingQualityCheckItem.getId())) {
                        labelingQualityCheckItemRepository.batchInsert(Collections.singletonList(newLabelingQualityCheckItem));
                    } else {
                        labelingQualityCheckItemRepository.updateByIdSelective(newLabelingQualityCheckItem);
                    }
                }
            }
            return dataItems;
        } finally {
            unlock(taskId);
        }
    }

    private boolean tryLock(Long taskId) {
        return squirrelClient.setnx(RedisConstant.LABELING_TASK_WRITE_BACK_LOCK, taskId.toString(), taskId);
    }

    private void unlock(Long taskId) {
        squirrelClient.delete(RedisConstant.LABELING_TASK_WRITE_BACK_LOCK, taskId.toString());
    }

    public void updateCheckItemAbleStatus(List<LabelingDetail> labelingDetails) {
        if (CollectionUtils.isEmpty(labelingDetails)) {
            return;
        }
        // 查询是否有关联的质检任务，如果有，需要更新是否可质检的状态
        List<Long> detailIds = labelingDetails.stream().filter(labelingDetail -> Objects
                        .equals(LabelingDetailSampleStatusEnum.SAMPLE.getCode(), labelingDetail.getSampleStatus()))
                .map(LabelingDetail::getId).collect(Collectors.toList());
        List<LabelingQualityCheckItem> labelingQualityCheckItemList = labelingQualityCheckItemRepository.listByDetailIdList(detailIds);
        if (CollectionUtils.isNotEmpty(detailIds) && CollectionUtils.isEmpty(labelingQualityCheckItemList)) {
            log.warn("保存标注结果时，更新质检任务可检状态，查询到质检详情为空，标注详情ID：{}", JSON.toJSONString(detailIds));
            return;
        }
        if (CollectionUtils.isNotEmpty(labelingQualityCheckItemList)) {
            labelingQualityCheckItemList.forEach(labelingQualityCheckItem -> {
                labelingQualityCheckItem.setCheckableStatus(QualityCheckAbleStatus.CHECK_ABLE.getCode());
            });
            labelingQualityCheckItemRepository.batchUpdateById(labelingQualityCheckItemList);
        }
    }

    /**
     * 检查标注结果保存参数
     *
     * @param dataItems        数据项列表
     * @param sessionDimension 会话维度数据
     * @throws AidaTrainingCheckException 参数校验异常
     */
    private static void checkLabelingResultSaveParam(List<LabelingResultSaveDTO> dataItems, LabelingResultSaveDTO sessionDimension) {
        if (CollectionUtils.isEmpty(dataItems) && Objects.isNull(sessionDimension)) {
            throw new AidaTrainingCheckException("标注内容不能为空");
        }
        if (CollectionUtils.isNotEmpty(dataItems)) {
            dataItems.forEach(item -> {
                if (LabelResult.hasEmptyValue(item.getLabelingItems())) {
                    throw new AidaTrainingCheckException("必填项标注结果不能为空");
                }
            });
        }
        if (Objects.nonNull(sessionDimension)) {
            if (LabelResult.hasEmptyValue(sessionDimension.getLabelingItems())) {
                throw new AidaTrainingCheckException("必填项标注结果不能为空");
            }
        }
    }

    /**
     * 子任务列表
     *
     * @param param 查询参数
     * @return 子任务列表分页结果
     */
    @Override
    public PageData<LabelingSubTaskListDTO> subTasks(LabelingListParam param) {
        // 获取当前用户信息
        User user = UserUtils.getUser();
        String mis = user != null ? user.getLogin() : null;
        // 判断是否为管理员，管理员不限制查询结果
        boolean isAdmin = permissionService.isAdminUser();
        param.setLabelerMis(mis);
        param.setAdmin(isAdmin);
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        //根据任务ID、任务名称和标注人mis标注任务列表
        List<LabelingSubTaskPO> labelingSubTaskList = labelingSubTaskRepository.listByTaskIdAndNameAndLabelerMis(param);
        if (CollectionUtils.isEmpty(labelingSubTaskList)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<LabelingSubTaskPO> pageInfo = new PageInfo<>(labelingSubTaskList);
        PageHelper.clearPage();
        //查询子任务标注进度
        List<LabelingSubTaskUnLabelPO> subTaskPoList;
        List<Long> subTaskIds = labelingSubTaskList.stream().map(LabelingSubTaskPO::getId).collect(Collectors.toList());
        if (param.getAssignType() != null && AssignTypeEnum.SESSION.getCode() == param.getAssignType()) {
            subTaskPoList = labelingDetailRepository.listSessionCountBySubTaskId(subTaskIds);
        } else {
            subTaskPoList = labelingDetailRepository.listBySubTaskId(subTaskIds);
        }
        // 构建标注子任务进度映射表
        Map<Long, Integer> unlabeledMap = subTaskPoList.stream().collect(Collectors.toMap(LabelingSubTaskUnLabelPO::getId, LabelingSubTaskUnLabelPO::getUnLabelCount));
        //构建返回结果
        List<LabelingSubTaskListDTO> labelingSubTaskDTOList = labelingSubTaskList.stream()
                .map(item -> buildLabelingSubTaskListDTO(item, unlabeledMap))
                .collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), labelingSubTaskDTOList);
    }

    /**
     * 构建标注子任务列表DTO
     *
     * @param labelingSubTask 子任务PO对象
     * @param unlabeledMap    子任务ID和未完成标注的映射表
     * @return 返回构建好的标注子任务列表DTO
     */
    private LabelingSubTaskListDTO buildLabelingSubTaskListDTO(LabelingSubTaskPO labelingSubTask,
                                                               Map<Long, Integer> unlabeledMap) {
        TaskSourceEnum taskSourceEnum = TaskSourceEnum.getByCode(labelingSubTask.getDataSetSource());
        Integer labeledSize = unlabeledMap.get(labelingSubTask.getId());
        return LabelingSubTaskListDTO.builder()
                .id(labelingSubTask.getId())
                .taskName(labelingSubTask.getTaskName())
                .dataSetSource(Objects.isNull(taskSourceEnum) ? "未知" : taskSourceEnum.getValue())
                .dataTotalCount(labelingSubTask.getDataTotalCount())
                .labelingStatus(labelingSubTask.getLabelingStatus())
                .labelingProgress(labelingSubTask.getDataTotalCount() - (labeledSize == null ? 0 : labeledSize))
                .labelerMis(labelingSubTask.getLabelerMis())
                .labelerName(labelingSubTask.getLabelerName())
                .taskType(labelingSubTask.getTaskType())
                .dataType(labelingSubTask.getDataType())
                .createTime(labelingSubTask.getCreateTime())
                .updateTime(labelingSubTask.getUpdateTime())
                .build();
    }

    @Override
    public PageData<LabelingDetailBySessionVO> pageBySession(LabelingDetailBySessionQueryParam param) {
        //设置分页默认值
        int pageSize = (Objects.isNull(param.getPageSize()) || param.getPageSize() < 1) ? 1 : param.getPageSize();
        int pageNum = (Objects.isNull(param.getPageNum()) || param.getPageSize() < 1) ? 1 : param.getPageNum();

        // 先通过会话ID分组进行分页查询
        PageHelper.startPage(pageNum, pageSize);
        // 这个sql要注意，会话下所有任务完成才算标注完成
        List<String> sessionPageList = labelingDetailRepository.pageSessionBySubtaskAndSessionIdAndStatus(param.getSubTaskId(), param.getSessionId(), param.getLabelStatus());
        if (CollectionUtils.isEmpty(sessionPageList)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<String> pageInfo = new PageInfo<>(sessionPageList);
        PageHelper.clearPage();
        // 再查询每个分组下会话的详细信息
        List<LabelingDetail> labelingDetailList = labelingDetailRepository.listBySubTaskIdAndSessionIdList(param.getSubTaskId(), sessionPageList, param.getLabelStatus());
        Map<String, List<LabelingDetail>> sessionMap = labelingDetailList.stream().collect(Collectors.groupingBy(LabelingDetail::getSessionId));
        // 获取任务数据，查看任务类型来决定session维度还是query维度标注
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(param.getSubTaskId());
        LabelingTask labelingTask = labelingTaskRepository.getById(labelingSubTask.getTaskId());
        //获取原数据列表
        List<Long> rawDataList = labelingDetailList.stream().map(LabelingDetail::getRawDataId).collect(Collectors.toList());
        List<LabelingTaskRawData> labelingTaskRawDataList = labelingTaskRawDataRepository.listByIds(rawDataList);
        Map<Long, LabelingTaskRawData> labelingTaskRawDataMap = labelingTaskRawDataList.stream().collect(Collectors.toMap(LabelingTaskRawData::getId, Function.identity()));
        // 组装返回结果
        List<LabelingDetailBySessionVO> labelingDetailBySessionVOList = new ArrayList<>();
        Map<String, List<LabelingTaskSessionData>> sessionDataMap = new HashMap<>();
        // 线上会话分析补充是否有上下文，防止多次调用
        if (LabelTaskDataType.SESSION_ANNOTATION.getCode() == labelingTask.getDataType() || LabelTaskDataType.QUERY_ANNOTATION.getCode() == labelingTask.getDataType()) {
            List<LabelingTaskSessionData> labelingTaskSessionDataList = labelingTaskSessionDataRepository.listByTaskIdAndSessionIdListWithBlobs(labelingSubTask.getTaskId(), sessionPageList);
            if (CollectionUtils.isNotEmpty(labelingTaskSessionDataList)) {
                sessionDataMap = labelingTaskSessionDataList.stream().collect(Collectors.groupingBy(LabelingTaskSessionData::getSessionId));
            }
        }
        Map<String, List<LabelingTaskSessionData>> finalSessionDataMap = sessionDataMap;
        sessionPageList.forEach(sessionId -> labelingDetailBySessionVOList
                .add(getLabelingDetailBySession(sessionId, sessionMap, labelingTask, labelingTaskRawDataMap, finalSessionDataMap)));
        return new PageData<>(pageInfo.getTotal(), labelingDetailBySessionVOList);
    }

    @Override
    public LabelDetailNumCountVO countNumBySubTask(Long subTaskId) {
        return labelingDetailRepository.countNumBySubTask(subTaskId);
    }

    @Override
    public List<TaskConditionDTO> getLabelingCondition(Long labelingSubTaskId) {
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(labelingSubTaskId);
        CheckUtil.operationCheck(Objects.nonNull(labelingSubTask), "标注任务不存在");
        LabelingTask labelingTask = labelingTaskRepository.getById(labelingSubTask.getTaskId());
        List<TaskConditionDTO> allCondition = new ArrayList<>();
        // 大模型消息检索条件
        List<TaskConditionDTO> llmCondition = LabelingTaskConditionHelper.getLlmCondition(labelingTask.getDataType());
        if (CollectionUtils.isNotEmpty(llmCondition)) {
            allCondition.addAll(llmCondition);
        }
        // 映射数据查询条件
        List<TaskConditionDTO> mappedCondition = LabelingTaskConditionHelper.getMappedCondition(labelingTask.getDataType());
        if (CollectionUtils.isNotEmpty(mappedCondition)) {
            allCondition.addAll(mappedCondition);
        }
        // 信号查询条件
        List<String> signalList = getSignalList(labelingSubTask, labelingTask.getDataType());
        List<TaskConditionDTO> signalCondition = LabelingTaskConditionHelper.getSignalCondition(labelingTask.getDataType(), signalList);
        if (CollectionUtils.isNotEmpty(signalCondition)) {
            allCondition.addAll(signalCondition);
        }
        // 标注项查询条件
        List<String> labelItemList = getLabelItemList(labelingTask);
        List<TaskConditionDTO> labelItemCondition = LabelingTaskConditionHelper.getLabelItemCondition(labelItemList);
        if (CollectionUtils.isNotEmpty(labelItemCondition)) {
            allCondition.addAll(labelItemCondition);
        }
        return allCondition;
    }

    @Override
    public void saveContext(LabelingTaskContextParam param) {
        CheckUtil.paramCheck(Objects.nonNull(param), "参数不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getSubTaskId()), "子任务ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getSessionId()), "会话ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getContext()), "上下文不能为空");
        ZebraForceMasterHelper.forceMasterInLocalContext();
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(param.getSubTaskId());
        List<LabelingTaskSessionData> labelingTaskSessionData = labelingTaskSessionDataRepository.listByTaskIdAndSessionIdList(labelingSubTask.getTaskId(), Collections.singletonList(param.getSessionId()));
        ZebraForceMasterHelper.clearLocalContext();
        LabelingTaskSessionData sessionData;
        // 如果已有数据，直接修改，如果没有数据，新建一条
        if (CollectionUtils.isEmpty(labelingTaskSessionData)) {
            sessionData = new LabelingTaskSessionData();
            sessionData.setTaskId(labelingSubTask.getTaskId());
            sessionData.setSessionId(param.getSessionId());
            sessionData.setContext(JSONObject.toJSONString(param.getContext()));
            sessionData.setCreateTime(new Date());
            sessionData.setUpdateTime(new Date());
            labelingTaskSessionDataRepository.insert(sessionData);
        } else {
            sessionData = labelingTaskSessionData.get(0);
            sessionData.setContext(JSONObject.toJSONString(param.getContext()));
            labelingTaskSessionDataRepository.updateById(sessionData);
        }
    }

    /**
     * 获取标注项列表
     *
     * @param labelingTask 标注任务
     * @return 标注项列表
     */
    private List<String> getLabelItemList(LabelingTask labelingTask) {
        List<List<LabelResult>> labelingResult = JSON.parseObject(labelingTask.getLabelingConfig(), new TypeReference<List<List<LabelResult>>>() {
        });
        if (CollectionUtils.isEmpty(labelingResult)) {
            return new ArrayList<>();
        }
        Set<String> labelItemSet = new HashSet<>();
        labelingResult.forEach(labelResultList -> {
            labelResultList.forEach(labelResult -> {
                labelItemSet.add(labelResult.getName());
            });
        });
        return new ArrayList<>(labelItemSet);
    }

    /**
     * 获取信号列表
     *
     * @param labelingSubTask 标注子任务
     * @param dataType        数据类型
     * @return 信号列表
     */
    private List<String> getSignalList(LabelingSubTask labelingSubTask, Integer dataType) {
        if (dataType != LabelTaskDataType.SFT_FINE_TUNING.getCode() && dataType != LabelTaskDataType.DPO_ALIGNMENT.getCode()) {
            return new ArrayList<>();
        }
        List<LabelingDetail> labelingDetailList = labelingDetailRepository.listBySubTaskId(labelingSubTask.getId());
        List<Long> rawDataIdList = labelingDetailList.stream().map(LabelingDetail::getRawDataId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rawDataIdList)) {
            return new ArrayList<>();
        }
        List<LabelingTaskRawData> rawDataList = labelingTaskRawDataRepository.listByIds(rawDataIdList);
        Set<String> signalSet = new HashSet<>();
        // 训练数据信号是excel上传，取表头字段
        rawDataList.forEach(rawData -> {
            if (StringUtils.isNotBlank(rawData.getRawDataHeaders())) {
                List<String> headers = JSONObject.parseArray(rawData.getRawDataHeaders(), String.class);
                if (CollectionUtils.isNotEmpty(headers)) {
                    signalSet.addAll(headers);
                }
            }
        });
        return new ArrayList<>(signalSet);
    }

    private LabelingDetailBySessionVO getLabelingDetailBySession(String sessionId, Map<String, List<LabelingDetail>> sessionMap, LabelingTask labelingTask, Map<Long, LabelingTaskRawData> labelingTaskRawDataMap, Map<String, List<LabelingTaskSessionData>> sessionDataMap) {
        List<LabelingDetail> labelingDetails = sessionMap.get(sessionId);
        LabelingDetailBySessionVO labelingDetailBySession = new LabelingDetailBySessionVO();
        // session维度和query维度指标放置的位置不一样
        labelingDetailBySession.setSessionId(sessionId);
        if (MapUtils.isNotEmpty(sessionDataMap) && sessionDataMap.containsKey(sessionId) && CollectionUtils.isNotEmpty(sessionDataMap.get(sessionId)) && StringUtils.isNotBlank(sessionDataMap.get(sessionId).get(0).getContext())) {
            labelingDetailBySession.setHasContext(true);
        }
        labelingDetailBySession.setUpdateTime(DateUtil.formatDate(labelingDetails.get(0).getSessionTime(), CommonConstants.DatePattern.DATE_TIME));
        labelingDetailBySession.setLabelingItems(JSON.parseObject(labelingTask.getLabelingConfig(), new TypeReference<List<List<LabelResult>>>() {
        }));
        if (CollectionUtils.isNotEmpty(labelingDetails)) {
            // 一个会话下，只要有一个记录被查看过，那么整个记录就不能再进行标注
            labelingDetailBySession.setCanLabel(CollectionUtils.isEmpty(labelingDetails.stream().filter(labelingDetail -> labelingDetail.getViewStatus() != null && labelingDetail.getViewStatus() == DataViewStatusEnum.VIEWED.getCode()).collect(Collectors.toList())));
        }
        // session维度标注在创建任务时会去重，保证一定只有一个会话，后续即使扩展，也要保证多条记录中的指标是一样的
        if (labelingTask.getDataType() == LabelTaskDataType.SESSION_ANNOTATION.getCode()) {
            String labelingItemsResult = labelingDetails.get(0).getLabelingItemsResult();
            List<List<LabelResult>> labelResult = JSON.parseObject(labelingItemsResult, new TypeReference<List<List<LabelResult>>>() {});
            
            try {
                // 获取并修改"问题标签"标注项
                if (labelingTask.getTaskSource().equals(TaskSourceEnum.CASE_ANALYSIS.getCode())
                        && labelingDetails.get(0).getStatus().equals(LabelingDetailStatus.WAITING_LABELING.getCode())) {
                    log.info("获取自动标注结果问题标签标注项, 会话ID:{}", sessionId);
                    String autoAnalysisResult = getAutoAnalysisResult(sessionId, labelingTask);
                    if (StringUtils.isNotBlank(autoAnalysisResult)) {
                        for (List<LabelResult> resultGroup : labelResult) {
                            for (LabelResult result : resultGroup) {
                                if (QUESTION_LABEL.equals(result.getName())) {
                                    result.setValue(autoAnalysisResult);
                                    break;
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取自动标注结果问题标签标注项异常,任务ID:{},异常信息：", labelingTask.getId(), e);
            }
            
            labelingDetailBySession.setSessionDimension(LabelingDetailCommonVO.builder()
                    .id(labelingDetails.get(0).getId())
                    .labelingItems(labelResult)
                    .build());
        } else {
            List<LabelingDetailInfoVO> conversations = labelingDetails.stream().map(labelingDetail -> {
                LabelingDetailInfoVO labelingDetailInfoVO = new LabelingDetailInfoVO();
                labelingDetailInfoVO.setId(labelingDetail.getId());
                labelingDetailInfoVO.setThirdMessageId(labelingDetail.getMessageId());
                if (labelingTaskRawDataMap.containsKey(labelingDetail.getRawDataId()) && StringUtils.isNotBlank(labelingTaskRawDataMap.get(labelingDetail.getRawDataId()).getExtraInfo())) {
                    RawDataExtraInfo rawDataExtraInfo = JSON.parseObject(labelingTaskRawDataMap.get(labelingDetail.getRawDataId()).getExtraInfo(), RawDataExtraInfo.class);
                    labelingDetailInfoVO.setMessageContent(rawDataExtraInfo.getMessageContent());
                    labelingDetailInfoVO.setMessageId(rawDataExtraInfo.getLlmMessageId());
                }
                labelingDetailInfoVO.setLabelStatus(labelingDetail.getStatus());
                labelingDetailInfoVO.setRawDataContent(Optional
                        .ofNullable(LabelingTaskUtil.filterNullHeadValue(labelingTaskRawDataMap.get(labelingDetail.getRawDataId()).getRawDataContent()))
                        .orElse(""));
                labelingDetailInfoVO.setRawDataHeaders(Optional.ofNullable(labelingTaskRawDataMap.get(labelingDetail.getRawDataId()))
                        .map(labelingTaskRawData -> LabelingTaskUtil.filterNullHeader(labelingTaskRawData.getRawDataHeaders()))
                        .orElse(""));
                if (StringUtils.isNotBlank(labelingDetail.getModifiedRawDataMappedContent())) {
                    labelingDetailInfoVO.setRawDataMappedContent(labelingDetail.getModifiedRawDataMappedContent());
                } else {
                    labelingDetailInfoVO.setRawDataMappedContent(Optional
                            .ofNullable(labelingTaskRawDataMap.get(labelingDetail.getRawDataId()).getRawDataMappedContent())
                            .orElse(""));
                }
                String labelingItemsResult = labelingDetail.getLabelingItemsResult();
                labelingDetailInfoVO.setLabelingItems(JSON.parseObject(labelingItemsResult, new TypeReference<List<List<LabelResult>>>() {
                }));
                return labelingDetailInfoVO;
            }).collect(Collectors.toList());
            // query维度标注当前是以session进行上传，所以可能存在messageId为空的场景，所以要进行过滤
            if (labelingTask.getDataType() == LabelTaskDataType.QUERY_ANNOTATION.getCode()) {
                conversations = conversations.stream().filter(conversation -> StringUtils.isNotBlank(conversation.getThirdMessageId())).collect(Collectors.toList());
            }
            labelingDetailBySession.setConversations(conversations);
        }
        return labelingDetailBySession;
    }

    /**
     * 获取自动标注结果
     * @param sessionId 会话ID
     * @param labelingTask 标注任务
     * @return 自动标注结果
     */
    private String getAutoAnalysisResult(String sessionId, LabelingTask labelingTask) {
        AutoAnalysisResultRequest autoAnalysisResultRequest = AutoAnalysisResultRequest.builder()
                .sessionId(sessionId)
                .manualLabelTaskId(labelingTask.getId())
                .build();
        ServiceResponse<String> autoAnalysisResultResponse = autoAnalysisToolRemoteService.getAutoLabelResultBySessionId(autoAnalysisResultRequest);
        if (autoAnalysisResultResponse.getCode() == BizErrCodeEnum.SUCCESS.getCode() && StringUtils.isNotBlank(autoAnalysisResultResponse.getData())) {
            log.info("sessionId:{} ,问题标签标注项自动标注结果:{}", sessionId, autoAnalysisResultResponse.getData());
            return autoAnalysisResultResponse.getData();
        }
        return null;
    }

    public void updateSubTaskStatusToWaitingRecycled(Long subTaskId) {
        /* 子任务处理开始 */
        LabelDetailNumCountVO labelDetailNumCountVO = labelingDetailRepository.countNumBySubTask(subTaskId);
        //子任务未标注完，不处理
        if (labelDetailNumCountVO.getWaitLabelingNum() > 0) {
            return;
        }
        log.info("子任务标注完成，开始修改子任务状态");
        //修改子任务状态为待回收
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(subTaskId);
        if (Objects.isNull(labelingSubTask)) {
            log.warn("修改子任务状态，未查询到子任务");
            return;
        }
        //只有标注中可以到待回收
        if (LabelingSubTaskStatus.IN_LABELING.getCode() == labelingSubTask.getStatus()) {
            labelingSubTask.setStatus(LabelingSubTaskStatus.WAITING_RECYCLED.getCode());
            labelingSubTask.setUpdateTime(new Date());
            labelingSubTaskRepository.updateById(labelingSubTask);
        }
        /* 分组处理 */
        updateGroupStatusToWaitingRecycled(labelingSubTask.getGroupId());
    }

    public void updateGroupStatusToWaitingRecycled(Long groupId) {
        //查询分组下所有子任务
        List<LabelingSubTask> labelingSubTasksByGroup = labelingSubTaskRepository.listByGroupId(groupId);
        if (CollectionUtils.isEmpty(labelingSubTasksByGroup)) {
            return;
        }
        //标注未完成的子任务
        List<LabelingSubTask> waitLabelingSubTasksByGroup = labelingSubTasksByGroup.stream()
                .filter(subTask -> !DONE_LABEL_STATUS.contains(subTask.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waitLabelingSubTasksByGroup)) {
            return;
        }
        log.info("分组标注完成，开始修改分组状态");
        //查询子任务所属分组
        LabelingTaskGroup labelingTaskGroup = labelingTaskGroupRepository.getLabelGroupById(groupId);
        if (Objects.isNull(labelingTaskGroup)) {
            log.warn("修改分组状态，未查询到分组");
            return;
        }
        labelingTaskGroup.setUpdateTime(new Date());
        //只有创建中可以到待回收
        if (LabelingTaskGroupStatus.CREATING.getCode() == labelingTaskGroup.getRecycleStatus()) {
            labelingTaskGroup.setRecycleStatus(LabelingTaskGroupStatus.PENDING_RECYCLE.getCode());
            labelingTaskGroup.setConsistencyRate(labelingDetailRepository.countConsistencyRateBySubTaskId(labelingSubTasksByGroup.get(0).getId()));
            labelingTaskGroupRepository.updateGroup(labelingTaskGroup);
        } else {
            //只更新一致率
            labelingTaskGroup.setConsistencyRate(labelingDetailRepository.countConsistencyRateBySubTaskId(labelingSubTasksByGroup.get(0).getId()));
            labelingTaskGroupRepository.updateGroup(labelingTaskGroup);
        }
        /* 主任务处理 */
        updateTaskStatusToWaitingRecycled(labelingTaskGroup.getTaskId());
    }

    public void updateTaskStatusToWaitingRecycled(Long taskId) {
        //查询主任务下所有分组
        List<LabelingTaskGroup> labelTaskGroups = labelingTaskGroupRepository.listAllLabelGroupByTaskId(taskId);
        if (CollectionUtils.isEmpty(labelTaskGroups)) {
            return;
        }
        //标注未完成的分组
        List<LabelingTaskGroup> waitLabelingGroups = labelTaskGroups.stream()
                .filter(group -> group.getRecycleStatus() == LabelingTaskGroupStatus.CREATING.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waitLabelingGroups)) {
            return;
        }
        log.info("主任务标注完成，开始修改主任务状态到待回收");
        //修改主任务状态为待回收 只有标注中可以到待回收
        LabelingTask labelingTask = labelingTaskRepository.getById(taskId);
        if (Objects.isNull(labelingTask)) {
            log.warn("修改主任务状态到待回收，未查询到主任务");
            return;
        }
        if (LabelingTaskStatus.IN_LABELING.getCode() == labelingTask.getLabelingStatus()) {
            labelingTaskRepository.updateTaskStatusById(labelingTask.getId(), LabelingTaskStatus.COMPLETE.getCode(), new Date());
            try {
                pushElephantService.pushElephant(String.format("标注任务「%s」已完成，请及时前往[训练系统|%s]回收。", labelingTask.getTaskName(), lionConfig.getCreateDataSetNotifyElephantUrl()), labelingTask.getLabelingManager());
            } catch (Exception e) {
                log.error("标注任务完成发送回收消息异常,任务ID:{},异常信息：", taskId, e);
            }
        }

        // 通过mafka异步发送人工标注结果
        try {
            if (TaskSourceEnum.CASE_ANALYSIS.getCode() == labelingTask.getTaskSource()) {
                SEND_MANUAL_LABEL_RESULT_EXECUTOR.submit(() -> {
                    // 发送人工标注结果
                    autoAnalysisToolManualLabelService.sendManualLabelingResults(labelingTask.getId());
                });
            }
        } catch (Exception e) {
            log.error("发送人工标注结果mafka消息异常,任务ID:{},异常信息：", taskId, e);
        }
    }

    public void labelingResultHandle(LabelingResultSaveDTO item, Map<Long, LabelingDetail> labelingDetailMap, Map<Long, LabelingTaskRawData> rawDataMap) {
        Long rawDataId = Optional.ofNullable(labelingDetailMap.get(item.getId())).map(LabelingDetail::getRawDataId).orElse(-1L);
        LabelingDetail labelingDetail = labelingDetailMap.get(item.getId());
        if (Objects.isNull(labelingDetail)) {
            log.warn("标注内容保存，未查询到标注详情");
            return;
        }
        // 比较原数据内容映射内容是否被修改, 不一致时需要更新表
        if (!LabelResultCompareUtil.compareRawDataMappedContent(item.getRawDataMappedContent(), rawDataMap.get(rawDataId), labelingDetail)) {
            labelingDetail.setModifiedRawDataMappedContent(item.getRawDataMappedContent());
        }
        labelingDetail.setLabelingItemsResult(JSON.toJSONString(item.getLabelingItems()));
        labelingDetail.setLabelerMis(Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse("unknown"));
        labelingDetail.setLabelerName(Optional.ofNullable(UserUtils.getUser()).map(User::getName).orElse(labelingDetail.getLabelerMis()));
        labelingDetail.setUpdateTime(new Date());
        // 待标注改为已标注
        if (LabelingDetailStatus.WAITING_LABELING.getCode() == labelingDetail.getStatus()) {
            labelingDetail.setStatus(LabelingDetailStatus.LABELED.getCode());
            // 给首次标注时间赋值
            labelingDetail.setFirstLabelingTime(new Date());
        }
        // 查询分组内其他子任务该数据是否完成标注
        // 查询所属分组内所有子任务ID
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(labelingDetail.getSubTaskId());
        if (Objects.isNull(labelingSubTask)) {
            throw new AidaTrainingServiceException("子任务不存在，任务ID：" + labelingDetail.getSubTaskId());
        }
        List<Long> subTaskIds = labelingSubTaskRepository.listSubTaskIdByGroupId(
                Optional.ofNullable(labelingSubTask.getGroupId()).orElse(-1L), labelingDetail.getSubTaskId());
        // 分组内只有一个子任务,不用比较,直接设为一致
        if (CollectionUtils.isEmpty(subTaskIds)) {
            labelingDetail.setComparItemsConsistent(CompareItemsConsistent.YES.getCode());
            // 更新质检任务可检状态
            updateCheckItemAbleStatus(Collections.singletonList(labelingDetail));
            return;
        }
        // 查询分组内其他子任务待标注的数据
        List<LabelingDetail> rawLabelingDetails = labelingDetailRepository.listBySubTaskIdRawDataIdAndStatus(subTaskIds, labelingDetail.getRawDataId(), null);
        if (CollectionUtils.isNotEmpty(rawLabelingDetails)) {
            List<LabelingDetail> waitingRawLabelingDetails = rawLabelingDetails.stream()
                    .filter(detail -> LabelingDetailStatus.WAITING_LABELING.getCode() == detail.getStatus())
                    .collect(Collectors.toList());
            // 数据未标注完成无需对比
            if (CollectionUtils.isNotEmpty(waitingRawLabelingDetails)) {
                return;
            }
            // 分组内子任务标注完成更新质检任务可检状态到可检
            List<LabelingDetail> allRawLabelingDetails = new ArrayList<>(rawLabelingDetails);
            allRawLabelingDetails.add(labelingDetail);
            updateCheckItemAbleStatus(allRawLabelingDetails);
            // 对比标注结果
            boolean same = rawLabelingDetails.stream()
                    .allMatch(detail -> LabelResultCompareUtil.compareLabelValues(
                            item.getLabelingItems(), JSON.parseObject(detail.getLabelingItemsResult(), new TypeReference<List<List<LabelResult>>>() {
                            }), true));
            Integer isConsistent = same ? CompareItemsConsistent.YES.getCode() : CompareItemsConsistent.NO.getCode();
            labelingDetail.setComparItemsConsistent(isConsistent);
            // 修改标注结果为一致
            rawLabelingDetails.forEach(detail -> detail.setComparItemsConsistent(isConsistent));
            labelingDetailRepository.batchUpdateLabelingDetail(rawLabelingDetails);
        }
    }

    /**
     * 获取分组名称
     *
     * @param labelingSubTasks
     * @return
     */
    private String getGroupName(List<LabelingSubTask> labelingSubTasks) {
        if (CollectionUtils.isEmpty(labelingSubTasks)) {
            return null;
        }
        return labelingSubTasks.stream().map(LabelingSubTask::getLabelerName).collect(Collectors.joining("vs"));
    }

}
