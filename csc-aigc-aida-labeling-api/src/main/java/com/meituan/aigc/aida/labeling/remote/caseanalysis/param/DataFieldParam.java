package com.meituan.aigc.aida.labeling.remote.caseanalysis.param;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-26 11:04
 * @description 数据字段
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataFieldParam implements Serializable {
    /**
     * 字段名称
     */
    private String columnName;
    /**
     * 数据类型
     * {@link FieldTypeEnum}
     */
    private Integer fieldType;
}
