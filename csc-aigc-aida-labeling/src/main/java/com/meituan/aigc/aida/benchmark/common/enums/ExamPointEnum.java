package com.meituan.aigc.aida.benchmark.common.enums;

import com.meituan.aigc.aida.common.enums.base.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考点枚举
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Getter
@AllArgsConstructor
public enum ExamPointEnum implements BaseEnum<Integer> {


    // 一级考点
    COMPREHENSIVE_SCORE(0, "综合得分", null, 1),
    QUESTION_UNDERSTANDING(1, "问题理解", null, 1),
    SOLUTION_GENERATION(2, "方案生成", null, 1),
    ANSWER_AUTHENTICITY(3, "回答真实性", null, 1),
    ANSWER_COMPLETENESS(4, "回答全面性", null, 1),
    INSTRUCTION_FOLLOWING(5, "回答指令遵循", null, 1),
    EXPRESSION_STYLE(6, "表达风格", null, 1),

    // 问题理解子考点
    SEMANTIC_UNDERSTANDING(11, "语义理解", QUESTION_UNDERSTANDING, 2),
    DOMAIN_KNOWLEDGE_UNDERSTANDING(12, "领域知识理解", QUESTION_UNDERSTANDING, 2),
    REASONABLE_FOLLOW_UP(13, "合理追问", QUESTION_UNDERSTANDING, 2),

    // 表达风格子考点
    PERSONIFICATION(61, "拟人化", EXPRESSION_STYLE, 2),
    HUMANIZATION(62, "人性化", EXPRESSION_STYLE, 2);

    /**
     * 考点代码
     */
    private final Integer code;

    /**
     * 考点名称
     */
    private final String value;

    /**
     * 父考点
     */
    private final ExamPointEnum parent;

    /**
     * 考点层级 (0:根节点, 1:一级考点, 2:二级考点)
     */
    private final Integer level;

    /**
     * 根据code获取value
     *
     * @param code 考点代码
     * @return 对应的考点名称，如果未找到则返回COMPREHENSIVE_SCORE
     */
    public static String getValueByCode(Integer code) {
        for (ExamPointEnum examPoint : ExamPointEnum.values()) {
            if (examPoint.getCode().equals(code)) {
                return examPoint.getValue();
            }
        }
        return COMPREHENSIVE_SCORE.value;
    }

    /**
     * 根据code获取枚举对象
     *
     * @param code 考点代码
     * @return 对应的枚举对象，如果未找到则返回COMPREHENSIVE_SCORE
     */
    public static ExamPointEnum getByCode(Integer code) {
        for (ExamPointEnum examPoint : ExamPointEnum.values()) {
            if (examPoint.getCode().equals(code)) {
                return examPoint;
            }
        }
        return COMPREHENSIVE_SCORE;
    }

    /**
     * 根据名称获取枚举对象
     *
     * @param name 考点名称
     * @return 对应的枚举对象，如果未找到则返回null
     */
    public static ExamPointEnum getByName(String name) {
        for (ExamPointEnum examPoint : ExamPointEnum.values()) {
            if (examPoint.getValue().equals(name)) {
                return examPoint;
            }
        }
        return null;
    }

    /**
     * 获取所有一级考点
     *
     * @return 一级考点列表
     */
    public static List<ExamPointEnum> getFirstLevelPoints() {
        return Arrays.stream(ExamPointEnum.values())
                .filter(point -> point.getLevel() == 1)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定父考点的所有子考点
     *
     * @param parent 父考点
     * @return 子考点列表
     */
    public static List<ExamPointEnum> getChildrenByParent(ExamPointEnum parent) {
        return Arrays.stream(ExamPointEnum.values())
                .filter(point -> point.getParent() == parent)
                .collect(Collectors.toList());
    }

    /**
     * 判断是否为根节点
     *
     * @return true为根节点，false为非根节点
     */
    public boolean isRoot() {
        return this.level == 0;
    }

    /**
     * 判断是否为叶子节点
     *
     * @return true为叶子节点，false为非叶子节点
     */
    public boolean isLeaf() {
        return getChildrenByParent(this).isEmpty();
    }

    /**
     * 获取考点的完整路径
     *
     * @return 考点路径（从根到当前节点）
     */
    public String getFullPath() {
        if (this.parent == null) {
            return this.value;
        }
        return this.parent.getFullPath() + "-" + this.value;
    }

    /**
     * 获取所有叶子节点的完整路径
     * 叶子节点是指没有子节点的考点
     *
     * @return 所有叶子节点的完整路径列表
     */
    public static List<String> getAllLeafFullPaths() {
        return Arrays.stream(ExamPointEnum.values())
                .filter(ExamPointEnum::isLeaf)
                .map(ExamPointEnum::getFullPath)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有叶子节点的枚举对象
     * 叶子节点是指没有子节点的考点
     *
     * @return 所有叶子节点的枚举对象列表
     */
    public static List<ExamPointEnum> getAllLeafNodes() {
        return Arrays.stream(ExamPointEnum.values())
                .filter(ExamPointEnum::isLeaf)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定层级的所有叶子节点完整路径
     *
     * @param level 指定层级 (1:一级考点, 2:二级考点)
     * @return 指定层级的叶子节点完整路径列表
     */
    public static List<String> getLeafFullPathsByLevel(Integer level) {
        return Arrays.stream(ExamPointEnum.values())
                .filter(point -> point.getLevel().equals(level))
                .filter(ExamPointEnum::isLeaf)
                .map(ExamPointEnum::getFullPath)
                .collect(Collectors.toList());
    }
} 