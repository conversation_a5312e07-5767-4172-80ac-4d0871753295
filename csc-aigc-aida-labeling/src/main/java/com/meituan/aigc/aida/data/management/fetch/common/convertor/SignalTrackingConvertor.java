package com.meituan.aigc.aida.data.management.fetch.common.convertor;

import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;
import com.meituan.aigc.aida.data.management.fetch.dto.SignalTrackingRuleDTO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-03 11:45
 * @description 埋点规则转换器
 */
public class SignalTrackingConvertor {

    public static SignalTrackingRuleDTO convertSignalTrackingRuleDTO(LlmSignalTrackingRules llmSignalTrackingRules) {
        if (Objects.isNull(llmSignalTrackingRules)) {
            return null;
        }
        SignalTrackingRuleDTO signalTrackingRuleDTO = new SignalTrackingRuleDTO();
        signalTrackingRuleDTO.setId(llmSignalTrackingRules.getId());
        signalTrackingRuleDTO.setName(llmSignalTrackingRules.getName());
        signalTrackingRuleDTO.setStatus(llmSignalTrackingRules.getStatus());
        signalTrackingRuleDTO.setChannel(llmSignalTrackingRules.getChannel());
        signalTrackingRuleDTO.setTypicalQuestionIds(llmSignalTrackingRules.getTypicalQuestionIds());
        signalTrackingRuleDTO.setMisIds(llmSignalTrackingRules.getMisIds());
        signalTrackingRuleDTO.setAcInterface(llmSignalTrackingRules.getAcInterface());
        signalTrackingRuleDTO.setAidaAppCfg(llmSignalTrackingRules.getAidaAppCfg());
        signalTrackingRuleDTO.setCreatorMis(llmSignalTrackingRules.getCreatorMis());
        signalTrackingRuleDTO.setCreatorName(llmSignalTrackingRules.getCreatorName());
        signalTrackingRuleDTO.setCreateTime(llmSignalTrackingRules.getCreateTime());
        signalTrackingRuleDTO.setUpdateTime(llmSignalTrackingRules.getUpdateTime());
        return signalTrackingRuleDTO;
    }

    public static LlmSignalTrackingRules convertLlmSignalTrackingRules(SignalTrackingRuleDTO signalTrackingRuleDTO) {
        if (Objects.isNull(signalTrackingRuleDTO)) {
            return null;
        }
        LlmSignalTrackingRules llmSignalTrackingRules = new LlmSignalTrackingRules();
        llmSignalTrackingRules.setId(signalTrackingRuleDTO.getId());
        llmSignalTrackingRules.setName(signalTrackingRuleDTO.getName().trim());
        llmSignalTrackingRules.setStatus(signalTrackingRuleDTO.getStatus());
        llmSignalTrackingRules.setChannel(signalTrackingRuleDTO.getChannel());
        llmSignalTrackingRules.setTypicalQuestionIds(signalTrackingRuleDTO.getTypicalQuestionIds());
        llmSignalTrackingRules.setMisIds(signalTrackingRuleDTO.getMisIds());
        llmSignalTrackingRules.setAcInterface(signalTrackingRuleDTO.getAcInterface());
        llmSignalTrackingRules.setAidaAppCfg(signalTrackingRuleDTO.getAidaAppCfg());
        llmSignalTrackingRules.setIsDelete(Boolean.FALSE);
        return llmSignalTrackingRules;
    }
}
