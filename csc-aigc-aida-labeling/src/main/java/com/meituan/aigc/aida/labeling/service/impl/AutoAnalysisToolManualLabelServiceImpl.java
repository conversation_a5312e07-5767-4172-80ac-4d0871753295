package com.meituan.aigc.aida.labeling.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.meituan.aigc.aida.labeling.common.enums.CompareItemsConsistent;
import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingDetailRepository;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingSubTaskRepository;
import com.meituan.aigc.aida.labeling.mq.producer.ManualLabelingResultsProducer;
import com.meituan.aigc.aida.labeling.remote.autoanalysis.dto.ManualLabelResultMessageDTO;
import com.meituan.aigc.aida.labeling.service.AutoAnalysisToolManualLabelService;
import com.sankuai.csccratos.aida.common.utils.GsonUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 自动分析工具人工标注服务实现类
 * 
 * <AUTHOR>
 * @date 2025/05/07
 */
@Service
@Slf4j
public class AutoAnalysisToolManualLabelServiceImpl implements AutoAnalysisToolManualLabelService {

    @Resource
    private ManualLabelingResultsProducer manualLabelingResultsProducer;

    @Resource
    private LabelingSubTaskRepository labelingSubTaskRepository;

    @Resource
    private LabelingDetailRepository labelingDetailRepository;

    @Override
    public void sendManualLabelingResults(Long taskId) {
        try {
            // 根据任务id获取所有子任务
            List<LabelingSubTask> labelingSubTasks = labelingSubTaskRepository.listAllByTaskId(taskId);
            if (CollectionUtils.isEmpty(labelingSubTasks)) {
                log.info("子任务列表为空列表，无需发送人工标注结果消息");
                return;
            }

            // 根据子任务id获取标注详情
            List<LabelingDetail> labelingDetails = new ArrayList<>();
            for (LabelingSubTask labelingSubTask : labelingSubTasks) {
                List<LabelingDetail> details = labelingDetailRepository.listBySubTaskIdAndConsistency(labelingSubTask.getId(), CompareItemsConsistent.YES.getCode());
                if (!CollectionUtils.isEmpty(details)) {
                    labelingDetails.addAll(details);
                }
            }

            if (CollectionUtils.isEmpty(labelingDetails)) {
                log.info("标注详情列表为空列表，无需发送人工标注结果消息");
                return;
            }

            // 对sessionId去重
            labelingDetails = labelingDetails.stream()
                    .collect(Collectors.groupingBy(LabelingDetail::getSessionId,
                            Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))))
                    .values()
                    .stream()
                    .collect(Collectors.toList());

            // 构造人工标注结果消息
            List<ManualLabelResultMessageDTO> manualLabelResultMessageDTOs = labelingDetails.stream()
                    .map(labelingDetail -> {
                        return ManualLabelResultMessageDTO.builder()
                                .taskId(taskId)
                                .sessionId(labelingDetail.getSessionId())
                                .sessionTime(Objects.isNull(labelingDetail.getSessionTime()) ? null : labelingDetail.getSessionTime().getTime())
                                .labelingItemsResult(labelingDetail.getLabelingItemsResult())
                                .build();
                    }).collect(Collectors.toList());

            // 发送人工标注结果消息
            String message = GsonUtil.toJsonStr(manualLabelResultMessageDTOs);
            log.info("正在发送人工标注结果消息，数据量：{}条", manualLabelResultMessageDTOs.size());
            manualLabelingResultsProducer.send(message);
            log.info("人工标注结果消息发送成功");
        } catch (Exception e) {
            log.error("发送人工标注结果消息失败：{}", e);
        }
    }
}
