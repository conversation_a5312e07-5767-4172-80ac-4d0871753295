package com.meituan.aigc.aida.common.enums;

import com.meituan.aigc.aida.common.enums.base.BaseEnum;
import lombok.Getter;

/**
 * ES属性类型枚举
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@Getter
public enum EsPropertyTypeEnum implements BaseEnum<String> {

    // ========== 文本类型 ==========
    /**
     * 全文搜索字段，会进行分词
     */
    TEXT("text", "文本类型"),

    /**
     * 精确值字段，不会分词，用于排序、聚合、精确匹配
     */
    KEYWORD("keyword", "关键词类型"),

    // ========== 数值类型 ==========
    /**
     * 长整型
     */
    LONG("long", "长整型"),

    /**
     * 整型
     */
    INTEGER("integer", "整型"),

    /**
     * 短整型
     */
    SHORT("short", "短整型"),

    /**
     * 字节型
     */
    BYTE("byte", "字节型"),

    /**
     * 双精度浮点型
     */
    DOUBLE("double", "双精度浮点型"),

    /**
     * 单精度浮点型
     */
    FLOAT("float", "单精度浮点型"),

    /**
     * 半精度浮点型
     */
    HALF_FLOAT("half_float", "半精度浮点型"),

    /**
     * 缩放浮点型
     */
    SCALED_FLOAT("scaled_float", "缩放浮点型"),

    // ========== 日期类型 ==========
    /**
     * 日期类型
     */
    DATE("date", "日期类型"),

    /**
     * 日期纳秒类型
     */
    DATE_NANOS("date_nanos", "日期纳秒类型"),

    // ========== 布尔类型 ==========
    /**
     * 布尔类型
     */
    BOOLEAN("boolean", "布尔类型"),

    // ========== 二进制类型 ==========
    /**
     * 二进制类型
     */
    BINARY("binary", "二进制类型"),

    // ========== 范围类型 ==========
    /**
     * 整数范围类型
     */
    INTEGER_RANGE("integer_range", "整数范围类型"),

    /**
     * 浮点数范围类型
     */
    FLOAT_RANGE("float_range", "浮点数范围类型"),

    /**
     * 长整数范围类型
     */
    LONG_RANGE("long_range", "长整数范围类型"),

    /**
     * 双精度范围类型
     */
    DOUBLE_RANGE("double_range", "双精度范围类型"),

    /**
     * 日期范围类型
     */
    DATE_RANGE("date_range", "日期范围类型"),

    /**
     * IP范围类型
     */
    IP_RANGE("ip_range", "IP范围类型"),

    // ========== 复合类型 ==========
    /**
     * 对象类型
     */
    OBJECT("object", "对象类型"),

    /**
     * 嵌套类型
     */
    NESTED("nested", "嵌套类型"),

    /**
     * 扁平化对象类型
     */
    FLATTENED("flattened", "扁平化对象类型"),

    // ========== 地理类型 ==========
    /**
     * 地理坐标点
     */
    GEO_POINT("geo_point", "地理坐标点"),

    /**
     * 地理形状
     */
    GEO_SHAPE("geo_shape", "地理形状"),

    // ========== 特殊类型 ==========
    /**
     * IP地址类型
     */
    IP("ip", "IP地址类型"),

    /**
     * 完成建议类型
     */
    COMPLETION("completion", "完成建议类型"),

    /**
     * 令牌计数类型
     */
    TOKEN_COUNT("token_count", "令牌计数类型"),

    /**
     * 映射器-解析器类型
     */
    MAPPER_MURMUR3("murmur3", "映射器-解析器类型"),

    /**
     * 注释类型
     */
    ANNOTATED_TEXT("annotated-text", "注释类型"),

    /**
     * 感知器类型
     */
    PERCOLATOR("percolator", "感知器类型"),

    /**
     * 连接类型
     */
    JOIN("join", "连接类型"),

    /**
     * 排名特征类型
     */
    RANK_FEATURE("rank_feature", "排名特征类型"),

    /**
     * 排名特征集合类型
     */
    RANK_FEATURES("rank_features", "排名特征集合类型"),

    /**
     * 密集向量类型
     */
    DENSE_VECTOR("dense_vector", "密集向量类型"),

    /**
     * 稀疏向量类型
     */
    SPARSE_VECTOR("sparse_vector", "稀疏向量类型"),

    /**
     * 搜索即类型
     */
    SEARCH_AS_YOU_TYPE("search_as_you_type", "搜索即类型"),

    /**
     * 别名类型
     */
    ALIAS("alias", "别名类型"),

    /**
     * 直方图类型
     */
    HISTOGRAM("histogram", "直方图类型"),

    /**
     * 常量关键词类型
     */
    CONSTANT_KEYWORD("constant_keyword", "常量关键词类型");

    /**
     * 操作类型代码
     */
    private final String code;

    /**
     * 操作类型描述
     */
    private final String value;

    /**
     * 构造函数
     *
     * @param code  类型代码
     * @param value 类型描述
     */
    EsPropertyTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 对应的枚举值
     */
    public static EsPropertyTypeEnum getByCode(String code) {
        for (EsPropertyTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为文本类型
     *
     * @return true if text type
     */
    public boolean isTextType() {
        return this == TEXT || this == KEYWORD || this == SEARCH_AS_YOU_TYPE;
    }

    /**
     * 判断是否为数值类型
     *
     * @return true if numeric type
     */
    public boolean isNumericType() {
        return this == LONG || this == INTEGER || this == SHORT || this == BYTE ||
                this == DOUBLE || this == FLOAT || this == HALF_FLOAT || this == SCALED_FLOAT;
    }

    /**
     * 判断是否为日期类型
     *
     * @return true if date type
     */
    public boolean isDateType() {
        return this == DATE || this == DATE_NANOS;
    }

    /**
     * 判断是否为范围类型
     *
     * @return true if range type
     */
    public boolean isRangeType() {
        return this == INTEGER_RANGE || this == FLOAT_RANGE || this == LONG_RANGE ||
                this == DOUBLE_RANGE || this == DATE_RANGE || this == IP_RANGE;
    }

    /**
     * 判断是否为复合类型
     *
     * @return true if complex type
     */
    public boolean isComplexType() {
        return this == OBJECT || this == NESTED || this == FLATTENED;
    }

    /**
     * 判断是否为地理类型
     *
     * @return true if geo type
     */
    public boolean isGeoType() {
        return this == GEO_POINT || this == GEO_SHAPE;
    }

    /**
     * 判断是否支持精确匹配查询（term查询）
     *
     * @return true if supports term query
     */
    public boolean supportsTermQuery() {
        return this == KEYWORD || isNumericType() || this == BOOLEAN ||
                this == DATE || this == IP || this == CONSTANT_KEYWORD;
    }

    /**
     * 判断是否支持全文搜索查询（match查询）
     *
     * @return true if supports match query
     */
    public boolean supportsMatchQuery() {
        return this == TEXT || this == SEARCH_AS_YOU_TYPE;
    }

    /**
     * 判断是否支持范围查询
     *
     * @return true if supports range query
     */
    public boolean supportsRangeQuery() {
        return isNumericType() || isDateType() || this == IP || isRangeType();
    }

    /**
     * 判断是否支持聚合操作
     *
     * @return true if supports aggregation
     */
    public boolean supportsAggregation() {
        return this == KEYWORD || isNumericType() || isDateType() ||
                this == BOOLEAN || this == IP || this == CONSTANT_KEYWORD;
    }
}
