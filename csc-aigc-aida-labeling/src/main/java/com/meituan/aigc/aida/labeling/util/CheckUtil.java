package com.meituan.aigc.aida.labeling.util;

import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingOperationException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import lombok.extern.slf4j.Slf4j;

/**
 * 校验工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class CheckUtil {

    public static void paramCheck(boolean expression, String message, Object... args) {
        if (!expression) {
            log.error(message, args);
            throw new AidaTrainingCheckException(message);
        }
    }

    public static void operationCheck(boolean expression, String message, Object... args) {
        if (!expression) {
            log.error(message, args);
            throw new AidaTrainingOperationException(message);
        }
    }

    public static void rpcParamCheck(boolean expression, String message, Object... args) {
        if (!expression) {
            log.error(message, args);
            throw new AidaRpcException(message);
        }
    }

    public static void rpcOperationCheck(boolean expression, String message, Object... args) {
        if (!expression) {
            log.error(message, args);
            throw new AidaRpcException(message);
        }
    }
}
