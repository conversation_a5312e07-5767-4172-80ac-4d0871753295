package com.meituan.aigc.aida.labeling.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-10 19:47
 * @description 标注项模版入参
 */
@Data
public class InspectionItemTemplateParam implements Serializable {
    /**
     * 模版id(保存新模版时不传)
     */
    private Long id;
    /**
     * 模版名称
     */
    private String templateName;
    /**
     * 模版描述
     */
    private String templateDesc;
    /**
     * 模版类型 1:标注模版 2:质检模版
     * @see com.meituan.aigc.aida.labeling.common.enums.LabelingItemTemplateType
     */
    private Integer templateType;
    /**
     * 标注项数据
     */
    private List<InspectionItemDetail> templateItems;
}
