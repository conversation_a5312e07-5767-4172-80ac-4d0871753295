package com.meituan.aigc.aida.labeling.strategy.transfer.impl;

import com.dianping.lion.client.util.StringUtils;
import com.meituan.aigc.aida.labeling.common.enums.AssignTypeEnum;
import com.meituan.aigc.aida.labeling.common.enums.QualityCheckDataStatus;
import com.meituan.aigc.aida.labeling.common.enums.QualityCheckTaskStatus;
import com.meituan.aigc.aida.labeling.common.enums.TransferTypeEnum;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingQualityCheckItemRepository;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingQualityCheckTaskRepository;
import com.meituan.aigc.aida.labeling.helper.TaskTransferHelper;
import com.meituan.aigc.aida.labeling.param.TaskTransferParam;
import com.meituan.aigc.aida.labeling.pojo.dto.TransferInfoDTO;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.strategy.transfer.TransferStrategy;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 质检转交实现
 *
 * <AUTHOR>
 */
@Service
public class QualityCheckTransfer implements TransferStrategy {

    @Autowired
    private LabelingQualityCheckTaskRepository labelingQualityCheckTaskRepository;

    @Autowired
    private LabelingQualityCheckItemRepository labelingQualityCheckItemRepository;

    @Autowired
    private PushElephantService pushElephantService;

    @Autowired
    private LionConfig lionConfig;

    /**
     * 转交任务发送大象消息格式
     */
    private static final String TRANSFER_TASK_MESSAGE_FORMAT = "任务「%s」转交已完成，请及时前往[训练系统|%s]查看。";

    /**
     * 质检任务转交逻辑：
     * 1、找到没有质检的数据，session维度按会话统计，query维度按原始数据统计，背标情况下算一份数据，随机分配给转让人
     * 2、给转让人创建新任务，和原任务相比，不一样的数据包括任务状态、质检人、创建时间、数据量
     * 3、转让质检数据，更新质检人和关联的质检任务
     * 4、处理原任务，分三种情况，1）原任务数据全部被转让，删除原任务 2）原任务可转让数据全部转让，还剩自己之前质检过的，更新任务状态为质检完成，更新剩余数据量 3）可转让数据还未完全转让，只更新数据量
     */
    @Override
    public void transfer(TaskTransferParam param) {
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(param.getSubTaskId());
        CheckUtil.operationCheck(labelingQualityCheckTask != null, "质检任务不存在");
        int maxTransferNum = getMaxTransferNum(labelingQualityCheckTask);
        int transferNum = (int) param.getTransferConfig().stream().mapToLong(TaskTransferParam.TransferConfig::getTransferNum).sum();
        CheckUtil.operationCheck(transferNum <= maxTransferNum, "最大可转交数量发生变化，请刷新页面后重新分配");
        List<LabelingQualityCheckItem> qualityCheckItemList = labelingQualityCheckItemRepository.listDataByQualityCheckTaskIdAndQualityCheckStatus(param.getSubTaskId(), QualityCheckDataStatus.WAITING_QUALITY_CHECK.getCode());
        CheckUtil.operationCheck(CollectionUtils.isNotEmpty(qualityCheckItemList), "质检任务数据发生变化，请刷新页面后重新分配");
        List<String> transferIdList = getTransferIdList(labelingQualityCheckTask, qualityCheckItemList);
        // 随机转交任务
        Map<String, List<String>> transferMisMap = TaskTransferHelper.transferTask(transferIdList, param.getTransferConfig());
        Map<String, TaskTransferParam.TransferConfig> transferMisConfigMap = param.getTransferConfig().stream().collect(Collectors.toMap(TaskTransferParam.TransferConfig::getTransferMis, Function.identity()));
        Date now = new Date();
        Map<String, List<LabelingQualityCheckItem>> labelingQualityCheckItemMap;
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), labelingQualityCheckTask.getAssignType())) {
            labelingQualityCheckItemMap = qualityCheckItemList.stream().collect(Collectors.groupingBy(LabelingQualityCheckItem::getSessionId));
        } else {
            labelingQualityCheckItemMap = qualityCheckItemList.stream().collect(Collectors.groupingBy(item -> String.valueOf(item.getRawDataId())));
        }

        List<LabelingQualityCheckTask> allQualityCheckTaskList = labelingQualityCheckTaskRepository.listByTaskId(labelingQualityCheckTask.getTaskId());
        Map<String, List<LabelingQualityCheckTask>> misQualityCheckTaskMap = allQualityCheckTaskList.stream().collect(Collectors.groupingBy(LabelingQualityCheckTask::getQualityCheckMis));

        // 处理转交任务的数据
        handleTransferTask(labelingQualityCheckTask, transferMisMap, transferMisConfigMap, now, labelingQualityCheckItemMap, misQualityCheckTaskMap);

        // 处理转交后历史任务的数据
        handleOldTask(labelingQualityCheckTask, maxTransferNum, transferNum, now);

        // 发送大象消息
        pushElephant(labelingQualityCheckTask, transferMisMap, param.getOperateMis());
    }

    @Override
    public Long getLabelingTaskId(Long subTaskId) {
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(subTaskId);
        CheckUtil.operationCheck(labelingQualityCheckTask != null, "质检任务不存在");
        return labelingQualityCheckTask.getTaskId();
    }

    @Override
    public TransferInfoDTO getTransferInfo(Long taskId, Integer transferType) {
        TransferInfoDTO transferInfo = new TransferInfoDTO();
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(taskId);
        CheckUtil.operationCheck(labelingQualityCheckTask != null, "质检任务不存在");
        int maxTransferNum = getMaxTransferNum(labelingQualityCheckTask);
        transferInfo.setMaxTransferNum(maxTransferNum);
        return transferInfo;
    }

    @Override
    public Integer getStrategyCode() {
        return TransferTypeEnum.QUALITY_CHECK.getCode();
    }

    @Override
    public Integer getCurrentTaskStatus(Long qualityCheckTaskId) {
        LabelingQualityCheckTask labelingQualityCheckTask = labelingQualityCheckTaskRepository.getById(qualityCheckTaskId);
        CheckUtil.paramCheck(labelingQualityCheckTask != null, "质检任务不存在");
        return labelingQualityCheckTask.getStatus();
    }

    @Override
    public Boolean canTransfer(Long subTaskId) {
        Integer currentTaskStatus = getCurrentTaskStatus(subTaskId);
        return QualityCheckTaskStatus.getCanCheckStatus().contains(currentTaskStatus);
    }

    /**
     * 发送大象消息
     *
     * @param labelingQualityCheckTask 质检任务
     * @param transferMisMap           转交人mis
     */
    private void pushElephant(LabelingQualityCheckTask labelingQualityCheckTask, Map<String, List<String>> transferMisMap, String userMis) {
        List<String> misList = new ArrayList<>(transferMisMap.keySet());
        if (StringUtils.isNotEmpty(userMis)) {
            misList.add(userMis);
        }
        String messageId = String.format(TRANSFER_TASK_MESSAGE_FORMAT, labelingQualityCheckTask.getName(), lionConfig.getQualityCheckDistributeNotifyElephantUrl());
        pushElephantService.pushElephant(messageId, misList);
    }

    /**
     * 构建新的质检任务
     *
     * @param labelingQualityCheckTask 原始质检任务
     * @param transferMisConfigMap     转交配置
     * @param now                      当前时间
     * @param mis                      转交人mis
     * @param curMisTransferIdList     转交数据id
     * @return 新的质检任务
     */
    private LabelingQualityCheckTask buildNewLabelingQualityCheckTask(LabelingQualityCheckTask labelingQualityCheckTask, Map<String, TaskTransferParam.TransferConfig> transferMisConfigMap, Date now, String mis, List<String> curMisTransferIdList) {
        LabelingQualityCheckTask newQualityCheckTask = new LabelingQualityCheckTask();
        newQualityCheckTask.setName(labelingQualityCheckTask.getName());
        newQualityCheckTask.setTaskId(labelingQualityCheckTask.getTaskId());
        newQualityCheckTask.setSampleSize(labelingQualityCheckTask.getSampleSize());
        newQualityCheckTask.setQualityCheckConfig(labelingQualityCheckTask.getQualityCheckConfig());
        newQualityCheckTask.setStatus(QualityCheckTaskStatus.CREATING.getCode());
        newQualityCheckTask.setTenantId(labelingQualityCheckTask.getTenantId());
        newQualityCheckTask.setQualityCheckMis(mis);
        newQualityCheckTask.setQualityCheckName(transferMisConfigMap.get(mis).getTransferName());
        newQualityCheckTask.setCreateMis(labelingQualityCheckTask.getCreateMis());
        newQualityCheckTask.setCreateTime(now);
        newQualityCheckTask.setUpdateTime(now);
        newQualityCheckTask.setIsDeleted(false);
        newQualityCheckTask.setRawDataSampleSize(curMisTransferIdList.size());
        newQualityCheckTask.setAssignType(labelingQualityCheckTask.getAssignType());
        return newQualityCheckTask;
    }

    /**
     * 获取转交数据id
     *
     * @param labelingQualityCheckTask 质检任务
     * @param qualityCheckItemList     质检数据
     * @return 转交数据id
     */
    private List<String> getTransferIdList(LabelingQualityCheckTask labelingQualityCheckTask, List<LabelingQualityCheckItem> qualityCheckItemList) {
        List<String> transferIdList;
        // session维度要根据会话来计算数量
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), labelingQualityCheckTask.getAssignType())) {
            transferIdList = qualityCheckItemList.stream().map(LabelingQualityCheckItem::getSessionId).distinct().collect(Collectors.toList());
        } else {
            // query维度要根据原数据来计算数量
            transferIdList = qualityCheckItemList.stream().map(labelingQualityCheckItem -> String.valueOf(labelingQualityCheckItem.getRawDataId())).distinct().collect(Collectors.toList());
        }
        return transferIdList;
    }

    /**
     * 获取最大可转交数量
     *
     * @param labelingQualityCheckTask 质检任务
     * @return 最大可转交数量
     */
    private int getMaxTransferNum(LabelingQualityCheckTask labelingQualityCheckTask) {
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), labelingQualityCheckTask.getAssignType())) {
            return labelingQualityCheckItemRepository.countSessionBySubTaskIdAndStatus(labelingQualityCheckTask.getId(), QualityCheckDataStatus.WAITING_QUALITY_CHECK.getCode());
        } else if (Objects.equals(AssignTypeEnum.QUERY.getCode(), labelingQualityCheckTask.getAssignType())) {
            return labelingQualityCheckItemRepository.countQueryBySubTaskIdAndStatus(labelingQualityCheckTask.getId(), QualityCheckDataStatus.WAITING_QUALITY_CHECK.getCode());
        }
        return 0;
    }

    /**
     * 处理转交任务
     *
     * @param labelingQualityCheckTask    质检任务
     * @param transferMisMap              转交人信息
     * @param transferMisConfigMap        转交人配置信息
     * @param now                         当前时间
     * @param labelingQualityCheckItemMap 质检数据
     * @param misQualityCheckTaskMap      转交人已质检任务
     */
    private void handleTransferTask(LabelingQualityCheckTask labelingQualityCheckTask, Map<String, List<String>> transferMisMap, Map<String, TaskTransferParam.TransferConfig> transferMisConfigMap, Date now, Map<String, List<LabelingQualityCheckItem>> labelingQualityCheckItemMap, Map<String, List<LabelingQualityCheckTask>> misQualityCheckTaskMap) {
        for (Map.Entry<String, List<String>> entry : transferMisMap.entrySet()) {
            // 创建新的质检任务
            String mis = entry.getKey();
            List<String> curMisTransferIdList = entry.getValue();
            LabelingQualityCheckTask newQualityCheckTask;
            // 优先找到历史的质检任务，如果没有新建一个任务
            if (misQualityCheckTaskMap.containsKey(mis)) {
                newQualityCheckTask = misQualityCheckTaskMap.get(mis).get(0);
                newQualityCheckTask.setRawDataSampleSize(newQualityCheckTask.getRawDataSampleSize() + curMisTransferIdList.size());
                newQualityCheckTask.setUpdateTime(now);
                newQualityCheckTask.setStatus(QualityCheckTaskStatus.CREATING.getCode());
                labelingQualityCheckTaskRepository.updateById(newQualityCheckTask);
            } else {
                newQualityCheckTask = buildNewLabelingQualityCheckTask(labelingQualityCheckTask, transferMisConfigMap, now, mis, curMisTransferIdList);
                labelingQualityCheckTaskRepository.insert(newQualityCheckTask);
            }
            // 分配数据
            List<LabelingQualityCheckItem> curMisLabelingQualityCheckItem = new ArrayList<>();
            for (String transferId : curMisTransferIdList) {
                curMisLabelingQualityCheckItem.addAll(labelingQualityCheckItemMap.get(transferId));
            }
            // 更新质检人和关联的质检任务
            curMisLabelingQualityCheckItem.forEach(item -> {
                item.setQualityCheckMis(mis);
                item.setQualityCheckName(transferMisConfigMap.get(mis).getTransferName());
                item.setQualityCheckTaskId(newQualityCheckTask.getId());
            });

            // 转让完成后更新质检任务的状态为质检中
            if (newQualityCheckTask.getStatus() == QualityCheckTaskStatus.CREATING.getCode()) {
                newQualityCheckTask.setStatus(QualityCheckTaskStatus.CHECKING.getCode());
                labelingQualityCheckTaskRepository.updateById(newQualityCheckTask);
            }

            // 更新数据
            labelingQualityCheckItemRepository.batchUpdateById(curMisLabelingQualityCheckItem);
        }
    }

    /**
     * 更新原任务
     * 可转交数据全部转交时，分两种情况，如果剩余还有数据，则更新为质检完成， 如果没有剩余数据，删除当前任务
     *
     * @param labelingQualityCheckTask 质检任务
     * @param maxTransferNum           最大可转交数量
     * @param transferNum              已转交数量
     * @param now                      当前时间
     */
    private void handleOldTask(LabelingQualityCheckTask labelingQualityCheckTask, int maxTransferNum, int transferNum, Date now) {
        // 找到已质检的数据
        int restDataNum = getRestDataNum(labelingQualityCheckTask);
        // 如果还有已质检数据，更新状态
        if (restDataNum > 0 || transferNum < maxTransferNum) {
            if (transferNum == maxTransferNum) {
                labelingQualityCheckTask.setStatus(QualityCheckTaskStatus.COMPLETED.getCode());
            }
            labelingQualityCheckTask.setRawDataSampleSize(maxTransferNum - transferNum + restDataNum);
            labelingQualityCheckTask.setUpdateTime(now);
            labelingQualityCheckTaskRepository.updateById(labelingQualityCheckTask);
        } else {
            // 如果没有剩余数据，直接逻辑删除任务
            labelingQualityCheckTask.setIsDeleted(true);
            labelingQualityCheckTaskRepository.updateById(labelingQualityCheckTask);
        }
    }

    /**
     * 获取剩余未质检的数据量
     *
     * @param labelingQualityCheckTask 质检任务
     * @return 剩余未质检的数据量
     */
    private int getRestDataNum(LabelingQualityCheckTask labelingQualityCheckTask) {
        int restDataNum;
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), labelingQualityCheckTask.getAssignType())) {
            restDataNum = labelingQualityCheckItemRepository.countSessionBySubTaskIdAndStatus(labelingQualityCheckTask.getId(), QualityCheckDataStatus.QUALITY_CHECKED.getCode());
        } else {
            restDataNum = labelingQualityCheckItemRepository.countQueryBySubTaskIdAndStatus(labelingQualityCheckTask.getId(), QualityCheckDataStatus.QUALITY_CHECKED.getCode());
        }
        return restDataNum;
    }
}
