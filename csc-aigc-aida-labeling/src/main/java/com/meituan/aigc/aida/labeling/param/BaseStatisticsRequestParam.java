package com.meituan.aigc.aida.labeling.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 请求基础参数
 */
@Data
public class BaseStatisticsRequestParam implements Serializable {
    /**
     * 业务类型列表
     * 1.外客在线 2.拼好饭在线 3.骑手在线 4.外商在线 5.骑手电话 6.外客电话 7.其他
     * 为null表示所有业务类型全选
     */
    private List<Long> bizTypes;

    /**
     * 任务类型 1:评测集标注 2:训练集标注 3:线上会话标注
     */
    private Integer taskType;

    /**
     * 数据类型 1:模型评测 2:Agent评测 3:有监督微调(SFT) 4:偏好对齐(DPO) 5:Session维度 6:Query维度
     */
    private Integer dataType;

    /**
     * 起始时间 yyyy-MM-dd
     */
    private String startDate;

    /**
     * 终止时间 yyyy-MM-dd
     */
    private String endDate;

} 