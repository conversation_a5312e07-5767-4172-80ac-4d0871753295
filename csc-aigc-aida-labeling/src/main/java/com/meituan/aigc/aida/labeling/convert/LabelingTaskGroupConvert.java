package com.meituan.aigc.aida.labeling.convert;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelTaskGroupDTO;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-03-06 17:52
 * @description
 */
@Slf4j
@Data
public class LabelingTaskGroupConvert {

    public static LabelTaskGroupDTO convertLabelTaskGroupDTO(LabelingTaskGroup labelingTaskGroup) {
        if (Objects.isNull(labelingTaskGroup)) {
            return null;
        }
        LabelTaskGroupDTO labelTaskGroupDTO = new LabelTaskGroupDTO();
        try {
            labelTaskGroupDTO.setId(labelingTaskGroup.getId());
            labelTaskGroupDTO.setTaskId(labelingTaskGroup.getTaskId());
            labelTaskGroupDTO.setGroupName(labelingTaskGroup.getGroupName());
            labelTaskGroupDTO.setParentId(labelingTaskGroup.getParentId());
            labelTaskGroupDTO.setRecycleStatus(labelingTaskGroup.getRecycleStatus());
            labelTaskGroupDTO.setRecycleDataCount(labelingTaskGroup.getRecycleDataCount());
            labelTaskGroupDTO.setTotalDataCount(labelingTaskGroup.getTotalDataCount());
            labelTaskGroupDTO.setRecycleSubTaskId(labelingTaskGroup.getRecycleSubTaskId());
            labelTaskGroupDTO.setConsistencyRate(labelingTaskGroup.getConsistencyRate());
            labelTaskGroupDTO.setUpdateTime(DateUtil.formatDate(labelingTaskGroup.getUpdateTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD));
            return labelTaskGroupDTO;
        } catch (Exception e) {
            log.error("日期转换失败，分组id:{}", labelingTaskGroup.getId(), e);
        }
        return labelTaskGroupDTO;
    }
}
