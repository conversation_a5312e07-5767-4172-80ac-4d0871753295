package com.meituan.aigc.aida.labeling.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.meituan.aigc.aida.labeling.common.CatConstant;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.common.constant.RedisConstant;
import com.meituan.aigc.aida.labeling.common.enums.*;
import com.meituan.aigc.aida.labeling.common.enums.sub.task.LabelingDetailSampleStatusEnum;
import com.meituan.aigc.aida.labeling.common.s3.S3Service;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.convert.LabelingRawDataConvert;
import com.meituan.aigc.aida.labeling.dao.model.*;
import com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckSummaryPO;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubtaskCountPo;
import com.meituan.aigc.aida.labeling.dao.po.LabelingTaskRawDataPO;
import com.meituan.aigc.aida.labeling.dao.repository.*;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingServiceException;
import com.meituan.aigc.aida.labeling.param.*;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckDistributeParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckMisParam;
import com.meituan.aigc.aida.labeling.param.task.LabelingDistributionParam;
import com.meituan.aigc.aida.labeling.param.task.LabelingGroupConsistencyParam;
import com.meituan.aigc.aida.labeling.param.task.RecycleTaskParam;
import com.meituan.aigc.aida.labeling.pojo.dto.*;
import com.meituan.aigc.aida.labeling.pojo.dto.group.GroupConfigList;
import com.meituan.aigc.aida.labeling.pojo.dto.task.*;
import com.meituan.aigc.aida.labeling.pojo.vo.*;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.BaseNameCodeVO;
import com.meituan.aigc.aida.labeling.service.DashboardStatisticsService;
import com.meituan.aigc.aida.labeling.service.TaskService;
import com.meituan.aigc.aida.labeling.service.common.PermissionService;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.strategy.dimension.DimensionStrategy;
import com.meituan.aigc.aida.labeling.strategy.dimension.DimensionStrategyFactory;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategy;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategyFactory;
import com.meituan.aigc.aida.labeling.strategy.transfer.TransferStrategyFactory;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.ThreadPoolUtil;
import com.meituan.aigc.aida.labeling.util.labeling.task.LabelingTaskUtil;
import com.meituan.csc.aigc.runtime.api.UserRemoteService;
import com.meituan.csc.aigc.runtime.dto.UserDTO;
import com.meituan.csc.aigc.runtime.dto.aida.AidaBaseResponse;
import com.meituan.csc.aigc.runtime.enums.aida.AidaSysErrCodeEnum;
import com.meituan.csc.aigc.runtime.inner.api.AidaUserInfoRemoteService;
import com.meituan.csc.aigc.runtime.inner.dto.InnerMisInfoDTO;
import com.meituan.csc.aigc.runtime.inner.param.MisInfoParam;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meituan.aigc.aida.labeling.common.utils.LabelResultCompareUtil.IS_COMPARE;

/**
 * 任务服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TaskServiceImpl implements TaskService {

    @Resource
    private AidaUserInfoRemoteService aidaUserInfoRemoteService;

    @Resource
    private LabelingTaskRepository labelingTaskRepository;

    @Resource
    private LabelingTaskGroupRepository labelingTaskGroupRepository;

    @Resource
    private LabelingSubTaskRepository labelingSubTaskRepository;

    @Resource
    private LabelingDetailRepository labelingDetailRepository;

    @Resource
    private LabelingQualityCheckTaskRepository labelingQualityCheckTaskRepository;

    @Resource
    private LabelingQualityCheckItemRepository labelingQualityCheckItemRepository;

    @Resource
    private LabelingTaskRawDataRepository labelingTaskRawDataRepository;

    @Resource
    private InspectionItemTemplateRepository inspectionItemTemplateRepository;

    @Resource
    private InspectionItemRepository inspectionItemRepository;

    @Resource
    private CreateTaskStrategyFactory createTaskStrategyFactory;

    @Resource
    private PermissionService permissionService;

    @Resource
    private LionConfig lionConfig;

    @Resource
    private PushElephantService pushElephantService;

    @Resource
    private S3Service s3Service;

    @Autowired
    private TransferStrategyFactory transferStrategyFactory;

    @Autowired(required = false)
    @Qualifier("redisClient")
    private RedisStoreClient redisStoreClient;

    @Resource
    private DimensionStrategyFactory dimensionStrategyFactory;

    @Resource
    private UserRemoteService userRemoteService;

    @Resource
    private DashboardStatisticsService dashboardStatisticsService;

    /**
     * 系统可用的CPU核心数
     */
    private static final int CPU_CORE_COUNT = 4;

    /**
     * 处理标注任务导出的线程池
     */
    private static final ExecutorService EXPORT_LABELING_DATA_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "export-labeling-data-%d");

    /**
     * 处理质检任务的线程池
     */
    private static final ExecutorService QUALITY_CHECK_TASK_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "quality-check-task-%d");

    /**
     * 处理标注任务原始数据的线程池
     */
    private static final ExecutorService RAW_DATA_PROCESS_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "raw-data-process-%d");

    /**
     * 处理标注任务分配的线程池
     */
    private static final ExecutorService TASK_DISTRIBUTION_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "task-distribution-%d");


    /**
     * 处理任务转交的线程池
     */
    private static final ExecutorService TASK_TRANSFER_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "task-transfer-%d");

    /**
     * 获取mis
     *
     * @param mis mis号
     * @return 人员信息
     */
    @Override
    public List<MisDataDTO> getMisList(String mis) {
        if (StringUtils.isBlank(mis)) {
            return new ArrayList<>();
        }
        // 如果mis不为空，通过逗号分隔开，然后构建人员信息查询参数对象
        String[] misList = mis.split(CommonConstants.FieldName.COMMA);
        List<MisDataDTO> misDataDTOList = new ArrayList<>();
        for (String misItem : misList) {
            if (StringUtils.isBlank(misItem)) {
                continue;
            }
            if (lionConfig.getSearchMisSwitch()) {
                builderMisSearch(misItem, misDataDTOList);
            } else {
                aidaMisSearch(misItem, misDataDTOList);
            }
        }
        return misDataDTOList;
    }

    /**
     * 调用builder接口查询mis
     *
     * @param misItem        mis号
     * @param misDataDTOList 人员信息列表
     */
    private void builderMisSearch(String misItem, List<MisDataDTO> misDataDTOList) {
        List<UserDTO> userDTOList = userRemoteService.searchMisInOrg(misItem);
        if (CollectionUtils.isEmpty(userDTOList)) {
            return;
        }
        misDataDTOList.addAll(userDTOList.stream().filter(Objects::nonNull).map(misDataDTO -> new MisDataDTO(misDataDTO.getMis(), misDataDTO.getName()))
                .collect(Collectors.toList()));
    }

    /**
     * 调用aida接口查询mis
     *
     * @param misItem mis号
     * @return misDataDTOList 人员信息列表
     */
    private void aidaMisSearch(String misItem, List<MisDataDTO> misDataDTOList) {
        MisInfoParam param = getMisInfoParam(misItem);
        // 调用runtime包装的rpc接口
        AidaBaseResponse<List<InnerMisInfoDTO>> response = aidaUserInfoRemoteService.getMisInfo(param);
        // 如果当前mis不存在，则跳过
        if (response == null || !AidaSysErrCodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            return;
        }
        // 获取人员信息
        List<InnerMisInfoDTO> misInfoDTOList = response.getData();
        // 如果当前mis不存在，则跳过
        if (CollectionUtils.isEmpty(misInfoDTOList)) {
            return;
        }
        misDataDTOList.addAll(misInfoDTOList.stream().filter(Objects::nonNull).map(misDataDTO -> new MisDataDTO(misDataDTO.getMis(), misDataDTO.getName()))
                .collect(Collectors.toList()));
    }

    /**
     * 校验任务并获取任务信息
     *
     * @param param 参数
     * @return 标注任务对象
     */
    private LabelingTask validateAndGetTask(RecycleTaskParam param, String userMis) {
        CheckUtil.paramCheck(Objects.nonNull(param.getTaskId()), "请选择一个任务");
        LabelingTask labelingTask = labelingTaskRepository.getById(param.getTaskId());
        if (Objects.isNull(labelingTask)) {
            throw new AidaTrainingCheckException("标注任务不存在");
        }
        return labelingTask;
    }

    /**
     * 获取当前用户MIS
     *
     * @return 用户MIS
     */
    private String getCurrentUserMis() {
        return Optional.ofNullable(UserUtils.getUser())
                .map(User::getLogin)
                .orElse(CommonConstants.FieldName.UNKNOWN);
    }

    /**
     * 获取导出任务数据
     *
     * @param labelingTask 标注任务
     * @param userMis      用户MIS
     * @return 导出任务数据
     */
    private ExportTaskData getExportTaskData(LabelingTask labelingTask, String userMis, RecycleTaskParam param) {
        // 管理员可以导出部分标注完成的任务
        boolean isAdmin = lionConfig.isAdmin(userMis);
        boolean isNotRecycled = !Objects.equals(LabelingTaskStatus.COMPLETE.getCode(),
                labelingTask.getLabelingStatus());

        if (isAdmin && isNotRecycled) {
            return getAdminExportData(labelingTask.getId());
        } else {
            return getNormalExportData(labelingTask, param);
        }
    }

    /**
     * 获取管理员导出数据
     *
     * @param taskId 任务ID
     * @return 导出任务数据
     */
    private ExportTaskData getAdminExportData(Long taskId) {
        List<LabelingSubTask> labelingSubTasks = labelingSubTaskRepository.listAllByTaskId(taskId);
        if (CollectionUtils.isEmpty(labelingSubTasks)) {
            return new ExportTaskData(new ArrayList<>(), LabelingDetailStatus.LABELED.getCode());
        }

        List<Long> subTaskIds = labelingSubTasks.stream()
                .map(LabelingSubTask::getId)
                .collect(Collectors.toList());
        return new ExportTaskData(subTaskIds, LabelingDetailStatus.LABELED.getCode());
    }

    /**
     * 获取普通用户导出数据
     *
     * @param labelingTask 标注任务
     * @return 导出任务数据
     */
    private ExportTaskData getNormalExportData(LabelingTask labelingTask, RecycleTaskParam param) {
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(param.getGroupConifgList()), "请选择标注人");
        if (Objects.isNull(labelingTask.getLabelingStatus())
                || LabelingTaskStatus.COMPLETE.getCode() != labelingTask.getLabelingStatus()) {
            throw new AidaTrainingCheckException("只有标注完成的任务才能导出");
        }
        List<Long> recycleSubTaskIds = param.getGroupConifgList().stream().map(RecycleTaskParam.GroupConfig::getSubTaskId).collect(Collectors.toList());
        return new ExportTaskData(recycleSubTaskIds, LabelingDetailStatus.LABELED.getCode());
    }

    /**
     * 提交导出任务
     *
     * @param labelingTask   标注任务
     * @param exportTaskData 导出任务数据
     */
    private void submitExportTask(LabelingTask labelingTask, ExportTaskData exportTaskData) {
        String mis = UserUtils.getUser().getLogin();
        // 提交导出任务
        EXPORT_LABELING_DATA_EXECUTOR.submit(() -> createTaskStrategyFactory.runStrategy(
                labelingTask.getDataType(),
                labelingTask.getTaskName() + "_" + labelingTask.getId(),
                exportTaskData.getSubTaskIds(),
                exportTaskData.getLabelDetailStatus(),
                mis));
    }

    /**
     * 导出标注数据
     *
     * @param param 请求参数
     */
    @Override
    public void recycleLabelingData(RecycleTaskParam param) {
        log.info("开始导出标注数据, 参数: {}", JSON.toJSONString(param));
        // 获取当前用户MIS
        String userMis = getCurrentUserMis();
        // 校验任务是否存在
        LabelingTask labelingTask = validateAndGetTask(param, userMis);
        // 获取子任务ID和标注状态
        ExportTaskData exportTaskData = getExportTaskData(labelingTask, userMis, param);
        // 提交导出任务
        submitExportTask(labelingTask, exportTaskData);
    }

    /**
     * 质检分配
     *
     * @param param 质检参数
     */
    @Override
    public void distributeQualityCheck(QualityCheckDistributeParam param) {
        CheckUtil.paramCheck(Objects.nonNull(param)
                && Objects.nonNull(param.getTaskId())
                && CollectionUtils.isNotEmpty(param.getLabelers())
                && CollectionUtils.isNotEmpty(param.getGroupConfigList()), "质检分配参数错误!");
        LabelingTask labelingTask = labelingTaskRepository.getById(param.getTaskId());
        CheckUtil.operationCheck(Objects.isNull(labelingTask.getQualityCheckStatus()) || QualityCheckTaskStatus.getDistributeStatus().contains(labelingTask.getQualityCheckStatus()),
                "当前状态不支持分配质检任务");
        // 过滤数量为0的分组
        List<QualityCheckDistributeParam.GroupConfig> groupConfigs = param.getGroupConfigList().stream().filter(groupConfig -> groupConfig.getCount() > 0).collect(Collectors.toList());
        CheckUtil.operationCheck(CollectionUtils.isNotEmpty(groupConfigs), "所有分组数量均为0，无需分配");
        param.setGroupConfigList(groupConfigs);
        // 尝试获取分发质检任务的锁
        boolean isLock = tryLockDistributeQuality(param.getTaskId());
        CheckUtil.operationCheck(isLock, "质检任务正在分发中，请稍后重试!");
        try {
            // 任务状态更新为创建中
            labelingTask.setQualityCheckStatus(QualityCheckTaskStatus.CREATING.getCode());
            labelingTaskRepository.updateByPrimaryKeySelective(labelingTask);
            // 为每个选定的质检员生成质检任务
            List<LabelingQualityCheckTask> qualityCheckTasks = buildQualityCheckTask(param, labelingTask);
            // 同步批量插入质检任务
            labelingQualityCheckTaskRepository.batchInsert(qualityCheckTasks);
            // 异步为质检任务生成"质检任务抽检数量"个质检详情状态（待质检）
            QUALITY_CHECK_TASK_EXECUTOR.submit(() -> addQualityCheckDetails(param, labelingTask, qualityCheckTasks));
        } catch (Exception e) {
            // 发生异常后释放锁，没有发生异常在异步任务中释放锁
            redisStoreClient.delete(new StoreKey(RedisConstant.DISTRIBUTE_QUALITY_TASK_ID, param.getTaskId()));
        }

    }

    /**
     * 创建标注任务
     * <p>
     * param
     */
    @Override
    public Long createLabelingTask(LabelingTaskParam param) {
        checkCreateLabelingTaskParam(param);
        MultipartFile file = param.getFile();
        // 校验文件行数是否超过限制（包括校验是否有数据内容）
        checkFileRowsLimit(file);

        CreateTaskStrategy createTaskStrategy = createTaskStrategyFactory.getInstance(param.getDataType());
        createTaskStrategy.checkHeader(file);
        // 存储任务表
        LabelingTask labelingTask = buildLabelingTask(param);
        labelingTaskRepository.insertSelective(labelingTask);
        log.info("任务创建成功, 任务ID: {}, 任务名称: {}", labelingTask.getId(), labelingTask.getTaskName());
        // 异步处理标注任务原数据
        RAW_DATA_PROCESS_EXECUTOR.submit(() -> processLabelingTaskRawData(file, labelingTask, createTaskStrategy));

        return labelingTask.getId();
    }

    private void checkCreateLabelingTaskParam(LabelingTaskParam param) {
        CheckUtil.paramCheck(Objects.nonNull(param) && StringUtils.isNotBlank(param.getTaskName())
                        && StringUtils.isNotBlank(param.getResponsiblePerson())
                        && StringUtils.isNotBlank(param.getResponsiblePersonName()) && ObjectUtil.isNotNull(param.getDataType())
                        && ObjectUtil.isNotNull(param.getTaskType()) && CollectionUtils.isNotEmpty(param.getLabelResultList()),
                "创建标注任务参数错误!");
        // 判断是否存在必填项
        boolean existRequired = param.getLabelResultList().stream().flatMap(Collection::stream)
                .anyMatch(
                        labelResult -> Objects.equals(labelResult.getIsRequired(), LabelingItemRequired.YES.getCode()));
        CheckUtil.paramCheck(existRequired, "标注项数据必须有一个是必填的!");
        param.getLabelResultList().forEach(labelResults -> labelResults.forEach(labelResult -> {
            CheckUtil.paramCheck(StringUtils.isNotBlank(labelResult.getName()), "标注项名称不能为空!");
            CheckUtil.paramCheck(Objects.nonNull(labelResult.getDataType()), "标注项类型不能为空!");
            if (labelResult.getDataType() == LabelingDataType.ENUMERATION.getCode()) {
                CheckUtil.paramCheck(CollectionUtils.isNotEmpty(labelResult.getEnumList()), "枚举类型标注项的枚举列表不能为空!");
            }
        }));
        if (Objects.isNull(param.getBizType())){
            param.setBizType(-1L);
        }
        // 选择了业务类型才校验
        if (!Objects.equals(param.getBizType(), -1L)){
            List<BaseNameCodeVO> bizTypes = dashboardStatisticsService.getBizTypes();
            CheckUtil.paramCheck(CollectionUtils.isNotEmpty(bizTypes), "当前无生效的业务场景");
            Set<String> bizTypeIds = bizTypes.stream().map(BaseNameCodeVO::getCode).collect(Collectors.toSet());
            CheckUtil.paramCheck(bizTypeIds.contains(String.valueOf(param.getBizType())), "请选择正确的业务场景");
        }

    }

    /**
     * 任务列表
     *
     * @param taskName 任务名称
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 任务列表
     */
    @Override
    public PageData<LabelingTaskDTO> taskList(String taskName, Long taskId, Integer pageNum, Integer pageSize) {
        log.info("查询标注任务列表, 任务名称: {}, 页码: {}, 每页数量: {}", taskName, pageNum, pageSize);
        // 获取当前用户信息
        User user = UserUtils.getUser();
        String mis = user != null ? user.getLogin() : null;
        // 判断是否为管理员，管理员不限制查询结果
        boolean isAdmin = permissionService.isAdminUser();
        // 根据任务名称和标注人分页查询任务列表
        PageHelper.startPage(pageNum, pageSize);
        List<LabelingTask> labelingTaskList = labelingTaskRepository.listByIdNameAndMis(taskName, taskId, mis, isAdmin);
        if (CollectionUtils.isEmpty(labelingTaskList)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<LabelingTask> pageInfo = new PageInfo<>(labelingTaskList);
        PageHelper.clearPage();
        // 获取主任务，查询每个主任务的原数据数据量
        List<Long> taskIds = labelingTaskList.stream().map(LabelingTask::getId).collect(Collectors.toList());
        List<LabelingTaskRawDataPO> rawDataList = labelingTaskRawDataRepository.getCountByTaskIds(taskIds);
        // 将主任务ID和原数据数据量转成map
        Map<Long, Integer> map = rawDataList.stream()
                .collect(Collectors.toMap(LabelingTaskRawDataPO::getTaskId, LabelingTaskRawDataPO::getTotalNum));
        // 构建返回结果
        List<LabelingTaskDTO> labelingTaskDTOList = labelingTaskList.stream()
                .map(item -> buildLabelingTaskDTO(item, map))
                .collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), labelingTaskDTOList);
    }

    /**
     * 构建标注任务DTO
     *
     * @param labelingTask 标注任务
     * @param map          任务ID和原数据数据量的映射
     * @return 返回构建好的标注任务DTO
     */
    private LabelingTaskDTO buildLabelingTaskDTO(LabelingTask labelingTask,
                                                 Map<Long, Integer> map) {
        TaskSourceEnum taskSourceEnum = TaskSourceEnum.getByCode(labelingTask.getTaskSource());
        return LabelingTaskDTO.builder()
                .id(labelingTask.getId())
                .taskName(labelingTask.getTaskName())
                .dataSetSource(Objects.isNull(taskSourceEnum) ? "未知" : taskSourceEnum.getValue())
                .dataTotalCount(map.get(labelingTask.getId()))
                .labelingStatus(labelingTask.getLabelingStatus())
                .qualityCheckStatus(labelingTask.getQualityCheckStatus())
                .responsiblePersonMis(labelingTask.getLabelingManager())
                .responsiblePersonName(labelingTask.getLabelingManagerName())
                .taskType(labelingTask.getTaskType())
                .dataType(labelingTask.getDataType())
                .assignType(labelingTask.getAssignType())
                .createTime(labelingTask.getCreateTime())
                .build();
    }

    @Override
    public TaskGroupDTO listGroupsByTaskId(Long taskId) {
        log.info("查询任务组列表, 任务ID: {}", taskId);
        CheckUtil.paramCheck(Objects.nonNull(taskId), "任务id不能为空!");
        TaskGroupDTO taskGroupDTO = new TaskGroupDTO();
        List<LabelTaskGroupDTO> labelTaskGroupDTOList = labelingTaskGroupRepository.listLabelGroupByTaskId(taskId);
        if (CollectionUtils.isEmpty(labelTaskGroupDTOList)) {
            taskGroupDTO.setGroupList(Collections.emptyList());
            return taskGroupDTO;
        }
        // 找出所有父分组
        List<LabelTaskGroupDTO> parentGroups = labelTaskGroupDTOList.stream()
                .filter(group -> group.getParentId() == null || group.getParentId() == -1)
                .collect(Collectors.toList());
        // 处理每个父分组
        parentGroups.forEach(parentGroup -> {
            parentGroup.setLabelers(getLabelers(parentGroup.getTaskId(), parentGroup.getId()));
            List<LabelTaskGroupDTO> allChildren = new ArrayList<>();
            findAllChildren(parentGroup.getId(), labelTaskGroupDTOList, allChildren);
            allChildren.sort(Comparator.comparing(LabelTaskGroupDTO::getUpdateTime).reversed());
            parentGroup.setSubGroupList(allChildren);
        });
        parentGroups.sort(Comparator.comparing(LabelTaskGroupDTO::getUpdateTime).reversed());
        taskGroupDTO.setGroupList(parentGroups);
        return taskGroupDTO;
    }

    @Override
    public void distributeLabelingTask(LabelingDistributionParam param) {
        List<LabelingDistributionGroupParam> groupParams = param.getGroupConfigList();
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(groupParams), "分配入参信息不能为空!");
        // 获取任务ID
        Long taskId = Optional.ofNullable(groupParams.get(0))
                .map(LabelingDistributionGroupParam::getTaskId)
                .orElse(null);
        CheckUtil.paramCheck(Objects.nonNull(taskId), "任务ID不能为空!");
        // 尝试获取分布式锁，用于分配标注任务
        boolean isLock = tryLockDistributeLabeling(taskId);
        CheckUtil.operationCheck(isLock, "当前任务正在分配中，请稍后重试");
        try {
            LabelingTask labelingTask = labelingTaskRepository.getById(taskId);
            CheckUtil.operationCheck(Objects.nonNull(labelingTask), "任务不存在");
            CheckUtil.operationCheck(LabelingTaskStatus.WAITING_ASSIGNED.getCode() == labelingTask.getLabelingStatus(),
                    "当前任务状态不是待分配状态，无法进行分配");
            // 原属数据数量
            long rawDataCount = labelingTaskRawDataRepository.countByTaskId(taskId);
            CheckUtil.operationCheck(rawDataCount != 0, "任务原始数据为空，无法分配!");
            // 校验分配的数据
            checkDistributeRawData(groupParams, rawDataCount);
            // 修改任务状态为分配中
            labelingTask.setLabelingStatus(LabelingTaskStatus.ASSIGNING.getCode());
            labelingTask.setAssignType(param.getAssignType());
            labelingTaskRepository.updateByPrimaryKeySelective(labelingTask);
            // 异步分配任务
            TASK_DISTRIBUTION_EXECUTOR.submit(() -> processLabelingData(labelingTask, param));
        } catch (Exception e) {
            // 发生异常后释放锁，没有发生异常在异步任务中释放锁
            redisStoreClient.delete(new StoreKey(RedisConstant.DISTRIBUTE_LABELING_TASK_ID, taskId));
            throw e;
        }
    }

    @Override
    public void deleteLabelingTask(Long taskId) {
        log.info("开始删除标注任务, 任务ID: {}", taskId);
        CheckUtil.paramCheck(Objects.nonNull(taskId), "任务ID不能为空!");
        LabelingTask labelingTask = labelingTaskRepository.getById(taskId);
        CheckUtil.paramCheck(Objects.nonNull(labelingTask), "任务不存在", taskId);
        // 删除主任务
        labelingTask.setIsDeleted(Boolean.TRUE);
        labelingTaskRepository.updateByPrimaryKeySelective(labelingTask);
        log.info("成功删除主任务, 任务ID: {}", taskId);
        // 删除标注任务
        List<LabelingSubTask> subTaskList = labelingSubTaskRepository.listAllByTaskId(taskId);
        if (CollectionUtils.isNotEmpty(subTaskList)) {
            subTaskList.forEach(subTask -> {
                subTask.setIsDeleted(Boolean.TRUE);
                labelingSubTaskRepository.updateById(subTask);
            });
            log.info("成功删除标注子任务, 任务ID: {}, 子任务数量: {}", taskId, subTaskList.size());
        }
        // 删除质检任务
        List<LabelingQualityCheckTask> qualityCheckTaskList = labelingQualityCheckTaskRepository.listByTaskId(taskId);
        if (CollectionUtils.isNotEmpty(qualityCheckTaskList)) {
            qualityCheckTaskList.forEach(qualityCheckTask -> {
                qualityCheckTask.setIsDeleted(Boolean.TRUE);
                labelingQualityCheckTaskRepository.updateById(qualityCheckTask);
            });
            log.info("已处理质检任务, 任务ID: {}, 质检任务数量: {}", taskId, qualityCheckTaskList.size());
        }
        log.info("标注任务删除完成, 任务ID: {}", taskId);
    }

    @Override
    public PageData<LabelingTaskRawDataDTO> pageRawDataByTaskId(Long taskId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        CheckUtil.paramCheck(Objects.nonNull(taskId), "任务ID不能为空!");
        List<LabelingTaskRawData> rawDataByTaskId = labelingTaskRawDataRepository.getRawDataByTaskId(taskId);
        if (CollectionUtils.isEmpty(rawDataByTaskId)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<LabelingTaskRawData> pageInfo = new PageInfo<>(rawDataByTaskId);
        PageHelper.clearPage();
        List<LabelingTaskRawDataDTO> rawDataDTOList = rawDataByTaskId.stream()
                .map(LabelingRawDataConvert::convertRawDataDTO).filter(Objects::nonNull).collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), rawDataDTOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLabelingTemplates(InspectionItemTemplateParam inspectionItemTemplateParam) {
        // 校验入参
        checkInspectionItemParam(inspectionItemTemplateParam);
        List<InspectionItemDetail> templateItems = inspectionItemTemplateParam.getTemplateItems();
        User user = UserUtils.getUser();
        String mis = user != null ? user.getLogin() : CommonConstants.FieldName.UNKNOWN;
        if (inspectionItemTemplateParam.getId() == null) {
            log.info("保存标注模板: {},", JSON.toJSONString(inspectionItemTemplateParam));
            InspectionItemTemplate template = createInspectionItemTemplate(inspectionItemTemplateParam, mis);
            // 保存所有标注项
            for (int i = 0; i < templateItems.size(); i++) {
                saveInspectionItem(templateItems.get(i), template.getId(), i + 1, mis);
            }
            log.info("新增标注模板成功, templateId: {}", template.getId());
        } else {
            Long templateId = inspectionItemTemplateParam.getId();
            log.info("更新标注模板,templateId: {}", templateId);
            updateItemTemplate(inspectionItemTemplateParam, templateId, mis);
            // 获取数据库中已有的标注项
            List<InspectionItem> inspectionItems = inspectionItemRepository.listByTemplateId(templateId);
            Map<Long, InspectionItem> existingItemMap = inspectionItems.stream()
                    .collect(Collectors.toMap(InspectionItem::getId, item -> item));
            // 记录需要保留的标注项ID
            Set<Long> retainedItemIds = new HashSet<>();
            // 处理标注项
            for (int i = 0; i < templateItems.size(); i++) {
                InspectionItemDetail inspectionItemDetail = templateItems.get(i);
                if (inspectionItemDetail.getId() != null && existingItemMap.containsKey(inspectionItemDetail.getId())) {
                    // 更新已有标注项
                    updateInspectionItem(inspectionItemDetail, templateId, i + 1, mis);
                    retainedItemIds.add(inspectionItemDetail.getId());
                } else {
                    // 新增标注项
                    saveInspectionItem(inspectionItemDetail, templateId, i + 1, mis);
                }
            }
            // 删除数据库中有但传入参数中没有的标注项（已被删除的）
            List<InspectionItem> itemsToDeleteList = inspectionItems.stream()
                    .filter(item -> !retainedItemIds.contains(item.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemsToDeleteList)) {
                itemsToDeleteList.forEach(item -> item.setIsDeleted(Boolean.TRUE));
                inspectionItemRepository.batchUpdateItems(itemsToDeleteList);
            }
            log.info("更新标注模板成功, templateId: {}", templateId);
        }
    }

    @Override
    @SuppressWarnings("all")
    public LabelingGroupDetailDTO listGroupConsistency(LabelingGroupConsistencyParam param) {
        // 查询总数量和标注人mis
        LabelingTaskGroup labelingTaskGroup = labelingTaskGroupRepository.getLabelGroupById(param.getGroupId());
        CheckUtil.operationCheck(labelingTaskGroup != null, "分组不存在");
        List<LabelingSubTask> subTaskList = labelingSubTaskRepository.listByGroupId(param.getGroupId());
        CheckUtil.operationCheck(CollectionUtils.isNotEmpty(subTaskList), "分组下子任务为空");
        LabelingGroupDetailDTO labelingGroupDetailDTO = new LabelingGroupDetailDTO();
        labelingGroupDetailDTO.setTotalCount(labelingTaskGroup.getTotalDataCount());
        if (labelingTaskGroup.getConsistencyRate() == null) {
            labelingGroupDetailDTO.setSameCount(0);
        } else {
            labelingGroupDetailDTO
                    .setSameCount(labelingTaskGroup.getTotalDataCount() * labelingTaskGroup.getConsistencyRate() / 100);
        }
        labelingGroupDetailDTO
                .setDiffCount(labelingGroupDetailDTO.getTotalCount() - labelingGroupDetailDTO.getSameCount());
        Map<String, String> labelingMisMap = subTaskList.stream()
                .collect(Collectors.toMap(
                        LabelingSubTask::getLabelerMis,
                        LabelingSubTask::getLabelerName,
                        (existing, replacement) -> existing));
        labelingGroupDetailDTO.setLabelingMisMap(labelingMisMap);

        // 分页查询一致信息，随机取一个子任务分页查询，查询完成后补充其余子任务的分页信息
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        LabelingSubTask labelingSubTask = subTaskList.get(0);
        List<LabelingDetail> labelingTaskRawDataList = labelingDetailRepository
                .listBySubTaskIdAndConsistency(labelingSubTask.getId(), param.getConsistency());
        PageInfo<LabelingDetail> pageInfo = new PageInfo<>(labelingTaskRawDataList);
        PageHelper.clearPage();
        // 查询原始数据
        List<Long> rawDataIdList = labelingTaskRawDataList.stream().map(LabelingDetail::getRawDataId)
                .collect(Collectors.toList());
        List<LabelingTaskRawData> rawDataList = labelingTaskRawDataRepository.listByIds(rawDataIdList);
        Map<Long, LabelingTaskRawData> rawDataMap = rawDataList.stream()
                .collect(Collectors.toMap(LabelingTaskRawData::getId, Function.identity()));
        Map<Long, List<LabelingDetail>> rawLabelingDetailMap;
        // 大于1个标注人时才进行对比
        if (subTaskList.size() > 1) {
            // 获取其他标注人的标注信息
            List<Long> ohterSubTaskIdList = subTaskList.stream().map(LabelingSubTask::getId)
                    .filter(id -> !id.equals(labelingSubTask.getId())).collect(Collectors.toList());
            List<LabelingDetail> otherLabelingDetailList = labelingDetailRepository
                    .listBySubTaskIdListAndRawDataIdList(ohterSubTaskIdList, rawDataIdList);
            rawLabelingDetailMap = otherLabelingDetailList.stream()
                    .collect(Collectors.groupingBy(LabelingDetail::getRawDataId));
        } else {
            rawLabelingDetailMap = new HashMap<>();
        }
        // 组装返回
        List<LabelingGroupDTO> labelingGroupDTOList = labelingTaskRawDataList.stream().map(labelingDetail -> {
            return convertLabelingGroupResult(rawDataMap, rawLabelingDetailMap, labelingDetail);
        }).collect(Collectors.toList());
        labelingGroupDetailDTO.setPageData(new PageData<>(pageInfo.getTotal(), labelingGroupDTOList));
        return labelingGroupDetailDTO;
    }

    @Override
    public GroupConfigList listLabelingTemplateGroup(Long taskId) {
        GroupConfigList groupConfigList = new GroupConfigList();
        // 查询任务下所有分组
        List<LabelingTaskGroup> labelingTaskGroupList = labelingTaskGroupRepository.listAllLabelGroupByTaskId(taskId);
        if (CollectionUtils.isEmpty(labelingTaskGroupList)) {
            return groupConfigList;
        }
        // 获取所有子任务和统计总数和可分配数量
        LabelingTask labelingTask = labelingTaskRepository.getById(taskId);
        CheckUtil.paramCheck(labelingTask != null, "任务不存在或已被删除");
        List<LabelingSubTask> subTaskList = labelingSubTaskRepository.listAllByTaskId(taskId);
        List<LabelingSubtaskCountPo> subtaskCountPoList = labelingSubTaskRepository.countSubTask(taskId);
        Map<Long, List<LabelingSubTask>> subTaskMap = subTaskList.stream().collect(Collectors.groupingBy(LabelingSubTask::getGroupId));
        Map<Long, List<LabelingSubtaskCountPo>> subtaskCountPoMap = subtaskCountPoList.stream().collect(Collectors.groupingBy(LabelingSubtaskCountPo::getGroupId));
        // 处理每个分组，拼接所需信息
        List<GroupConfigList.GroupData> groupDataList = labelingTaskGroupList.stream().map(labelingTaskGroup -> convertLabelingGroup(labelingTask, labelingTaskGroup, subTaskMap, subtaskCountPoMap)).collect(Collectors.toList());
        groupConfigList.setGroupDataList(groupDataList);
        return groupConfigList;
    }

    @Override
    public LabelingTaskCountDTO getLabelingTaskCount(Long taskId) {
        return labelingTaskRawDataRepository.listCountByTaskId(taskId);
    }

    @Override
    public TransferInfoDTO getTransferInfo(Long taskId, Integer transferType) {
        return transferStrategyFactory.getTransferStrategy(transferType).getTransferInfo(taskId, transferType);
    }

    @Override
    public void transfer(TaskTransferParam param) {
        // 异步执行任务转交
        TASK_TRANSFER_EXECUTOR.submit(() -> {
            // 任务维度加锁，每个任务同时只能有一个转交任务在执行
            Long taskId = transferStrategyFactory.getTransferStrategy(param.getTransferType()).getLabelingTaskId(param.getSubTaskId());
            boolean lock = tryLockTransfer(taskId);
            if (lock) {
                try {
                    transferStrategyFactory.getTransferStrategy(param.getTransferType()).transfer(param);
                } finally {
                    unlockTransfer(taskId);
                }
            }
        });
    }

    /**
     * 尝试获取任务锁
     *
     * @param taskId 任务ID
     * @return 是否获取到锁
     */
    private boolean tryLockTransfer(Long taskId) {
        try {
            return redisStoreClient.set(new StoreKey(RedisConstant.TASK_TRANSFER_LOCK, taskId), taskId);
        } catch (Exception e) {
            log.error("获取任务转交锁失败, taskId:{}", taskId, e);
            Cat.logError(e);
        }
        // 如果获取锁异常，默认不加锁，避免影响业务使用
        return true;
    }

    /**
     * 释放任务锁
     *
     * @param taskId 任务ID
     */
    private void unlockTransfer(Long taskId) {
        try {
            redisStoreClient.delete(new StoreKey(RedisConstant.TASK_TRANSFER_LOCK, taskId));
        } catch (Exception e) {
            log.error("释放任务转交锁失败, taskId:{}", taskId, e);
            Cat.logError(e);
        }
    }

    /**
     * 组装分组信息
     *
     * @param labelingTaskGroup 分组信息
     * @return GroupData 分组数据对象
     */
    private GroupConfigList.GroupData convertLabelingGroup(LabelingTask labelingTask, LabelingTaskGroup labelingTaskGroup, Map<Long, List<LabelingSubTask>> subTaskMap, Map<Long, List<LabelingSubtaskCountPo>> subtaskCountPoMap) {
        GroupConfigList.GroupData groupData = new GroupConfigList.GroupData();
        groupData.setGroupId(labelingTaskGroup.getId());
        groupData.setGroupName(labelingTaskGroup.getGroupName());
        groupData.setTotalCount(labelingTaskGroup.getTotalDataCount());
        List<LabelingSubtaskCountPo> subtaskCountList = subtaskCountPoMap.get(labelingTaskGroup.getId());
        if (CollectionUtils.isNotEmpty(subtaskCountList)) {
            // 一个分组下的每个子任务是否被抽检状态是一致的，因此只需要按照其中一个子任务统计即可
            LabelingSubtaskCountPo subtaskCountPo = subtaskCountList.get(0);
            if (labelingTask.getAssignType() == AssignTypeEnum.SESSION.getCode()) {
                groupData.setAssignCount(subtaskCountPo.getTotalSessionCount() - subtaskCountPo.getInspectSessionCount());
            } else {
                groupData.setAssignCount(subtaskCountPo.getTotalCount() - subtaskCountPo.getInspectCount());
            }
        }
        List<LabelingSubTask> subTaskList = subTaskMap.get(labelingTaskGroup.getId());
        if (CollectionUtils.isNotEmpty(subTaskList)) {
            List<GroupConfigList.SubTaskConfig> subTaskConfigList = subTaskList.stream().map(subTask -> {
                GroupConfigList.SubTaskConfig subTaskConfig = new GroupConfigList.SubTaskConfig();
                subTaskConfig.setId(subTask.getId());
                subTaskConfig.setLabelMis(subTask.getLabelerMis());
                subTaskConfig.setLabelName(subTask.getLabelerName());
                return subTaskConfig;
            }).collect(Collectors.toList());
            groupData.setSubTaskConfigList(subTaskConfigList);
        }
        return groupData;
    }

    /**
     * 组装返回结果
     *
     * @param rawDataMap           原始数据映射表
     * @param rawLabelingDetailMap 原始数据标注信息映射表
     * @param labelingDetail       当前标注信息
     * @return LabelingGroupDTO 标注分组DTO对象
     */
    private LabelingGroupDTO convertLabelingGroupResult(Map<Long, LabelingTaskRawData> rawDataMap,
                                                        Map<Long, List<LabelingDetail>> rawLabelingDetailMap, LabelingDetail labelingDetail) {
        LabelingGroupDTO labelingGroup = new LabelingGroupDTO();
        // 原始数据
        LabelingTaskRawData rawData = rawDataMap.get(labelingDetail.getRawDataId());
        if (Objects.isNull(rawData)) {
            log.error("标注回收详情查看原数据缺失!标注详情数据ID：{}", labelingDetail.getId());
            throw new AidaTrainingServiceException("回收详情查看原数据缺失");
        }
        // 其他人标注信息
        List<LabelingDetail> otherRawDataLabelingDetailList = rawLabelingDetailMap.get(labelingDetail.getRawDataId());
        List<LabelingDetail> compareLabelingDetailList = new ArrayList<>();
        compareLabelingDetailList.add(labelingDetail);
        // 判断其他人标注信息是否为空，如果不为空则将其他人标注信息添加到比较标注信息列表中
        if (CollectionUtils.isNotEmpty(otherRawDataLabelingDetailList)) {
            compareLabelingDetailList.addAll(otherRawDataLabelingDetailList);
        }
        labelingGroup.setId(rawData.getId());
        labelingGroup.setRawDataContent(rawData.getRawDataContent());
        labelingGroup.setRawDataMappedContent(rawData.getRawDataMappedContent());
        labelingGroup.setIsConsistent(Objects.equals(labelingDetail.getComparItemsConsistent(), IS_COMPARE));
        labelingGroup.setSessionId(rawData.getSessionId());
        labelingGroup.setSessionTime(DateUtil.format(rawData.getSessionTime(), CommonConstants.DatePattern.DATE_TIME));
        // 背靠背一致标注项
        List<LabelingGroupDTO.LabelingGroupItem> compareLabelingItems = new ArrayList<>();
        // 非背靠背一致标注项
        List<LabelingGroupDTO.LabelingGroupItem> noCompareLabelingItems = new ArrayList<>();
        compareLabelingDetailList.forEach(curlabelingDetail ->
                // 构造指标结果，区分背靠背一致指标
                buildLabelingItems(compareLabelingItems, noCompareLabelingItems, curlabelingDetail));
        if (CollectionUtils.isNotEmpty(compareLabelingItems)) {
            labelingGroup.setCompareLabelingItems(compareLabelingItems);
        }
        if (CollectionUtils.isNotEmpty(noCompareLabelingItems)) {
            labelingGroup.setNoCompareLabelingItems(noCompareLabelingItems);
        }
        return labelingGroup;
    }

    /**
     * 构造指标结果
     *
     * @param compareLabelingItems   背靠背一致指标列表
     * @param noCompareLabelingItems 非背靠背一致指标列表
     * @param curLabelingDetail      当前标注信息
     */
    private void buildLabelingItems(List<LabelingGroupDTO.LabelingGroupItem> compareLabelingItems,
                                    List<LabelingGroupDTO.LabelingGroupItem> noCompareLabelingItems, LabelingDetail curLabelingDetail) {
        String labelingItemResultStr = curLabelingDetail.getLabelingItemsResult();
        List<List<LabelResult>> labelingItemResultList = JSON.parseObject(labelingItemResultStr,
                new TypeReference<List<List<LabelResult>>>() {
                });
        List<LabelingGroupDTO.LabelingResult> compareLabelingResult = new ArrayList<>();
        List<LabelingGroupDTO.LabelingResult> noCompareLabelingResult = new ArrayList<>();
        labelingItemResultList.forEach(labelResults -> {
            labelResults.forEach(labelResult -> {
                LabelingGroupDTO.LabelingResult labelingResult = new LabelingGroupDTO.LabelingResult();
                labelingResult.setName(labelResult.getName());
                labelingResult.setValue(labelResult.getValue());
                if (IS_COMPARE.equals(labelResult.getIsCompare())) {
                    compareLabelingResult.add(labelingResult);
                } else {
                    noCompareLabelingResult.add(labelingResult);
                }
            });
        });
        // 背靠背一致率指标
        LabelingGroupDTO.LabelingGroupItem compareLabelingGroupItem = new LabelingGroupDTO.LabelingGroupItem();
        compareLabelingGroupItem.setLabelerName(
                StringUtils.isNotBlank(curLabelingDetail.getLabelerName()) ? curLabelingDetail.getLabelerName()
                        : curLabelingDetail.getLabelerMis());
        compareLabelingGroupItem
                .setLabelingTime(DateUtil.format(curLabelingDetail.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
        if (CollectionUtils.isNotEmpty(compareLabelingResult)) {
            compareLabelingGroupItem.setLabelingItemResult(compareLabelingResult);
        }
        compareLabelingItems.add(compareLabelingGroupItem);
        // 非背靠背一致率指标 有非背靠背一致率标注项时才放入标注人姓名和标注时间
        if (CollectionUtils.isNotEmpty(noCompareLabelingResult)) {
            LabelingGroupDTO.LabelingGroupItem noCompareLabelingGroupItem = new LabelingGroupDTO.LabelingGroupItem();
            noCompareLabelingGroupItem.setLabelerName(
                    StringUtils.isNotBlank(curLabelingDetail.getLabelerName()) ? curLabelingDetail.getLabelerName()
                            : curLabelingDetail.getLabelerMis());
            noCompareLabelingGroupItem
                    .setLabelingTime(DateUtil.format(curLabelingDetail.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            noCompareLabelingGroupItem.setLabelingItemResult(noCompareLabelingResult);
            noCompareLabelingItems.add(noCompareLabelingGroupItem);
        }
    }

    public void processLabelingData(LabelingTask labelingTask, LabelingDistributionParam param) {
        Long taskId = labelingTask.getId();
        List<LabelingDistributionGroupParam> groupParams = param.getGroupConfigList();
        try {
            // 上一个分组分配到的位置
            int lastGroupIndex = 0;
            for (LabelingDistributionGroupParam groupParam : groupParams) {
                Map<String, String> labelers = groupParam.getLabelers();
                // step1:创建分组表
                LabelingTaskGroup labelingTaskGroup = createLabelingTaskGroup(taskId, groupParam, labelers);
                // step2:创建子任务表
                List<LabelingSubTask> labelingSubTasks = createLabelingSubTasks(labelingTask, taskId, groupParam,
                        labelers, labelingTaskGroup, param.getAssignType());
                // step3:分配标注数据
                lastGroupIndex = distributeData(lastGroupIndex, labelingTask, labelingTaskGroup.getId(),
                        labelingSubTasks, groupParam.getDataCount(), param.getAssignType());
                // step4:修改子任务状态到标注中
                for (LabelingSubTask labelingSubTask : labelingSubTasks) {
                    labelingSubTask.setStatus(LabelingSubTaskStatus.IN_LABELING.getCode());
                    labelingSubTask.setUpdateTime(new Date());
                    labelingSubTaskRepository.updateById(labelingSubTask);
                }
            }
            // step5:修改任务状态到标注中
            labelingTask.setLabelingStatus(LabelingTaskStatus.IN_LABELING.getCode());
            labelingTask.setUpdateTime(new Date());
            labelingTaskRepository.updateByPrimaryKeySelective(labelingTask);
            // 分配成功大象消息，通知用户分配任务成功
            sendSuccessNotification(taskId, groupParams);
        } catch (Exception e) {
            // 发送分配失败通知并更新主任务状态为待分配
            Cat.logError(CatConstant.DISTRIBUTE_LABELING_TASK_ERROR + "-" + labelingTask.getId(), e);
            log.error("分配任务失败,labelingTask={},groupParams={}", JSON.toJSONString(labelingTask),
                    JSON.toJSONString(groupParams), e);
            sendFailureNotification(labelingTask, e.getMessage());
            // 更新任务状态为待分配
            labelingTask.setLabelingStatus(LabelingTaskStatus.WAITING_ASSIGNED.getCode());
            labelingTask.setUpdateTime(new Date());
            labelingTaskRepository.updateByPrimaryKeySelective(labelingTask);
            // 更新分组表为失败
            labelingTaskGroupRepository.updateGroupStatusByTaskId(taskId, LabelingTaskGroupStatus.FAILED.getCode());
            // 更新子任务表为失败
            labelingSubTaskRepository.updateStatusByTaskId(taskId, LabelingSubTaskStatus.FAILED.getCode());
        } finally {
            // 清理redis缓存
            redisStoreClient.delete(new StoreKey(RedisConstant.DISTRIBUTE_LABELING_TASK_ID, taskId));
        }
    }

    /**
     * 创建子任务
     */
    public List<LabelingSubTask> createLabelingSubTasks(LabelingTask labelingTask, Long taskId, LabelingDistributionGroupParam groupParam,
                                                        Map<String, String> labelers, LabelingTaskGroup labelingTaskGroup, Integer assignType) {
        List<LabelingSubTask> labelingSubTasks = new ArrayList<>();
        labelers.forEach((mis, name) -> {
            LabelingSubTask labelingSubTask = new LabelingSubTask();
            labelingSubTask.setTaskId(taskId);
            labelingSubTask.setGroupId(labelingTaskGroup.getId());
            labelingSubTask.setName(labelingTask.getTaskName() + "-" + labelingTaskGroup.getGroupName());
            labelingSubTask.setLabelerName(name);
            labelingSubTask.setLabelerMis(mis);
            labelingSubTask.setStatus(LabelingSubTaskStatus.CREATING.getCode());
            labelingSubTask.setTotalDataCount(groupParam.getDataCount());
            labelingSubTask.setAssignType(assignType);
            User user = UserUtils.getUser();
            labelingSubTask.setCreatorId(user != null ? user.getLogin() : "unknwon");
            labelingSubTasks.add(labelingSubTask);
        });
        // 批量添加子任务数据
        labelingSubTaskRepository.batchInsertSubTask(labelingSubTasks);
        return labelingSubTasks;
    }

    /**
     * 创建分组
     */
    public LabelingTaskGroup createLabelingTaskGroup(Long taskId, LabelingDistributionGroupParam groupParam,
                                                     Map<String, String> labelers) {
        LabelingTaskGroup labelingTaskGroup = new LabelingTaskGroup();
        labelingTaskGroup.setTaskId(taskId);
        labelingTaskGroup.setGroupName(String.join("vs", labelers.values()));
        labelingTaskGroup.setParentId(groupParam.getParentId());
        labelingTaskGroup.setRecycleStatus(LabelingTaskGroupStatus.CREATING.getCode());
        labelingTaskGroup.setTotalDataCount(groupParam.getDataCount());
        labelingTaskGroupRepository.insertLabelingTaskGroup(labelingTaskGroup);
        return labelingTaskGroup;
    }

    public void checkDistributeRawData(List<LabelingDistributionGroupParam> groupParams, long totalCount) {
        long sum = 0;
        for (int i = 0; i < groupParams.size(); i++) {
            LabelingDistributionGroupParam param = groupParams.get(i);
            // 检查dataCount不为null且大于0
            if (param.getDataCount() == null || param.getDataCount() <= 0) {
                throw new AidaTrainingCheckException("第" + (i + 1) + "个分组的分配数量必须大于0");
            }
            // 累加分配数量
            sum += param.getDataCount();
            Map<String, String> labelers = param.getLabelers();
            CheckUtil.paramCheck(MapUtils.isNotEmpty(labelers), "标注人列表不能为空");
            // 检查所有entry的key和value是否有效
            boolean allEntriesValid = labelers.entrySet().stream()
                    .allMatch(entry -> entry.getKey() != null && !entry.getKey().trim().isEmpty() &&
                            entry.getValue() != null && !entry.getValue().trim().isEmpty());

            CheckUtil.paramCheck(allEntriesValid, "标注人信息不完整，MIS和姓名不能为空");
        }
//        CheckUtil.operationCheck(sum == totalCount, String.format("所有分组的分配数量总和:%s必须等于原始数据总数:%s", sum, totalCount));
    }

    /**
     * 执行数据分配
     */
    public int distributeData(int lastGroupIndex, LabelingTask labelingTask, Long groupId,
                              List<LabelingSubTask> labelingSubTasks, Integer totalDataCount, Integer assignType) {
        DistributionDimensionRequest request = DistributionDimensionRequest
                .builder()
                .lastGroupIndex(lastGroupIndex)
                .labelingTask(labelingTask)
                .groupId(groupId)
                .labelingSubTasks(labelingSubTasks)
                .totalDataCount(totalDataCount)
                .build();

        DimensionStrategy dimensionStrategy = dimensionStrategyFactory.getStrategy(assignType);
        return dimensionStrategy.labelingDistribute(request);
    }

    /**
     * 发送分配成功通知
     */
    public void sendSuccessNotification(Long taskId, List<LabelingDistributionGroupParam> groupParams) {
        try {
            Map<String, String> labelers = new HashMap<>(10);
            groupParams.forEach(groupParam -> labelers.putAll(groupParam.getLabelers()));
            LabelingTask task = labelingTaskRepository.getById(taskId);
            List<String> misList = new ArrayList<>(labelers.keySet());
            String content = String.format("您有一个新的标注任务「%s」已分配给您，请及时前往[训练系统|%s]处理。", task.getTaskName(),
                    lionConfig.getSubTaskUrl());
            // 给所有分配的标注人发送通知
            pushElephantService.pushElephant(content, misList);
            log.info("发送分配成功通知给标注人 {}", misList);
            // 给标注负责人发送大象通知
            String labelingManager = task.getLabelingManager();
            String managerContent = String.format("您负责的标注任务「%s」已成功分配，前往[训练系统|%s]查看分配结果。", task.getTaskName(),
                    lionConfig.getCreateDataSetNotifyElephantUrl());
            pushElephantService.pushElephant(managerContent, labelingManager);
            log.info("发送分配成功通知给负责人 {}", labelingManager);
        } catch (Exception e) {
            Cat.logError(CatConstant.PUSH_ELEPHANT_ERROR + "-" + taskId, e);
            log.error("大象发送消息失败, taskId: {}", taskId, e);
        }
    }

    /**
     * 发送分配失败通知
     */
    public void sendFailureNotification(LabelingTask task, String errorMsg) {
        try {
            String taskName = task.getTaskName();
            String creatorId = task.getCreatorMis();
            // 给任务创建者发送失败通知
            String content = String.format("您创建的标注任务「%s」分配失败，原因：%s，请前往[训练系统|%s]重新分配。", taskName, errorMsg,
                    lionConfig.getCreateDataSetNotifyElephantUrl());
            pushElephantService.pushElephant(content, creatorId);
            log.info("发送分配失败通知给创建者 {}", creatorId);
        } catch (Exception e) {
            log.error("发送分配失败通知失败, task: {}", JSON.toJSONString(task), e);
        }
    }

    /**
     * 查找所有子分组(包括所有层级)
     *
     * @param parentId              父分组ID
     * @param labelTaskGroupDTOList 分组列表
     * @param childrenResult        结果列表
     */
    private void findAllChildren(Long parentId, List<LabelTaskGroupDTO> labelTaskGroupDTOList,
                                 List<LabelTaskGroupDTO> childrenResult) {
        // 找出直接子分组
        List<LabelTaskGroupDTO> directChildren = labelTaskGroupDTOList.stream()
                .filter(group -> Objects.equals(group.getParentId(), parentId))
                .collect(Collectors.toList());

        // 处理子分组
        for (LabelTaskGroupDTO child : directChildren) {
            child.setLabelers(getLabelers(child.getTaskId(), child.getId()));
            // 添加到结果中
            childrenResult.add(child);
            // 递归查找子分组的子分组
            findAllChildren(child.getId(), labelTaskGroupDTOList, childrenResult);
        }
    }

    /**
     * 获取分组标注人
     *
     * @param taskId  任务ID
     * @param groupId 分组ID
     * @return 标注人
     */
    private String getLabelers(Long taskId, Long groupId) {
        List<LabelSubTaskDTO> labelSubTask = labelingSubTaskRepository.listSubTaskByGroupId(taskId, groupId);
        if (CollectionUtils.isEmpty(labelSubTask)) {
            return null;
        }
        List<String> labelerName = labelSubTask.stream().map(LabelSubTaskDTO::getLabelerName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return String.join(" vs ", labelerName);
    }

    /**
     * 质检模板列表
     *
     * @return 质检模板列表
     */
    @Override
    public List<InspectionItemTemplateDTO> templateList() {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        // 根据模版类型查询质检模板列表（目前只查询标注模版的，sql模版类型写死）
        List<InspectionItemTemplate> itemTemplateList = inspectionItemTemplateRepository.listByTemplateType();
        if (CollectionUtils.isEmpty(itemTemplateList)) {
            return Collections.emptyList();
        }
        List<Long> templateIdList = itemTemplateList.stream().map(InspectionItemTemplate::getId)
                .collect(Collectors.toList());
        List<InspectionItem> itemList = inspectionItemRepository.listByTemplateIdList(templateIdList);
        Map<Long, List<InspectionItem>> itemMap = itemList.stream()
                .collect(Collectors.groupingBy(InspectionItem::getTemplateId));
        ZebraForceMasterHelper.clearLocalContext();
        return itemTemplateList.stream()
                .map(itemTemplate -> convertToTemplateDTO(itemTemplate, itemMap))
                .collect(Collectors.toList());
    }

    /**
     * 将InspectionItemTemplate转换为InspectionItemTemplateDTO
     *
     * @param itemTemplate 模板实体
     * @param itemMap      按模板ID分组的标注项Map
     * @return 转换后的DTO对象
     */
    private InspectionItemTemplateDTO convertToTemplateDTO(InspectionItemTemplate itemTemplate,
                                                           Map<Long, List<InspectionItem>> itemMap) {
        InspectionItemTemplateDTO templateDTO = new InspectionItemTemplateDTO();
        templateDTO.setId(itemTemplate.getId());
        templateDTO.setTemplateName(itemTemplate.getTemplateName());
        templateDTO.setTemplateDesc(itemTemplate.getTemplateDesc());

        // 获取该模板下的所有标注项
        List<InspectionItem> items = itemMap.get(itemTemplate.getId());
        if (CollectionUtils.isNotEmpty(items)) {
            templateDTO.setKeys(convertItemsToKeysList(items));
        }

        return templateDTO;
    }

    /**
     * 将标注项列表转换为模板DTO中的keys结构
     *
     * @param items 标注项列表
     * @return 转换后的结构
     */
    private List<List<InspectionItemDTO>> convertItemsToKeysList(List<InspectionItem> items) {
        return items.stream()
                .sorted(Comparator.comparing(InspectionItem::getRankNum))
                .map(this::convertToInspectionItemDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将InspectionItem转换为InspectionItemDTO
     *
     * @param item 标注项实体
     * @return 包含单个DTO的列表
     */
    private List<InspectionItemDTO> convertToInspectionItemDTO(InspectionItem item) {
        InspectionItemDTO dto = new InspectionItemDTO();
        dto.setId(item.getId());
        dto.setName(item.getName());
        dto.setType(item.getDataType());
        dto.setIsCompare(item.getIsCompare());
        dto.setIsRequired(item.getIsRequired());

        // 处理枚举类型的值
        if (item.getDataType() == LabelingDataType.ENUMERATION.getCode()) {
            processEnumValues(dto, item.getEnumList());
        }

        return Collections.singletonList(dto);
    }

    /**
     * 处理枚举类型标注项的值
     *
     * @param dto         DTO对象
     * @param enumListStr 枚举值字符串（逗号分隔）
     */
    private void processEnumValues(InspectionItemDTO dto, String enumListStr) {
        if (StringUtils.isNotBlank(enumListStr)) {
            List<String> enumList = Arrays.asList(enumListStr.split(","));
            if (CollectionUtils.isNotEmpty(enumList)) {
                dto.setValue(enumList.stream()
                        .map(InspectionItemValueDTO::new)
                        .collect(Collectors.toList()));
            }
        }
    }

    /**
     * 质检项列表
     *
     * @param templateId 质检模板ID
     * @return 质检项列表
     */
    @Override
    public List<InspectionItemDTO> itemList(Long templateId) {
        List<InspectionItem> itemList = inspectionItemRepository.listByTemplateId(templateId);
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        return itemList.stream().map(item -> {
            InspectionItemDTO dto = new InspectionItemDTO();
            dto.setId(item.getId());
            dto.setName(item.getName());
            dto.setType(item.getDataType());
            dto.setIsCompare(item.getIsCompare());
            dto.setIsRequired(item.getIsRequired());
            if (item.getDataType() == LabelingDataType.ENUMERATION.getCode()) {
                List<String> enumList = item.getEnumList() != null ? Arrays.asList(item.getEnumList().split(","))
                        : Collections.emptyList();
                dto.setValue(enumList.stream().map(InspectionItemValueDTO::new)
                        .collect(Collectors.toList()));
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 质检概览
     *
     * @param taskId 主任务ID
     * @return 质检分组详情
     */
    @Override
    public QualityCheckOverviewVO qualityCheckDetails(Long taskId) {
        // 查询主任务ID下的所有质检任务
        List<LabelingQualityCheckTask> qualityCheckTaskList = labelingQualityCheckTaskRepository.listByTaskId(taskId);
        if (CollectionUtils.isEmpty(qualityCheckTaskList)) {
            log.warn("查询质检概览,无质检任务！");
            return QualityCheckOverviewVO.builder().
                    qualityCheckGroupItems(Collections.emptyList()).
                    completeData(0).
                    sampleData(0).
                    build();
        }
        // 获取质检任务ID
        List<Long> qualityCheckTaskIds = qualityCheckTaskList.stream().map(LabelingQualityCheckTask::getId)
                .collect(Collectors.toList());
        // 标注员维度统计质检任务质检情况
        List<LabelingDataCheckResultVO> checkDetailCountResult = labelingQualityCheckItemRepository.countCheckDetailByCheckTaskIds(qualityCheckTaskIds);

        // 质检任务Map Key 质检任务ID  value 质检任务详情
        Map<Long, LabelingQualityCheckTask> taskMap = qualityCheckTaskList.stream()
                .collect(Collectors.toMap(LabelingQualityCheckTask::getId, Function.identity()));
        //标注员维度质检详情统计
        List<QualityCheckGroupItemsVO> qualityCheckGroupItems = new ArrayList<>();
        checkDetailCountResult.forEach(result -> {
            QualityCheckGroupItemsVO qualityCheckGroupItemsVO = buildLabelingDataCheckResultVo(result, taskMap);
            if (Objects.nonNull(qualityCheckGroupItemsVO)) {
                qualityCheckGroupItems.add(qualityCheckGroupItemsVO);
            }
        });
        //按质检任务ID进行排序,让一个任务的内容展示在一起
        List<QualityCheckGroupItemsVO> qualityCheckGroupItemsSorted = qualityCheckGroupItems.stream().sorted(Comparator.comparing(QualityCheckGroupItemsVO::getQualityCheckTaskId)).collect(Collectors.toList());
        Integer completeDataCount = labelingQualityCheckItemRepository.countRawDataNumByCheckTaskIdsAndStatus(qualityCheckTaskIds, QualityCheckDataStatus.QUALITY_CHECKED.getCode());
        Integer sampleDataCount = qualityCheckTaskList.stream().mapToInt(qualityCheckTask -> qualityCheckTask.getRawDataSampleSize() == null ? 0 : qualityCheckTask.getRawDataSampleSize()).sum();
        return QualityCheckOverviewVO.builder().
                qualityCheckGroupItems(qualityCheckGroupItemsSorted).
                completeData(completeDataCount).
                sampleData(sampleDataCount).
                build();
    }

    private QualityCheckGroupItemsVO buildLabelingDataCheckResultVo(LabelingDataCheckResultVO result, Map<Long, LabelingQualityCheckTask> taskMap) {
        if (Objects.isNull(result)) {
            log.warn("查询质检概览,无数据！");
            return null;
        }
        QualityCheckGroupItemsVO qualityCheckGroupItemsVO = new QualityCheckGroupItemsVO();
        qualityCheckGroupItemsVO.setQualityCheckTaskId(result.getQualityCheckTaskId());
        qualityCheckGroupItemsVO.setLabelerMis(result.getLabelerMis());
        qualityCheckGroupItemsVO.setLabelerName(result.getLabelerName());
        qualityCheckGroupItemsVO.setDataTotal(result.getSampleCount());
        qualityCheckGroupItemsVO.setErrorCount(result.getErrorCount());
        qualityCheckGroupItemsVO.setFinishCount(result.getFinishCheckCount());
        qualityCheckGroupItemsVO.setWaitingLabelCount(result.getWaitingLabelCount());
        if (CollectionUtils.isNotEmpty(taskMap) && Objects.nonNull(taskMap.get(result.getQualityCheckTaskId()))) {
            LabelingQualityCheckTask labelingQualityCheckTask = taskMap.get(result.getQualityCheckTaskId());
            qualityCheckGroupItemsVO.setQualityChecker(labelingQualityCheckTask.getQualityCheckMis());
            qualityCheckGroupItemsVO.setQualityCheckerName(labelingQualityCheckTask.getQualityCheckName());
            qualityCheckGroupItemsVO.setQualityCheckTime(
                    Objects.nonNull(labelingQualityCheckTask.getCreateTime()) ? DateUtil.format(
                            labelingQualityCheckTask.getCreateTime(), CommonConstants.DatePattern.DATE_TIME) : "");
        }
        return qualityCheckGroupItemsVO;
    }

    /**
     * 构造质检分组详情对象
     *
     * @param qualityCheckSummary 质检任务汇总信息
     * @param taskMap             质检任务Map
     * @return 质检分组详情对象
     */
    private QualityCheckGroupItemsVO buildQualityCheckOverviewDTO(LabelingQualityCheckSummaryPO qualityCheckSummary,
                                                                  Map<Long, LabelingQualityCheckTask> taskMap) {
        LabelingQualityCheckTask checkTask = taskMap.get(qualityCheckSummary.getQualityCheckTaskId());
        return QualityCheckGroupItemsVO.builder()
                .qualityCheckTaskId(qualityCheckSummary.getQualityCheckTaskId())
                .qualityChecker(checkTask.getQualityCheckMis())
                .qualityCheckerName(checkTask.getQualityCheckName())
                .dataTotal(qualityCheckSummary.getDataTotal())
                .finishCount(qualityCheckSummary.getFinishCount())
                .errorCount(qualityCheckSummary.getErrorCount())
                .build();
    }

    /**
     * 构造质检任务对象
     *
     * @param param        请求参数
     * @param labelingTask 任务
     * @return 质检任务对象
     */
    private List<LabelingQualityCheckTask> buildQualityCheckTask(QualityCheckDistributeParam param, LabelingTask labelingTask) {
        Date now = new Date();

        //为每个质检员构建质检任务
        return buildQualityCheckTaskForEachMis(param, labelingTask, now);
    }

    /**
     * 为每个质检员构建质检任务对象
     *
     * @param param        请求参数
     * @param labelingTask 任务
     * @param now          当前时间
     * @return 质检任务列表
     */
    private List<LabelingQualityCheckTask> buildQualityCheckTaskForEachMis(QualityCheckDistributeParam param, LabelingTask labelingTask, Date now) {
        List<LabelingQualityCheckTask> qualityCheckTasks = new ArrayList<>();

        for (QualityCheckMisParam qualityCheckMis : param.getLabelers()) {
            LabelingQualityCheckTask task = new LabelingQualityCheckTask();
            task.setTaskId(param.getTaskId());
            task.setName(labelingTask.getTaskName());
            task.setQualityCheckMis(qualityCheckMis.getQualityCheckMis());
            task.setQualityCheckName(qualityCheckMis.getQualityCheckName());
            task.setStatus(QualityCheckTaskStatus.CREATING.getCode());
            // 质检任务分配方式和标注方式相同，整个任务必须采用同样的方式进行分配
            task.setAssignType(labelingTask.getAssignType());

            // 创建人为当前登陆人
            User user = UserUtils.getUser();
            task.setCreateMis(user != null ? user.getLogin() : CommonConstants.FieldName.UNKNOWN);
            task.setCreateTime(now);
            task.setUpdateTime(now);

            qualityCheckTasks.add(task);
        }

        return qualityCheckTasks;
    }

    /**
     * 新增质检详情
     *
     * @param param             请求参数
     * @param labelingTask      任务
     * @param qualityCheckTasks 质检任务列表
     */
    private void addQualityCheckDetails(QualityCheckDistributeParam param, LabelingTask labelingTask, List<LabelingQualityCheckTask> qualityCheckTasks) {
        // 质检任务ID
        List<Long> qualityCheckTaskIds = new ArrayList<>();
        // 质检任务名字
        String qualityCheckName = "";
        try {
            // 获取所有的子任务列表信息
            List<Long> groupIdList = param.getGroupConfigList().stream().map(QualityCheckDistributeParam.GroupConfig::getGroupId).collect(Collectors.toList());
            List<LabelingSubTask> subTaskList = labelingSubTaskRepository.listByGroupIdList(groupIdList);

            // 随机抽取指定数量标注详情ID，平均分配给指定人员
            Map<String, List<LabelingDetail>> misLabelDetailsMap = randomLabelingDetailToPersons(param, labelingTask, subTaskList);

            // 获取质检任务ID
            qualityCheckTaskIds = qualityCheckTasks.stream().map(LabelingQualityCheckTask::getId).collect(Collectors.toList());
            // 创建质检详情，更新质检信息和状态，回写抽检状态
            List<Long> subTaskIdList = subTaskList.stream().map(LabelingSubTask::getId).collect(Collectors.toList());
            // 创建质检详情数据
            createQualityCheckItem(subTaskIdList, qualityCheckTasks, misLabelDetailsMap);
            // 将更新的信息回写到数据库
            qualityCheckTasks.forEach(labelingQualityCheckTask -> labelingQualityCheckTask.setStatus(QualityCheckTaskStatus.CHECKING.getCode()));
            labelingQualityCheckTaskRepository.batchUpdateSelective(qualityCheckTasks);
            // 修改主任务质检状态为待质检
            labelingTask.setQualityCheckStatus(QualityCheckTaskStatus.CHECKING.getCode());
            labelingTaskRepository.updateByPrimaryKeySelective(labelingTask);
            // 获取质检任务名字
            qualityCheckName = qualityCheckTasks.get(0).getName();
            // 发送大象通知质检分配成功
            String message = String.format("质检任务「%s」分配成功，请前往[训练系统|%s]查看。", qualityCheckName,
                    lionConfig.getQualityCheckDistributeNotifyElephantUrl());
            pushElephantService.pushElephant(message, new ArrayList<>(misLabelDetailsMap.keySet()));
        } catch (Exception e) {
            log.error("质检分配失败,param={}", JSONObject.toJSONString(param), e);
            // 更新质检任务状态为失败
            labelingQualityCheckTaskRepository.updateStatusByTaskId(param.getTaskId(), QualityCheckTaskStatus.FAILED.getCode());
            // 通过质检任务ID物理删除下面所有的质检详情
            if (CollectionUtils.isNotEmpty(qualityCheckTaskIds)) {
                labelingQualityCheckItemRepository.deleteByQualityCheckTaskIds(qualityCheckTaskIds);
            }
            // 更新质检状态为失败
            LabelingTask taskData = new LabelingTask();
            taskData.setId(param.getTaskId());
            LabelingTaskUtil.appendExtraInfo(taskData, e.getMessage());
            taskData.setQualityCheckStatus(QualityCheckTaskStatus.FAILED.getCode());
            labelingTaskRepository.updateByPrimaryKeySelective(taskData);

            // 判断异常类型
            String errorMessage;
            if (e instanceof AidaTrainingCheckException) {
                errorMessage = String.format("质检任务「%s」分配失败，原因：%s", qualityCheckName, e.getMessage());
            } else {
                errorMessage = String.format("质检任务「%s」分配失败，原因：%s", qualityCheckName, "系统异常");
            }

            // 发送大象通知质检分配失败
            String message = String.format("%s ，请前往[训练系统|%s]查看。", errorMessage,
                    lionConfig.getCreateDataSetNotifyElephantUrl());
            List<String> misList = param.getLabelers().stream().map(QualityCheckMisParam::getQualityCheckMis).collect(Collectors.toList());
            pushElephantService.pushElephant(message, misList);
        } finally {
            //清除缓存
            redisStoreClient.delete(new StoreKey(RedisConstant.DISTRIBUTE_QUALITY_TASK_ID, param.getTaskId()));
        }
    }

    /**
     * 创建质检详情
     *
     * @param checkTasks         质检任务
     * @param misLabelDetailsMap 质检人mis和标注详情的映射
     */
    private void createQualityCheckItem(List<Long> subTaskIdList, List<LabelingQualityCheckTask> checkTasks, Map<String, List<LabelingDetail>> misLabelDetailsMap) {
        Date now = new Date();
        Map<String, LabelingQualityCheckTask> labelingQualityCheckTaskMap = checkTasks.stream().collect(
                Collectors.toMap(LabelingQualityCheckTask::getQualityCheckMis, Function.identity()));
        List<LabelingQualityCheckItem> qualityCheckItemList = new ArrayList<>();
        // 根据rawDataId找到当前所有对这个数据标注的数据，将这些数据都分配给同一个质检人
        Map<Long, LabelingDetail> labelingDetailMap = misLabelDetailsMap.values().stream().flatMap(Collection::stream).collect(Collectors.toMap(LabelingDetail::getId, Function.identity()));
        Map<Long, List<LabelingDetail>> labelDetailIdListMap = getLabelDetailIdMap(subTaskIdList, labelingDetailMap);
        List<LabelingDetail> labelingDetails = new ArrayList<>();
        // 按质检人分组，分配指定的数据
        for (Map.Entry<String, List<LabelingDetail>> entry : misLabelDetailsMap.entrySet()) {
            String qualityCheckMis = entry.getKey();
            List<LabelingDetail> detailList = entry.getValue();
            // 初始化质检详情
            List<LabelingQualityCheckItem> qualityCheckItems = initQualityCheckItem(detailList, labelDetailIdListMap, labelingQualityCheckTaskMap.get(qualityCheckMis), now);
            labelingDetails.addAll(qualityCheckItems.stream().map(qualityCheckItem -> {
                LabelingDetail labelingDetail = new LabelingDetail();
                labelingDetail.setId(qualityCheckItem.getLabelingDataId());
                labelingDetail.setSampleStatus(LabelingDetailSampleStatusEnum.SAMPLE.getCode());
                return labelingDetail;
            }).collect(Collectors.toList()));
            qualityCheckItemList.addAll(qualityCheckItems);
        }
        // 按照上传时的顺序进行排序后再插入数据库，保证插入的顺序和上传一样
        qualityCheckItemList = qualityCheckItemList.stream().sorted(Comparator.comparing(LabelingQualityCheckItem::getLabelingDataId)).collect(Collectors.toList());
        int batchSize = getBatchSize();
        // 每次最多拆入1000条，超过1000条时分批插入
        if (qualityCheckItemList.size() < batchSize) {
            labelingQualityCheckItemRepository.batchInsert(qualityCheckItemList);
        } else {
            List<List<LabelingQualityCheckItem>> partition = Lists.partition(qualityCheckItemList, batchSize);
            for (List<LabelingQualityCheckItem> labelingQualityCheckItems : partition) {
                labelingQualityCheckItemRepository.batchInsert(labelingQualityCheckItems);
            }
        }
        // 回填被抽检标注数据的抽检状态字段
        if (labelingDetails.size() < batchSize) {
            labelingDetailRepository.batchUpdateLabelingDetail(labelingDetails);
        } else {
            List<List<LabelingDetail>> partitionList = Lists.partition(labelingDetails, batchSize);
            for (List<LabelingDetail> partition : partitionList) {
                labelingDetailRepository.batchUpdateLabelingDetail(partition);
            }
        }
    }

    /**
     * 获取labelDetailId和LabelingDetail对象列表的映射
     *
     * @param subTaskIdList     子任务ID列表
     * @param labelingDetailMap 标注详情对象映射
     * @return labelDetailId和LabelingDetail对象列表的映射
     */
    private Map<Long, List<LabelingDetail>> getLabelDetailIdMap(List<Long> subTaskIdList, Map<Long, LabelingDetail> labelingDetailMap) {
        List<Long> rawDataIdList = labelingDetailMap.values().stream().map(LabelingDetail::getRawDataId).collect(Collectors.toList());
        // 需要通过labelDetailId和rawDataId的映射，还有rawDataId和LabelingDetail对下列表的映射，找到labelDetailId和LabelingDetail对象列表的映射
        // 在给质检人分配任务时，需要将当前数据的其他标注人也进行分配，保证同一个人质检一份数据的多个标注
        Map<Long, Long> labelDetailIdRawDataIdMap = labelingDetailMap.values().stream().collect(Collectors.toMap(LabelingDetail::getId, LabelingDetail::getRawDataId));
        List<LabelingDetail> labelingDetails = labelingDetailRepository.listBysubTaskIdListAndRawDataIdList(subTaskIdList, rawDataIdList);
        Map<Long, List<LabelingDetail>> rawDataIdLabelingDetailsMap = labelingDetails.stream().collect(Collectors.groupingBy(LabelingDetail::getRawDataId));
        Map<Long, List<LabelingDetail>> labelDetailIdListMap = new HashMap<>();
        for (Map.Entry<Long, Long> entry : labelDetailIdRawDataIdMap.entrySet()) {
            Long labelDetailId = entry.getKey();
            Long rawDataId = entry.getValue();
            List<LabelingDetail> rawDatalabelingDetailList = rawDataIdLabelingDetailsMap.get(rawDataId);
            labelDetailIdListMap.put(labelDetailId, rawDatalabelingDetailList);
        }
        return labelDetailIdListMap;
    }

    /**
     * 初始化质检详情
     *
     * @param detailList       标注详情ID列表
     * @param qualityCheckTask 质检任务ID
     * @param now              当前时间
     * @return 质检详情列表
     */
    private List<LabelingQualityCheckItem> initQualityCheckItem(List<LabelingDetail> detailList, Map<Long, List<LabelingDetail>> labelDetailIdListMap,
                                                                LabelingQualityCheckTask qualityCheckTask, Date now) {
        // 抽样数量和总数量，总数量是抽样数量乘以每个数据的标注人
        int sampleSize = 0;
        int totalSize = 0;
        List<LabelingQualityCheckItem> res = new ArrayList<>();
        for (LabelingDetail detail : detailList) {
            List<LabelingDetail> labelingDetails = labelDetailIdListMap.get(detail.getId());
            //查询原数据对应的标注详情是否有待标注的
            List<LabelingDetail> waitingLabelingDetails = labelingDetails.stream().filter(labelingDetail -> Objects.equals(LabelingDetailStatus.WAITING_LABELING.getCode(), labelingDetail.getStatus())).collect(Collectors.toList());
            //如果没有待标注的数据，可以进行质检，反之不可以
            Integer checkableStatus = CollectionUtils.isEmpty(waitingLabelingDetails) ? QualityCheckAbleStatus.CHECK_ABLE.getCode() : QualityCheckAbleStatus.UNDETECTABLE.getCode();
            for (LabelingDetail labelingDetail : labelingDetails) {
                LabelingQualityCheckItem item = new LabelingQualityCheckItem();
                item.setSessionId(labelingDetail.getSessionId());
                item.setMessageId(labelingDetail.getMessageId());
                item.setRawDataId(labelingDetail.getRawDataId());
                item.setLabelingDataId(labelingDetail.getId());
                item.setQualityCheckTaskId(qualityCheckTask.getId());
                // 初始状态为待质检
                item.setStatus(QualityCheckDataStatus.WAITING_QUALITY_CHECK.getCode());
                // 未标注的信息不可检
                item.setCheckableStatus(checkableStatus);
                item.setCreateTime(now);
                item.setUpdateTime(now);
                res.add(item);
                totalSize++;
            }
            sampleSize++;
        }
        qualityCheckTask.setSampleSize(totalSize);
        qualityCheckTask.setRawDataSampleSize(sampleSize);
        return res;
    }

    /**
     * 随机抽取指定数量标注详情ID，平均分配给指定人员
     *
     * @param param       分配参数
     * @param subTaskList 子任务列表
     * @return 每个mis对应的分配结果
     */
    private Map<String, List<LabelingDetail>> randomLabelingDetailToPersons(QualityCheckDistributeParam param, LabelingTask labelingTask, List<LabelingSubTask> subTaskList) {
        DimensionStrategy dimensionStrategy = dimensionStrategyFactory.getStrategy(labelingTask.getAssignType());
        return dimensionStrategy.qualityCheckDistribute(param, labelingTask, subTaskList);
    }

    /**
     * 处理标注任务原数据
     *
     * @param file               上传文件
     * @param labelingTask       标注任务
     * @param createTaskStrategy 要处理的数据类型对应的策略
     */
    private void processLabelingTaskRawData(MultipartFile file, LabelingTask labelingTask,
                                            CreateTaskStrategy createTaskStrategy) {
        log.info("开始异步处理标注任务文件, 任务ID: {}, 任务名称: {}", labelingTask.getId(), labelingTask.getTaskName());
        try {
            // 存储s3地址
            uploadFile(file, labelingTask);
            // 读取文件内容
            LabelingTaskRawDataVO rawDataVO = createTaskStrategyFactory.runStrategy(file);
            // 表头
            List<String> headers = rawDataVO.getHeaders();
            // 原始数据内容
            List<List<String>> rawDataContent = rawDataVO.getRawDataContent();
            if (CollectionUtils.isEmpty(rawDataContent)) {
                log.error("创建标注任务解析文件后数据为空！");
                throw new AidaTrainingCheckException("创建标注任务解析文件后数据为空!");
            }
            List<LabelingTaskRawData> labelingTaskRawDataList = createTaskStrategy.processData(rawDataContent,
                    headers);
            // 补充行数据信息
            labelingTaskRawDataList.forEach(data -> {
                data.setTaskId(labelingTask.getId());
                Date date = new Date();
                data.setCreateTime(date);
                data.setUpdateTime(date);
            });
            // 批量插入数据，超过一定数量后分批插入
            int batchSize = getBatchSize();
            if (labelingTaskRawDataList.size() > batchSize) {
                List<List<LabelingTaskRawData>> partition = Lists.partition(labelingTaskRawDataList, batchSize);
                for (List<LabelingTaskRawData> labelingTaskRawData : partition) {
                    labelingTaskRawDataRepository.batchInsert(labelingTaskRawData);
                }
            } else {
                labelingTaskRawDataRepository.batchInsert(labelingTaskRawDataList);
            }
            // 更新任务状态为待分配
            LabelingTask taskData = new LabelingTask();
            taskData.setId(labelingTask.getId());
            taskData.setLabelingStatus(LabelingTaskStatus.WAITING_ASSIGNED.getCode());
            labelingTaskRepository.updateByPrimaryKeySelective(taskData);
            // 发送大象通知任务创建成功
            String message = String.format("标注任务「%s」创建成功，请前往[训练系统|%s]查看。", labelingTask.getTaskName(),
                    lionConfig.getCreateDataSetNotifyElephantUrl());
            pushElephantService.pushElephant(message, getDxMisList(labelingTask));
        } catch (Exception e) {
            log.error("处理标注任务文件异常, 任务ID: {}, 异常信息: {}", labelingTask.getId(), e.getMessage(), e);
            // 更新任务状态为失败
            LabelingTask taskData = new LabelingTask();
            taskData.setId(labelingTask.getId());
            LabelingTaskUtil.appendExtraInfo(taskData, e.getMessage());
            taskData.setLabelingStatus(LabelingTaskStatus.FAILED.getCode());
            labelingTaskRepository.updateByPrimaryKeySelective(taskData);

            String errorMessage;
            if (e instanceof AidaTrainingCheckException) {
                errorMessage = String.format("标注任务「%s」创建失败，原因：%s", labelingTask.getTaskName(), e.getMessage());
            } else {
                errorMessage = String.format("标注任务「%s」创建失败，原因：%s", labelingTask.getTaskName(), "系统异常");
            }

            // 发送大象通知任务创建失败
            String message = String.format("%s ，请前往[训练系统|%s]查看。", errorMessage,
                    lionConfig.getCreateDataSetNotifyElephantUrl());
            pushElephantService.pushElephant(message, getDxMisList(labelingTask));

            // 根据主任务ID物理删除原数据
            labelingTaskRawDataRepository.deleteByTaskId(labelingTask.getId());
        }
    }

    /**
     * 上传文件到S3
     *
     * @param file
     * @param labelingTask
     */
    private void uploadFile(MultipartFile file, LabelingTask labelingTask) {
        if (Objects.isNull(file)) {
            log.warn("上传的文件为空");
            throw new AidaTrainingServiceException("请上传文件");
        }
        if (Objects.isNull(labelingTask)) {
            log.warn("标注任务为空");
            throw new AidaTrainingServiceException("标注任务为空");
        }
        String fileName = labelingTask.getTaskName() + "_" + labelingTask.getId() + CommonConstants.FileType.XLSX;
        String fileUrl = s3Service.upload(file, fileName);
        labelingTask.setUploadFilePath(fileUrl);
        labelingTaskRepository.updateByPrimaryKeySelective(labelingTask);
    }

    /**
     * 获取大象通知人列表
     *
     * @param labelingTask 标注任务
     * @return 通知人列表
     */
    private List<String> getDxMisList(LabelingTask labelingTask) {
        List<String> dxMisList = new ArrayList<>();
        dxMisList.add(labelingTask.getLabelingManager());
        if (StringUtils.isNotBlank(labelingTask.getCreatorMis())) {
            dxMisList.add(labelingTask.getCreatorMis());
        }
        return dxMisList;
    }

    /**
     * 获取mis号分页大小
     *
     * @return 分页大小
     */
    private Integer getPageSize() {
        Integer pageSize = lionConfig.getMisPageSize();
        if (pageSize == null) {
            // 默认10条
            return CommonConstants.Pagination.DEFAULT_PAGE_SIZE;
        }
        return pageSize;
    }

    /**
     * 每批处理数
     *
     * @return 批次大小
     */
    private Integer getBatchSize() {
        Integer batchSize = lionConfig.getPerBatchSize();
        if (batchSize == null) {
            // 默认1000
            return CommonConstants.Pagination.DEFAULT_BATCH_SIZE;
        }
        return batchSize;
    }

    /**
     * 判断当前主任务下所有的分组是否都已经回收
     *
     * @param labelTaskGroup 分组信息
     * @return 是否回收
     */
    private boolean isAllGroupRecycled(List<LabelingTaskGroup> labelTaskGroup) {
        return labelTaskGroup.stream()
                .map(LabelingTaskGroup::getRecycleStatus)
                .allMatch(LabelingTaskGroupStatus::isRecycled);
    }

    /**
     * 构建任务对象
     *
     * @param param 参数对象
     * @return 任务对象
     */
    private LabelingTask buildLabelingTask(LabelingTaskParam param) {
        LabelingTask labelingTask = new LabelingTask();
        labelingTask.setTaskName(param.getTaskName().trim());
        labelingTask.setTaskType(param.getTaskType());
        labelingTask.setDataType(param.getDataType());
        labelingTask.setBizType(param.getBizType());
        labelingTask.setLabelingStatus(LabelingTaskStatus.CREATE.getCode());
        labelingTask.setLabelingManager(param.getResponsiblePerson());
        labelingTask.setLabelingManagerName(param.getResponsiblePersonName());
        labelingTask.setTaskSource(Objects.nonNull(param.getTaskSource()) ? param.getTaskSource() : TaskSourceEnum.UPLOAD.getCode());
        List<List<LabelResult>> labelResultList = getLabelResultList(param);
        labelingTask.setLabelingConfig(JSON.toJSONString(labelResultList));
        // 当前登陆人的mis号
        User user = UserUtils.getUser();
        labelingTask.setCreatorMis(user != null ? user.getLogin() : CommonConstants.FieldName.UNKNOWN);
        Date date = new Date();
        labelingTask.setCreateTime(date);
        labelingTask.setUpdateTime(date);
        return labelingTask;
    }

    /**
     * 处理标注项，当标注项中一个背靠背标注项项也没有选时，按全是背靠背标注项处理
     *
     * @param param 入参
     * @return 处理结果
     */
    private static List<List<LabelResult>> getLabelResultList(LabelingTaskParam param) {
        List<List<LabelResult>> labelResultList = param.getLabelResultList();
        boolean allCompareFlag = true;
        for (List<LabelResult> labelResults : labelResultList) {
            if (allCompareFlag) {
                for (LabelResult labelResult : labelResults) {
                    if (Objects.nonNull(labelResult.getIsCompare())) {
                        allCompareFlag = false;
                        break;
                    }
                }
            }
        }
        if (allCompareFlag) {
            labelResultList.forEach(labelResults -> labelResults
                    .forEach(labelResult -> labelResult.setIsCompare(CommonConstants.DataStatus.IS_COMPARE)));
        }
        return labelResultList;
    }

    /**
     * 更新模板信息
     *
     * @param inspectionItemTemplateParam 模板参数对象
     * @param templateId                  模板ID
     */
    public void updateItemTemplate(InspectionItemTemplateParam inspectionItemTemplateParam, Long templateId,
                                   String mis) {
        InspectionItemTemplate template = inspectionItemTemplateRepository.getTemplateById(templateId);
        CheckUtil.paramCheck(Objects.nonNull(template), "模板不存在, templateId: " + templateId);
        template.setTemplateName(inspectionItemTemplateParam.getTemplateName());
        template.setTemplateDesc(inspectionItemTemplateParam.getTemplateDesc());
        template.setTemplateType(inspectionItemTemplateParam.getTemplateType());
        template.setUpdateMis(mis);
        template.setUpdateTime(new Date());
        inspectionItemTemplateRepository.updateTemplate(template);
    }

    /**
     * 保存模板基本信息
     *
     * @param inspectionItemTemplateParam 模板参数对象
     * @return 保存后的模板对象
     */
    public InspectionItemTemplate createInspectionItemTemplate(InspectionItemTemplateParam inspectionItemTemplateParam,
                                                               String mis) {
        InspectionItemTemplate template = new InspectionItemTemplate();
        template.setTemplateName(inspectionItemTemplateParam.getTemplateName());
        template.setTemplateDesc(inspectionItemTemplateParam.getTemplateDesc());
        template.setTemplateType(inspectionItemTemplateParam.getTemplateType());
        template.setIsDeleted(Boolean.FALSE);
        template.setCreateMis(mis);
        template.setUpdateMis(mis);
        inspectionItemTemplateRepository.insertTemplate(template);
        return template;
    }

    /**
     * 保存标注项
     */
    public void saveInspectionItem(InspectionItemDetail itemDetail, Long templateId, Integer num, String mis) {
        InspectionItem item = new InspectionItem();
        item.setTemplateId(templateId);
        item.setName(itemDetail.getName());
        item.setDataType(itemDetail.getDataType());
        item.setItemType(LabelingItemType.LABEL.getCode());
        if (CollectionUtils.isNotEmpty(itemDetail.getEnumList())) {
            item.setEnumList(String.join(",", itemDetail.getEnumList()));
        }
        item.setIsCompare(itemDetail.getIsCompare());
        item.setIsRequired(itemDetail.getIsRequired());
        item.setRankNum(num);
        item.setParentId(itemDetail.getParentId());
        item.setParentEnum(itemDetail.getParentEnumValue());
        item.setIsDeleted(Boolean.FALSE);
        item.setCreateMis(mis);
        item.setUpdateMis(mis);
        inspectionItemRepository.insertInspectionItem(item);
    }

    /**
     * 更新标注项
     */
    public void updateInspectionItem(InspectionItemDetail itemDetail, Long templateId, Integer num, String mis) {
        InspectionItem item = inspectionItemRepository.getById(itemDetail.getId());
        CheckUtil.paramCheck(Objects.nonNull(item), "标注项不存在, itemId: " + itemDetail.getId());
        item.setTemplateId(templateId);
        item.setName(itemDetail.getName());
        item.setDataType(itemDetail.getDataType());
        if (CollectionUtils.isNotEmpty(itemDetail.getEnumList())) {
            item.setEnumList(String.join(",", itemDetail.getEnumList()));
        }
        item.setIsCompare(itemDetail.getIsCompare());
        item.setIsRequired(itemDetail.getIsRequired());
        item.setRankNum(num);
        item.setParentId(itemDetail.getParentId());
        item.setParentEnum(itemDetail.getParentEnumValue());
        item.setUpdateMis(mis);
        item.setUpdateTime(new Date());
        inspectionItemRepository.updateInspectionItem(item);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLabelingTemplate(Long templateId) {
        log.info("删除标注模板, templateId: {}", templateId);
        // 检查参数
        CheckUtil.paramCheck(Objects.nonNull(templateId), "模板ID不能为空!");

        // 获取当前用户
        User user = UserUtils.getUser();
        String mis = user != null ? user.getLogin() : CommonConstants.FieldName.UNKNOWN;

        // 查询模板是否存在
        InspectionItemTemplate template = inspectionItemTemplateRepository.getTemplateById(templateId);
        CheckUtil.paramCheck(Objects.nonNull(template), "模板不存在!");

        // 先删除关联的标注项
        List<InspectionItem> inspectionItems = inspectionItemRepository.listByTemplateId(templateId);
        if (CollectionUtils.isNotEmpty(inspectionItems)) {
            // 将所有标注项标记为已删除
            for (InspectionItem item : inspectionItems) {
                item.setIsDeleted(true);
            }
            inspectionItemRepository.batchUpdateItems(inspectionItems);
        }

        // 将模板标记为已删除
        template.setIsDeleted(true);
        inspectionItemTemplateRepository.updateTemplate(template);

        log.info("删除标注模板成功, templateId: {}, operator: {}", templateId, mis);
    }

    /**
     * 校验文件行数是否超过限制
     *
     * @param file 上传文件
     */
    private void checkFileRowsLimit(MultipartFile file) {
        try {
            // 使用专门的策略类计算文件内容行数
            int contentRowCount = createTaskStrategyFactory.countContentRows(file);
            if (contentRowCount <= 0) {
                throw new AidaTrainingCheckException("上传文件中不能没有数据!");
            }

            // 获取最大行数限制
            Integer maxFileRows = lionConfig.getMaxFileRows();
            if (maxFileRows == null) {
                // 默认最大行数
                maxFileRows = CommonConstants.Pagination.DEFAULT_FILE_ROWS;
            }

            if (contentRowCount > maxFileRows) {
                throw new AidaTrainingCheckException(
                        String.format("上传文件内容行数(%d)超过最大限制(%d)，请减少数据量后重试!", contentRowCount, maxFileRows));
            }
            log.info("文件行数校验通过，内容行数: {}, 最大限制: {}", contentRowCount, maxFileRows);
        } catch (AidaTrainingCheckException e) {
            throw e;
        } catch (Exception e) {
            log.error("校验文件行数失败", e);
            throw new AidaTrainingCheckException("校验文件行数失败: " + e.getMessage());
        }
    }

    /**
     * 入参校验
     *
     * @param inspectionItemTemplateParam 模板参数对象
     */
    private static void checkInspectionItemParam(InspectionItemTemplateParam inspectionItemTemplateParam) {
        CheckUtil.paramCheck(Objects.nonNull(inspectionItemTemplateParam), "入参不能为空!");
        CheckUtil.paramCheck(Objects.nonNull(inspectionItemTemplateParam.getTemplateName()), "模板名称不能为空!");
        CheckUtil.paramCheck(Objects.nonNull(inspectionItemTemplateParam.getTemplateDesc()), "模板描述不能为空!");
        CheckUtil.paramCheck(Objects.nonNull(inspectionItemTemplateParam.getTemplateType()), "模板类型不能为空!");
        List<InspectionItemDetail> templateItems = inspectionItemTemplateParam.getTemplateItems();
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(templateItems), "标注项数据不能为空!");
        // 所有的标注项必须有一个是必填的，不能全部都是非必填的
        boolean existRequired = templateItems.stream()
                .anyMatch(item -> Objects.equals(item.getIsRequired(), LabelingItemRequired.YES.getCode()));
        CheckUtil.paramCheck(existRequired, "标注项数据必须有一个是必填的!");
        templateItems.forEach(item -> {
            CheckUtil.paramCheck(Objects.nonNull(item.getName()), "标注项名称不能为空!");
            CheckUtil.paramCheck(Objects.nonNull(item.getDataType()), "标注项类型不能为空!");
            if (item.getDataType() == LabelingDataType.ENUMERATION.getCode()) {
                CheckUtil.paramCheck(CollectionUtils.isNotEmpty(item.getEnumList()), "枚举类型标注项的枚举列表不能为空!");
            }
        });
    }

    /**
     * 入参校验
     *
     * @param param 参数对象
     */
    private static void checkParam(LabelRecycleParam param) {
        CheckUtil.paramCheck(Objects.nonNull(param), "参数不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getTaskId()), "请选择要回收的任务");
        CheckUtil.paramCheck(Objects.nonNull(param.getGroupId()), "请选择要回收的分组");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getLabelerMis()), "请选择要回收任务的标注人");
    }

    /**
     * 分配任务幂等校验
     *
     * @param taskId 任务ID
     */
    private boolean tryLockDistributeLabeling(Long taskId) {
        try {
            log.info("分配任务taskId:{} 存入缓存", taskId);
            return redisStoreClient.setnx(new StoreKey(RedisConstant.DISTRIBUTE_LABELING_TASK_ID, taskId), taskId,
                    CommonConstants.NetworkConfig.REDIS_EXPIRE_TIME);
        } catch (Exception e) {
            log.error("分配任务校验异常,taskId:{}", taskId, e);
            return false;
        }
    }

    /**
     * 分配质检幂等校验
     *
     * @param taskId 任务ID
     */
    private boolean tryLockDistributeQuality(Long taskId) {
        try {
            log.info("质检任务taskId:{} 存入缓存", taskId);
            return redisStoreClient.setnx(new StoreKey(RedisConstant.DISTRIBUTE_QUALITY_TASK_ID, taskId), taskId,
                    CommonConstants.NetworkConfig.REDIS_EXPIRE_TIME);
        } catch (Exception e) {
            log.error("分配质检校验异常,taskId:{}", taskId, e);
            return false;
        }
    }

    /**
     * 构建人员信息查询参数对象
     *
     * @param misItem
     * @return
     */
    private MisInfoParam getMisInfoParam(String misItem) {
        MisInfoParam param = new MisInfoParam();
        param.setMis(misItem.trim());
        param.setSize(getPageSize());
        return param;
    }

}
