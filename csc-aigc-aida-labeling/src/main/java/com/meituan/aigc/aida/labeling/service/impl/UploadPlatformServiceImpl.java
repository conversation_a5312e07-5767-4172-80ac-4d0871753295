package com.meituan.aigc.aida.labeling.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.aigc.aida.labeling.common.s3.S3Service;
import com.meituan.aigc.aida.labeling.service.UploadPlatformService;
import com.meituan.sec.distributeplatform.thrift.ApiUploadFileParam;
import com.meituan.sec.distributeplatform.thrift.ApiUploadFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UploadPlatformServiceImpl implements UploadPlatformService {

    @Resource
    private ApiUploadFileService.Iface apiUploadFileClient;

    @Resource
    private S3Service s3Service;

    @Value("${eval.service.domain}")
    private String domain;

    /**
     * 将s3url转换成文枢url
     *
     * @return 文枢url
     */
    private String encryptionUrl(String s3Url, String staffMis, String fileName, String fileType) {
        try {
            ApiUploadFileParam apiUploadFileParam = createApiUploadFileParam(s3Url, staffMis, fileName, fileType);
            String result = apiUploadFileClient.apiUploadFile(apiUploadFileParam, "0");
            JSONObject jsonObject = JSON.parseObject(result);
            return jsonObject.getString("data");
        } catch (Exception e) {
            log.error("调用文枢上传异常, s3Url={}, fileName={}", s3Url, fileName, e);
        }
        return null;
    }

    /**
     * 创建ApiUploadFileParam对象
     *
     * @return ApiUploadFileParam
     */
    private ApiUploadFileParam createApiUploadFileParam(String s3Url, String staffMis, String fileName, String fileType) {
        ApiUploadFileParam apiUploadFileParam = new ApiUploadFileParam();
        apiUploadFileParam.setAppKey("com.sankuai.csccratos.eval.service");
        apiUploadFileParam.setFileName(fileName);
        apiUploadFileParam.setFileSource(domain);
        apiUploadFileParam.setFileType(fileType);
        apiUploadFileParam.setIsInner("1");
        apiUploadFileParam.setMisId(staffMis);
        apiUploadFileParam.setS3Url(s3Url);
        apiUploadFileParam.setUserName(staffMis);
        return apiUploadFileParam;
    }

    @Override
    public String uploadS3AndEncryption(MultipartFile multipartFile, String s3FileName, String encryptionFileName, String fileType, String staffMis) {
        String s3Url = s3Service.upload(multipartFile, s3FileName);
        return this.encryptionUrl(s3Url, staffMis, encryptionFileName, fileType);
    }

    @Override
    public String uploadS3AndEncryption(ByteArrayInputStream inputStream, String s3FileName, String encryptionFileName, String fileType, String staffMis) {
        String s3Url = s3Service.upload(inputStream, s3FileName);
        log.info("s3上传成功,s3FileName={},s3Url={}", s3FileName, s3Url);
        return this.encryptionUrl(s3Url, staffMis, encryptionFileName, fileType);
    }
}
