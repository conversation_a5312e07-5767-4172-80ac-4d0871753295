package com.meituan.aigc.aida.data.management.dataset.controller;

import com.meituan.aigc.aida.data.management.dataset.param.DataProcessCreateTaskParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataProcessTaskPageParam;
import com.meituan.aigc.aida.data.management.dataset.param.LabelRobotParam;
import com.meituan.aigc.aida.data.management.dataset.param.NonCustomRobotQueryParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataMarkParam;
import com.meituan.aigc.aida.data.management.dataset.dto.DataProcessTaskDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.LabelRobotInfoDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.NonCustomLabelInfoDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.NonCustomRobotDTO;
import com.meituan.aigc.aida.data.management.dataset.service.DataProcessService;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据清洗和打标控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/data/processing")
public class DataProcessController {

    @Autowired
    private DataProcessService dataProcessService;

    /**
     * 创建清洗任务
     *
     * @param param 创建清洗任务参数
     * @return 创建结果
     */
    @PostMapping("/task/create")
    public Result<?> createProcessTask(@RequestBody DataProcessCreateTaskParam param) {
        log.info("创建清洗任务, taskName: {}, datasetId: {}", param.getTaskName(), param.getDatasetId());
        dataProcessService.createProcessTask(param);
        return Result.create();
    }

    /**
     * 获取清洗任务列表
     *
     * @param param 查询参数
     * @return 清洗任务分页列表
     */
    @GetMapping("/task/page")
    public Result<PageData<DataProcessTaskDTO>> pageListProcessTask(DataProcessTaskPageParam param) {
        log.info("获取清洗任务列表, taskName: {}, datasetId: {}, pageNum: {}, pageSize: {}", 
                param.getTaskName(), param.getDatasetId(), param.getPageNum(), param.getPageSize());
        return Result.ok(dataProcessService.listProcessTask(param));
    }

    /**
     * 获取打标机器人信息
     *
     * @param param 机器人参数
     * @return 机器人信息
     */
    @GetMapping("/custom/label")
    public Result<LabelRobotInfoDTO> getLabelRobotInfo(LabelRobotParam param) {
        log.info("获取打标机器人信息, workspaceId: {}, robotId: {}, robotVersionId: {}", 
                param.getWorkspaceId(), param.getRobotId(), param.getRobotVersionId());
        return Result.ok(dataProcessService.getLabelRobotInfo(param));
    }

    /**
     * 获取非自定义标签信息
     *
     * @return 非自定义标签信息
     */
    @GetMapping("/non/custom")
    public Result<NonCustomLabelInfoDTO> getNonCustomLabelInfo() {
        log.info("获取非自定义标签信息");
        return Result.ok(dataProcessService.getNonCustomLabelInfo());
    }

    /**
     * 查询打标机器人
     *
     * @param param 查询参数
     * @return 打标机器人列表
     */
    @GetMapping("/non/custom/robot")
    public Result<List<NonCustomRobotDTO>> queryNonCustomRobot(NonCustomRobotQueryParam param) {
        log.info("查询打标机器人, sceneId: {}, purposeId: {}, labelIdList: {}", 
                param.getSceneId(), param.getPurposeId(), param.getLabelIdList());
        return Result.ok(dataProcessService.queryNonCustomRobot(param));
    }

    /**
     * 数据打标
     *
     * @param param 打标参数
     * @return 打标结果
     */
    @PostMapping("/non/custom/mark")
    public Result<?> markData(@RequestBody DataMarkParam param) {
        log.info("数据打标, labelType: {}, robotList: {}", param.getLabelType(), param.getRobotList().size());
        dataProcessService.markData(param);
        return Result.create();
    }
} 