package com.meituan.aigc.aida.benchmark.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.benchmark.common.config.BenchmarkLionConfig;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion;
import com.meituan.aigc.aida.benchmark.dao.repository.BenchmarkVersionRepository;
import com.meituan.aigc.aida.benchmark.param.BenchmarkDatasetListParam;
import com.meituan.aigc.aida.benchmark.param.IndicatorComputeTaskParam;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkDatasetItemVO;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkDatasetListVO;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkVersionItemVO;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkVersionListVO;
import com.meituan.aigc.aida.benchmark.service.BenchmarkManagementService;
import com.meituan.aigc.aida.benchmark.service.IndicatorComputingService;
import com.meituan.aigc.aida.benchmark.util.BenchmarkDataTransUtils;
import com.meituan.aigc.aida.benchmark.util.BenchmarkNameUtils;
import com.meituan.aigc.aida.common.util.ObjectConverter;
import com.meituan.aigc.aida.config.rhino.RhinoThreadPoolConfig;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import com.meituan.aigc.aida.labeling.dao.po.LabelingTaskRawDataPO;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskRawDataRepository;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskRepository;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.aigc.aida.labeling.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Benchmark管理Service实现类
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Slf4j
@Service
public class BenchmarkManagementServiceImpl implements BenchmarkManagementService {

    @Resource
    private BenchmarkVersionRepository benchmarkVersionRepository;

    @Resource
    private LabelingTaskRawDataRepository labelingTaskRawDataRepository;

    @Resource
    private LabelingTaskRepository labelingTaskRepository;

    @Resource
    private BenchmarkLionConfig benchmarkLionConfig;

    @Resource
    private TaskService taskService;

    @Resource
    private IndicatorComputingService indicatorComputingService;

    @Override
    public BenchmarkVersionListVO listAllBenchmarkVersions() {
        log.info("开始查询所有Benchmark版本列表");

        // 查询所有版本信息
        List<BenchmarkVersion> benchmarkVersions = benchmarkVersionRepository.listAllBenchmarkVersions();

        if (CollectionUtils.isEmpty(benchmarkVersions)) {
            log.info("未查询到Benchmark版本信息");
            return BenchmarkVersionListVO.builder()
                    .versions(Collections.emptyList())
                    .build();
        }

        // 转换为VO对象
        List<BenchmarkVersionItemVO> versionItems = benchmarkVersions.stream()
                .map(this::convertToBenchmarkVersionItemVO)
                .collect(Collectors.toList());

        // 确定默认版本：优先选择已发布的第一个版本，如果没有已发布版本则选择第一个版本
        setDefaultVersion(versionItems);

        return BenchmarkVersionListVO.builder()
                .versions(versionItems)
                .build();
    }

    @Override
    public BenchmarkDatasetListVO listBenchmarkDatasets(BenchmarkDatasetListParam param) {
        log.info("开始查询Benchmark数据集列表，版本ID：{}，页码：{}，每页大小：{}",
                param.getVersionId(), param.getPageNum(), param.getPageSize());

        // 首先验证版本是否存在
        BenchmarkVersion benchmarkVersion = benchmarkVersionRepository.getBenchmarkVersionById(param.getVersionId());
        if (Objects.isNull(benchmarkVersion)) {
            log.warn("未找到对应的Benchmark版本，版本ID：{}", param.getVersionId());
            return BenchmarkDatasetListVO.builder()
                    .total(0L)
                    .datasetList(Collections.emptyList())
                    .indicatorInfo(Collections.emptyList())
                    .build();
        }

        // 获取指标信息
        List<String> indicatorInfo = benchmarkLionConfig.getIndicatorConfigList(benchmarkVersion.getVersionName());

        // 获取标注任务任务
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<LabelingTask> labelingTaskList = labelingTaskRepository.listCheckDataByName(benchmarkVersion.getVersionName());
        if (CollectionUtils.isEmpty(labelingTaskList)) {
            log.info("未查询到数据集信息, param= {}", param);
            return BenchmarkDatasetListVO.builder()
                    .total(0L)
                    .datasetList(Collections.emptyList())
                    .indicatorInfo(indicatorInfo)
                    .build();
        }
        PageInfo<LabelingTask> pageInfo = new PageInfo<>(labelingTaskList);
        PageHelper.clearPage();

        // 获取主任务，查询每个主任务的原数据数据量
        List<Long> taskIds = labelingTaskList.stream().map(LabelingTask::getId).collect(Collectors.toList());
        List<LabelingTaskRawDataPO> rawDataList = labelingTaskRawDataRepository.getCountByTaskIds(taskIds);
        // 将主任务ID和原数据数据量转成map
        Map<Long, Integer> map = rawDataList.stream()
                .collect(Collectors.toMap(LabelingTaskRawDataPO::getTaskId, LabelingTaskRawDataPO::getTotalNum));

        // 转换为VO对象
        List<BenchmarkDatasetItemVO> datasetItems = labelingTaskList.stream()
                .map(item -> BenchmarkDataTransUtils.convertToBenchmarkDatasetItemVO(item, map))
                .collect(Collectors.toList());

        log.info("查询到{}个数据集，当前页{}个", datasetItems.size(), datasetItems.size());

        return BenchmarkDatasetListVO.builder()
                .total(pageInfo.getTotal())
                .datasetList(datasetItems)
                .indicatorInfo(indicatorInfo)
                .build();
    }

    @Override
    public void indicatorComputing(IndicatorComputeTaskParam param) {
        if (Objects.isNull(param) || StringUtils.isEmpty(param.getVersionId())) {
            log.error("indicatorComputing param or versionId is null, param = {}", param);
            return;
        }

        // 1. 获取benchmark版本
        BenchmarkVersion benchmarkVersion = benchmarkVersionRepository.getBenchmarkVersionById(param.getVersionId());
        if (Objects.isNull(benchmarkVersion)) {
            log.error("indicatorComputing benchmarkVersion is null, versionId = {}", param.getVersionId());
            return;
        }

        // 2. 获取计算指标
        List<String> indicatorConfigList = benchmarkLionConfig.getIndicatorConfigList(benchmarkVersion.getVersionName());

        // 3.1 获取标注任务
        List<LabelingTask> labelingTaskList = labelingTaskRepository.listCheckDataByName(benchmarkVersion.getVersionName());
        if (CollectionUtils.isEmpty(labelingTaskList)) {
            log.error("indicatorComputing labelingTaskList is null, versionName = {}", benchmarkVersion.getVersionName());
            return;
        }

        // 3.2 获取标注任务的子任务列表
        List<Long> taskIdList = labelingTaskList.stream()
                .map(LabelingTask::getId)
                .collect(Collectors.toList());
        Map<Long, List<Long>> subtaskList = taskService.listSubTaskIdsByTaskIdList(taskIdList);

        // 4 获取模型纬度的标注数据信息
        Map<String, List<Long>> modelEvalTaskMap = getModelEvalTaskMap(labelingTaskList, benchmarkVersion.getVersionName());

        // 5 获取考点列表
        List<String> examList = getExamList(labelingTaskList.get(0).getLabelingConfig());

        // 6 调用指标计算服务计算每个模型的指标
        for (Map.Entry<String, List<Long>> entry : modelEvalTaskMap.entrySet()) {
            String modelName = entry.getKey();
            List<Long> modelTaskIds = entry.getValue();

            log.info("开始计算模型 {} 的指标，任务数量: {}", modelName, modelTaskIds.size());

            CompletableFuture.runAsync(() -> {
                indicatorComputingService.computingModelIndicator(
                        benchmarkVersion.getId(),
                        modelName,
                        modelTaskIds,
                        subtaskList,
                        examList,
                        indicatorConfigList
                );
            }, RhinoThreadPoolConfig.INDICATOR_CALCULATE_TASK_THREAD_POOL.getExecutor());
        }

        log.info("指标计算完成, 版本: {}, 考点数量: {}, 模型数量: {}",
                benchmarkVersion.getVersionName(), examList.size(), modelEvalTaskMap.size());
    }

    /**
     * 从标注配置中提取考点列表
     *
     * <p>解析JSON格式的标注配置，提取所有标注项的name字段，
     * 并过滤掉包含"备注"、"类型"或"描述"的项目</p>
     *
     * @param labelingConfig JSON格式的标注配置字符串
     * @return 考点名称列表，去重后的结果
     */
    private List<String> getExamList(String labelingConfig) {
        if (StringUtils.isEmpty(labelingConfig)) {
            log.info("标注配置为空，无法提取考点列表");
            return Collections.emptyList();
        }

        try {
            // 解析JSON字符串为嵌套数组结构
            List<List<LabelResult>> configList = JSON.parseObject(labelingConfig,
                    new TypeReference<List<List<LabelResult>>>() {
                    });

            if (CollectionUtils.isEmpty(configList)) {
                log.warn("标注配置解析后为空数组");
                return Collections.emptyList();
            }

            // 使用Set去重，然后转换为List
            Set<String> examNameSet = new LinkedHashSet<>();

            // 遍历嵌套数组结构
            for (List<LabelResult> innerList : configList) {
                if (CollectionUtils.isEmpty(innerList)) {
                    continue;
                }

                for (LabelResult item : innerList) {
                    if (item == null || StringUtils.isEmpty(item.getName()) || containsFilterKeywords(item.getName())) {
                        continue;
                    }
                    examNameSet.add(item.getName());
                }
            }

            List<String> examList = new ArrayList<>(examNameSet);
            log.info("从标注配置中提取到{}个考点", examList.size());

            return examList;

        } catch (Exception e) {
            log.error("解析标注配置JSON失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 检查名称是否包含需要过滤的关键词
     *
     * @param name 待检查的名称
     * @return true表示包含过滤关键词，应该被过滤；false表示不包含，可以保留
     */
    private boolean containsFilterKeywords(String name) {
        if (StringUtils.isEmpty(name)) {
            return true;
        }

        // 定义过滤关键词
        String[] filterKeywords = {"备注", "类型", "描述"};

        for (String keyword : filterKeywords) {
            if (name.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 构建模型评测任务映射关系
     *
     * @param labelingTaskList 标注任务列表，不能为空
     * @param versionName      版本名称，用于日志记录，不能为空
     * @return 模型评测任务映射关系，如果输入参数无效则返回空Map
     */
    private Map<String, List<Long>> getModelEvalTaskMap(List<LabelingTask> labelingTaskList, String versionName) {
        // 参数有效性检查
        if (CollectionUtils.isEmpty(labelingTaskList) || StringUtils.isEmpty(versionName)) {
            log.warn("标注任务列表或benchmark名称为空，无法构建模型评测任务映射, labelingTaskList: {}, versionName: {}", labelingTaskList, versionName);
            return Collections.emptyMap();
        }

        // 使用嵌套Map存储：模型名称 -> 任务ID
        Map<String, List<Long>> modelEvalTaskMap = new HashMap<>();

        for (LabelingTask labelingTask : labelingTaskList) {
            // 从任务名称中解析模型名称和评测对象
            String modelName = BenchmarkNameUtils.getModeNameByTaskName(labelingTask.getTaskName());

            // 验证解析结果的有效性
            if (StringUtils.isEmpty(modelName)) {
                log.info("无法从任务名称中解析出有效的模型名称或评测对象, taskName: {}, modelName: {}",
                        labelingTask.getTaskName(), modelName);
                continue;
            }

            // 使用computeIfAbsent确保模型对应的Map存在，然后添加评测对象映射
            modelEvalTaskMap.computeIfAbsent(modelName, k -> new ArrayList<>())
                    .add(labelingTask.getId());
        }

        log.info("构建模型评测任务映射完成, 版本: {}, 模型数量: {}, 总任务数量: {}",
                versionName, modelEvalTaskMap.size(), labelingTaskList.size());

        return modelEvalTaskMap;
    }

    /**
     * 转换为BenchmarkVersionItemVO
     *
     * @param benchmarkVersion 数据库实体
     * @return VO对象
     */
    private BenchmarkVersionItemVO convertToBenchmarkVersionItemVO(BenchmarkVersion benchmarkVersion) {
        return BenchmarkVersionItemVO.builder()
                .id(benchmarkVersion.getId())
                .name(benchmarkVersion.getVersionName())
                .isDefault(false)
                .build();
    }

    /**
     * 确定默认版本
     * 选择时间最新的版本作为默认版本（列表已按创建时间倒序排列）
     *
     * @param benchmarkVersions 版本列表
     */
    private void setDefaultVersion(List<BenchmarkVersionItemVO> benchmarkVersions) {
        if (CollectionUtils.isEmpty(benchmarkVersions)) {
            return;
        }

        // 设置最新的版本为默认版本
        benchmarkVersions.get(0).setIsDefault(Boolean.TRUE);
    }
} 