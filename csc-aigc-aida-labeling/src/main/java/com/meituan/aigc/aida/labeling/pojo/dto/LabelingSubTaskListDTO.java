package com.meituan.aigc.aida.labeling.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 子任务列表DTO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LabelingSubTaskListDTO implements Serializable {

    /**
     * 子任务ID
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据来源
     */
    private String dataSetSource;

    /**
     * 数据量
     */
    private Integer dataTotalCount;

    /**
     * 标注状态
     */
    private Integer labelingStatus;

    /**
     * 标注进度
     */
    private Integer labelingProgress;

    /**
     * 标注人Mis
     */
    private String labelerMis;

    /**
     * 标注人名字
     */
    private String labelerName;

    /**
     * 标注任务类型 1:评测集标注 2:训练集标注 3:线上会话标注
     */
    private Integer taskType;

    /**
     * 数据类型 1:模型评测 2:Agent评测 3:有监督微调(SFT) 4:偏好对齐(DPO) 5:Session维度 6:Query维度
     */
    private Integer dataType;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}

