package com.meituan.aigc.aida.data.management.fetch.dto.mq;

import com.meituan.aigc.aida.data.management.fetch.dto.BaseSingalTrackingBusParamDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: guowenhui
 * @Create: 2025/5/7 16:27
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DelayTriggerRobotDTO implements Serializable {

    /**
     * 机器人ID
     */
    private String robotId;
    /**
     * 加工机器人信号类型
     */
    private String signalType;
    /**
     * 输入参数
     */
    private Map inputData;
    /**
     * 信号埋点业务参数BaseDTO
     */
    private BaseSingalTrackingBusParamDTO busParamDTO;
    /**
     * acId
     */
    private String acIdStr;
    /**
     * traceId
     */
    private String traceId;
}
