package com.meituan.aigc.aida.data.management.es.query;

import com.meituan.aigc.aida.common.enums.EsOperationEnum;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 批量插入结果
 *
 * <AUTHOR>
 * @date 2025/5/24
 */
@Data
@Builder
public class BatchResult {

    /**
     * 是否全部成功
     */
    private boolean allSuccess;

    /**
     * 总数据量
     */
    private int totalCount;

    /**
     * 成功数量
     */
    private int successCount;

    /**
     * 失败数量
     */
    private int failedCount;

    /**
     * 总耗时（毫秒）
     */
    private long totalTimeMs;

    /**
     * 失败的数据ID列表
     */
    private List<String> failedDocIds;

    /**
     * 失败原因统计
     */
    private Map<String, Integer> failureReasons;

    /**
     * 详细错误信息
     */
    private List<String> errorMessages;

    /**
     * 写入的ES索引
     */
    private String index;

    /**
     * 操作类型
     */
    private EsOperationEnum operation;

    /**
     * 成功率
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }

    /**
     * 获取结果摘要
     */
    public String getSummary() {
        return String.format("BatchInsert Result - Total: %d, Success: %d, Failed: %d, Success Rate: %.2f%%, Time: %dms",
                totalCount, successCount, failedCount, getSuccessRate(), totalTimeMs);
    }

    /**
     * 创建空结果（用于空数据集的情况）
     */
    public static BatchResult empty(EsOperationEnum operation) {
        return BatchResult.builder()
                .allSuccess(true)
                .totalCount(0)
                .successCount(0)
                .failedCount(0)
                .totalTimeMs(0)
                .failedDocIds(Collections.emptyList())
                .failureReasons(Collections.emptyMap())
                .errorMessages(Collections.emptyList())
                .operation(operation)
                .build();
    }

    /**
     * 创建失败结果（用于参数错误等情况）
     */
    public static BatchResult failure(int totalCount, String errorMessage) {
        return BatchResult.builder()
                .allSuccess(false)
                .totalCount(totalCount)
                .successCount(0)
                .failedCount(totalCount)
                .totalTimeMs(0)
                .failedDocIds(Collections.emptyList())
                .errorMessages(Collections.singletonList(errorMessage))
                .build();
    }
}