package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-28 12:08
 * @description 标注分组dto
 */
@Data
public class LabelTaskGroupDTO {
    /**
     * 分组id
     */
    private Long id;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 父分组id
     */
    private Long parentId;
    /**
     * 分组状态
     */
    private Integer recycleStatus;
    /**
     * 分组下数据总数
     */
    private Integer totalDataCount;
    /**
     * 分组下回收数据总数
     */
    private Integer recycleDataCount;

    /**
     * 回收采纳子任务id
     */
    private Long recycleSubTaskId;

    /**
     * 一致率
     */
    private Integer consistencyRate;
    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 标注人 张三vs 李四
     */
    private String labelers;

    /**
     * 子分组列表
     */
    private List<LabelTaskGroupDTO> subGroupList;

    /**
     * 子任务列表(只包含关键信息，子任务id，标注人姓名，标注人mis)
     */
    private List<LabelingSubTaskSimpleDTO> subTaskSimpleList;

}
