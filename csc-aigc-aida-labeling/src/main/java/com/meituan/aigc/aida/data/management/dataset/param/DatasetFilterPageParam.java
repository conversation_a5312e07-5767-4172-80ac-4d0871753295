package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 数据集筛选分页查询参数
 *
 * <AUTHOR>
 * @date 2025-06-04
 * @description 数据集筛选分页查询请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetFilterPageParam implements Serializable {

    /**
     * 数据集ID
     */
    private Long datasetId;

    /**
     * 数据集版本ID
     */
    private Long datasetVersionId;

    /**
     * 页码（从0开始）
     */
    private Integer pageNum = 0;

    /**
     * 页大小
     */
    private Integer pageSize = 20;

    /**
     * 查询条件列表
     */
    private List<FilterCondition> conditionList;

    /**
     * 需要返回的列列表
     */
    private List<String> columnList;

    /**
     * 筛选条件
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FilterCondition implements Serializable {

        /**
         * 列名（支持多级，如["一级","二级"]）
         */
        private List<String> columnName;

        /**
         * 条件类型：1-等于，2-包含
         */
        private Integer conditionType;

        /**
         * 查询的值
         */
        private String value;
    }
} 