package com.meituan.aigc.aida.data.management.fetch.strategy.export;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.fetch.common.constans.FieldNameConstants;
import com.meituan.aigc.aida.data.management.fetch.common.convertor.DataFetchTaskConvertor;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchRecordRepository;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchTaskRepository;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchRecordDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskCreateOutputFieldDTO;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategy;
import com.meituan.aigc.aida.labeling.strategy.task.create.ExportUtils;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-09 14:54
 * @description 导出数据获取记录
 */
@Slf4j
@Component
public class ExportDataFetch {

    private static final int TRUE = 1;

    private static final int FALSE = 0;

    @Resource
    private DataFetchRecordRepository dataFetchRecordRepository;

    @Resource
    private PushElephantService pushElephantService;

    @Resource
    private ExportUtils exportUtils;

    @Resource
    private DataFetchTaskRepository dataFetchTaskRepository;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    public void exportDataFetchRecord(DataFetchTask task, String mis) {
        log.info("开始导出数据获取记录数据，taskId: {}", task.getId());
        String sheetName = "数据获取记录";
        // 创建工作簿，使用SXSSF模式处理大数据量
        try (SXSSFWorkbook workbook = new SXSSFWorkbook(500)) {
            Sheet sheet = workbook.createSheet(sheetName);
            DataFetchRecordWithBLOBs dataFetchRecord = dataFetchRecordRepository.getOneByTaskId(task.getId());
            // 准备导出所需的字段数据
            ExportDataFetchRecord exportDataFetchRecord = prepareExportFields(task.getName(), dataFetchRecord);
            if (exportDataFetchRecord == null) {
                return;
            }
            // 创建表头样式
            CellStyle headerStyle = CreateTaskStrategy.createHeaderStyle(workbook);
            // 创建内容样式（添加自动换行）
            CellStyle contentStyle = createContentCellStyle(workbook);
            // 创建表头和填充数据
            int[] headerAndFillData = createHeaderAndFillData(sheet, headerStyle, contentStyle, dataFetchRecord);
            // 设置冻结窗格，冻结第一行
            sheet.createFreezePane(0, 1);
            // 上传文件到S3并发送大象消息
            String message = "";
            if (headerAndFillData.length > 2 && headerAndFillData[2] == TRUE) {
                message = "任务「%s」总数据量: 【" + headerAndFillData[1] + "】条，达到导出上限，只支持导出前【" + dataManagementLionConfig.getExportDataFetchItemTotalNum() + "】条数据，导出成功[请点击|%s]下载。";
            }
            exportUtils.uploadS3AndSendDxMessage(workbook, task.getName(), message, mis);
            log.info("数据获取记录导出完成，taskName: {}, 总数据量: {}", task.getName(), headerAndFillData[1]);
        } catch (Exception e) {
            log.error("导出数据获取记录发生异常，taskName: {}, 异常信息:", task.getName(), e);
            pushElephantService.pushElephant(String.format("数据获取记录「%s」导出失败，请重新导出。", task.getName()),
                    Collections.singletonList(UserUtils.getUser().getLogin()));
        }
    }

    /**
     * 创建内容单元格样式
     *
     * @param workbook 工作簿
     * @return 单元格样式
     */
    private CellStyle createContentCellStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        // 设置自动换行
        cellStyle.setWrapText(true);
        // 设置垂直对齐方式为顶部
        cellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.TOP);
        // 设置水平对齐方式为左对齐
        cellStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.LEFT);
        return cellStyle;
    }

    protected int[] createHeaderAndFillData(Sheet sheet, CellStyle headerStyle, CellStyle contentStyle, DataFetchRecordWithBLOBs dataFetchRecord) {
        if (Objects.isNull(dataFetchRecord)) {
            return new int[]{0, 0, 0};
        }
        // 创建表头行
        Row headerRow = sheet.createRow(0);
        // 设置表头行高
        headerRow.setHeight((short) 500);
        int colIndex = 0;
        // 填充表头并返回信号数据的表头
        Set<String> signalDataSet = handleSignalData(dataFetchRecord);
        colIndex = fillHeader(colIndex, headerRow, headerStyle, signalDataSet);
        // 填充数据行
        int rowIndex = 1;
        Integer totalCount = dataFetchRecordRepository.countByTaskId(dataFetchRecord.getTaskId());
        Integer finalTotalCount = totalCount;
        int greaterTotalCountFlag = FALSE;
        // 超过上限后截断数据
        if (totalCount > dataManagementLionConfig.getExportDataFetchItemTotalNum()) {
            totalCount = dataManagementLionConfig.getExportDataFetchItemTotalNum();
            greaterTotalCountFlag = TRUE;
        }
        List<DataFetchRecordWithBLOBs> dataFetchRecordList = dataFetchRecordRepository.pageByTaskId(dataFetchRecord.getTaskId(), 0, totalCount);
        if (CollectionUtils.isNotEmpty(dataFetchRecordList)) {
            fillData(rowIndex, sheet, dataFetchRecordList, signalDataSet, contentStyle);
            dataFetchRecordList.clear();
        }
        // 设置列宽
        setupColumnWidths(sheet, colIndex);
        return new int[]{colIndex, finalTotalCount, greaterTotalCountFlag};
    }

    /**
     * 填充表头
     *
     * @param colIndex
     * @param headerRow
     * @param headerStyle
     */
    private int fillHeader(int colIndex,
                           Row headerRow,
                           CellStyle headerStyle,
                           Set<String> signalDataSet) {

        // 设置基础字段表头
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.SESSION_ID, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.CONTACT_ID, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.STAFF_MIS, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.CUSTOMER_MESSAGE_CONTENT, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.STAFF_MESSAGE_CONTENT, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.MESSAGE_ID, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.CREATE_TIME, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.CHAT_MESSAGE_FROM_TYPE, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.CONTACT_TYPE, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.TYPICAL_QUESTION_IDS, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.POPUP_AC_ID, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.COMMON_INFO, headerStyle);
        colIndex = createCell(headerRow, colIndex, FieldNameConstants.INPUTS, headerStyle);


        // 将集合中所有的信号数据中的key去重当作表头
        try {
            // 填充信号数据表头
            for (String key : signalDataSet) {
                colIndex = createCell(headerRow, colIndex, key, headerStyle);
            }
            return colIndex;
        } catch (Exception e) {
            log.error("解析信号数据内容失败", e);
        }
        return colIndex;
    }

    private Set<String> handleSignalData(DataFetchRecordWithBLOBs dataFetchRecord) {
        if (Objects.isNull(dataFetchRecord)) {
            return Collections.emptySet();
        }
        Long taskId = dataFetchRecord.getTaskId();
        Set<String> signalDataKeySet = new HashSet<>();
        DataFetchTask dataFetchTask = dataFetchTaskRepository.getTaskById(taskId);
        if (Objects.isNull(dataFetchTask)) {
            log.warn("导出数据获取记录，根据数据拉取任务taskId查询任务为空,taskId = {}", taskId);
            if (StringUtils.isNotBlank(dataFetchRecord.getSignalDataContent())) {
                Map<String, String> signalDataMap = JSON.parseObject(dataFetchRecord.getSignalDataContent(),
                        new TypeReference<LinkedHashMap<String, String>>() {
                        });
                signalDataKeySet.addAll(signalDataMap.keySet());
            }
        } else {
            String outputFields = dataFetchTask.getOutputFields();
            List<DataFetchTaskCreateOutputFieldDTO> outputFieldList = JSON.parseObject(outputFields, new TypeReference<List<DataFetchTaskCreateOutputFieldDTO>>() {
            });
            if (CollectionUtils.isNotEmpty(outputFieldList)) {
                List<String> fieldNameList = outputFieldList.stream().map(DataFetchTaskCreateOutputFieldDTO::getFieldName).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                signalDataKeySet.addAll(fieldNameList);
            }
        }
        return signalDataKeySet;
    }

    /**
     * 填充数据
     *
     * @param rowIndex
     * @param sheet
     * @param dataFetchRecordList
     * @param signalDataSet
     * @param contentStyle        单元格样式
     */
    private void fillData(int rowIndex,
                          Sheet sheet,
                          List<DataFetchRecordWithBLOBs> dataFetchRecordList,
                          Set<String> signalDataSet,
                          CellStyle contentStyle) {
        for (DataFetchRecordWithBLOBs record : dataFetchRecordList) {
            Row dataRow = sheet.createRow(rowIndex++);
            // 设置行高，以便显示更多内容
            dataRow.setHeight((short) 800);
            int cellIndex = 0;

            // 填充基础字段（应用内容样式）
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getSessionId()) ? record.getSessionId() : "", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getContactId()) ? record.getContactId() : "", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getStaffMis()) ? record.getStaffMis() : "", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getCustomerMessageContent()) ? record.getCustomerMessageContent() : "-", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getStaffMessageContent()) ? record.getStaffMessageContent() : "-", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getMessageId()) ? record.getMessageId() : "", contentStyle);
            createContentCell(dataRow, cellIndex++, Objects.nonNull(record.getMessageOccurredTime()) ? DateUtil.formatDate(record.getMessageOccurredTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD) : "-", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getChatMessageFromType()) ? record.getChatMessageFromType() : "-", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getContactType()) ? record.getContactType() : "-", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getTypicalQuestionIds()) ? record.getTypicalQuestionIds() : "-", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getPopupAcId()) ? record.getPopupAcId() : "-", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getCommonInfo()) ? record.getCommonInfo() : "-", contentStyle);
            createContentCell(dataRow, cellIndex++, StringUtils.isNotBlank(record.getInputs()) ? record.getInputs() : "-", contentStyle);

            // 填充信号数据字段
            try {
                if (StringUtils.isNotBlank(record.getSignalDataContent())) {
                    Map<String, String> recordSignalDataMap = JSON.parseObject(record.getSignalDataContent(), new TypeReference<LinkedHashMap<String, String>>() {
                    });
                    for (String key : signalDataSet) {
                        String value = recordSignalDataMap.get(key);
                        if (StringUtils.isBlank(value) || value.length() >= 32767) {
                            value = "-";
                        }
                        createContentCell(dataRow, cellIndex++, value, contentStyle);
                    }
                } else {
                    // 如果没有信号数据，用破折号填充
                    for (String ignored : signalDataSet) {
                        createContentCell(dataRow, cellIndex++, "-", contentStyle);
                    }
                }
            } catch (Exception e) {
                log.error("填充信号数据字段失败", e);
                // 填充破折号以保持列对齐
                createContentCell(dataRow, cellIndex, "-", contentStyle);
            }
        }
    }

    /**
     * 设置列宽
     *
     * @param sheet    工作表
     * @param colIndex 列数
     */
    private void setupColumnWidths(Sheet sheet, int colIndex) {
        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 6000);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 8000);
        sheet.setColumnWidth(4, 6000);
        sheet.setColumnWidth(5, 6000);
        for (int i = 6; i < colIndex; i++) {
            sheet.setColumnWidth(i, 8000);
        }
    }

    /**
     * 创建表头单元格
     */
    protected int createCell(Row row, int colIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(colIndex++);
        cell.setCellValue(value);
        cell.setCellStyle(cellStyle);
        return colIndex;
    }

    /**
     * 创建内容单元格（带样式）
     */
    protected void createContentCell(Row row, int colIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(colIndex);
        cell.setCellValue(value);
        cell.setCellStyle(cellStyle);
    }

    @Getter
    protected static class ExportDataFetchRecord {
        private final DataFetchRecordDTO sample;

        public ExportDataFetchRecord(DataFetchRecordDTO sample) {
            this.sample = sample;
        }
    }

    private ExportDataFetchRecord prepareExportFields(String taskName, DataFetchRecordWithBLOBs dataFetchRecord) {
        if (Objects.isNull(dataFetchRecord)) {
            log.error("导出数据获取数据，taskName: {}, 数据记录为空!", taskName);
            return null;
        }
        // 设置动态表头
        return new ExportDataFetchRecord(DataFetchTaskConvertor.buildDataFetchRecordDTO(dataFetchRecord));
    }
}
