package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON>
 * @Create: 2025/2/28 16:45
 * @Version: 1.0
 */
@Getter
public enum LabelingDetailStatus {
    WAITING_LABELING(1, "待标注"),
    LABELED(2, "已标注"),
    ;

    private final int code;
    private final String value;

    LabelingDetailStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LabelingDetailStatus getByCode(int code) {
        for (LabelingDetailStatus status : LabelingDetailStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
