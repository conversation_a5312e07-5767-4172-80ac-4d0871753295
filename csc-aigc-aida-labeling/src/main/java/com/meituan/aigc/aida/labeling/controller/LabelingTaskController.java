package com.meituan.aigc.aida.labeling.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meituan.aigc.aida.labeling.param.*;
import com.meituan.aigc.aida.labeling.param.InspectionItemTemplateParam;
import com.meituan.aigc.aida.labeling.param.LabelingTaskParam;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.param.TaskTransferParam;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckDistributeParam;
import com.meituan.aigc.aida.labeling.param.task.LabelingDistributionParam;
import com.meituan.aigc.aida.labeling.param.task.LabelingGroupConsistencyParam;
import com.meituan.aigc.aida.labeling.param.task.RecycleTaskParam;
import com.meituan.aigc.aida.labeling.pojo.dto.*;
import com.meituan.aigc.aida.labeling.pojo.dto.group.GroupConfigList;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingGroupDetailDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingTaskCountDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingTaskDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.task.TaskGroupDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.aigc.aida.labeling.pojo.vo.QualityCheckOverviewVO;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.meituan.aigc.aida.labeling.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/labeling-tasks")
@Slf4j
public class LabelingTaskController {

    @Autowired
    private TaskService taskService;

    /**
     * 获取mis信息
     *
     * @param mis mis号
     * @return mis信息列表
     */
    @GetMapping("/labeling-personnel")
    public Result<MisInfoDTO> getMisList(@RequestParam(value = "mis") String mis) {
        List<MisDataDTO> misList = taskService.getMisList(mis);
        return Result.ok(new MisInfoDTO(misList));
    }

    /**
     * 标注任务质检分配
     *
     * @param param 质检参数
     * @return 质检结果
     */
    @PostMapping("/quality-check/groups")
    public Result<?> distributeQualityCheck(@RequestBody QualityCheckDistributeParam param) {
        taskService.distributeQualityCheck(param);
        return Result.create();
    }

    /**
     * @param param 各分组回收信息
     * @return 是否成功
     */
    @PostMapping("/export")
    public Result<?> recycleLabelingData(@RequestBody RecycleTaskParam param) {
        taskService.recycleLabelingData(param);
        return Result.create();
    }

    /**
     * 创建标注任务
     *
     * @param file                  上传文件
     * @param taskName              任务名称
     * @param taskType              任务类型
     * @param dataType              数据类型
     * @param responsiblePerson     标注人mis
     * @param responsiblePersonName 标注人姓名
     * @param labelResultList       标签列表
     * @return 创建结果
     */
    @PostMapping("/createTask")
    public Result<?> createLabelingTask(@RequestParam MultipartFile file,
                                        @RequestParam String taskName,
                                        @RequestParam Integer taskType,
                                        @RequestParam Integer dataType,
                                        @RequestParam String responsiblePerson,
                                        @RequestParam String responsiblePersonName,
                                        @RequestParam (required = false) Long bizType,
                                        @RequestParam String labelResultList) {
        LabelingTaskParam taskParam = buildTaskParam(file, taskName, taskType, dataType, bizType,
                responsiblePerson, responsiblePersonName, labelResultList);
        taskService.createLabelingTask(taskParam);
        return Result.create();
    }

    /**
     * 获取任务统计信息
     *
     * @param taskId 任务id
     * @return 任务统计信息
     */
    @GetMapping("/count")
    public Result<LabelingTaskCountDTO> getLabelingTaskCount(@RequestParam Long taskId) {
        return Result.ok(taskService.getLabelingTaskCount(taskId));
    }

    /**
     * 查询标注任务列表
     *
     * @param taskName 任务名称
     * @param taskId   任务id
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 任务列表
     */
    @GetMapping("/taskList")
    public Result<PageData<LabelingTaskDTO>> taskList(
            @RequestParam(value = "taskName", required = false) String taskName,
            @RequestParam(value = "taskId", required = false) Long taskId,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return Result.ok(taskService.taskList(taskName, taskId, pageNum, pageSize));
    }

    /**
     * 根据任务Id查询任务分组列表
     *
     * @param taskId 任务ID
     * @return 分组列表
     */
    @GetMapping("/groups")
    public Result<TaskGroupDTO> listGroupsByTaskId(@RequestParam("taskId") Long taskId) {
        return Result.ok(taskService.listGroupsByTaskId(taskId));
    }

    /**
     * 获取质检模板列表
     *
     * @return 质检模板列表
     */
    @GetMapping("/labeling-templates")
    public Result<List<InspectionItemTemplateDTO>> templateList() {
        return Result.ok(taskService.templateList());
    }

    /**
     * 获取质检模板下的质检项列表
     *
     * @param templateId 质检模板ID
     * @return 质检项列表
     */
    @GetMapping("/labeling-templates/items")
    public Result<List<InspectionItemDTO>> itemList(@RequestParam("templateId") Long templateId) {
        return Result.ok(taskService.itemList(templateId));
    }

    /**
     * 质检概览
     *
     * @param taskId 任务id
     * @return 质检结果
     */
    @GetMapping("/quality-check/groups/details")
    public Result<QualityCheckOverviewVO> qualityCheckDetails(@RequestParam("taskId") Long taskId) {
        return Result.ok(taskService.qualityCheckDetails(taskId));
    }

    /**
     * 分配标注任务
     *
     * @param param 分组参数
     * @return 分配结果
     */
    @PostMapping("/groups/distribute")
    public Result<?> distributeLabelingTask(@RequestBody LabelingDistributionParam param) {
        taskService.distributeLabelingTask(param);
        return Result.create();
    }

    /**
     * 删除标注任务
     *
     * @param taskId 任务id
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public Result<?> deleteLabelingTask(@RequestParam Long taskId) {
        taskService.deleteLabelingTask(taskId);
        return Result.create();
    }

    /**
     * 分页查询任务原始数据
     *
     * @param taskId   任务id
     * @param pageNum  页数
     * @param pageSize 每页多少条
     * @return 原始数据
     */
    @GetMapping("/raw-data")
    public Result<PageData<LabelingTaskRawDataDTO>> pageRawData(@RequestParam(value = "taskId") Long taskId,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                                @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return Result.ok(taskService.pageRawDataByTaskId(taskId, pageNum, pageSize));
    }

    @PostMapping("/groups/consistency")
    public Result<LabelingGroupDetailDTO> listGroupConsistency(@RequestBody LabelingGroupConsistencyParam param) {
        return Result.ok(taskService.listGroupConsistency(param));
    }

    /**
     * 保存标注项模板
     *
     * @param inspectionItemTemplateParam 模板参数
     * @return 保存结果
     */
    @PostMapping("/labeling-templates/save")
    public Result<?> saveLabelingTemplates(@RequestBody InspectionItemTemplateParam inspectionItemTemplateParam) {
        taskService.saveLabelingTemplates(inspectionItemTemplateParam);
        return Result.create();
    }

    /**
     * 删除标注项模板
     *
     * @param templateId 模板ID
     * @return 删除结果
     */
    @DeleteMapping("/labeling-templates/delete")
    public Result<?> deleteLabelingTemplate(@RequestParam Long templateId) {
        taskService.deleteLabelingTemplate(templateId);
        return Result.create();
    }

    @GetMapping("/group/list")
    public Result<GroupConfigList> listLabelingTemplateGroup(@RequestParam Long taskId) {
        return Result.ok(taskService.listLabelingTemplateGroup(taskId));
    }

    /**
     * 获取转交相关信息
     *
     * @param taskId       任务id
     * @param transferType 转交类型 1-标注 2-质检
     * @return 转交信息
     */
    @GetMapping("/transfer/info")
    public Result<TransferInfoDTO> getTransferInfo(@RequestParam Long taskId, @RequestParam Integer transferType) {
        return Result.ok(taskService.getTransferInfo(taskId, transferType));
    }

    /**
     * 转交任务
     *
     * @param param 转交参数
     * @return 是否成功
     */
    @PostMapping("/transfer")
    public Result<?> transfer(@RequestBody TaskTransferParam param) {
        taskService.transfer(param);
        return Result.create();
    }

    /**
     * 构建任务参数
     *
     * @param file                  上传文件
     * @param taskName              任务名称
     * @param taskType              任务类型
     * @param dataType              数据类型
     * @param responsiblePerson     标注人mis
     * @param responsiblePersonName 标注人姓名
     * @param labelResultList       标注项
     * @return 任务参数对象
     */
    private LabelingTaskParam buildTaskParam(MultipartFile file, String taskName, Integer taskType, Integer dataType, Long bizType,
                                             String responsiblePerson, String responsiblePersonName, String labelResultList) {
        return LabelingTaskParam.builder()
                .taskType(taskType)
                .taskName(taskName)
                .file(file)
                .dataType(dataType)
                .responsiblePerson(responsiblePerson)
                .bizType(bizType)
                .responsiblePersonName(responsiblePersonName)
                .labelResultList(JSON.parseObject(labelResultList, new TypeReference<List<List<LabelResult>>>() {
                })).build();
    }
}
