package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-04-21 19:22
 * @description 外部指标类型
 */
@Getter
public enum ExternalMetricTypeEnum {
    /**
     * 指标类型
     */
    ENUMERATION(1, "枚举型"),
    TEXT(2, "文本输入型")
    ;

    private final int code;

    private final String description;

    ExternalMetricTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    public static ExternalMetricTypeEnum parse(int code){
        for (ExternalMetricTypeEnum externalMetricTypeEnum : ExternalMetricTypeEnum.values()) {
            if (externalMetricTypeEnum.getCode() == code) {
                return externalMetricTypeEnum;
            }
        }
        return null;
    }

    /**
     * 通过枚举名称获取对应的枚举值
     * 
     * @param name 枚举名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static ExternalMetricTypeEnum getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        try {
            return ExternalMetricTypeEnum.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 通过枚举名称获取对应的code值
     * 
     * @param name 枚举名称
     * @return 对应的code值，如果未找到则返回null
     */
    public static Integer getCodeByName(String name) {
        ExternalMetricTypeEnum enumValue = getByName(name);
        return enumValue != null ? enumValue.getCode() : null;
    }
}
