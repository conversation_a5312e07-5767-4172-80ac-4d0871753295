package com.meituan.aigc.aida.data.management.fetch.dao.repository;

import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/7 15:52
 * @Version: 1.0
 */
public interface DataFetchRecordRepository {

    /**
     * 批量插入数据
     *
     * @param dataFetchRecordList 数据
     */
    void batchInsert(List<DataFetchRecordWithBLOBs> dataFetchRecordList);

    DataFetchRecordWithBLOBs getById(Long lastId);

    void updateByIdSelective(DataFetchRecordWithBLOBs dataFetchRecord);

    /**
     * 根据任务ID逻辑删除记录
     *
     * @param taskId 任务ID
     */
    void deleteByTaskId(Long taskId);

    /**
     * 根据taskId查询总条数
     *
     * @param taskId 任务Id
     * @return 条数
     */
    Integer countByTaskId(Long taskId);

    /**
     * 根据任务Id查询一条数据
     *
     * @param taskId 任务Id
     * @return
     */
    DataFetchRecordWithBLOBs getOneByTaskId(Long taskId);

    /**
     * 根据任务Id分页查询
     *
     * @param taskId   任务Id
     * @param offset   偏移量
     * @param pageSize 每页记录数
     * @return 结果
     */
    List<DataFetchRecordWithBLOBs> pageByTaskId(Long taskId, Integer offset, Integer pageSize);

    /**
     * 根据taskId和inputs查询数据记录列表
     *
     * @param taskId 任务ID
     * @param inputs 应用入参
     * @return 结果
     */
    List<DataFetchRecordWithBLOBs> listByTaskIdAndLikeByInputs(Long taskId, String inputs);
}
