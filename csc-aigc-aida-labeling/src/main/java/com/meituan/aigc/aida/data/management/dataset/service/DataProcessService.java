package com.meituan.aigc.aida.data.management.dataset.service;

import com.meituan.aigc.aida.data.management.dataset.dto.*;
import com.meituan.aigc.aida.data.management.dataset.param.DataMarkParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataProcessCreateTaskParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataProcessTaskPageParam;
import com.meituan.aigc.aida.data.management.dataset.param.LabelRobotParam;
import com.meituan.aigc.aida.data.management.dataset.param.NonCustomRobotQueryParam;
import com.meituan.aigc.aida.data.management.dataset.param.SignalUpdateParam;
import com.meituan.aigc.aida.data.management.fetch.vo.RobotVO;
import com.meituan.aigc.aida.labeling.param.PageData;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 数据处理服务接口
 */
public interface DataProcessService {

    /**
     * 创建数据处理任务
     *
     * @param param 创建任务参数
     */
    void createProcessTask(DataProcessCreateTaskParam param);

    /**
     * 分页查询数据处理任务
     *
     * @param param 查询参数
     * @return 任务列表
     */
    PageData<DataProcessTaskDTO> listProcessTask(DataProcessTaskPageParam param);

    /**
     * 获取打标机器人信息
     *
     * @param param 机器人参数
     * @return 机器人信息
     */
    LabelRobotInfoDTO getLabelRobotInfo(LabelRobotParam param);

    /**
     * 获取非自定义标签的场景信息
     *
     * @return 场景列表DTO
     */
    SceneListDTO getNonCustomScene();

    /**
     * 获取非自定义标签的标签信息
     *
     * @param sceneId 场景ID
     * @return 标签列表DTO
     */
    LabelListDTO getNonCustomLabel(Long sceneId);

    /**
     * 查询非自定义打标机器人
     *
     * @param sceneId     场景ID
     * @param labelIdList 标签ID列表
     * @return 打标机器人列表
     */
    List<NonCustomRobotDTO> queryNonCustomRobot(Long sceneId, @RequestParam List<String> labelIdList);

    /**
     * 数据打标
     *
     * @param param 打标参数
     */
    void markData(DataMarkParam param);

    /**
     * 删除数据
     *
     * @param taskId 任务ID
     */
    void deleteData(Long taskId);


    /**
     * 获取机器人列表
     *
     * @param dataSetId 数据集ID
     * @return 机器人列表
     */
    List<RobotVO> getRobotList(Long dataSetId, Long versionId);

    /**
     * 获取机器人版本列表
     *
     * @param robotId 机器人ID
     * @return 机器人版本列表
     */
    SignalRefreshRobotVersionInfo getRobotVersionList(String robotId);

    /**
     * 信号回刷
     *
     * @param param 信号回刷参数
     */
    void updateSignal(SignalUpdateParam param);

} 