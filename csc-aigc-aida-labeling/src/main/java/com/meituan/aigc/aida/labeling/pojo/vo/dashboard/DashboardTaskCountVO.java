package com.meituan.aigc.aida.labeling.pojo.vo.dashboard;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guowenhui
 * @Create: 2025/5/20 14:27
 * @Version: 1.0
 */
@Data
public class DashboardTaskCountVO implements Serializable {

    /**
     * 任务总数
     */
    private Integer totalTaskCount;
    /**
     * 标注完成任务量
     */
    private Integer labelingCompletedTaskCount;
    /**
     * 质检完成任务量
     */
    private Integer checkCompletedTaskCount;
    /**
     * 总样本数
     */
    private Integer totalSampleCount;
    /**
     * 总任务数量周同比
     */
    private Double wowTotalTaskRate;
    /**
     * 标注完成任务量周同比
     */
    private Double wowCompletedTaskRate;
    /**
     * 质检完成任务量周同比
     */
    private Double wowCheckCompletedTaskRate;
    /**
     * 总样本数周同比
     */
    private Double wowTotalSampleRate;

}
