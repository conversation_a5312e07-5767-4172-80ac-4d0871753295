package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.meituan.aigc.aida.labeling.dao.mapper.InspectionItemMapper;
import com.meituan.aigc.aida.labeling.dao.model.InspectionItem;
import com.meituan.aigc.aida.labeling.dao.model.InspectionItemExample;
import com.meituan.aigc.aida.labeling.dao.repository.InspectionItemRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:00
 * @Version: 1.0
 */
@Repository
public class InspectionItemRepositoryImpl implements InspectionItemRepository {

    @Resource
    private InspectionItemMapper inspectionItemMapper;

    @Override
    public List<InspectionItem> listByTemplateId(Long templateId) {
        if (templateId == null) {
            return new ArrayList<>();
        }
        InspectionItemExample example = new InspectionItemExample();
        InspectionItemExample.Criteria criteria = example.createCriteria();
        criteria.andTemplateIdEqualTo(templateId)
                .andIsDeletedEqualTo(Boolean.FALSE);
        return inspectionItemMapper.selectByExample(example);
    }

    @Override
    public List<InspectionItem> listByTemplateIdList(List<Long> templateIdList) {
        if (CollectionUtils.isEmpty(templateIdList)) {
            return new ArrayList<>();
        }
        InspectionItemExample example = new InspectionItemExample();
        InspectionItemExample.Criteria criteria = example.createCriteria();
        criteria.andTemplateIdIn(templateIdList)
                .andIsDeletedEqualTo(Boolean.FALSE);
        return inspectionItemMapper.selectByExample(example);
    }

    @Override
    public void insertInspectionItem(InspectionItem inspectionItem) {
        if (Objects.isNull(inspectionItem)) {
            return;
        }
        inspectionItemMapper.insertSelective(inspectionItem);
    }

    @Override
    public void updateInspectionItem(InspectionItem inspectionItem) {
        if(Objects.isNull(inspectionItem)){
            return;
        }
        inspectionItemMapper.updateByPrimaryKeySelective(inspectionItem);
    }

    @Override
    public InspectionItem getById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
      return inspectionItemMapper.selectByPrimaryKey(id);
    }

    @Override
    public void batchUpdateItems(List<InspectionItem> inspectionItems) {
        if(CollectionUtils.isEmpty(inspectionItems)){
            return;
        }
        inspectionItemMapper.batchUpdateItems(inspectionItems);
    }
}
