<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.LabelingQualityCheckTaskMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="sample_size" jdbcType="INTEGER" property="sampleSize" />
    <result column="raw_data_sample_size" jdbcType="INTEGER" property="rawDataSampleSize" />
    <result column="quality_check_config" jdbcType="CHAR" property="qualityCheckConfig" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="quality_check_mis" jdbcType="VARCHAR" property="qualityCheckMis" />
    <result column="quality_check_name" jdbcType="VARCHAR" property="qualityCheckName" />
    <result column="create_mis" jdbcType="VARCHAR" property="createMis" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="assign_type" jdbcType="TINYINT" property="assignType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, task_id, sample_size, raw_data_sample_size, quality_check_config, status,
    tenant_id, quality_check_mis, quality_check_name, create_mis, create_time, update_time, 
    is_deleted, assign_type
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from labeling_quality_check_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from labeling_quality_check_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from labeling_quality_check_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTaskExample">
    delete from labeling_quality_check_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask" useGeneratedKeys="true" keyProperty="id">
    insert into labeling_quality_check_task (id, name, task_id,
      sample_size, raw_data_sample_size, quality_check_config,
      status, tenant_id, quality_check_mis,
      quality_check_name, create_mis, create_time,
      update_time, is_deleted, assign_type
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{taskId,jdbcType=BIGINT},
      #{sampleSize,jdbcType=INTEGER}, #{rawDataSampleSize,jdbcType=INTEGER}, #{qualityCheckConfig,jdbcType=CHAR},
      #{status,jdbcType=TINYINT}, #{tenantId,jdbcType=VARCHAR}, #{qualityCheckMis,jdbcType=VARCHAR},
      #{qualityCheckName,jdbcType=VARCHAR}, #{createMis,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=BIT}, #{assignType,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask">
    insert into labeling_quality_check_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="sampleSize != null">
        sample_size,
      </if>
      <if test="rawDataSampleSize != null">
        raw_data_sample_size,
      </if>
      <if test="qualityCheckConfig != null">
        quality_check_config,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="qualityCheckMis != null">
        quality_check_mis,
      </if>
      <if test="qualityCheckName != null">
        quality_check_name,
      </if>
      <if test="createMis != null">
        create_mis,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="assignType != null">
        assign_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sampleSize != null">
        #{sampleSize,jdbcType=INTEGER},
      </if>
      <if test="rawDataSampleSize != null">
        #{rawDataSampleSize,jdbcType=INTEGER},
      </if>
      <if test="qualityCheckConfig != null">
        #{qualityCheckConfig,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckMis != null">
        #{qualityCheckMis,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckName != null">
        #{qualityCheckName,jdbcType=VARCHAR},
      </if>
      <if test="createMis != null">
        #{createMis,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="assignType != null">
        #{assignType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTaskExample" resultType="java.lang.Long">
    select count(*) from labeling_quality_check_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update labeling_quality_check_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.sampleSize != null">
        sample_size = #{record.sampleSize,jdbcType=INTEGER},
      </if>
      <if test="record.rawDataSampleSize != null">
        raw_data_sample_size = #{record.rawDataSampleSize,jdbcType=INTEGER},
      </if>
      <if test="record.qualityCheckConfig != null">
        quality_check_config = #{record.qualityCheckConfig,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityCheckMis != null">
        quality_check_mis = #{record.qualityCheckMis,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityCheckName != null">
        quality_check_name = #{record.qualityCheckName,jdbcType=VARCHAR},
      </if>
      <if test="record.createMis != null">
        create_mis = #{record.createMis,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.assignType != null">
        assign_type = #{record.assignType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update labeling_quality_check_task
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=BIGINT},
      sample_size = #{record.sampleSize,jdbcType=INTEGER},
      raw_data_sample_size = #{record.rawDataSampleSize,jdbcType=INTEGER},
      quality_check_config = #{record.qualityCheckConfig,jdbcType=CHAR},
      status = #{record.status,jdbcType=TINYINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      quality_check_mis = #{record.qualityCheckMis,jdbcType=VARCHAR},
      quality_check_name = #{record.qualityCheckName,jdbcType=VARCHAR},
      create_mis = #{record.createMis,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      assign_type = #{record.assignType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask">
    update labeling_quality_check_task
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sampleSize != null">
        sample_size = #{sampleSize,jdbcType=INTEGER},
      </if>
      <if test="rawDataSampleSize != null">
        raw_data_sample_size = #{rawDataSampleSize,jdbcType=INTEGER},
      </if>
      <if test="qualityCheckConfig != null">
        quality_check_config = #{qualityCheckConfig,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckMis != null">
        quality_check_mis = #{qualityCheckMis,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckName != null">
        quality_check_name = #{qualityCheckName,jdbcType=VARCHAR},
      </if>
      <if test="createMis != null">
        create_mis = #{createMis,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="assignType != null">
        assign_type = #{assignType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask">
    update labeling_quality_check_task
    set name = #{name,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=BIGINT},
      sample_size = #{sampleSize,jdbcType=INTEGER},
      raw_data_sample_size = #{rawDataSampleSize,jdbcType=INTEGER},
      quality_check_config = #{qualityCheckConfig,jdbcType=CHAR},
      status = #{status,jdbcType=TINYINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      quality_check_mis = #{qualityCheckMis,jdbcType=VARCHAR},
      quality_check_name = #{qualityCheckName,jdbcType=VARCHAR},
      create_mis = #{createMis,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=BIT},
      assign_type = #{assignType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into labeling_quality_check_task (id, name, task_id,
      sample_size, quality_check_config, status,
      tenant_id, quality_check_mis, quality_check_name, create_mis,
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR}, #{item.taskId,jdbcType=BIGINT},
      #{item.sampleSize,jdbcType=INTEGER}, #{item.qualityCheckConfig,jdbcType=CHAR}, #{item.status,jdbcType=TINYINT},
      #{item.tenantId,jdbcType=VARCHAR}, #{item.qualityCheckMis,jdbcType=VARCHAR}, #{item.qualityCheckName,jdbcType=VARCHAR}, #{item.createMis,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="listByTaskId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from labeling_quality_check_task
    where task_id = #{taskId,jdbcType=BIGINT} and status != 4 and is_deleted = 0
  </select>
  <update id="updateByTaskId" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask">
    update labeling_quality_check_task
    set status = #{status,jdbcType=TINYINT}
    where task_id = #{taskId,jdbcType=BIGINT}
  </update>
  <select id="countSampleSizeByTaskId" resultType="com.meituan.aigc.aida.labeling.dao.po.QualityCheckSampleCountPO">
    select count(distinct item.raw_data_id) as totalCount, count(distinct item.session_id) as totalSessionCount
    from  labeling_quality_check_task task
    left join labeling_quality_check_item item on task.id = item.quality_check_task_id
    where task.task_id = #{taskId,jdbcType=BIGINT}
  </select>
  <select id="listByTaskIdAndNameAndQualityCheckerMis" resultType="com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO">
    select
    c.id as id,
    c.name as taskName,
    c.task_id as taskId,
    c.status as qualityCheckStatus,
    c.quality_check_mis as qualityCheckMis,
    c.quality_check_name as qualityCheckName,
    if(c.raw_data_sample_size is not null, c.raw_data_sample_size, c.sample_size) as sampleCount,
    t.task_source as dataSetSource,
    t.task_type as taskType
    from labeling_quality_check_task c
    left join labeling_task t
    on c.task_id = t.id
    <where>
      status != 1
      <if test="taskId != null">
        and c.task_id = #{taskId}
      </if>
      <if test="!isAdmin and qualityCheckerMis != null and qualityCheckerMis != ''">
        and (c.quality_check_mis = #{qualityCheckerMis} or t.labeling_manager = #{qualityCheckerMis})
      </if>
      <if test="taskName != null">
        and c.name like concat("%",#{taskName},"%")
      </if>
      <if test="assignType != null">
        and t.assign_type = #{assignType}
      </if>
      and c.is_deleted = 0
    </where>
    order by c.id DESC, c.update_time desc
  </select>
  <select id="countByTaskIdAndStatus" resultType="java.lang.Integer">
    SELECT COUNT(id)
    FROM labeling_quality_check_task
    <where>
      <if test="taskId != null">
        task_id = #{taskId}
      </if>
      <if test="statusList != null">
        AND status IN
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
      </if>
    </where>
  </select>
</mapper>