package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 数据集筛选分页查询响应
 *
 * <AUTHOR>
 * @date 2025-06-04
 * @description 数据集筛选分页查询响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetFilterPageResponseDTO implements Serializable {

    /**
     * 总记录数
     */
    private Long totalCount;

    /**
     * 数据列表
     */
    private List<DataItem> data;

    /**
     * 数据项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataItem implements Serializable {

        /**
         * 结果列表
         */
        private List<FieldResult> resultList;
    }

    /**
     * 字段结果
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FieldResult implements Serializable {

        /**
         * 字段名称
         */
        private String name;

        /**
         * 字段类型
         */
        private String fieldType;

        /**
         * 字段值（动态类型）
         */
        private Object value;
    }
} 