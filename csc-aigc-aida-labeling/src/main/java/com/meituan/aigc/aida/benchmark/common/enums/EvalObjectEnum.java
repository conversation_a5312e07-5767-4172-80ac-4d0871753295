package com.meituan.aigc.aida.benchmark.common.enums;

import com.meituan.aigc.aida.common.enums.base.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import scala.Int;

/**
 * 评测对象枚举
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Getter
@AllArgsConstructor
public enum EvalObjectEnum implements BaseEnum<Integer> {

    BASE_MODEL(1, "基础模型");

    /**
     * 操作类型代码
     */
    private final Integer code;

    /**
     * 操作类型描述
     */
    private final String value;

    /**
     * 根据code获取value
     *
     * @param code 操作类型代码
     * @return 对应的操作类型描述，如果未找到则返回null
     */
    public static String getValueByCode(Integer code) {
        for (EvalObjectEnum evalObject : EvalObjectEnum.values()) {
            if (evalObject.getCode().equals(code)) {
                return evalObject.getValue();
            }
        }
        return BASE_MODEL.value;
    }
}
