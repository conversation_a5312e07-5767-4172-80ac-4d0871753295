package com.meituan.aigc.aida.labeling.pojo.vo.dashboard;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 19:11
 * @Version: 1.0
 */
@Data
public class DashboardTaskItemVO implements Serializable {

    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 标注负责人mis
     */
    private String labelingManager;
    /**
     * 标注负责人名称
     */
    private String labelingManagerName;
    /**
     * 状态code
     */
    private Integer labelingStatus;
    /**
     * 状态名称
     */
    private String statusName;
    /**
     * 质检状态code
     */
    private Integer qualityCheckStatus;
    /**
     * 质检状态名称
     */
    private String qualityCheckStatusName;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 业务类型
     */
    private Long bizType;
    /**
     * 业务类型名称
     */
    private String bizTypeName;
    /**
     * 总样本数
     */
    private Integer totalSampleCount;

}
