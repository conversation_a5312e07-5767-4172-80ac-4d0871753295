package com.meituan.aigc.aida.labeling.convert;

import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingDetailTemporaryStorageDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingResultSaveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * @Author: guowenhui
 * @Create: 2025/6/11 15:30
 * @Version: 1.0
 */
@Mapper
public interface LabelingDetailConverter {

    LabelingDetailConverter INSTANCE = Mappers.getMapper(LabelingDetailConverter.class);

    @Mappings(value = {
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "subTaskId", ignore = true),
            @Mapping(target = "rawDataId", ignore = true),
            @Mapping(target = "sampleStatus", expression = "java(com.meituan.aigc.aida.labeling.common.enums.sub.task.LabelingDetailSampleStatusEnum.NOT_SAMPLE.getCode())"),
            @Mapping(target = "viewStatus", expression = "java(com.meituan.aigc.aida.labeling.common.enums.DataViewStatusEnum.NOT_VIEW.getCode())")
    })
    LabelingDetail converterToLabelingDetail(LabelingDetail labelingDetail);


    @Mapping(target = "thirdMessageId", source = "thirdMessageId")
    LabelingDetailTemporaryStorageDTO converterToLabelingDetailTemporaryStorageDTO(LabelingResultSaveDTO labelingResultSaveDTO);

}
