package com.meituan.aigc.aida.labeling.pojo.dto.task;

import com.meituan.aigc.aida.labeling.param.PageData;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class LabelingGroupDetailDTO implements Serializable {

    /**
     * 分页数据
     */
    private PageData<LabelingGroupDTO> pageData;
    /**
     * 一致数量
     */
    private Integer sameCount;
    /**
     * 不一致数量
     */
    private Integer diffCount;
    /**
     * 总数量
     */
    private Integer totalCount;
    /**
     * 标注人mis映射
     */
    private Map<String, String> labelingMisMap;
}
