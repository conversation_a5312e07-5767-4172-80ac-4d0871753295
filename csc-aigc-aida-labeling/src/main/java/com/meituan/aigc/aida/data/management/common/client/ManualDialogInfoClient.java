package com.meituan.aigc.aida.data.management.common.client;

import com.dianping.haitun.cases.dto.SessionBriefDTO;
import com.dianping.haitun.cases.service.ManualDialogInfoService;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/4/3 17:32
 * @Version: 1.0
 * 在线海豚人工对话服务$
 */
@Slf4j
@Component
public class ManualDialogInfoClient {

    @Autowired
    private ManualDialogInfoService manualDialogInfoService;


    /**
     * 根据太平洋会话id获取全链路sessionId
     */
    public String getPacificSessionIdByContactId(Long contactId) {
        try {
            if (Objects.isNull(contactId)) {
                return null;
            }
            // 根据chatId获取SessionBriefDTO对象
            SessionBriefDTO sessionBriefDTO = manualDialogInfoService.getSessionByManualDialogId(contactId);
            // 如果sessionBriefDTO为空或者sessionId为空，则返回null
            if (Objects.isNull(sessionBriefDTO) || Objects.isNull(sessionBriefDTO.getSessionId())) {
                return null;
            }
            // 返回sessionId的字符串形式
            return String.valueOf(sessionBriefDTO.getSessionId());
        } catch (Exception e) {
            // 捕获异常并记录日志
            log.warn("getPacificSessionIdByChatId error,contactId:{},e:", contactId, e);
            return null;
        }
    }

}
