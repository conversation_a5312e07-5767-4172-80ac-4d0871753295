package com.meituan.aigc.aida.data.management.es;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.meituan.aigc.aida.common.enums.EsOperationEnum;
import com.meituan.aigc.aida.config.EsLionConfig;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.es.exception.TrainingEsException;
import com.meituan.aigc.aida.data.management.es.query.*;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.aigc.aida.labeling.common.CatConstant.DATASET_ES_OPERATION;
import static org.eclipse.jetty.webapp.MetaDataComplete.True;

/**
 * 数据集-ES服务
 * 给上层提供数据集相关ES的查询能力
 *
 * <AUTHOR>
 * @date 2025/5/21
 */
@Slf4j
@Service
public class DatasetEsIndexService {
    /**
     * 数据集Es索引基础服务
     */
    @Resource
    private DatasetEsBaseIndexService datasetEsBaseIndexService;

    /**
     * 数据集重试服务
     */
    @Resource
    private DatasetRetryService retryService;

    /**
     * Es相关Lion配置
     */
    @Resource
    private EsLionConfig esLionConfig;

    /**
     * 单条数据写入
     *
     * @param dataset    数据集
     * @param dataSource 数据来源
     * @return true表示插入成功，false表示插入失败
     */
    public boolean insertDataset(DatasetEsIndex dataset, String dataSource) {
        if (Objects.isNull(dataset)) {
            log.warn("insertDataset failed, dataset is null");
            return true;
        }

        if (StringUtils.isBlank(dataSource)) {
            log.error("insertDataset failed, dataSource is blank");
            return false;
        }

        // 获取目标索引名称
        String indexName = getNowIndexNameByDataSource(dataSource);

        log.info("Inserting dataset to index: {}", indexName);

        try {
            IndexResponse response = datasetEsBaseIndexService.insert(dataset, indexName);
            if (response != null && response.status() == RestStatus.CREATED) {
                log.info("Successfully inserted dataset to index: {}", indexName);
                return true;
            } else {
                log.error("Failed to insert dataset to index: {}, response: {}", indexName, response);
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to insert dataset to index: {}", indexName, e);
            throw new TrainingEsException("DatasetEsIndexService——插入异常", e);
        }
    }

    /**
     * 批量写入数据集到ES
     * 根据数据来源字段自动路由到对应的索引
     *
     * @param datasetList 数据集列表
     * @param dataSource  数据来源
     * @param indexName   索引名称
     * @return 批量插入结果
     */
    public BatchResult batchInsertDatasets(List<DatasetEsIndex> datasetList, String dataSource, String indexName) {
        if (CollectionUtils.isEmpty(datasetList) || StringUtils.isBlank(dataSource)) {
            log.info("datasetList or dataSource is empty");
            return BatchResult.empty(EsOperationEnum.INSERT);
        }

        Event event = Cat.newEvent(DATASET_ES_OPERATION, dataSource + "-" + CommonConstants.INSERT);
        try {
            // 获取目标索引名称
            if (StringUtils.isBlank(indexName)) {
                indexName = getNowIndexNameByDataSource(dataSource);
            }
            BatchResult result = retryService.executeWithRetry(datasetList, indexName, EsOperationEnum.INSERT);
            event.setSuccessStatus();
            return result;
        } catch (Exception e) {
            log.error("数据批量插入异常", e);
            event.setStatus(e);
            throw new TrainingEsException("DatasetEsIndexService——批量插入异常", e);
        } finally {
            event.complete();
        }
    }

    /**
     * 单条数据更新
     * 使用业务ID作为ES的doc_id进行更新
     *
     * @param dataset    数据集（必须包含datasetId作为业务ID）
     * @param dataSource 数据来源
     * @param indexName  索引名称
     * @return true表示更新成功，false表示更新失败
     */
    public boolean updateDataset(DatasetEsIndex dataset, String dataSource, String indexName) {
        if (dataset == null) {
            log.warn("updateDataset failed, dataset is null");
            return false;
        }

        if (StringUtils.isBlank(dataSource)) {
            log.error("updateDataset failed, dataSource is blank");
            return false;
        }

        log.info("Updating dataset with datasetId: {} to index: {}", dataset.getDatasetId(), indexName);
        try {
            UpdateResponse updateResponse = datasetEsBaseIndexService.update(dataset, dataset.getDocumentId(), indexName);
            if (updateResponse != null && updateResponse.getResult() == DocWriteResponse.Result.UPDATED) {
                log.info("Successfully updated dataset with datasetId: {} to index: {}", dataset.getDatasetId(), indexName);
                return true;
            }
            log.error("数据更新失败，response={}", updateResponse);
            return false;
        } catch (Exception e) {
            log.error("Failed to update dataset with datasetId: {} to index: {}", dataset.getDatasetId(), indexName, e);
            throw new TrainingEsException("DatasetEsIndexService——更新数据异常", e);
        }
    }

    /**
     * 批量更新数据集到ES
     * 使用业务ID作为ES的doc_id进行更新
     *
     * @param datasetList 数据集列表（每个对象必须包含datasetId作为业务ID）
     * @param dataSource  数据来源
     * @param indexName   索引名称
     * @return true表示全部更新成功，false表示有更新失败
     */
    public BatchResult batchUpdateDatasets(List<DatasetEsIndex> datasetList, String dataSource, String indexName) {
        if (CollectionUtils.isEmpty(datasetList) || StringUtils.isBlank(dataSource)) {
            log.info("batchUpdateDatasets failed, datasetList is empty");
            return BatchResult.empty(EsOperationEnum.UPDATE);
        }

        // 验证所有数据集都有doc_id（数据ID）
        for (int i = 0; i < datasetList.size(); i++) {
            DatasetEsIndex dataset = datasetList.get(i);
            if (dataset == null || StringUtils.isBlank(dataset.getDocumentId())) {
                log.error("batchUpdateDatasets failed, dataset at index {} has no datasetId", i);
                return BatchResult.empty(EsOperationEnum.UPDATE);
            }
        }

        Event event = Cat.newEvent(DATASET_ES_OPERATION, indexName + "-" + CommonConstants.UPDATE);
        log.info("Batch updating {} datasets to index: {}", datasetList.size(), indexName);
        try {
            BatchResult result = retryService.executeWithRetry(datasetList, indexName, EsOperationEnum.UPDATE);
            event.setSuccessStatus();
            return result;
        } catch (Exception e) {
            log.error("Failed to batch update datasets to index: {}", indexName, e);
            event.setStatus(e);
            throw new TrainingEsException("DatasetEsIndexService——批量更新数据异常", e);
        } finally {
            event.complete();
        }
    }


    /**
     * 根据文档ID删除单条数据
     *
     * @param docId      文档Id
     * @param dataSource 数据来源
     * @param indexName  索引名称
     * @return true表示删除成功，false表示删除失败
     */
    public boolean deleteByDocId(String docId, String dataSource, String indexName) {
        if (StringUtils.isBlank(docId) || StringUtils.isBlank(dataSource)) {
            log.warn("deleteByDocId failed, docId or dataSource is blank");
            return false;
        }

        try {
            DeleteResponse result = datasetEsBaseIndexService.delete(docId, indexName);

            // 正确的删除成功判断方式
            if (result != null && result.getResult() == DeleteResponse.Result.DELETED) {
                log.info("Successfully deleted dataset with docId: {} from index: {}", docId, indexName);
                return true;
            }
            log.error("删除数据失败, result={}", result);
            return false;
        } catch (Exception e) {
            log.error("Failed to delete dataset by docId: {} from index: {}", docId, indexName, e);
            throw new TrainingEsException("DatasetEsIndexService——删除数据异常", e);
        }
    }

    /**
     * 批量删除数据集（根据DatasetEsIndex对象列表）
     * 对象中必须包含documentId字段
     *
     * @param datasetList 数据集列表（每个对象必须包含documentId）
     * @param dataSource  数据来源
     * @param indexName   索引名称
     * @return true表示全部删除成功，false表示有删除失败
     */
    public BatchResult batchDeleteDatasets(List<DatasetEsIndex> datasetList, String dataSource, String indexName) {
        if (CollectionUtils.isEmpty(datasetList)) {
            log.info("batchDeleteDatasets failed, datasetList is empty");
            return BatchResult.empty(EsOperationEnum.DELETE);
        }

        if (StringUtils.isBlank(dataSource)) {
            log.error("batchDeleteDatasets failed, dataSource is blank");
            throw new IllegalArgumentException("dataSource cannot be blank");
        }

        // 验证所有数据集都有documentId
        for (int i = 0; i < datasetList.size(); i++) {
            DatasetEsIndex dataset = datasetList.get(i);
            if (dataset == null || StringUtils.isBlank(dataset.getDocumentId())) {
                log.error("batchDeleteDatasets failed, dataset at index {} has no documentId", i);
                return BatchResult.empty(EsOperationEnum.DELETE);
            }
        }

        log.info("Batch deleting {} datasets from index: {}", datasetList.size(), indexName);
        try {
            return retryService.executeWithRetry(datasetList, indexName, EsOperationEnum.DELETE);
        } catch (Exception e) {
            log.error("Failed to batch delete datasets from index: {}", indexName, e);
            throw new TrainingEsException("DatasetEsIndexService——批量删除数据异常", e);
        }
    }

    /**
     * 删除指定数据集中的字段
     * 使用UpdateByQuery API批量删除符合条件的文档中的指定字段
     *
     * @param fieldName      要删除的字段名（支持嵌套字段，如 "textData.title"）
     * @param index          索引名称
     * @param datasetId      数据集ID
     * @param datasetVersion 数据集版本
     * @return true表示删除成功，false表示删除失败
     */
    public boolean deleteField(String fieldName, String index, String datasetId, String datasetVersion) {
        // 参数验证
        if (!isValidFieldName(fieldName) || StringUtils.isEmpty(index) ||
                StringUtils.isEmpty(datasetId) || StringUtils.isEmpty(datasetVersion)) {
            log.warn("deleteField failed, invalid parameters");
            return false;
        }

        log.info("Starting to delete field: {} from index: {}, datasetId: {}, datasetVersion: {}",
                fieldName, index, datasetId, datasetVersion);

        try {
            UpdateByQueryRequest request = new UpdateByQueryRequest(index);

            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("datasetId", datasetId))
                    .must(QueryBuilders.termQuery("versionId", datasetVersion))
                    .must(QueryBuilders.existsQuery(fieldName));
            request.setQuery(boolQuery);

            // 使用参数化脚本
            Map<String, Object> params = buildDeleteFieldParams(fieldName);
            String scriptSource = buildSafeDeleteFieldScript(fieldName);
            Script script = new Script(ScriptType.INLINE, "painless", scriptSource, params);
            request.setScript(script);

            // 设置请求参数
            request.setBatchSize(1000);
            request.setConflicts("proceed");
            request.setRefresh(true);
            request.setTimeout("5m");
            request.setRequestsPerSecond(100);

            // 执行更新操作
            BulkByScrollResponse response = datasetEsBaseIndexService.handleCommonUpdateByQueryRequest(request, index);

            // 检查执行结果
            if (response != null) {
                long updated = response.getUpdated();
                long total = response.getTotal();
                long failures = response.getBulkFailures().size();

                log.info("Delete field completed. Total: {}, Updated: {}, Failures: {}, Field: {}, Index: {}",
                        total, updated, failures, fieldName, index);

                // 如果有失败的操作，记录详细信息
                if (!response.getBulkFailures().isEmpty()) {
                    response.getBulkFailures().forEach(failure -> {
                        log.error("Bulk failure during field deletion: {}", failure.getMessage());
                    });
                }

                // 如果有搜索失败，记录详细信息
                if (!response.getSearchFailures().isEmpty()) {
                    response.getSearchFailures().forEach(failure -> {
                        log.error("Search failure during field deletion", failure.getReason());
                    });
                }

                // 判断是否成功：没有失败且有更新或者总数为0（没有匹配的文档）
                boolean success = failures == 0 && (updated > 0 || total == 0);

                if (success) {
                    log.info("Successfully deleted field: {} from {} documents in index: {}",
                            fieldName, updated, index);
                } else {
                    log.error("Failed to delete field: {} from index: {}, failures: {}",
                            fieldName, index, failures);
                }

                return success;
            } else {
                log.error("Delete field failed, response is null. Field: {}, Index: {}", fieldName, index);
                return false;
            }

        } catch (Exception e) {
            log.error("Exception occurred while deleting field", e);
            return false;
        }
    }

    /**
     * 分页查询数据集
     * 支持多种查询条件的组合查询
     *
     * @param condition 查询条件
     * @return 分页查询结果
     */
    public DatasetPageResult pageQuery(@Validated DatasetEsQueryCondition condition) {
        if (Objects.isNull(condition)) {
            log.warn("pageQuery failed, condition is null");
            return DatasetPageResult.of(new ArrayList<>(), 0L, 0, 20, 0L, null);
        }

        // 验证分页参数
        condition.validatePagination();

        // 根据数据来源获取索引名称，如果没有指定则查询所有索引
        String[] indices = getIndicesForQuery(condition.getDataSource(), condition.getIndexName());

        log.info("Starting page query with condition: {}, indices: {}", condition, Arrays.toString(indices));

        Event event = Cat.newEvent(DATASET_ES_OPERATION, CommonConstants.SEARCH);
        try {
            // 构建查询请求
            SearchRequest searchRequest = buildSearchRequest(condition, indices);

            // 执行查询
            long startTime = System.currentTimeMillis();
            SearchResponse searchResponse = null;
            if (StringUtils.isNotEmpty(condition.getScrollId())) {
                searchResponse = datasetEsBaseIndexService.scrollSearch(condition.getScrollId(), condition.getScrollTime(), String.join(",", indices));
            } else {
                searchResponse = datasetEsBaseIndexService.handleCommonQuerySearchRequest(searchRequest, String.join(",", indices));
            }
            long took = System.currentTimeMillis() - startTime;

            // 解析查询结果
            DatasetPageResult result = parseSearchResponse(searchResponse, condition, took);
            // 清除滚动上下文
            if (StringUtils.isNotBlank(result.getScrollId()) && !result.getHasNext()) {
                datasetEsBaseIndexService.clearScroll(result.getScrollId());
            }
            event.setSuccessStatus();
            return result;
        } catch (Exception e) {
            log.error("Failed to execute page query, condition: {}", condition, e);
            event.setStatus(e);
            throw new TrainingEsException("DatasetEsIndexService——查询数据异常", e);
        } finally {
            event.complete();
        }
    }

    /**
     * 批量查询数据集
     *
     * @param datasetIds 数据集ID列表
     * @param dataSource 数据来源
     * @return 数据集列表
     */
    public List<DatasetEsIndex> listByDatasetIds(List<String> datasetIds, String dataSource) {
        if (CollectionUtils.isEmpty(datasetIds) || StringUtils.isBlank(dataSource)) {
            log.warn("listByDatasetIds failed, datasetIds is empty or dataSource is blank");
            return new ArrayList<>();
        }

        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource(dataSource)
                .pageNum(0)
                .pageSize(datasetIds.size())
                .build();

        DatasetPageResult result = pageQuery(condition);
        return result != null ? result.getRecords() : new ArrayList<>();
    }

    /**
     * 根据数据来源获取要查询的索引
     *
     * @param dataSource 单个数据来源
     * @param indexName  索引名称
     * @return 索引名称数组
     */
    private String[] getIndicesForQuery(String dataSource, String indexName) {
        Set<String> indices = new HashSet<>();

        if (StringUtils.isEmpty(dataSource) && StringUtils.isEmpty(indexName)) {
            throw new IllegalArgumentException("查询数据索引不能为空");
        }

        // 如果指定索引，则走指定索引
        if (StringUtils.isNotBlank(indexName)) {
            indices.add(indexName);
            return indices.toArray(new String[0]);
        }
        // 查询集群上所有的数据来源索引
        return getActualIndicesForDataSource(dataSource);
    }

    /**
     * 构建搜索请求
     *
     * @param condition 查询条件
     * @param indices   索引名称
     * @return 搜索请求
     */
    private SearchRequest buildSearchRequest(DatasetEsQueryCondition condition, String[] indices) {
        SearchRequest searchRequest = new SearchRequest(indices);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // 构建查询条件
        QueryBuilder queryBuilder = EsQueryTransform.buildQueryBuilder(condition);
        searchSourceBuilder.query(queryBuilder);

        // 设置聚合查询条件
        EsQueryTransform.buildAggregationSearch(searchSourceBuilder, condition);

        // 设置分页
        if (StringUtils.isEmpty(condition.getScrollTime()) && StringUtils.isEmpty(condition.getScrollId())) {
            searchSourceBuilder.from(condition.getFrom());
        }
        searchSourceBuilder.size(condition.getSize());

        // 设置排序
        SortBuilder<?>[] sortBuilders = EsQueryTransform.buildSortBuilders(condition);
        for (SortBuilder<?> sortBuilder : sortBuilders) {
            searchSourceBuilder.sort(sortBuilder);
        }

        // 设置返回的字段
        if (CollectionUtils.isNotEmpty(condition.getFieldPath())) {
            searchSourceBuilder.fetchSource(condition.getFieldPath().toArray(new String[0]), null);
        }

        // 设置高亮
        if (Boolean.TRUE.equals(condition.getIncludeHighlight()) && CollectionUtils.isNotEmpty(condition.getHighlightFields())) {
            HighlightBuilder highlightBuilder = new HighlightBuilder();
            condition.getHighlightFields().forEach(highlightBuilder::field);
            highlightBuilder.preTags("<em>").postTags("</em>");
            searchSourceBuilder.highlighter(highlightBuilder);
        }

        searchRequest.source(searchSourceBuilder);

        // 设置滚动参数
        if (StringUtils.isNotEmpty(condition.getScrollTime())) {
            searchRequest.scroll(condition.getScrollTime());
        }
        return searchRequest;
    }

    /**
     * 解析搜索响应
     *
     * @param searchResponse 搜索响应
     * @param condition      查询条件
     * @param took           查询耗时
     * @return 分页结果
     */
    private DatasetPageResult parseSearchResponse(SearchResponse searchResponse, DatasetEsQueryCondition condition, long took) {
        SearchHits hits = searchResponse.getHits();
        long total = hits.getTotalHits().value;

        // 解析查询结果
        List<DatasetEsIndex> records = new ArrayList<>();
        Map<String, Map<String, List<String>>> highlights = new HashMap<>();

        for (SearchHit hit : hits.getHits()) {
            // 解析文档
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            sourceAsMap.put("documentId", hit.getId());
            DatasetEsIndex dataset = parseDatasetFromMap(sourceAsMap);
            records.add(dataset);

            // 解析高亮
            if (hit.getHighlightFields() != null && !hit.getHighlightFields().isEmpty()) {
                Map<String, List<String>> docHighlights = new HashMap<>();
                hit.getHighlightFields().forEach((field, highlightField) -> {
                    List<String> fragments = Arrays.stream(highlightField.getFragments())
                            .map(Text::string)
                            .collect(Collectors.toList());
                    docHighlights.put(field, fragments);
                });
                highlights.put(hit.getId(), docHighlights);
            }
        }

        // 构建分页结果
        DatasetPageResult result = DatasetPageResult.of(records, total, condition.getPageNum(), condition.getPageSize(), took, searchResponse.getScrollId());
        if (StringUtils.isNotBlank(searchResponse.getScrollId())) {
            result.setHasNext(hasNextByScrollSearch(searchResponse));
        }

        // 设置高亮结果
        if (!highlights.isEmpty()) {
            result.setHighlights(highlights);
        }

        // 解析聚合结果
        result.setTermsAggResult(parseAggResult(searchResponse, condition));

        return result;
    }

    /**
     * 解析聚合结果
     *
     * @param searchResponse 搜索响应
     * @param condition      查询条件
     * @return 聚合结果
     */
    private Map<String, Map<String, Long>> parseAggResult(SearchResponse searchResponse, DatasetEsQueryCondition condition) {
        if (Objects.isNull(condition) || Objects.isNull(searchResponse.getAggregations())) {
            return new HashMap<>();
        }

        Map<String, Map<String, Long>> aggResults = new HashMap<>();

        // 遍历所有聚合结果
        for (Map.Entry<String, Aggregation> entry : searchResponse.getAggregations().asMap().entrySet()) {
            String aggName = entry.getKey();
            Aggregation aggregation = entry.getValue();

            // 处理嵌套聚合
            if (aggregation instanceof ParsedNested) {
                ParsedNested nestedAgg = (ParsedNested) aggregation;
                // 获取嵌套聚合中的terms聚合
                for (Aggregation subAgg : nestedAgg.getAggregations()) {
                    Terms terms = (Terms) subAgg;
                    Map<String, Long> buckets = new HashMap<>();
                    for (Terms.Bucket bucket : terms.getBuckets()) {
                        buckets.put(bucket.getKeyAsString(), bucket.getDocCount());
                    }
                    aggResults.put(aggName, buckets);
                }
            } else if (aggregation instanceof Terms) {
                Terms terms = (Terms) aggregation;
                Map<String, Long> buckets = new HashMap<>();
                for (Terms.Bucket bucket : terms.getBuckets()) {
                    buckets.put(bucket.getKeyAsString(), bucket.getDocCount());
                }
                aggResults.put(aggName, buckets);
            }
        }

        return aggResults;
    }

    /**
     * 从Map解析DatasetEsIndex对象
     *
     * @param sourceAsMap 源数据Map
     * @return DatasetEsIndex对象
     */
    @SuppressWarnings("unchecked")
    private DatasetEsIndex parseDatasetFromMap(Map<String, Object> sourceAsMap) {
        DatasetEsIndex dataset = new DatasetEsIndex();

        // 设置基础字段
        dataset.setDocumentId((String) sourceAsMap.get("documentId"));
        dataset.setDatasetId((String) sourceAsMap.get("datasetId"));
        dataset.setVersionId((String) sourceAsMap.get("versionId"));
        dataset.setSessionId((String) sourceAsMap.get("sessionId"));
        dataset.setDataSource((String) sourceAsMap.get("dataSource"));
        dataset.setFieldMapping((String) sourceAsMap.get("fieldMapping"));

        // 设置时间字段
        if (sourceAsMap.get("createTime") != null) {
            dataset.setCreateTime(new Date((Long) sourceAsMap.get("createTime")));
        }
        if (sourceAsMap.get("updateTime") != null) {
            dataset.setUpdateTime(new Date((Long) sourceAsMap.get("updateTime")));
        }

        // 设置复杂字段
        if (sourceAsMap.get("textData") != null) {
            dataset.setTextData((Map<String, Object>) sourceAsMap.get("textData"));
        }
        if (sourceAsMap.get("flattenedData") != null) {
            dataset.setFlattenedData((Map<String, Object>) sourceAsMap.get("flattenedData"));
        }
        if (sourceAsMap.get("commonData") != null) {
            dataset.setCommonData((Map<String, Object>) sourceAsMap.get("commonData"));
        }
        if (sourceAsMap.get("dateData") != null) {
            Map<String, Object> dateDataMap = (Map<String, Object>) sourceAsMap.get("dateData");
            Map<String, Date> dateData = new HashMap<>();
            dateDataMap.forEach((key, value) -> {
                if (value instanceof Long) {
                    dateData.put(key, new Date((Long) value));
                }
            });
            dataset.setDateData(dateData);
        }

        return dataset;
    }

    /**
     * 动态获取指定数据源的所有实际存在的索引
     * 通过查询ES集群状态获取真实存在的索引列表
     *
     * @param dataSource 数据来源
     * @return 实际存在的索引名称数组
     */
    private String[] getActualIndicesForDataSource(String dataSource) {
        try {
            String indexPrefix = getIndexPrefixByDataSource(dataSource);

            // 使用索引模式查询，让ES返回实际匹配的索引
            // 这种方式会自动过滤掉不存在的索引
            return new String[]{indexPrefix + "_*"};

            // 注意：如果需要获取精确的索引列表，可以使用以下方式：
            // 1. 使用 IndicesClient.get() 方法获取索引信息
            // 2. 使用 ClusterClient.state() 方法获取集群状态
            // 但这需要额外的ES客户端调用，可能影响性能

        } catch (Exception e) {
            log.error("Failed to get actual indices for dataSource: {}", dataSource, e);
            throw new RuntimeException("获取索引失败");
        }
    }

    /**
     * 根据数据来源获取索引名称
     *
     * @param dataSource 数据来源
     * @return 完整的索引名称（包含时间后缀）
     */
    private String getNowIndexNameByDataSource(String dataSource) {
        String indexPrefix = getIndexPrefixByDataSource(dataSource);
        // 生成时间后缀 yyyy-MM
        String timeSuffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        return indexPrefix + "_" + timeSuffix;
    }

    /**
     * 根据数据来源获取索引前缀（不包含时间后缀）
     *
     * @param dataSource 数据来源
     * @return 索引前缀
     */
    private String getIndexPrefixByDataSource(String dataSource) {
        DataSourceEnum source = DataSourceEnum.fromValue(dataSource);

        switch (source) {
            case CASE:
                return esLionConfig.getDatasetEsIndexFromCase();
            case ONLINE:
                return esLionConfig.getDatasetEsIndexFromOnline();
            case UPLOAD:
                return esLionConfig.getDatasetEsIndexFromUpload();
            default:
                throw new IllegalArgumentException("Unsupported data source: " + dataSource);
        }
    }

    /**
     * 判断滚动查询是否有下一页
     *
     * @param response ES检索结果
     * @return 是否有下一页
     */
    private boolean hasNextByScrollSearch(SearchResponse response) {
        if (Objects.isNull(response)) {
            return false;
        }
        SearchHit[] hits = response.getHits().getHits();
        // scroll 查询只要本次返回不为空，就可以继续滚动
        return hits != null && hits.length > 0;
    }

    /**
     * 构建安全的删除字段脚本（使用参数化）
     *
     * @param fieldName 字段名
     * @return 安全的删除字段脚本
     */
    private String buildSafeDeleteFieldScript(String fieldName) {
        if (fieldName.contains(".")) {
            // 嵌套字段使用参数化
            return "if (ctx._source.containsKey(params.parentField) && " +
                    "ctx._source[params.parentField] instanceof Map && " +
                    "ctx._source[params.parentField].containsKey(params.childField)) {" +
                    "  ctx._source[params.parentField].remove(params.childField);" +
                    "}";
        } else {
            // 顶级字段使用参数化
            return "if (ctx._source.containsKey(params.fieldName)) {" +
                    "  ctx._source.remove(params.fieldName);" +
                    "}";
        }
    }

    /**
     * 构建脚本参数
     *
     * @param fieldName 字段名
     * @return 参数映射
     */
    private Map<String, Object> buildDeleteFieldParams(String fieldName) {
        Map<String, Object> params = new HashMap<>();

        if (fieldName.contains(".")) {
            String[] parts = fieldName.split("\\.", 2);
            params.put("parentField", parts[0]);
            params.put("childField", parts[1]);
        } else {
            params.put("fieldName", fieldName);
        }

        return params;
    }

    /**
     * 验证字段名是否安全
     *
     * @param fieldName 字段名
     */
    private boolean isValidFieldName(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return false;
        }

        // 1. 长度限制
        if (fieldName.length() > 100) {
            log.warn("Field name too long: {}", fieldName.length());
            return false;
        }

        // 2. 字符白名单验证：只允许字母、数字、下划线、点号、连字符
        String fieldNamePattern = "^[a-zA-Z0-9._-]+$";
        if (!fieldName.matches(fieldNamePattern)) {
            log.warn("Invalid field name format: {}", fieldName);
            return false;
        }

        // 3. 嵌套层级限制（防止过深嵌套）
        String[] parts = fieldName.split("\\.");
        if (parts.length > 5) {
            log.warn("Field name nesting too deep: {}", parts.length);
            return false;
        }
        return true;
    }
}