package com.meituan.aigc.aida.data.management.fetch.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-04-03 10:28
 * @description 信号埋点规则
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SignalTrackingRuleDTO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 状态 1禁用 2启用
     */
    private Integer status;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 标准问ID
     */
    private String typicalQuestionIds;

    /**
     * 客服mis号
     */
    private String misIds;

    /**
     * 太平洋弹屏AC接口ID
     */
    private String acInterface;

    /**
     * AI搭机器人配置
     */
    private String aidaAppCfg;

    /**
     * 创建人mis
     */
    private String creatorMis;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
