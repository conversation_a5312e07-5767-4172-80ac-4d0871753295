<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.benchmark.dao.mapper.BenchmarkLeaderboardDetailsMapper">
    <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="version_id" jdbcType="BIGINT" property="versionId"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="metric_value" jdbcType="DECIMAL" property="metricValue"/>
        <result column="sum" jdbcType="DECIMAL" property="sum"/>
        <result column="avg" jdbcType="DECIMAL" property="avg"/>
        <result column="std" jdbcType="DECIMAL" property="std"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="metric_name" jdbcType="VARCHAR" property="metricName"/>
        <result column="subjective_score" jdbcType="DECIMAL" property="subjectiveScore"/>
        <result column="objective_score" jdbcType="DECIMAL" property="objectiveScore"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , version_id, model_name, metric_value, sum, avg, std, created_at, updated_at,
    is_deleted, metric_name, subjective_score, objective_score
    </sql>
    <select id="selectByExample"
            parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetailsExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from benchmark_leaderboard_details
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from benchmark_leaderboard_details
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from benchmark_leaderboard_details
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetailsExample">
        delete from benchmark_leaderboard_details
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails">
        insert into benchmark_leaderboard_details (id, version_id, model_name,
                                                   metric_value, sum, avg,
                                                   std, created_at, updated_at,
                                                   is_deleted, metric_name, subjective_score,
                                                   objective_score)
        values (#{id,jdbcType=BIGINT}, #{versionId,jdbcType=BIGINT}, #{modelName,jdbcType=VARCHAR},
                #{metricValue,jdbcType=DECIMAL}, #{sum,jdbcType=DECIMAL}, #{avg,jdbcType=DECIMAL},
                #{std,jdbcType=DECIMAL}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP},
                #{isDeleted,jdbcType=BIT}, #{metricName,jdbcType=VARCHAR}, #{subjectiveScore,jdbcType=DECIMAL},
                #{objectiveScore,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails">
        insert into benchmark_leaderboard_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="versionId != null">
                version_id,
            </if>
            <if test="modelName != null">
                model_name,
            </if>
            <if test="metricValue != null">
                metric_value,
            </if>
            <if test="sum != null">
                sum,
            </if>
            <if test="avg != null">
                avg,
            </if>
            <if test="std != null">
                std,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="metricName != null">
                metric_name,
            </if>
            <if test="subjectiveScore != null">
                subjective_score,
            </if>
            <if test="objectiveScore != null">
                objective_score,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="versionId != null">
                #{versionId,jdbcType=BIGINT},
            </if>
            <if test="modelName != null">
                #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="metricValue != null">
                #{metricValue,jdbcType=DECIMAL},
            </if>
            <if test="sum != null">
                #{sum,jdbcType=DECIMAL},
            </if>
            <if test="avg != null">
                #{avg,jdbcType=DECIMAL},
            </if>
            <if test="std != null">
                #{std,jdbcType=DECIMAL},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BIT},
            </if>
            <if test="metricName != null">
                #{metricName,jdbcType=VARCHAR},
            </if>
            <if test="subjectiveScore != null">
                #{subjectiveScore,jdbcType=DECIMAL},
            </if>
            <if test="objectiveScore != null">
                #{objectiveScore,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <!-- 批量插入排行榜详情数据 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into benchmark_leaderboard_details (
        version_id, model_name, metric_value, sum, avg, std,
        created_at, updated_at, is_deleted, metric_name,
        subjective_score, objective_score
        ) values
        <foreach collection="records" item="record" separator=",">
            (
            #{record.versionId,jdbcType=BIGINT},
            #{record.modelName,jdbcType=VARCHAR},
            #{record.metricValue,jdbcType=DECIMAL},
            #{record.sum,jdbcType=DECIMAL},
            #{record.avg,jdbcType=DECIMAL},
            #{record.std,jdbcType=DECIMAL},
            COALESCE(#{record.createdAt,jdbcType=TIMESTAMP}, NOW()),
            COALESCE(#{record.updatedAt,jdbcType=TIMESTAMP}, NOW()),
            COALESCE(#{record.isDeleted,jdbcType=BIT}, false),
            #{record.metricName,jdbcType=VARCHAR},
            #{record.subjectiveScore,jdbcType=DECIMAL},
            #{record.objectiveScore,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <select id="countByExample"
            parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetailsExample"
            resultType="java.lang.Long">
        select count(*) from benchmark_leaderboard_details
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update benchmark_leaderboard_details
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.versionId != null">
                version_id = #{record.versionId,jdbcType=BIGINT},
            </if>
            <if test="record.modelName != null">
                model_name = #{record.modelName,jdbcType=VARCHAR},
            </if>
            <if test="record.metricValue != null">
                metric_value = #{record.metricValue,jdbcType=DECIMAL},
            </if>
            <if test="record.sum != null">
                sum = #{record.sum,jdbcType=DECIMAL},
            </if>
            <if test="record.avg != null">
                avg = #{record.avg,jdbcType=DECIMAL},
            </if>
            <if test="record.std != null">
                std = #{record.std,jdbcType=DECIMAL},
            </if>
            <if test="record.createdAt != null">
                created_at = #{record.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedAt != null">
                updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.isDeleted != null">
                is_deleted = #{record.isDeleted,jdbcType=BIT},
            </if>
            <if test="record.metricName != null">
                metric_name = #{record.metricName,jdbcType=VARCHAR},
            </if>
            <if test="record.subjectiveScore != null">
                subjective_score = #{record.subjectiveScore,jdbcType=DECIMAL},
            </if>
            <if test="record.objectiveScore != null">
                objective_score = #{record.objectiveScore,jdbcType=DECIMAL},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update benchmark_leaderboard_details
        set id = #{record.id,jdbcType=BIGINT},
        version_id = #{record.versionId,jdbcType=BIGINT},
        model_name = #{record.modelName,jdbcType=VARCHAR},
        metric_value = #{record.metricValue,jdbcType=DECIMAL},
        sum = #{record.sum,jdbcType=DECIMAL},
        avg = #{record.avg,jdbcType=DECIMAL},
        std = #{record.std,jdbcType=DECIMAL},
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
        is_deleted = #{record.isDeleted,jdbcType=BIT},
        metric_name = #{record.metricName,jdbcType=VARCHAR},
        subjective_score = #{record.subjectiveScore,jdbcType=DECIMAL},
        objective_score = #{record.objectiveScore,jdbcType=DECIMAL}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails">
        update benchmark_leaderboard_details
        <set>
            <if test="versionId != null">
                version_id = #{versionId,jdbcType=BIGINT},
            </if>
            <if test="modelName != null">
                model_name = #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="metricValue != null">
                metric_value = #{metricValue,jdbcType=DECIMAL},
            </if>
            <if test="sum != null">
                sum = #{sum,jdbcType=DECIMAL},
            </if>
            <if test="avg != null">
                avg = #{avg,jdbcType=DECIMAL},
            </if>
            <if test="std != null">
                std = #{std,jdbcType=DECIMAL},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BIT},
            </if>
            <if test="metricName != null">
                metric_name = #{metricName,jdbcType=VARCHAR},
            </if>
            <if test="subjectiveScore != null">
                subjective_score = #{subjectiveScore,jdbcType=DECIMAL},
            </if>
            <if test="objectiveScore != null">
                objective_score = #{objectiveScore,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails">
        update benchmark_leaderboard_details
        set version_id       = #{versionId,jdbcType=BIGINT},
            model_name       = #{modelName,jdbcType=VARCHAR},
            metric_value     = #{metricValue,jdbcType=DECIMAL},
            sum              = #{sum,jdbcType=DECIMAL},
            avg              = #{avg,jdbcType=DECIMAL},
            std              = #{std,jdbcType=DECIMAL},
            created_at       = #{createdAt,jdbcType=TIMESTAMP},
            updated_at       = #{updatedAt,jdbcType=TIMESTAMP},
            is_deleted       = #{isDeleted,jdbcType=BIT},
            metric_name      = #{metricName,jdbcType=VARCHAR},
            subjective_score = #{subjectiveScore,jdbcType=DECIMAL},
            objective_score  = #{objectiveScore,jdbcType=DECIMAL}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>