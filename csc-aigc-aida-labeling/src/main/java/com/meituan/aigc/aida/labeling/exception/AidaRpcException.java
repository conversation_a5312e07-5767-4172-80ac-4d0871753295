package com.meituan.aigc.aida.labeling.exception;

import com.meituan.aigc.aida.labeling.remote.common.enums.ResultCode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 训练系统rpc执行异常
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AidaRpcException extends AidaTrainingException {

    /**
     * 异常码
     */
    private ResultCode resultCode;

    public AidaRpcException() {
        super();
    }

    public AidaRpcException(String message) {
        super(message);
    }

    public AidaRpcException(Throwable t) {
        super(t);
    }
}
