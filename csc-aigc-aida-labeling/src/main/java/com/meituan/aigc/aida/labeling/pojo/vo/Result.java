package com.meituan.aigc.aida.labeling.pojo.vo;


import com.meituan.mtrace.Tracer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;

@Data
public class Result<T> implements Serializable {
    private String traceId;
    private Integer code;
    private String message;
    private T data;

    public Result() {
        this.traceId = Tracer.id();
    }

    public Result(String traceId, Integer code, String message, T data) {
        this.traceId = traceId;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> Result<T> create() {
        Result<T> result = new Result<>();
        result.setCode(ResultCode.SUCCESS.getCode());
        return result;
    }

    public static <T> Result<T> ok(T data) {
        Result<T> result = create();
        result.setData(data);
        return result;
    }


    public static <T> Result<T> fail(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    @Getter
    @AllArgsConstructor
    public enum ResultCode {
        SUCCESS(0, "success"),
        CLIENT_EXCEPTION(400, "client exception"),
        SERVICE_EXCEPTION(500, "service exception"),
        PARAM_INVALID(100001, "param invalid"),
        PARAM_EMPTY(100002, "param empty"),
        SYSTEM_COMMON_ERROR(200000, "system common error"),
        THIRD_PARTY_ERROR(300000, "third party error"),
        ;

        private final Integer code;
        private final String info;

        public static ResultCode parseByCode(Integer code) {
            for (ResultCode resultCode : ResultCode.values()) {
                if (resultCode.getCode().equals(code)) {
                    return resultCode;
                }
            }
            throw new IllegalArgumentException("Invalid code: " + code);
        }

    }
}
