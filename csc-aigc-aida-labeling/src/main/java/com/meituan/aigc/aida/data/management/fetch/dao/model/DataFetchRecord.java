package com.meituan.aigc.aida.data.management.fetch.dao.model;

import lombok.Data;

import java.util.Date;

@Data
public class DataFetchRecord {

    /**
     * ID
     */
    private Long id;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 智能侧SessionId
     */
    private String sessionId;
    /**
     * 联络id，在线: 转人工chatId, 热线: 热线联络id
     */
    private String contactId;
    /**
     * 信号数据内容
     */
    private String signalDataContent;
    /**
     * 客服MIS
     */
    private String staffMis;
    /**
     * 消息ID，用于标识一条消息的唯一ID，比如在线的海豚消息ID
     */
    private String messageId;
    /**
     * 消息发送时间
     */
    private Date messageOccurredTime;
    /**
     * 消息来源: STAFF: 客服发送的消息, CUSTOMER: 用户发送的消息
     */
    private String chatMessageFromType;
    /**
     * 联系类型，online: 在线渠道, call: 电话渠道
     */
    private String contactType;
    /**
     * 标问ID列表
     */
    private String typicalQuestionIds;
    /**
     * 弹屏AC ID
     */
    private String popupAcId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Boolean isDelete;

}