<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.LabelingQualityCheckItemMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="labeling_data_id" jdbcType="BIGINT" property="labelingDataId" />
    <result column="quality_check_task_id" jdbcType="BIGINT" property="qualityCheckTaskId" />
    <result column="quality_check_mis" jdbcType="VARCHAR" property="qualityCheckMis" />
    <result column="quality_check_name" jdbcType="VARCHAR" property="qualityCheckName" />
    <result column="quality_check_result" jdbcType="TINYINT" property="qualityCheckResult" />
    <result column="quality_check_items_result" jdbcType="CHAR" property="qualityCheckItemsResult" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="checkable_status" jdbcType="TINYINT" property="checkableStatus" />
    <result column="modified_labeling_items_result" jdbcType="CHAR" property="modifiedLabelingItemsResult" />
    <result column="modified_raw_data_mapped_content" jdbcType="CHAR" property="modifiedRawDataMappedContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="first_check_time" jdbcType="TIMESTAMP" property="firstCheckTime" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="raw_data_id" jdbcType="BIGINT" property="rawDataId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, labeling_data_id, quality_check_task_id, quality_check_mis, quality_check_name, 
    quality_check_result, quality_check_items_result, status, checkable_status, modified_labeling_items_result, 
    modified_raw_data_mapped_content, create_time, update_time, first_check_time, session_id,
    message_id, raw_data_id
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from labeling_quality_check_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from labeling_quality_check_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from labeling_quality_check_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItemExample">
    delete from labeling_quality_check_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem">
    insert into labeling_quality_check_item (id, labeling_data_id, quality_check_task_id, 
      quality_check_mis, quality_check_name, quality_check_result, 
      quality_check_items_result, status, checkable_status, 
      modified_labeling_items_result, modified_raw_data_mapped_content, 
      create_time, update_time, first_check_time,
      session_id, message_id, raw_data_id
      )
    values (#{id,jdbcType=BIGINT}, #{labelingDataId,jdbcType=BIGINT}, #{qualityCheckTaskId,jdbcType=BIGINT}, 
      #{qualityCheckMis,jdbcType=VARCHAR}, #{qualityCheckName,jdbcType=VARCHAR}, #{qualityCheckResult,jdbcType=BIT}, 
      #{qualityCheckItemsResult,jdbcType=CHAR}, #{status,jdbcType=TINYINT}, #{checkableStatus,jdbcType=TINYINT}, 
      #{modifiedLabelingItemsResult,jdbcType=CHAR}, #{modifiedRawDataMappedContent,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{firstCheckTime,jdbcType=TIMESTAMP},
      #{sessionId,jdbcType=VARCHAR}, #{messageId,jdbcType=VARCHAR}, #{rawDataId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem">
    insert into labeling_quality_check_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="labelingDataId != null">
        labeling_data_id,
      </if>
      <if test="qualityCheckTaskId != null">
        quality_check_task_id,
      </if>
      <if test="qualityCheckMis != null">
        quality_check_mis,
      </if>
      <if test="qualityCheckName != null">
        quality_check_name,
      </if>
      <if test="qualityCheckResult != null">
        quality_check_result,
      </if>
      <if test="qualityCheckItemsResult != null">
        quality_check_items_result,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="checkableStatus != null">
        checkable_status,
      </if>
      <if test="modifiedLabelingItemsResult != null">
        modified_labeling_items_result,
      </if>
      <if test="modifiedRawDataMappedContent != null">
        modified_raw_data_mapped_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="firstCheckTime != null">
        first_check_time,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="rawDataId != null">
        raw_data_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labelingDataId != null">
        #{labelingDataId,jdbcType=BIGINT},
      </if>
      <if test="qualityCheckTaskId != null">
        #{qualityCheckTaskId,jdbcType=BIGINT},
      </if>
      <if test="qualityCheckMis != null">
        #{qualityCheckMis,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckName != null">
        #{qualityCheckName,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckResult != null">
        #{qualityCheckResult,jdbcType=BIT},
      </if>
      <if test="qualityCheckItemsResult != null">
        #{qualityCheckItemsResult,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="checkableStatus != null">
        #{checkableStatus,jdbcType=TINYINT},
      </if>
      <if test="modifiedLabelingItemsResult != null">
        #{modifiedLabelingItemsResult,jdbcType=CHAR},
      </if>
      <if test="modifiedRawDataMappedContent != null">
        #{modifiedRawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstCheckTime != null">
        #{firstCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="rawDataId != null">
        #{rawDataId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItemExample" resultType="java.lang.Long">
    select count(*) from labeling_quality_check_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update labeling_quality_check_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labelingDataId != null">
        labeling_data_id = #{record.labelingDataId,jdbcType=BIGINT},
      </if>
      <if test="record.qualityCheckTaskId != null">
        quality_check_task_id = #{record.qualityCheckTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.qualityCheckMis != null">
        quality_check_mis = #{record.qualityCheckMis,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityCheckName != null">
        quality_check_name = #{record.qualityCheckName,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityCheckResult != null">
        quality_check_result = #{record.qualityCheckResult,jdbcType=BIT},
      </if>
      <if test="record.qualityCheckItemsResult != null">
        quality_check_items_result = #{record.qualityCheckItemsResult,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.checkableStatus != null">
        checkable_status = #{record.checkableStatus,jdbcType=TINYINT},
      </if>
      <if test="record.modifiedLabelingItemsResult != null">
        modified_labeling_items_result = #{record.modifiedLabelingItemsResult,jdbcType=CHAR},
      </if>
      <if test="record.modifiedRawDataMappedContent != null">
        modified_raw_data_mapped_content = #{record.modifiedRawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstCheckTime != null">
        first_check_time = #{record.firstCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sessionId != null">
        session_id = #{record.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="record.messageId != null">
        message_id = #{record.messageId,jdbcType=VARCHAR},
      </if>
      <if test="record.rawDataId != null">
        raw_data_id = #{record.rawDataId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update labeling_quality_check_item
    set id = #{record.id,jdbcType=BIGINT},
      labeling_data_id = #{record.labelingDataId,jdbcType=BIGINT},
      quality_check_task_id = #{record.qualityCheckTaskId,jdbcType=BIGINT},
      quality_check_mis = #{record.qualityCheckMis,jdbcType=VARCHAR},
      quality_check_name = #{record.qualityCheckName,jdbcType=VARCHAR},
      quality_check_result = #{record.qualityCheckResult,jdbcType=BIT},
      quality_check_items_result = #{record.qualityCheckItemsResult,jdbcType=CHAR},
      status = #{record.status,jdbcType=TINYINT},
      checkable_status = #{record.checkableStatus,jdbcType=TINYINT},
      modified_labeling_items_result = #{record.modifiedLabelingItemsResult,jdbcType=CHAR},
      modified_raw_data_mapped_content = #{record.modifiedRawDataMappedContent,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      first_check_time = #{record.firstCheckTime,jdbcType=TIMESTAMP},
      session_id = #{record.sessionId,jdbcType=VARCHAR},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      raw_data_id = #{record.rawDataId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem">
    update labeling_quality_check_item
    <set>
      <if test="labelingDataId != null">
        labeling_data_id = #{labelingDataId,jdbcType=BIGINT},
      </if>
      <if test="qualityCheckTaskId != null">
        quality_check_task_id = #{qualityCheckTaskId,jdbcType=BIGINT},
      </if>
      <if test="qualityCheckMis != null">
        quality_check_mis = #{qualityCheckMis,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckName != null">
        quality_check_name = #{qualityCheckName,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckResult != null">
        quality_check_result = #{qualityCheckResult,jdbcType=BIT},
      </if>
      <if test="qualityCheckItemsResult != null">
        quality_check_items_result = #{qualityCheckItemsResult,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="checkableStatus != null">
        checkable_status = #{checkableStatus,jdbcType=TINYINT},
      </if>
      <if test="modifiedLabelingItemsResult != null">
        modified_labeling_items_result = #{modifiedLabelingItemsResult,jdbcType=CHAR},
      </if>
      <if test="modifiedRawDataMappedContent != null">
        modified_raw_data_mapped_content = #{modifiedRawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstCheckTime != null">
        first_check_time = #{firstCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="rawDataId != null">
        raw_data_id = #{rawDataId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem">
    update labeling_quality_check_item
    set labeling_data_id = #{labelingDataId,jdbcType=BIGINT},
      quality_check_task_id = #{qualityCheckTaskId,jdbcType=BIGINT},
      quality_check_mis = #{qualityCheckMis,jdbcType=VARCHAR},
      quality_check_name = #{qualityCheckName,jdbcType=VARCHAR},
      quality_check_result = #{qualityCheckResult,jdbcType=BIT},
      quality_check_items_result = #{qualityCheckItemsResult,jdbcType=CHAR},
      status = #{status,jdbcType=TINYINT},
      checkable_status = #{checkableStatus,jdbcType=TINYINT},
      modified_labeling_items_result = #{modifiedLabelingItemsResult,jdbcType=CHAR},
      modified_raw_data_mapped_content = #{modifiedRawDataMappedContent,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      first_check_time = #{firstCheckTime,jdbcType=TIMESTAMP},
      session_id = #{sessionId,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      raw_data_id = #{rawDataId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
      insert into labeling_quality_check_item (id,session_id,message_id,raw_data_id, labeling_data_id,
      quality_check_task_id, quality_check_mis, quality_check_name, quality_check_result, quality_check_items_result,
      status,checkable_status, modified_labeling_items_result, modified_raw_data_mapped_content, create_time, update_time,
      first_check_time)
      values
      <foreach collection="list" item="item" separator=",">
          (#{item.id,jdbcType=BIGINT},
          #{item.sessionId,jdbcType=VARCHAR},#{item.messageId,jdbcType=VARCHAR},#{item.rawDataId,jdbcType=BIGINT},
          #{item.labelingDataId,jdbcType=BIGINT},
          #{item.qualityCheckTaskId,jdbcType=BIGINT},
          #{item.qualityCheckMis,jdbcType=VARCHAR}, #{item.qualityCheckName,jdbcType=VARCHAR},
          #{item.qualityCheckResult,jdbcType=BIT}, #{item.qualityCheckItemsResult,jdbcType=CHAR},
          #{item.status,jdbcType=TINYINT},#{item.checkableStatus,jdbcType=TINYINT},
          #{item.modifiedLabelingItemsResult,jdbcType=CHAR},#{item.modifiedRawDataMappedContent,jdbcType=CHAR},
          #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},#{item.firstCheckTime,jdbcType=TIMESTAMP})
      </foreach>
  </insert>
  <select id="listByQualityCheckTaskIdAndQualityCheckStatus" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckItemPO">
    SELECT
      i.id,
      i.labeling_data_id AS dataId,
      r.raw_data_content AS rawDataContent,
      r.raw_data_headers AS rawDataHeaders,
      CASE
        WHEN i.modified_raw_data_mapped_content IS NOT NULL THEN i.modified_raw_data_mapped_content
        WHEN d.modified_raw_data_mapped_content IS NOT NULL THEN d.modified_raw_data_mapped_content
        ELSE r.raw_data_mapped_content
      END AS rawDataMappedContent,
      i.quality_check_result AS qualityCheckResult,
      d.labeler_mis AS labelerMis,
      d.labeler_name AS labelerName,
      d.labeling_items_result AS labelingItems,
      i.quality_check_items_result AS qualityCheckRemark,
      i.quality_check_name AS qualityChecker,
      i.status AS qualityCheckStatus,
      i.update_time AS qualityCheckTime,
      r.session_id AS sessionId,
      r.session_time AS sessionTime,
      d.update_time AS labelTime,
      r.message_id AS messageId,
      r.id AS rawDataId,
      r.extra_info AS extraInfo,
      CASE
        WHEN i.modified_labeling_items_result IS NOT NULL THEN i.modified_labeling_items_result
      ELSE d.labeling_items_result
      END AS modifiedLabelingItems
    FROM
      labeling_quality_check_item i
    LEFT JOIN labeling_detail d ON i.labeling_data_id = d.id
    LEFT JOIN labeling_task_raw_data r ON d.raw_data_id = r.id
      <where>
          i.quality_check_task_id = #{param.qualityCheckTaskId,jdbcType=BIGINT}
          <if test="param.qualityCheckStatus != null">
              and i.status = #{param.qualityCheckStatus,jdbcType=TINYINT}
          </if>
          <if test="param.labelerMis != null">
              and d.labeler_mis = #{param.labelerMis, jdbcType=VARCHAR}
          </if>
          <if test="param.qualityChecker != null">
              and (i.quality_check_mis = #{param.qualityChecker, jdbcType=VARCHAR} or i.quality_check_name = #{param.qualityChecker, jdbcType=VARCHAR})
          </if>
          <if test="param.sessionId != null">
              and i.session_id = #{param.sessionId, jdbcType=VARCHAR}
          </if>
          <if test="param.startTime != null and param.endTime != null">
              and i.update_time between #{param.startTime, jdbcType=TIMESTAMP} and #{param.endTime, jdbcType=TIMESTAMP}
          </if>
          <if test="param.qualityCheckResult != null">
              and i.quality_check_result = #{param.qualityCheckResult, jdbcType=TINYINT}
          </if>
      </where>
      ORDER BY
      i.id ASC
  </select>
  <delete id="deleteByQualityCheckTaskIds" parameterType="java.util.List">
    delete from labeling_quality_check_item
    where quality_check_task_id in
    <foreach collection="list" item="qualityCheckTaskId" open="(" separator="," close=")">
      #{qualityCheckTaskId,jdbcType=BIGINT}
    </foreach>
  </delete>
  <select id="listQualityCheckItemByTaskIdAndRawDataIds" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckItemPO">
    select
    i.id as id,
    i.labeling_data_id as labelingDataId,
    r.raw_data_content as rawDataContent,
    r.raw_data_headers as rawDataHeaders,
    r.session_id as sessionId,
    r.session_time as sessionTime,
    case
      when i.modified_raw_data_mapped_content is not null then i.modified_raw_data_mapped_content
      when d.modified_raw_data_mapped_content is not null then d.modified_raw_data_mapped_content
      else r.raw_data_mapped_content
    end as rawDataMappedContent,
    i.quality_check_result as qualityCheckResult,
    i.quality_check_items_result as qualityCheckRemark,
    i.quality_check_mis as qualityChecker,
    i.update_time as qualityCheckTime,
    i.status as qualityCheckStatus,
    d.labeler_mis as labelerMis,
    d.labeler_name as labelerName,
    d.labeling_items_result as labelingItems,
    d.view_status as viewStatus,
    d.raw_data_id as rawDataId,
    d.update_time as labelTime,
    i.modified_labeling_items_result as modifiedLabelingItems
    from
    labeling_quality_check_item i
    left join labeling_detail d on i.labeling_data_id = d.id
    left join labeling_task_raw_data r on d.raw_data_id = r.id
    <where>
      <if test="qualityCheckTaskId != null">
        i.quality_check_task_id = #{qualityCheckTaskId,jdbcType=BIGINT}
      </if>
      <if test="rawDataIds != null">
        AND d.raw_data_id IN
        <foreach collection="rawDataIds" item="rawDataId" open="(" separator="," close=")">
          #{rawDataId}
        </foreach>
      </if>
    </where>
    ORDER BY
    i.id ASC
  </select>
  <select id="listQualityCheckItemByTaskIdAndSessionIdList" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckItemPO">
    select
    i.id as id,
    i.labeling_data_id as labelingDataId,
    r.raw_data_content as rawDataContent,
    r.raw_data_headers as rawDataHeaders,
    r.session_id as sessionId,
    r.session_time as sessionTime,
    r.message_id as messageId,
    case
      when i.modified_raw_data_mapped_content is not null then i.modified_raw_data_mapped_content
      when d.modified_raw_data_mapped_content is not null then d.modified_raw_data_mapped_content
      else r.raw_data_mapped_content
    end as rawDataMappedContent,
    i.quality_check_result as qualityCheckResult,
    i.quality_check_items_result as qualityCheckRemark,
    i.quality_check_mis as qualityChecker,
    i.update_time as qualityCheckTime,
    i.status as qualityCheckStatus,
    d.labeler_mis as labelerMis,
    d.labeler_name as labelerName,
    d.view_status as viewStatus,
    d.labeling_items_result as labelingItems,
    d.raw_data_id as rawDataId,
    d.update_time as labelTime,
    i.modified_labeling_items_result as modifiedLabelingItems
    from
    labeling_quality_check_item i
    left join labeling_detail d on i.labeling_data_id = d.id
    left join labeling_task_raw_data r on d.raw_data_id = r.id
    <where>
      <if test="qualityCheckTaskId != null">
        i.quality_check_task_id = #{qualityCheckTaskId,jdbcType=BIGINT}
      </if>
      <if test="sessionIdList != null">
        AND d.session_id IN
        <foreach collection="sessionIdList" item="sessionId" open="(" separator="," close=")">
          #{sessionId}
        </foreach>
      </if>
    </where>
    ORDER BY
    i.id ASC
  </select>
  <select id="listByQualityCheckTaskId" resultType="com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO">
    SELECT
    label.quality_check_task_id AS id,
    COUNT(DISTINCT detail.raw_data_id) AS sampleCount,
    COUNT(DISTINCT CASE WHEN label.status = 1 THEN detail.raw_data_id ELSE NULL END) AS unFinishedCount,
    COUNT(DISTINCT detail.session_id) AS sampleSessionCount,
    COUNT(DISTINCT CASE WHEN label.status = 1 THEN detail.session_id ELSE NULL END) AS unFinishedSessionCount
    FROM
    labeling_quality_check_item label LEFT JOIN labeling_detail detail ON label.labeling_data_id = detail.id
    <where>
      label.quality_check_task_id IN
      <foreach collection="qualityCheckTaskIds" item="qualityCheckTaskId" open="(" separator="," close=")">
        #{qualityCheckTaskId}
      </foreach>
    </where>
    GROUP BY
    label.quality_check_task_id
  </select>
  <select id="countByCheckTaskIdAndStatus" resultType="java.lang.Integer">
    SELECT COUNT(id)
    FROM labeling_quality_check_item
    <where>
      <if test="qualityCheckTaskId != null">
        quality_check_task_id = #{qualityCheckTaskId}
      </if>
      <if test="qualityCheckDataStatus != null">
        AND status = #{qualityCheckDataStatus}
      </if>
    </where>
  </select>
  <select id="countByTaskIdAndLabelerMis" resultType="com.meituan.aigc.aida.labeling.pojo.dto.PageQueryDTO">
    SELECT
      COUNT(quality.id) AS allDataTotalCount,
      SUM(CASE WHEN quality.status = 1 THEN 1 ELSE 0 END) AS noQualityCheckedCount,
      SUM(CASE WHEN quality.status = 2 THEN 1 ELSE 0 END) AS qualityCheckedCount
    FROM
      labeling_quality_check_item quality LEFT JOIN labeling_detail labeling ON quality.labeling_data_id = labeling.id
    WHERE
      quality.quality_check_task_id = #{checkTaskId}
      <if test="labelerMis != null">
        AND labeling.labeler_mis = #{labelerMis}
      </if>
  </select>
  <select id="countCheckDetailByCheckTaskIds" resultType="com.meituan.aigc.aida.labeling.pojo.vo.LabelingDataCheckResultVO">
    SELECT
      COUNT(quality.id) AS sampleCount,
      COALESCE(SUM(quality.quality_check_result = 2), 0) AS errorCount,
      COALESCE(SUM(quality.status = 2), 0) AS finishCheckCount,
      quality.quality_check_task_id AS qualityCheckTaskId,
      COALESCE(SUM(labeling.status = 1), 0) AS waitingLabelCount,
      labeling.labeler_mis AS labelerMis,
      labeling.labeler_name AS labelerName
    FROM
      labeling_quality_check_item AS quality
    LEFT JOIN
      labeling_detail AS labeling ON quality.labeling_data_id = labeling.id
    WHERE
      quality.quality_check_task_id IN
    <foreach collection="qualityCheckTaskIds" item="qualityCheckTaskId" open="(" separator="," close=")">
        #{qualityCheckTaskId}
    </foreach>
    GROUP BY
      labeling.labeler_mis,
      labeling.labeler_name,
      quality.quality_check_task_id;
  </select>
  <select id="countRawDataNumByCheckTaskIdsAndStatus" resultType="java.lang.Integer">
    SELECT
      COUNT(DISTINCT labeling.raw_data_id)
    FROM
      labeling_quality_check_item AS quality
    LEFT JOIN
      labeling_detail AS labeling ON quality.labeling_data_id = labeling.id
    <where>
      <if test="qualityCheckTaskIds != null">
        quality.quality_check_task_id IN
        <foreach collection="qualityCheckTaskIds" item="qualityCheckTaskId" open="(" separator="," close=")">
          #{qualityCheckTaskId}
        </foreach>
      </if>
      <if test="checkStatus != null">
        AND quality.status = #{checkStatus}
      </if>
    </where>
  </select>
  <select id="countCheckTaskNumByQualityCheckItemIds" resultType="java.lang.Integer">
    SELECT
      COUNT(DISTINCT quality_check_task_id)
    FROM
      labeling_quality_check_item
    WHERE
      id IN
      <foreach collection="checkItemIds" item="checkItemId" open="(" separator="," close=")">
        #{checkItemId}
      </foreach>
  </select>
  <select id="listRawDataIdsByTaskAndRawDataIdAndStatus" resultType="java.lang.Long">
    SELECT
     DISTINCT labeling.raw_data_id
    FROM
      labeling_quality_check_item AS quality
    LEFT JOIN labeling_detail AS labeling ON quality.labeling_data_id = labeling.id
    <where>
      <if test="qualityCheckTaskId != null">
        quality.quality_check_task_id = #{qualityCheckTaskId}
      </if>
      <if test="checkAbleStatus != null">
        AND quality.checkable_status = #{checkAbleStatus}
      </if>
      <if test="qualityStatus != null">
        AND quality.status = #{qualityStatus}
      </if>
      <if test="rawDataId != null">
        AND labeling.raw_data_id = #{rawDataId}
      </if>
    </where>
    ORDER BY labeling.raw_data_id
  </select>
  <select id="listSessionIdByTaskAndSessionIdAndStatus" resultType="java.lang.String">
    SELECT
    DISTINCT session_id
    FROM
    labeling_quality_check_item
    <where>
      <if test="qualityCheckTaskId != null">
        quality_check_task_id = #{qualityCheckTaskId}
      </if>
      <if test="checkAbleStatus != null">
        AND checkable_status = #{checkAbleStatus}
      </if>
      <if test="qualityStatus != null">
        AND status = #{qualityStatus}
      </if>
      <if test="sessionId != null and sessionId != ''">
        AND session_id = #{sessionId}
      </if>
    </where>
    ORDER BY session_id
  </select>
  <select id="countRawDataIdsByTaskAndStatus" resultType="java.lang.Integer">
    SELECT
     COUNT(DISTINCT labeling.raw_data_id)
    FROM
    labeling_quality_check_item AS quality
    LEFT JOIN labeling_detail AS labeling ON quality.labeling_data_id = labeling.id
    <where>
      <if test="qualityCheckTaskId != null">
        quality.quality_check_task_id = #{qualityCheckTaskId}
      </if>
      <if test="checkAbleStatus != null">
        AND quality.checkable_status = #{checkAbleStatus}
      </if>
      <if test="qualityStatus != null">
        AND quality.status = #{qualityStatus}
      </if>
    </where>
  </select>
  <update id="batchUpdateByPrimaryKeySelective" parameterType="java.util.List">
    <foreach collection="labelingQualityCheckItemList" item="item" separator=";">
      UPDATE labeling_quality_check_item
      <set>
        <if test="item.labelingDataId != null">
          labeling_data_id = #{item.labelingDataId},
        </if>
        <if test="item.qualityCheckTaskId != null">
          quality_check_task_id = #{item.qualityCheckTaskId},
        </if>
        <if test="item.qualityCheckMis != null">
          quality_check_mis = #{item.qualityCheckMis},
        </if>
        <if test="item.qualityCheckName != null">
          quality_check_name = #{item.qualityCheckName},
        </if>
        <if test="item.qualityCheckResult != null">
          quality_check_result = #{item.qualityCheckResult},
        </if>
        <if test="item.qualityCheckItemsResult != null">
          quality_check_items_result = #{item.qualityCheckItemsResult},
        </if>
        <if test="item.status != null">
          status = #{item.status},
        </if>
        <if test="item.checkableStatus != null">
          checkable_status = #{item.checkableStatus},
        </if>
        <if test="item.modifiedLabelingItemsResult != null">
          modified_labeling_items_result = #{item.modifiedLabelingItemsResult},
        </if>
        <if test="item.modifiedRawDataMappedContent != null">
          modified_raw_data_mapped_content = #{item.modifiedRawDataMappedContent},
        </if>
        update_time = CURRENT_TIMESTAMP
      </set>
      WHERE id = #{item.id}
    </foreach>
  </update>
  <select id="countQualityCheckModifiedDataByTaskId" resultType="com.meituan.aigc.aida.labeling.pojo.vo.QualityCheckModifiedDataCountVO">
    SELECT
      COALESCE(SUM(modified_raw_data_mapped_content IS NOT NULL), 0) AS modifiedRawDataMappedContentNum,
      COALESCE(SUM(modified_labeling_items_result IS NOT NULL), 0) AS modifiedLabelingItemsNum
    FROM
      labeling_quality_check_item
    WHERE
      quality_check_task_id IN (
        SELECT
          id
        FROM
          labeling_quality_check_task
        WHERE
          task_id = (
            SELECT
              task_id
            FROM
              labeling_sub_task
            WHERE
              id = #{subTaskId}
          )
      )
  </select>
  <select id="countSessionBySubTaskIdAndStatus" resultType="java.lang.Integer">
    SELECT count(distinct session_id)
    FROM labeling_quality_check_item
    <where>
      <if test="subTaskId != null">
        quality_check_task_id = #{subTaskId}
      </if>
      <if test="status != null">
        AND status = #{status}
      </if>
    </where>
  </select>
  <select id="countQueryBySubTaskIdAndStatus" resultType="java.lang.Integer">
    SELECT count(distinct raw_data_id)
    FROM labeling_quality_check_item
    <where>
      <if test="subTaskId != null">
        quality_check_task_id = #{subTaskId}
      </if>
      <if test="status != null">
        AND status = #{status}
      </if>
    </where>
  </select>
  <select id="statisticsPersonQualityCheckData" resultType="com.meituan.aigc.aida.labeling.job.dashboard.po.PersonQualityCheckStatisticsPO">
    SELECT
      item.quality_check_mis AS qualityCheckerMis,
      item.quality_check_name AS qualityCheckerName,
      COALESCE(SUM(item.status = 2) ,0) AS qualityCheckCount,
      COALESCE(SUM(item.status = 2 AND item.quality_check_result = 1), 0) AS qualityCheckCorrectCount,
      task.biz_type AS bizType,
      task.data_type AS dataType,
      task.task_type AS taskType
    FROM labeling_quality_check_item AS item
    LEFT JOIN labeling_quality_check_task AS checkTask ON checkTask.id = item.quality_check_task_id
    LEFT JOIN labeling_task AS task ON checkTask.task_id = task.id
    WHERE
      item.quality_check_mis IS NOT NULL
      AND item.first_check_time &gt;= #{startTime}
      AND item.first_check_time &lt;= #{endTime}
    GROUP BY item.quality_check_mis, item.quality_check_name, task.biz_type, task.data_type, task.task_type
  </select>

</mapper>