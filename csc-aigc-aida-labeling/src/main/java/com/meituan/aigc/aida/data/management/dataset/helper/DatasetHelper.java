package com.meituan.aigc.aida.data.management.dataset.helper;

import com.dianping.lion.client.Lion;
import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.inf.xmdlog.ConfigUtil;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class DatasetHelper {

    private static final int MAX_BATCH_SIZE = 100;

    private static final String DEFAULT_DATA_SOURCE = "default";

    /**
     * 获取数据集批次大小
     *
     * @param dataSource 数据来源
     * @return 批次大小
     */
    public int getDatasetBatchSize(String dataSource) {
        Map<String, Integer> batchSizeMap = Lion.getMap(ConfigUtil.getAppkey(), LionConstant.DATA_FETCH_TASK_BATCH_SIZE, Integer.class);
        if (MapUtils.isEmpty(batchSizeMap)) {
            return MAX_BATCH_SIZE;
        }
        if (StringUtils.isBlank(dataSource) || !batchSizeMap.containsKey(dataSource)) {
            dataSource = DEFAULT_DATA_SOURCE;
        }
        return batchSizeMap.get(dataSource);
    }
}
