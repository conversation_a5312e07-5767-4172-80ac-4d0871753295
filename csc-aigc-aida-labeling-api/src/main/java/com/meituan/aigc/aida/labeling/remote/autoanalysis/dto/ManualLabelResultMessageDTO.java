package com.meituan.aigc.aida.labeling.remote.autoanalysis.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人工标注结果消息DTO
 * 
 * <AUTHOR>
 * @date 2025/05/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ManualLabelResultMessageDTO implements Serializable {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 会话时间(yyyy-MM-dd HH:mm:ss 或者 yyyy/MM/dd HH:mm:ss)
     */
    private Long sessionTime;
    
    /**
     * 自定义标注项标注结果
     */
    private String labelingItemsResult;
}