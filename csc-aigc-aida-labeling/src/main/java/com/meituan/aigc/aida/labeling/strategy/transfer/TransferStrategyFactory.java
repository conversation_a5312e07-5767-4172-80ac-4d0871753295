package com.meituan.aigc.aida.labeling.strategy.transfer;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class TransferStrategyFactory {

    @Autowired
    private List<TransferStrategy> transferStrategyList;

    private Map<Integer, TransferStrategy> transferStrategyMap;

    @PostConstruct
    public void init() {
        if (MapUtils.isEmpty(transferStrategyMap)) {
            transferStrategyMap = transferStrategyList.stream().collect(Collectors.toMap(TransferStrategy::getStrategyCode, Function.identity()));
        }
    }

    /**
     * 获取转交策略
     *
     * @param strategyCode 策略code
     * @return 转交策略
     */
    public TransferStrategy getTransferStrategy(Integer strategyCode) {
        return transferStrategyMap.get(strategyCode);
    }
}
