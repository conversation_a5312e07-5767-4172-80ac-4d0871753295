package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.meituan.aigc.aida.labeling.dao.mapper.LabelingQualityCheckItemMapper;
import com.meituan.aigc.aida.labeling.dao.mapper.LabelingQualityCheckTaskMapper;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItemExample;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckSampleCountPO;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingQualityCheckTaskRepository;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckParam;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:07
 * @Version: 1.0
 */
@Repository
public class LabelingQualityCheckTaskRepositoryImpl implements LabelingQualityCheckTaskRepository {

    @Resource
    private LabelingQualityCheckTaskMapper labelingQualityCheckTaskMapper;

    @Resource
    private LabelingQualityCheckItemMapper labelingQualityCheckItemMapper;

    @Override
    public int insert(LabelingQualityCheckTask labelingQualityCheckTask) {
        return labelingQualityCheckTaskMapper.insert(labelingQualityCheckTask);
    }

    @Override
    public int batchInsert(List<LabelingQualityCheckTask> qualityCheckTasks) {
        return labelingQualityCheckTaskMapper.batchInsert(qualityCheckTasks);
    }

    @Override
    public List<LabelingQualityCheckTask> listByTaskId(Long taskId) {
        return labelingQualityCheckTaskMapper.listByTaskId(taskId);
    }

    @Override
    public void updateStatusByTaskId(Long taskId, int status) {
        LabelingQualityCheckTask record = new LabelingQualityCheckTask();
        record.setTaskId(taskId);
        record.setStatus(status);
        labelingQualityCheckTaskMapper.updateByTaskId(record);
    }

    @Override
    public List<QualityCheckTaskPO> listByTaskIdAndNameAndQualityCheckerMis(QualityCheckParam param) {
        return labelingQualityCheckTaskMapper.listByTaskIdAndNameAndQualityCheckerMis(param);
    }

    @Override
    public void updateStatusById(Long id, Integer status) {
        LabelingQualityCheckTask record = new LabelingQualityCheckTask();
        record.setId(id);
        record.setStatus(status);
        record.setUpdateTime(new Date());
        labelingQualityCheckTaskMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public LabelingQualityCheckTask getById(Long qualityCheckTaskId) {
        if (Objects.isNull(qualityCheckTaskId)) {
            return null;
        }
        return labelingQualityCheckTaskMapper.selectByPrimaryKey(qualityCheckTaskId);
    }

    @Override
    public Integer countByTaskIdAndStatus(Long taskId, List<Integer> statusList) {
        return labelingQualityCheckTaskMapper.countByTaskIdAndStatus(taskId, statusList);
    }

    @Override
    public void updateById(LabelingQualityCheckTask labelingQualityCheckTask) {
        labelingQualityCheckTaskMapper.updateByPrimaryKeySelective(labelingQualityCheckTask);
    }

    @Override
    public void batchUpdateSelective(List<LabelingQualityCheckTask> labelingQualityCheckTasks) {
        for (LabelingQualityCheckTask labelingQualityCheckTask : labelingQualityCheckTasks) {
            labelingQualityCheckTaskMapper.updateByPrimaryKeySelective(labelingQualityCheckTask);
        }
    }

    @Override
    public QualityCheckSampleCountPO countSampleSizeByTaskId(Long taskId) {
        return labelingQualityCheckTaskMapper.countSampleSizeByTaskId(taskId);
    }
}
