package com.meituan.aigc.aida.labeling.remote.caseanalysis.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-26 10:48
 * @description 数据集创建结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetCreateResponseDTO implements Serializable {
    /**
     * 数据集id
     */
    private Long dataSetId;
    /**
     * 数据传输锁
     */
    private String lockId;
}
