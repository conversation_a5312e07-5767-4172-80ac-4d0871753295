package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/3/28 17:33
 * @Version: 1.0
 */
@Data
public class LabelingQualityCheckItemVO {

    /**
     * 原数据ID
     */
    private Long rowDataId;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 会话时间
     */
    private String sessionTime;
    /**
     * 质检详情列表
     */
    private List<QualityCheckedItemVO> qualityCheckedItemList;
    /**
     * 是否已查看，如果已经查看过，则不再调用查看功能
     */
    private Boolean viewed;

    /**
     * query维度的线上会话标注和其他场景不一样，是二维数组，需要根据选择的不同query来切换展示项
     */
    private List<QueryQualityCheckedItem> queryQualityCheckedItemList;

    @Data
    public static class QueryQualityCheckedItem implements Serializable {
        /**
         * 消息ID
         */
        private String messageId;
        /**
         * 原数据ID
         */
        private Long rowDataId;
        /**
         * 质检详情列表
         */
        private List<QualityCheckedItemVO> qualityCheckedItemList;
    }

}
