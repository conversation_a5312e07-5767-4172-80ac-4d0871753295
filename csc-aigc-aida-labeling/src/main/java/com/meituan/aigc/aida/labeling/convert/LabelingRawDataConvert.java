package com.meituan.aigc.aida.labeling.convert;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelingTaskRawDataDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-03-10 17:10
 * @description
 */
@Slf4j
@Data
public class LabelingRawDataConvert {

    public static LabelingTaskRawDataDTO convertRawDataDTO(LabelingTaskRawData rawData) {
        if (Objects.isNull(rawData)) {
            return null;
        }
        LabelingTaskRawDataDTO rawDataDTO = new LabelingTaskRawDataDTO();
        rawDataDTO.setDataId(rawData.getId());
        rawDataDTO.setTaskId(rawData.getTaskId());
        rawDataDTO.setSessionId(rawData.getSessionId());
        rawDataDTO.setMessageId(rawData.getMessageId());
        rawDataDTO.setSessionTime(rawData.getSessionTime());
        rawDataDTO.setRawDataContent(rawData.getRawDataContent());
        rawDataDTO.setRawDataMappedContent(rawData.getRawDataMappedContent());
        return rawDataDTO;
    }
}
