package com.meituan.aigc.aida.data.management.fetch.config;

import com.dianping.csc.aircraft.api.service.inner.IFaceRemoteService;
import com.dianping.csc.analtics.ctx.service.TypicalQuestionStatisticsService;
import com.dianping.csc.haitun.kefu.service.StaffFacadeService;
import com.dianping.haitun.cases.service.ManualDialogInfoService;
import com.meituan.csc.aigc.runtime.api.AidaConfigService;
import com.meituan.csc.aigc.runtime.api.AidaGptService;
import com.meituan.csc.aigc.runtime.inner.api.AidaConfigInnerRemoteService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.mdp.csc.pacific.context.api.remote.ContextRemoteService;
import com.sankuai.pacific.cscpacific.contact.chat.api.remote.PopupRemoteService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DataFetchRpcConfig {

    /**
     * AI搭配置服务
     */
    @MdpPigeonClient(timeout = 5000L, remoteAppKey = "com.sankuai.csccratos.prompt.runtime")
    private AidaConfigService aidaConfigService;

    @MdpPigeonClient(timeout = 50000, remoteAppKey = "com.sankuai.csccratos.prompt.runtime")
    private AidaGptService aidaGptPigeonService;

    @MdpPigeonClient(timeout = 5000)
    private ContextRemoteService contextRemoteService;

    @MdpPigeonClient(timeout = 5000)
    private TypicalQuestionStatisticsService typicalQuestionStatisticsService;

    @MdpPigeonClient(timeout = 5000)
    private StaffFacadeService staffFacadeService;

    @MdpPigeonClient(timeout = 5000)
    private PopupRemoteService popupRemoteService;

    @MdpPigeonClient(timeout = 5000)
    private IFaceRemoteService faceRemoteService;

    @MdpPigeonClient(timeout = 5000)
    private ManualDialogInfoService manualDialogInfoService;

    @MdpPigeonClient(timeout = 5000, remoteAppKey = "com.sankuai.csccratos.prompt.runtime")
    private AidaConfigInnerRemoteService aidaConfigInnerRemoteService;

}
