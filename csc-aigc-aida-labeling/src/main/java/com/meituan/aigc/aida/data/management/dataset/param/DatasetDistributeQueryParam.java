package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 数据集分布情况查询参数
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集分析 - 分布查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetDistributeQueryParam implements Serializable {
    
    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long datasetId;
    
    /**
     * 数据集版本ID
     */
    @NotNull(message = "数据集版本ID不能为空")
    private Long datasetVersionId;
    
    /**
     * 选择的列名
     */
    @NotEmpty(message = "列名不能为空")
    private String columnName;
} 