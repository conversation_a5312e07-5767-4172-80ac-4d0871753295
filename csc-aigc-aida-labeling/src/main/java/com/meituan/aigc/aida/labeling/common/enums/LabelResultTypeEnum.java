package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-04-23 10:59
 * @description 标注结果类型枚举
 */
@Getter
public enum LabelResultTypeEnum {

    INNER(1, "内部标注项"),
    EXTERNAL(2, "外部标注项");

    private int code;
    private String description;

    LabelResultTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    public static LabelResultTypeEnum getByCode(int code) {
        for (LabelResultTypeEnum type : LabelResultTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

}
