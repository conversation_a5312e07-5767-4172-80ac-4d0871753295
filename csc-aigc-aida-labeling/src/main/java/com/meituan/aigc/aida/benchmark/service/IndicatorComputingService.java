package com.meituan.aigc.aida.benchmark.service;

import java.util.List;
import java.util.Map;

/**
 * Benchmark指标计算服务
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
public interface IndicatorComputingService {

    /**
     * 计算模型指标
     *
     * @param versionId     版本ID
     * @param modelName     模型名称
     * @param taskIds       标注任务ID列表
     * @param subtaskList   子任务映射
     * @param examList      考点列表
     * @param indicatorList 指标列表
     */
    void computingModelIndicator(Long versionId, String modelName, List<Long> taskIds, 
                                Map<Long, List<Long>> subtaskList, List<String> examList, 
                                List<String> indicatorList);
}
