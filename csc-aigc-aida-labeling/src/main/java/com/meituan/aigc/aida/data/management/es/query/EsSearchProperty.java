package com.meituan.aigc.aida.data.management.es.query;

import com.meituan.aigc.aida.common.enums.EsPropertyTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * ES搜索属性
 * 封装字段名和字段类型信息
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EsSearchProperty {

    /**
     * 字段名
     */
    @NotNull
    private String fieldName;

    /**
     * 字段类型
     */
    private EsPropertyTypeEnum fieldType;

    /**
     * 字段权重（用于查询时的boost）
     */
    @Builder.Default
    private Float boost = 1.0f;

    /**
     * 是否为嵌套字段
     */
    @Builder.Default
    private Boolean nested = false;

    /**
     * 嵌套路径（如果是嵌套字段）
     * 即叶子路径上nested的路径
     */
    private String nestedPath;

    /**
     * 字段路径
     */
    @NotNull
    private String path;

    /**
     * 字段描述
     */
    private String description;

    /**
     * 创建搜索属性（仅指定字段名）
     *
     * @param fieldName 字段名
     * @param path      字段路径
     * @return EsSearchProperty
     */
    public static EsSearchProperty of(String fieldName, String path) {
        return EsSearchProperty.builder()
                .fieldName(fieldName)
                .path(path)
                .build();
    }

    /**
     * 创建搜索属性（指定字段名和类型）
     *
     * @param fieldName 字段名
     * @param fieldType 字段类型
     * @return EsSearchProperty
     */
    public static EsSearchProperty of(String fieldName, EsPropertyTypeEnum fieldType) {
        return EsSearchProperty.builder()
                .fieldName(fieldName)
                .fieldType(fieldType)
                .build();
    }

    /**
     * 创建带权重的搜索属性
     *
     * @param fieldName 字段名
     * @param fieldType 字段类型
     * @param boost     权重
     * @return EsSearchProperty
     */
    public static EsSearchProperty of(String fieldName, EsPropertyTypeEnum fieldType, Float boost) {
        return EsSearchProperty.builder()
                .fieldName(fieldName)
                .fieldType(fieldType)
                .boost(boost)
                .build();
    }

    /**
     * 创建嵌套字段搜索属性
     *
     * @param fieldName  字段名
     * @param nestedPath 嵌套路径
     * @param path       完整字段路径
     * @return EsSearchProperty
     */
    public static EsSearchProperty nested(String fieldName, String nestedPath, String path) {
        return EsSearchProperty.builder()
                .fieldName(fieldName)
                .fieldType(EsPropertyTypeEnum.NESTED)
                .nested(true)
                .nestedPath(nestedPath)
                .path(path)
                .build();
    }

    /**
     * 创建嵌套字段搜索属性（带字段类型）
     *
     * @param fieldName  字段名
     * @param nestedPath 嵌套路径
     * @param fieldType  字段类型
     * @return EsSearchProperty
     */
    public static EsSearchProperty nested(String fieldName, String nestedPath, EsPropertyTypeEnum fieldType) {
        return EsSearchProperty.builder()
                .fieldName(fieldName)
                .fieldType(fieldType)
                .nested(true)
                .nestedPath(nestedPath)
                .build();
    }

    /**
     * 获取完整的字段路径
     * 如果是嵌套字段，返回 nestedPath.fieldName
     * 否则返回 fieldName
     *
     * @return 完整字段路径
     */
    public String getFullFieldPath() {
        if (nested && nestedPath != null) {
            return nestedPath + "." + fieldName;
        }
        return fieldName;
    }

    /**
     * 判断是否支持精确匹配查询
     *
     * @return true if supports term query
     */
    public boolean supportsTermQuery() {
        return fieldType != null && fieldType.supportsTermQuery();
    }

    /**
     * 判断是否支持全文搜索查询
     *
     * @return true if supports match query
     */
    public boolean supportsMatchQuery() {
        return fieldType != null && fieldType.supportsMatchQuery();
    }

    /**
     * 判断是否支持范围查询
     *
     * @return true if supports range query
     */
    public boolean supportsRangeQuery() {
        return fieldType != null && fieldType.supportsRangeQuery();
    }

    /**
     * 判断是否支持聚合操作
     *
     * @return true if supports aggregation
     */
    public boolean supportsAggregation() {
        return fieldType != null && fieldType.supportsAggregation();
    }

    @Override
    public String toString() {
        return getFullFieldPath();
    }
}
