package com.meituan.aigc.aida.data.management.dataset.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;

/**
 * 文件解析工具类
 * 支持解析JSONL和Excel格式的文件
 */
@Slf4j
public class FileParserUtil {

    private static final String JSONL_SUFFIX = ".jsonl";
    private static final String EXCEL_SUFFIX = ".xlsx";
    private static final String XLS_SUFFIX = ".xls";

    /**
     * 解析文件的表头信息
     *
     * @param file 上传的文件
     * @return 表头字段列表
     */
    public static List<String> parseHeaders(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String extension = FilenameUtils.getExtension(originalFilename).toLowerCase();
        try {
            if (JSONL_SUFFIX.substring(1).equals(extension)) {
                return parseJsonlHeaders(file);
            } else if (EXCEL_SUFFIX.substring(1).equals(extension) || XLS_SUFFIX.substring(1).equals(extension)) {
                return parseExcelHeaders(file);
            } else {
                throw new AidaTrainingCheckException("不支持的文件格式，仅支持JSONL和Excel文件");
            }
        } catch (IOException e) {
            log.error("解析文件表头失败: {}", e.getMessage(), e);
            throw new AidaTrainingCheckException("解析文件表头失败");
        }
    }

    /**
     * 解析JSONL文件的表头
     *
     * @param file MultipartFile文件
     * @return 表头字段列表
     * @throws IOException IO异常
     */
    private static List<String> parseJsonlHeaders(MultipartFile file) throws IOException {
        Set<String> headers = new HashSet<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            String line;
            int lineCount = 0;
            // 最多解析前100行来获取表头
            int maxLinesToParse = 100;

            while ((line = reader.readLine()) != null && lineCount < maxLinesToParse) {
                lineCount++;
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                try {
                    JSONObject jsonObject = JSON.parseObject(line);
                    // 收集所有JSON对象的键作为表头
                    headers.addAll(jsonObject.keySet());
                } catch (Exception e) {
                    log.warn("解析JSONL文件第{}行时出错: {}", lineCount, e.getMessage());
                }
            }
        }

        return new ArrayList<>(headers);
    }

    /**
     * 解析Excel文件的表头
     *
     * @param file MultipartFile文件
     * @return 表头字段列表
     * @throws IOException IO异常
     */
    private static List<String> parseExcelHeaders(MultipartFile file) throws IOException {
        List<String> headers = new ArrayList<>();

        try (InputStream is = file.getInputStream();
             Workbook workbook = file.getOriginalFilename().toLowerCase().endsWith(EXCEL_SUFFIX.substring(1)) ?
                     new XSSFWorkbook(is) : WorkbookFactory.create(is)) {

            // 使用第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 获取Excel表头行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel文件没有表头行");
            }

            // 解析表头行中的每个单元格
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                String header = getCellValue(cell);
                if (StringUtils.isNotBlank(header)) {
                    headers.add(header);
                }
            }
        }

        return headers;
    }

    /**
     * 解析MultipartFile文件，使用提供的表头信息
     *
     * @param file     上传的文件
     * @param headList 表头信息
     * @return 解析的数据列表
     */
    public static List<DataSetRecordParam> parseFileWithHeaders(MultipartFile file, List<DataFieldParam> headList) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        if (headList == null || headList.isEmpty()) {
            throw new IllegalArgumentException("表头信息不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String extension = FilenameUtils.getExtension(originalFilename).toLowerCase();
        try {
            if (JSONL_SUFFIX.substring(1).equals(extension)) {
                return parseJsonlFileWithHeaders(file, headList);
            } else if (EXCEL_SUFFIX.substring(1).equals(extension) || XLS_SUFFIX.substring(1).equals(extension)) {
                return parseExcelFileWithHeaders(file, headList);
            } else {
                throw new AidaTrainingCheckException("不支持的文件格式，仅支持JSONL和Excel文件");
            }
        } catch (IOException e) {
            log.error("解析文件失败: {}", e.getMessage(), e);
            throw new AidaTrainingCheckException("解析文件失败");
        }
    }

    /**
     * 解析JSONL文件，使用提供的表头信息
     *
     * @param file     MultipartFile文件
     * @param headList 表头信息
     * @return 解析的数据列表
     * @throws IOException IO异常
     */
    private static List<DataSetRecordParam> parseJsonlFileWithHeaders(MultipartFile file, List<DataFieldParam> headList) throws IOException {
        List<DataSetRecordParam> dataList = new ArrayList<>();
        // 构建表头列名到字段类型的映射
        Map<String, Integer> headerTypeMap = new HashMap<>();
        for (DataFieldParam header : headList) {
            headerTypeMap.put(header.getColumnName(), header.getFieldType());
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            String line;
            int lineNum = 0;

            // 读取JSONL文件，每行一个JSON对象
            while ((line = reader.readLine()) != null) {
                lineNum++;
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                try {
                    JSONObject jsonObject = JSON.parseObject(line);
                    Map<String, Object> content = new HashMap<>();

                    // 只处理已定义的表头字段
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String key = entry.getKey();
                        if (headerTypeMap.containsKey(key)) {
                            content.put(key, entry.getValue());
                        }
                    }

                    // 创建数据记录
                    DataSetRecordParam record = new DataSetRecordParam();
                    record.setDataId(UUID.randomUUID().toString());
                    record.setContent(content);
                    dataList.add(record);
                } catch (Exception e) {
                    log.error("解析JSONL文件第{}行时出错: {}", lineNum, e.getMessage(), e);
                }
            }
        }

        return dataList;
    }

    /**
     * 解析Excel文件，使用提供的表头信息
     *
     * @param file     MultipartFile文件
     * @param headList 表头信息
     * @return 解析的数据列表
     * @throws IOException IO异常
     */
    private static List<DataSetRecordParam> parseExcelFileWithHeaders(MultipartFile file, List<DataFieldParam> headList) throws IOException {
        List<DataSetRecordParam> dataList = new ArrayList<>();
        // 构建表头列名到字段类型的映射
        Map<String, Integer> headerTypeMap = new HashMap<>();
        for (DataFieldParam header : headList) {
            headerTypeMap.put(header.getColumnName(), header.getFieldType());
        }

        try (InputStream is = file.getInputStream();
             Workbook workbook = file.getOriginalFilename().toLowerCase().endsWith(EXCEL_SUFFIX.substring(1)) ?
                     new XSSFWorkbook(is) : WorkbookFactory.create(is)) {

            // 使用第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 获取Excel表头行（仅用于匹配位置）
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel文件没有表头行");
            }

            // 创建列名和索引的映射
            Map<Integer, String> colIndexToName = new HashMap<>();
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                String excelColumnName = getCellValue(cell);
                if (StringUtils.isNotBlank(excelColumnName) && headerTypeMap.containsKey(excelColumnName)) {
                    colIndexToName.put(i, excelColumnName);
                }
            }

            // 解析数据行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Map<String, Object> content = new HashMap<>();
                boolean hasData = false;

                // 遍历每个单元格
                for (int j = 0; j < headerRow.getLastCellNum(); j++) {
                    if (!colIndexToName.containsKey(j) || j >= row.getLastCellNum()) continue;

                    Cell cell = row.getCell(j);
                    String columnName = colIndexToName.get(j);
                    Object value = getCellValue(cell);

                    if (value != null) {
                        content.put(columnName, value);
                        hasData = true;
                    }
                }

                // 如果行有数据，添加到数据列表
                if (hasData) {
                    DataSetRecordParam record = new DataSetRecordParam();
                    record.setDataId(UUID.randomUUID().toString());
                    record.setContent(content);
                    dataList.add(record);
                }
            }
        }

        return dataList;
    }


    /**
     * 获取单元格的值
     *
     * @param cell 单元格
     * @return 单元格值
     */
    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 避免科学计数法
                    double value = cell.getNumericCellValue();
                    // 使用误差范围比较浮点数，而不是直接使用相等运算符
                    double epsilon = 1E-10;
                    if (Math.abs(value - Math.floor(value)) < epsilon) {
                        return String.valueOf((long) value);
                    } else {
                        return String.valueOf(value);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        return cell.getCellFormula();
                    }
                }
            default:
                return null;
        }
    }
} 