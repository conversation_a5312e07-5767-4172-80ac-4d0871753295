package com.meituan.aigc.aida.benchmark.dao.repository;

import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkLeaderboardDetails;

import java.util.List;

/**
 * Benchmark排行榜详情Repository接口
 *
 * <AUTHOR>
 * @description Benchmark排行榜详情数据访问层接口
 * @date 2025/6/25
 */
public interface LeaderboardDetailsRepository {

    /**
     * 批量插入排行榜详情数据
     *
     * @param details 排行榜详情列表
     * @return 插入成功的记录数
     */
    int batchInsert(List<BenchmarkLeaderboardDetails> details);

    /**
     * 根据版本ID和模型名称查询详情
     *
     * @param versionId 版本ID
     * @param modelName 模型名称
     * @return 排行榜详情列表
     */
    List<BenchmarkLeaderboardDetails> listByVersionIdAndModelName(Long versionId, String modelName);

    /**
     * 根据版本ID查询所有详情
     *
     * @param versionId 版本ID
     * @return 排行榜详情列表
     */
    List<BenchmarkLeaderboardDetails> listByVersionId(Long versionId);

    /**
     * 删除指定版本和模型的记录
     *
     * @param versionId 版本ID
     * @param modelName 模型名称
     * @return 删除的记录数
     */
    int deleteByVersionIdAndModelName(Long versionId, String modelName);

    /**
     * 根据主键删除记录
     *
     * @param id 主键ID
     * @return 删除的记录数
     */
    int deleteById(Long id);

    /**
     * 插入单条记录
     *
     * @param detail 排行榜详情
     * @return 插入成功的记录数
     */
    int insert(BenchmarkLeaderboardDetails detail);
}
