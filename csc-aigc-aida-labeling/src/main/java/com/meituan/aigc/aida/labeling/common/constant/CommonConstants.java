package com.meituan.aigc.aida.labeling.common.constant;

/**
 * 通用常量类，存放系统中的常量定义
 *
 * <AUTHOR> wangyibo23
 * @date : 2025/3/14
 */
public class CommonConstants {

    /**
     * 文件类型常量
     */
    public static final class FileType {
        /**
         * xls文件类型
         */
        public static final String XLS = "xls";

        /**
         * xlsx文件类型
         */
        public static final String XLSX = ".xlsx";

        /**
         * csv文件类型
         */
        public static final String CSV = "csv";

        /**
         * 私有构造方法，防止实例化
         */
        private FileType() {
        }
    }

    /**
     * 数据状态常量
     */
    public static final class DataStatus {
        /**
         * 枚举类型常量
         */
        public static final Integer ENUM_DATA_TYPE = 2;

        /**
         * 背对背一致率标注项
         */
        public static final Integer IS_COMPARE = 1;

        /**
         * 私有构造方法，防止实例化
         */
        private DataStatus() {
        }
    }

    /**
     * 日期格式常量
     */
    public static final class DatePattern {
        /**
         * 标准日期格式：yyyy-MM-dd
         */
        public static final String DATE = "yyyy-MM-dd";

        /**
         * 标准日期时间格式：yyyy-MM-dd HH:mm:ss
         */
        public static final String DATE_TIME = "yyyy-MM-dd HH:mm:ss";
        /**
         * 标准日期时间格式：yyyy/MM/dd HH:mm:ss
         */
        public static final String DATE_TIME2 = "yyyy/MM/dd HH:mm:ss";

        /**
         * 紧凑日期格式：yyyyMMdd
         */
        public static final String COMPACT_DATE = "yyyyMMdd";
        /**
         * 日期正则yyyy-MM-dd HH:mm:ss
         */
        public static final String DATE_TIME_PATTERN = "^(\\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])\\s([01]\\d|2[0-3]):([0-5]\\d):([0-5]\\d)$";
        /**
         * 日期正则yyyy/MM/dd HH:mm:ss
         */
        public static final String DATE_TIME_PATTERN2 = "^(\\d{4})/(0[1-9]|1[0-2])/(0[1-9]|[12]\\d|3[01])\\s([01]\\d|2[0-3]):([0-5]\\d):([0-5]\\d)$";
        /**
         * 标准日期时间格式：MM/dd
         */
        public static final String MONTH_DAY = "MM/dd";

        /**
         * 私有构造方法，防止实例化
         */
        private DatePattern() {
        }
    }

    /**
     * 路径格式常量
     */
    public static final class PathFormat {
        /**
         * 通用路径格式：%s-%s-%s
         */
        public static final String COMMON_PATH = "%s-%s-%s";

        /**
         * 私有构造方法，防止实例化
         */
        private PathFormat() {
        }
    }

    /**
     * 字段名称常量
     */
    public static final class FieldName {
        /**
         * 会话ID字段名
         */
        public static final String SESSION_ID = "sessionId";

        /**
         * 大模型消息ID字段名
         */
        public static final String MESSAGE_ID = "大模型消息ID";

        /**
         * 消息内容字段名
         */
        public static final String MESSAGE_CONTENT = "大模型消息";

        /**
         * 上下文
         */
        public static final String CONTEXT = "上下文";

        /**
         * 信号
         */
        public static final String SIGNAL = "信号";

        /**
         * 时间字段名
         */
        public static final String TIME = "time";

        /**
         * 标注人MIS
         */
        public static final String LABELING_MIS = "标注人MIS";

        /**
         * 标注人姓名
         */
        public static final String LABELING_NAME = "标注人姓名";

        /**
         * 标注时间
         */
        public static final String LABELING_TIME = "标注时间";

        /**
         * 质检结果字段名
         */
        public static final String QUALITY_CHECK_RESULT = "标注质量评估";

        /**
         * 质检反馈字段名
         */
        public static final String QUALITY_CHECK_ITEM_RESULT = "质检反馈";

        /**
         * 质检人MIS
         */
        public static final String QUALITY_CHECK_MIS = "质检人MIS";

        /**
         * 质检人姓名
         */
        public static final String QUALITY_CHECK_NAME = "质检人姓名";

        /**
         * 质检时间
         */
        public static final String QUALITY_CHECK_TIME = "质检时间";

        /**
         * UNKNOWN
         */
        public static final String UNKNOWN = "unknown";

        /**
         * 逗号
         */
        public static final String COMMA = ",";

        /**
         * 私有构造方法，防止实例化
         */
        private FieldName() {
        }
    }

    /**
     * HTTP状态码常量
     */
    public static final class HttpStatus {
        /**
         * 成功状态码
         */
        public static final Integer SUCCESS = 0;

        /**
         * 未授权状态码
         */
        public static final Integer UNAUTHORIZED = 401;

        /**
         * 私有构造方法，防止实例化
         */
        private HttpStatus() {
        }
    }

    /**
     * 分页默认值常量
     */
    public static final class Pagination {
        /**
         * 默认页面大小
         */
        public static final Integer DEFAULT_PAGE_SIZE = 10;

        /**
         * 默认批处理大小
         */
        public static final Integer DEFAULT_BATCH_SIZE = 1000;

        /**
         * 默认批处理大小
         */
        public static final Integer DEFAULT_FILE_ROWS = 10000;

        /**
         * 私有构造方法，防止实例化
         */
        private Pagination() {
        }
    }

    /**
     * 网络配置常量
     */
    public static final class NetworkConfig {
        /**
         * 默认远程服务器端口
         */
        public static final int DEFAULT_REMOTE_SERVER_PORT = 9002;

        /**
         * 默认超时时间（毫秒）
         */
        public static final int DEFAULT_TIMEOUT = 10000;

        /**
         * Redis过期时间（秒）
         */
        public static final int REDIS_EXPIRE_TIME = 60 * 3;

        /**
         * 私有构造方法，防止实例化
         */
        private NetworkConfig() {
        }
    }

    /**
     * 私有构造方法，防止实例化
     */
    private CommonConstants() {
    }
}