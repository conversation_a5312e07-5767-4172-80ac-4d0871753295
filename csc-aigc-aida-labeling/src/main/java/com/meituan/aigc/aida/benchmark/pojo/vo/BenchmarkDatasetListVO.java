package com.meituan.aigc.aida.benchmark.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Benchmark数据集列表VO
 * 
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BenchmarkDatasetListVO {

    /**
     * 总数
     */
    private Long total;

    /**
     * 数据集列表
     */
    private List<BenchmarkDatasetItemVO> datasetList;

    /**
     * 指标信息
     */
    private List<String> indicatorInfo;
} 