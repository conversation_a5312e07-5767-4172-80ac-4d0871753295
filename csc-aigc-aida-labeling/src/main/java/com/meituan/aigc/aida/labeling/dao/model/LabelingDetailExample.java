package com.meituan.aigc.aida.labeling.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabelingDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LabelingDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdIsNull() {
            addCriterion("sub_task_id is null");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdIsNotNull() {
            addCriterion("sub_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdEqualTo(Long value) {
            addCriterion("sub_task_id =", value, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdNotEqualTo(Long value) {
            addCriterion("sub_task_id <>", value, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdGreaterThan(Long value) {
            addCriterion("sub_task_id >", value, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sub_task_id >=", value, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdLessThan(Long value) {
            addCriterion("sub_task_id <", value, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("sub_task_id <=", value, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdIn(List<Long> values) {
            addCriterion("sub_task_id in", values, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdNotIn(List<Long> values) {
            addCriterion("sub_task_id not in", values, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdBetween(Long value1, Long value2) {
            addCriterion("sub_task_id between", value1, value2, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSubTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("sub_task_id not between", value1, value2, "subTaskId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(String value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(String value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(String value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(String value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(String value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(String value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLike(String value) {
            addCriterion("session_id like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLike(String value) {
            addCriterion("session_id not like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<String> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<String> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(String value1, String value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(String value1, String value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionTimeIsNull() {
            addCriterion("session_time is null");
            return (Criteria) this;
        }

        public Criteria andSessionTimeIsNotNull() {
            addCriterion("session_time is not null");
            return (Criteria) this;
        }

        public Criteria andSessionTimeEqualTo(Date value) {
            addCriterion("session_time =", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeNotEqualTo(Date value) {
            addCriterion("session_time <>", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeGreaterThan(Date value) {
            addCriterion("session_time >", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("session_time >=", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeLessThan(Date value) {
            addCriterion("session_time <", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeLessThanOrEqualTo(Date value) {
            addCriterion("session_time <=", value, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeIn(List<Date> values) {
            addCriterion("session_time in", values, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeNotIn(List<Date> values) {
            addCriterion("session_time not in", values, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeBetween(Date value1, Date value2) {
            addCriterion("session_time between", value1, value2, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andSessionTimeNotBetween(Date value1, Date value2) {
            addCriterion("session_time not between", value1, value2, "sessionTime");
            return (Criteria) this;
        }

        public Criteria andRawDataIdIsNull() {
            addCriterion("raw_data_id is null");
            return (Criteria) this;
        }

        public Criteria andRawDataIdIsNotNull() {
            addCriterion("raw_data_id is not null");
            return (Criteria) this;
        }

        public Criteria andRawDataIdEqualTo(Long value) {
            addCriterion("raw_data_id =", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdNotEqualTo(Long value) {
            addCriterion("raw_data_id <>", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdGreaterThan(Long value) {
            addCriterion("raw_data_id >", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdGreaterThanOrEqualTo(Long value) {
            addCriterion("raw_data_id >=", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdLessThan(Long value) {
            addCriterion("raw_data_id <", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdLessThanOrEqualTo(Long value) {
            addCriterion("raw_data_id <=", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdIn(List<Long> values) {
            addCriterion("raw_data_id in", values, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdNotIn(List<Long> values) {
            addCriterion("raw_data_id not in", values, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdBetween(Long value1, Long value2) {
            addCriterion("raw_data_id between", value1, value2, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdNotBetween(Long value1, Long value2) {
            addCriterion("raw_data_id not between", value1, value2, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentIsNull() {
            addCriterion("modified_raw_data_mapped_content is null");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentIsNotNull() {
            addCriterion("modified_raw_data_mapped_content is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentEqualTo(String value) {
            addCriterion("modified_raw_data_mapped_content =", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentNotEqualTo(String value) {
            addCriterion("modified_raw_data_mapped_content <>", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentGreaterThan(String value) {
            addCriterion("modified_raw_data_mapped_content >", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentGreaterThanOrEqualTo(String value) {
            addCriterion("modified_raw_data_mapped_content >=", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentLessThan(String value) {
            addCriterion("modified_raw_data_mapped_content <", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentLessThanOrEqualTo(String value) {
            addCriterion("modified_raw_data_mapped_content <=", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentLike(String value) {
            addCriterion("modified_raw_data_mapped_content like", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentNotLike(String value) {
            addCriterion("modified_raw_data_mapped_content not like", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentIn(List<String> values) {
            addCriterion("modified_raw_data_mapped_content in", values, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentNotIn(List<String> values) {
            addCriterion("modified_raw_data_mapped_content not in", values, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentBetween(String value1, String value2) {
            addCriterion("modified_raw_data_mapped_content between", value1, value2, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentNotBetween(String value1, String value2) {
            addCriterion("modified_raw_data_mapped_content not between", value1, value2, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentIsNull() {
            addCriterion("compar_items_consistent is null");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentIsNotNull() {
            addCriterion("compar_items_consistent is not null");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentEqualTo(Byte value) {
            addCriterion("compar_items_consistent =", value, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentNotEqualTo(Byte value) {
            addCriterion("compar_items_consistent <>", value, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentGreaterThan(Byte value) {
            addCriterion("compar_items_consistent >", value, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentGreaterThanOrEqualTo(Byte value) {
            addCriterion("compar_items_consistent >=", value, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentLessThan(Byte value) {
            addCriterion("compar_items_consistent <", value, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentLessThanOrEqualTo(Byte value) {
            addCriterion("compar_items_consistent <=", value, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentIn(List<Byte> values) {
            addCriterion("compar_items_consistent in", values, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentNotIn(List<Byte> values) {
            addCriterion("compar_items_consistent not in", values, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentBetween(Byte value1, Byte value2) {
            addCriterion("compar_items_consistent between", value1, value2, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andComparItemsConsistentNotBetween(Byte value1, Byte value2) {
            addCriterion("compar_items_consistent not between", value1, value2, "comparItemsConsistent");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultIsNull() {
            addCriterion("labeling_items_result is null");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultIsNotNull() {
            addCriterion("labeling_items_result is not null");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultEqualTo(String value) {
            addCriterion("labeling_items_result =", value, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultNotEqualTo(String value) {
            addCriterion("labeling_items_result <>", value, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultGreaterThan(String value) {
            addCriterion("labeling_items_result >", value, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultGreaterThanOrEqualTo(String value) {
            addCriterion("labeling_items_result >=", value, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultLessThan(String value) {
            addCriterion("labeling_items_result <", value, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultLessThanOrEqualTo(String value) {
            addCriterion("labeling_items_result <=", value, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultLike(String value) {
            addCriterion("labeling_items_result like", value, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultNotLike(String value) {
            addCriterion("labeling_items_result not like", value, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultIn(List<String> values) {
            addCriterion("labeling_items_result in", values, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultNotIn(List<String> values) {
            addCriterion("labeling_items_result not in", values, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultBetween(String value1, String value2) {
            addCriterion("labeling_items_result between", value1, value2, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelingItemsResultNotBetween(String value1, String value2) {
            addCriterion("labeling_items_result not between", value1, value2, "labelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andLabelerMisIsNull() {
            addCriterion("labeler_mis is null");
            return (Criteria) this;
        }

        public Criteria andLabelerMisIsNotNull() {
            addCriterion("labeler_mis is not null");
            return (Criteria) this;
        }

        public Criteria andLabelerMisEqualTo(String value) {
            addCriterion("labeler_mis =", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisNotEqualTo(String value) {
            addCriterion("labeler_mis <>", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisGreaterThan(String value) {
            addCriterion("labeler_mis >", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisGreaterThanOrEqualTo(String value) {
            addCriterion("labeler_mis >=", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisLessThan(String value) {
            addCriterion("labeler_mis <", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisLessThanOrEqualTo(String value) {
            addCriterion("labeler_mis <=", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisLike(String value) {
            addCriterion("labeler_mis like", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisNotLike(String value) {
            addCriterion("labeler_mis not like", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisIn(List<String> values) {
            addCriterion("labeler_mis in", values, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisNotIn(List<String> values) {
            addCriterion("labeler_mis not in", values, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisBetween(String value1, String value2) {
            addCriterion("labeler_mis between", value1, value2, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisNotBetween(String value1, String value2) {
            addCriterion("labeler_mis not between", value1, value2, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerNameIsNull() {
            addCriterion("labeler_name is null");
            return (Criteria) this;
        }

        public Criteria andLabelerNameIsNotNull() {
            addCriterion("labeler_name is not null");
            return (Criteria) this;
        }

        public Criteria andLabelerNameEqualTo(String value) {
            addCriterion("labeler_name =", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameNotEqualTo(String value) {
            addCriterion("labeler_name <>", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameGreaterThan(String value) {
            addCriterion("labeler_name >", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameGreaterThanOrEqualTo(String value) {
            addCriterion("labeler_name >=", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameLessThan(String value) {
            addCriterion("labeler_name <", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameLessThanOrEqualTo(String value) {
            addCriterion("labeler_name <=", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameLike(String value) {
            addCriterion("labeler_name like", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameNotLike(String value) {
            addCriterion("labeler_name not like", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameIn(List<String> values) {
            addCriterion("labeler_name in", values, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameNotIn(List<String> values) {
            addCriterion("labeler_name not in", values, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameBetween(String value1, String value2) {
            addCriterion("labeler_name between", value1, value2, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameNotBetween(String value1, String value2) {
            addCriterion("labeler_name not between", value1, value2, "labelerName");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSampleStatusIsNull() {
            addCriterion("sample_status is null");
            return (Criteria) this;
        }

        public Criteria andSampleStatusIsNotNull() {
            addCriterion("sample_status is not null");
            return (Criteria) this;
        }

        public Criteria andSampleStatusEqualTo(Integer value) {
            addCriterion("sample_status =", value, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusNotEqualTo(Integer value) {
            addCriterion("sample_status <>", value, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusGreaterThan(Integer value) {
            addCriterion("sample_status >", value, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("sample_status >=", value, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusLessThan(Integer value) {
            addCriterion("sample_status <", value, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusLessThanOrEqualTo(Integer value) {
            addCriterion("sample_status <=", value, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusIn(List<Integer> values) {
            addCriterion("sample_status in", values, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusNotIn(List<Integer> values) {
            addCriterion("sample_status not in", values, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusBetween(Integer value1, Integer value2) {
            addCriterion("sample_status between", value1, value2, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andSampleStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("sample_status not between", value1, value2, "sampleStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeIsNull() {
            addCriterion("first_labeling_time is null");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeIsNotNull() {
            addCriterion("first_labeling_time is not null");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeEqualTo(Date value) {
            addCriterion("first_labeling_time =", value, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeNotEqualTo(Date value) {
            addCriterion("first_labeling_time <>", value, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeGreaterThan(Date value) {
            addCriterion("first_labeling_time >", value, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("first_labeling_time >=", value, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeLessThan(Date value) {
            addCriterion("first_labeling_time <", value, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeLessThanOrEqualTo(Date value) {
            addCriterion("first_labeling_time <=", value, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeIn(List<Date> values) {
            addCriterion("first_labeling_time in", values, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeNotIn(List<Date> values) {
            addCriterion("first_labeling_time not in", values, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeBetween(Date value1, Date value2) {
            addCriterion("first_labeling_time between", value1, value2, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andFirstLabelingTimeNotBetween(Date value1, Date value2) {
            addCriterion("first_labeling_time not between", value1, value2, "firstLabelingTime");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNull() {
            addCriterion("message_id is null");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNotNull() {
            addCriterion("message_id is not null");
            return (Criteria) this;
        }

        public Criteria andMessageIdEqualTo(String value) {
            addCriterion("message_id =", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotEqualTo(String value) {
            addCriterion("message_id <>", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThan(String value) {
            addCriterion("message_id >", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanOrEqualTo(String value) {
            addCriterion("message_id >=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThan(String value) {
            addCriterion("message_id <", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanOrEqualTo(String value) {
            addCriterion("message_id <=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLike(String value) {
            addCriterion("message_id like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotLike(String value) {
            addCriterion("message_id not like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIn(List<String> values) {
            addCriterion("message_id in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotIn(List<String> values) {
            addCriterion("message_id not in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdBetween(String value1, String value2) {
            addCriterion("message_id between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotBetween(String value1, String value2) {
            addCriterion("message_id not between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andViewStatusIsNull() {
            addCriterion("view_status is null");
            return (Criteria) this;
        }

        public Criteria andViewStatusIsNotNull() {
            addCriterion("view_status is not null");
            return (Criteria) this;
        }

        public Criteria andViewStatusEqualTo(Integer value) {
            addCriterion("view_status =", value, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusNotEqualTo(Integer value) {
            addCriterion("view_status <>", value, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusGreaterThan(Integer value) {
            addCriterion("view_status >", value, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("view_status >=", value, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusLessThan(Integer value) {
            addCriterion("view_status <", value, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusLessThanOrEqualTo(Integer value) {
            addCriterion("view_status <=", value, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusIn(List<Integer> values) {
            addCriterion("view_status in", values, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusNotIn(List<Integer> values) {
            addCriterion("view_status not in", values, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusBetween(Integer value1, Integer value2) {
            addCriterion("view_status between", value1, value2, "viewStatus");
            return (Criteria) this;
        }

        public Criteria andViewStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("view_status not between", value1, value2, "viewStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}