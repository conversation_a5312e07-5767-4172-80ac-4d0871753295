package com.meituan.aigc.aida.data.management.dataset.service.impl;

import com.meituan.aigc.aida.config.rhino.RhinoThreadPoolConfig;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.dataset.common.DataSetInvokeEs;
import com.meituan.aigc.aida.data.management.dataset.dao.model.*;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataProcessConfigRepository;
import com.meituan.aigc.aida.data.management.dataset.dto.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.aigc.aida.common.squirrel.SquirrelClient;
import com.meituan.aigc.aida.data.management.dataset.dto.DataProcessTaskDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.LabelRobotInfoDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.NonCustomRobotDTO;
import com.meituan.aigc.aida.data.management.dataset.enums.*;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.dataset.service.*;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategyFactory;
import com.meituan.aigc.aida.data.management.dataset.strategy.task.impl.DataSetFieldConstants;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetFieldUtils;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.data.management.es.query.DatasetPageResult;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchTaskRepository;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskCreateOutputFieldDTO;
import com.meituan.aigc.aida.data.management.fetch.helper.AidaSignalLoggingAppInvokeHelper;
import com.meituan.aigc.aida.data.management.fetch.param.AidaRobotInfoParam;
import com.meituan.aigc.aida.data.management.fetch.vo.RobotVO;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingException;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetPageQueryParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DatasetQueryResultDTO;
import com.meituan.aigc.aida.labeling.remote.common.dto.PageDataWithHead;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.aigc.aida.labeling.service.common.PermissionService;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.meituan.aigc.aida.labeling.util.ThreadPoolUtil;
import com.meituan.csc.aigc.runtime.dto.aida.AidaFinalRes;
import com.meituan.csc.aigc.runtime.dto.aida.MockConfigDTO;
import com.meituan.csc.aigc.runtime.dto.aida.MockRunParamDTO;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppVersionRespDTO;
import com.meituan.csc.aigc.runtime.inner.dto.InnerVersionDTO;
import com.meituan.csc.aigc.runtime.inner.param.InnerVersionQueryParam;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.csccratos.aida.config.client.dto.eval.VersionConfigDTO;
import com.sankuai.csccratos.aida.config.client.rpc.thrift.EvalRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.dto.tag.AidaDataTagDTO;
import com.sankuai.csccratos.csc.aida.label.client.dto.tag.AidaMarkBotInfoDTO;
import com.sankuai.csccratos.csc.aida.label.client.enums.UsageTypeEnum;
import com.sankuai.csccratos.csc.aida.label.client.api.AidaLabelTaskRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.dto.common.AidaResponse;
import com.sankuai.csccratos.csc.aida.label.client.dto.label.AidaLabelDataContentDTO;
import com.sankuai.csccratos.csc.aida.label.client.dto.label.AidaLabelTaskBindReferenceDTO;
import com.sankuai.csccratos.csc.aida.label.client.dto.label.AidaLabelTaskRequestDTO;
import com.sankuai.csccratos.csc.aida.label.client.dto.label.AidaLabelTaskResponseDTO;
import com.sankuai.meituan.auth.util.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataProcessTaskRepository;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.sankuai.csccratos.csc.aida.label.client.api.AidaSceneRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.api.AidaLabelRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.dto.scene.AidaSceneDTO;

import com.dianping.lion.client.Lion;

import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.csc.aigc.runtime.inner.api.AidaConfigInnerRemoteService;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppConfigDTO;
import com.meituan.csc.aigc.runtime.inner.param.InnerAppConfigRequestParam;
import com.meituan.csc.aigc.runtime.dto.aida.AidaBaseResponse;
import com.meituan.aigc.aida.common.constants.TrainingCommonConstants;

import javax.annotation.Resource;

import static com.meituan.aigc.aida.labeling.common.constant.RedisConstant.MARKING_TASK_ID;

/**
 * 数据处理服务实现类
 */
@Slf4j
@Service
public class DataProcessServiceImpl implements DataProcessService {


    @Autowired
    private SquirrelClient squirrelClient;

    @Autowired
    private DataSetRepository dataSetRepository;

    @Autowired
    private DataSetVersionRepository dataSetVersionRepository;

    @Autowired
    private DataProcessTaskRepository dataProcessTaskRepository;

    @Autowired
    private DataProcessConfigRepository dataProcessConfigRepository;

    @Autowired
    private DataSetVersionService dataSetVersionService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private AidaSceneRemoteService aidaSceneRemoteService;

    @Autowired
    private AidaLabelRemoteService aidaLabelRemoteService;

    @Resource
    private AidaLabelTaskRemoteService aidaLabelTaskRemoteService;

    @Resource
    private CaseAnalysisService caseAnalysisService;

    @Resource
    private DatasetVersionLockService versionLockService;

    @Autowired
    private AidaConfigInnerRemoteService aidaConfigInnerRemoteService;

    @Autowired
    private PushElephantService pushElephantService;

    @Autowired
    private EvalRemoteService.Iface aidaRemoteService;

    @Autowired
    private DataFieldStrategyFactory dataFieldStrategyFactory;

    @Autowired
    private DataSetInvokeEs dataSetInvokeEs;

    @Autowired
    private DatasetEsIndexService datasetEsIndexService;

    @Autowired
    private DataManagementLionConfig dataManagementLionConfig;

    @Autowired
    private AidaSignalLoggingAppInvokeHelper aidaAppInvokeHelper;

    @Autowired
    private DataFetchTaskRepository dataFetchTaskRepository;

    @Autowired
    private DataSetManagementService dataSetManagementService;

    private static final int FIRST_BATCH = 0;
    private static final int PAGE_SIZE = 10;
    private static final String ROBOT_INVOKE_INFO_FIELD = "信号加工机器人信息";

    /**
     * 创建数据处理任务执行器
     */
    private static final ExecutorService CREATE_DATA_PROCESS_TASK_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(10, 40, 200, "create-data-process-task-%d");

    /**
     * 信号回刷处理任务执行器
     */
    private static final ExecutorService SIGNAL_REFRESH_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(10, 40, 200, "signal-refresh-%d");

    @Override
    public void createProcessTask(DataProcessCreateTaskParam param) {
        // 1. 校验参数
        DataSet dataSet = validateCreateTaskParams(param);

        // 2. 创建数据处理任务
        DataProcessTask task = createDataProcessTask(param);

        // 3. 异步处理创建任务
        CREATE_DATA_PROCESS_TASK_EXECUTOR.submit(() -> {
            try {
                // 1. 复制一个新的数据集版本
                DataSetCopyVersionParam dataSetCopyVersionParam = new DataSetCopyVersionParam();
                dataSetCopyVersionParam.setDataSetId(dataSet.getId());
                dataSetCopyVersionParam.setVersionId(param.getVersionId());
                dataSetCopyVersionParam.setVersionName(param.getProcessDatasetVersionName());
                dataSetCopyVersionParam.setVersionType(DataSetVersionTypeEnum.DATA_PROCESS.getCode());
                dataSetCopyVersionParam.setVersionDescription("由数据处理任务创建: " + param.getTaskName());
                Long newVersionId = dataSetVersionService.copyVersion(dataSetCopyVersionParam);

                // 2. 更新任务信息
                updateTask(task, newVersionId);

                // 3. 发送成功通知
                String url = Lion.getString(ConfigUtil.getAppkey(), LionConstant.DATA_PROCESS_TASK_MANAGE_URL);
                String message = String.format("数据处理任务「%s」创建成功，[点击查看|%s]", param.getTaskName(), url);
                pushElephantService.pushElephant(message, task.getCreatorMis());

                log.info("数据处理任务创建成功，任务ID：{}，任务名称：{}", task.getId(), param.getTaskName());
            } catch (Exception e) {
                log.error("异步创建数据处理任务失败，任务ID：{}，任务名称：{}，异常信息：{}", task.getId(), param.getTaskName(), e.getMessage(), e);

                // 更新任务状态为失败
                updateTaskStatus(task.getId(), ProcessTaskStatusEnum.CREATE_FAILED);

                // 发送失败通知
                String errorMessage = String.format("数据处理任务「%s」创建失败，失败原因：%s", param.getTaskName(), e.getMessage());
                pushElephantService.pushElephant(errorMessage, task.getCreatorMis());
            }
        });
    }

    /**
     * 校验创建任务的参数
     *
     * @param param 创建任务参数
     * @return 数据集对象
     */
    private DataSet validateCreateTaskParams(DataProcessCreateTaskParam param) {
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getTaskName()), "任务名称不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getDatasetId()), "数据集ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getVersionId()), "数据集版本ID不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getProcessDatasetVersionName()), "清洗后数据集版本名称不能为空");

        // 查询数据集是否存在
        DataSet dataSet = dataSetRepository.getDataSetById(param.getDatasetId());
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        // 查询数据集版本是否存在
        DataSetVersion sourceVersion = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(param.getDatasetId(), param.getVersionId());
        CheckUtil.paramCheck(Objects.nonNull(sourceVersion), "数据集版本不存在");

        return dataSet;
    }

    /**
     * 创建数据处理任务
     *
     * @param param 任务参数
     */
    private DataProcessTask createDataProcessTask(DataProcessCreateTaskParam param) {
        Date now = new Date();

        DataProcessTask task = new DataProcessTask();
        task.setTaskName(param.getTaskName());
        task.setDatasetId(param.getDatasetId());
        task.setOriginalDatasetVersionId(param.getVersionId());
        task.setStatus(ProcessTaskStatusEnum.CREATING.getCode());
        task.setTotalCount(0); // 初始设置为0，待处理完成后更新
        // 设置创建者信息
        task.setCreatorMis(UserUtils.getUser().getLogin());
        task.setCreatorName(UserUtils.getUser().getName());
        // 设置创建和更新时间
        task.setCreateTime(now);
        task.setUpdateTime(now);
        task.setIsDeleted(Boolean.FALSE);

        // 插入任务数据
        dataProcessTaskRepository.createDataProcessTask(task);
        return task;
    }

    /**
     * 更新任务信息
     *
     * @param task         数据处理任务
     * @param newVersionId 新版本ID
     */
    private void updateTask(DataProcessTask task, Long newVersionId) {
        // 创建更新对象
        DataProcessTask updateTask = new DataProcessTask();
        updateTask.setId(task.getId());
        updateTask.setProcessDatasetVersionId(newVersionId);
        updateTask.setStatus(ProcessTaskStatusEnum.WAITING.getCode());
        updateTask.setUpdateTime(new Date());

        // 执行更新
        dataProcessTaskRepository.updateDataProcessTask(updateTask);
    }

    @Override
    public PageData<DataProcessTaskDTO> listProcessTask(DataProcessTaskPageParam param) {
        // 1. 校验参数，设置默认值
        if (param.getPageNum() == null || param.getPageNum() <= 0) {
            param.setPageNum(1);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(10);
        }

        // 2. 使用PageHelper进行数据库分页
        log.info("开始分页查询数据处理任务，参数：taskName={}, datasetId={}, pageNum={}, pageSize={}", param.getTaskName(), param.getDatasetId(), param.getPageNum(), param.getPageSize());

        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        // 3. 查询符合条件的数据处理任务
        List<DataProcessTask> tasks;

        // 获取当前登录用户
        String currentUserMis = UserUtils.getUser().getLogin();
        boolean isAdmin = permissionService.isAdminUser();

        // 根据用户权限决定是否按创建人过滤
        if (isAdmin) {
            // 管理员可以查看所有任务
            log.info("当前用户{}是管理员，可查看所有任务", currentUserMis);
            tasks = dataProcessTaskRepository.listDataProcessTaskByNameAndDatasetId(param.getTaskName(), param.getDatasetId());
        } else {
            // 非管理员只能查看自己创建的任务
            log.info("当前用户{}非管理员，只能查看自己创建的任务", currentUserMis);
            tasks = dataProcessTaskRepository.listDataProcessTaskByNameAndDatasetIdAndCreator(param.getTaskName(), param.getDatasetId(), currentUserMis);
        }

        if (CollectionUtils.isEmpty(tasks)) {
            PageHelper.clearPage();
            return new PageData<>(0L, Collections.emptyList());
        }

        // 4. 获取分页信息
        PageInfo<DataProcessTask> pageInfo = new PageInfo<>(tasks);
        PageHelper.clearPage();

        // 5. 转换为DTO
        List<DataProcessTaskDTO> taskDTOs = tasks.stream().map(this::convertToDTO).filter(Objects::nonNull).collect(Collectors.toList());

        return new PageData<>(pageInfo.getTotal(), taskDTOs);
    }

    /**
     * 将数据处理任务实体转换为DTO
     *
     * @param task 数据处理任务实体
     * @return 数据处理任务DTO
     */
    private DataProcessTaskDTO convertToDTO(DataProcessTask task) {
        if (Objects.isNull(task)) {
            return null;
        }

        DataProcessTaskDTO dto = new DataProcessTaskDTO();
        dto.setId(task.getId());
        dto.setTaskName(task.getTaskName());
        dto.setTaskStatus(task.getStatus().intValue());
        dto.setSourceDatasetId(task.getDatasetId());
        dto.setProcessedDatasetVersionId(task.getProcessDatasetVersionId());
        dto.setSourceDatasetVersionId(task.getOriginalDatasetVersionId());
        dto.setCreatorName(task.getCreatorName());
        dto.setCreateTime(task.getCreateTime() != null ? DateUtil.formatDate(task.getCreateTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD) : null);

        // TODO: 2025/6/16 改为批量查询
        // 查询数据集信息
        DataSet dataSet = dataSetRepository.getDataSetById(task.getDatasetId());
        if (Objects.nonNull(dataSet)) {
            dto.setSourceDatasetName(dataSet.getName());
            dto.setDataSource(dataSet.getDataSource());
        }

        // 查询数据集版本信息
        DataSetVersion sourceVersion = dataSetVersionRepository.getById(task.getOriginalDatasetVersionId());
        if (Objects.nonNull(sourceVersion)) {
            dto.setSourceDatasetVersionName(sourceVersion.getVersionName());
        }

        DataSetVersion processedVersion = dataSetVersionRepository.getById(task.getProcessDatasetVersionId());
        if (Objects.nonNull(processedVersion)) {
            dto.setProcessedDatasetVersionName(processedVersion.getVersionName());
        }

        return dto;
    }

    @Override
    public LabelRobotInfoDTO getLabelRobotInfo(LabelRobotParam param) {

        // 1. 参数校验
        CheckUtil.paramCheck(Objects.nonNull(param), "参数不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getRobotId()), "机器人ID不能为空");

        // 2. 构建请求参数
        InnerAppConfigRequestParam configParam = new InnerAppConfigRequestParam();
        configParam.setAppId(param.getRobotId());
        configParam.setModelConfigVersionId(param.getRobotVersionId());
        configParam.setSecret(TrainingCommonConstants.AIDA_SECRET);

        // 3. 调用外部接口获取机器人配置
        AidaBaseResponse<InnerAppConfigDTO> response = aidaConfigInnerRemoteService.getRobotConfigById(configParam);

        // 4. 校验响应结果
        if (Objects.isNull(response)) {
            log.error("调用AidaConfigInnerRemoteService#getRobotConfigById查询机器人配置无结果，机器人ID：{}", param.getRobotId());
            return createEmptyLabelRobotInfoDTO();
        }

        if (Objects.isNull(response.getCode()) || !Objects.equals(response.getCode(), TrainingCommonConstants.AIDA_SUCC_CODE)) {
            log.error("调用AidaConfigInnerRemoteService#getRobotConfigById查询机器人配置失败，机器人ID：{}，错误码：{}，错误信息：{}", param.getRobotId(), response.getCode(), response.getMessage());
            return createEmptyLabelRobotInfoDTO();
        }

        InnerAppConfigDTO appConfig = response.getData();
        if (Objects.isNull(appConfig)) {
            log.warn("调用AidaConfigInnerRemoteService#getRobotConfigById查询机器人配置，未查询到配置信息，机器人ID：{}", param.getRobotId());
            return createEmptyLabelRobotInfoDTO();
        }

        // 5. 转换为内部DTO
        LabelRobotInfoDTO result = convertToLabelRobotInfoDTO(appConfig);
        log.info("成功获取打标机器人信息，机器人ID：{}，输入参数数量：{}，输出参数数量：{}", param.getRobotId(), result.getInputParam() != null ? result.getInputParam().size() : 0, result.getOutputParam() != null ? result.getOutputParam().size() : 0);

        return result;
    }

    @Override
    public SceneListDTO getNonCustomScene() {
        // 调用外部接口获取所有启用的场景列表
        AidaResponse<List<AidaSceneDTO>> response = aidaSceneRemoteService.listAllScenes();

        if (Objects.isNull(response)) {
            log.error("调用AidaSceneRemoteService#listAllScenes查询场景列表无结果");
            return createEmptySceneListDTO();
        }

        if (Objects.isNull(response.getCode()) || response.getCode() != 0) {
            log.error("调用AidaSceneRemoteService#listAllScenes查询场景失败，错误码：{}，错误信息：{}", response.getCode(), response.getMessage());
            return createEmptySceneListDTO();
        }

        List<AidaSceneDTO> allScenes = response.getData();
        if (CollectionUtils.isEmpty(allScenes)) {
            log.warn("调用AidaSceneRemoteService#listAllScenes查询场景，未查询到启用场景");
            return createEmptySceneListDTO();
        }

        // 转换为内部DTO
        List<SceneInfo> sceneList = allScenes.stream().filter(Objects::nonNull).map(this::convertToSceneInfo).filter(Objects::nonNull).collect(Collectors.toList());

        log.info("成功获取场景列表，共{}个场景", sceneList.size());


        SceneListDTO result = new SceneListDTO();
        result.setSceneList(sceneList);
        return result;
    }

    @Override
    public LabelListDTO getNonCustomLabel(Long sceneId) {
        log.info("开始获取非自定义标签信息，场景ID：{}", sceneId);

        CheckUtil.paramCheck(Objects.nonNull(sceneId), "场景ID不能为空");

        try {
            // 调用外部接口根据场景ID和用途获取标签列表
            AidaResponse<List<AidaDataTagDTO>> response = aidaLabelRemoteService.listTagsBySceneIdAndUsage(sceneId, UsageTypeEnum.DATASET_PROCESSING);

            if (Objects.isNull(response)) {
                log.error("调用AidaLabelRemoteService#listTagsBySceneIdAndUsage查询标签列表无结果，场景ID：{}", sceneId);
                return createEmptyLabelListDTO();
            }

            if (Objects.isNull(response.getCode()) || response.getCode() != 0) {
                log.error("调用AidaLabelRemoteService#listTagsBySceneIdAndUsage查询标签失败，场景ID：{}，错误码：{}，错误信息：{}", sceneId, response.getCode(), response.getMessage());
                return createEmptyLabelListDTO();
            }

            List<AidaDataTagDTO> allTags = response.getData();
            if (CollectionUtils.isEmpty(allTags)) {
                log.warn("调用AidaLabelRemoteService#listTagsBySceneIdAndUsage查询标签，未查询到标签，场景ID：{}", sceneId);
                return createEmptyLabelListDTO();
            }

            // 转换为内部DTO
            List<LabelInfo> labelList = allTags.stream().filter(Objects::nonNull).map(this::convertToLabelInfo).filter(Objects::nonNull).collect(Collectors.toList());

            log.info("成功获取标签列表，场景ID：{}，共{}个标签", sceneId, labelList.size());

            LabelListDTO result = new LabelListDTO();
            result.setLabelList(labelList);
            return result;

        } catch (Exception e) {
            log.error("查询标签列表异常，场景ID：{}", sceneId, e);
            return createEmptyLabelListDTO();
        }
    }

    /**
     * 创建空的场景列表DTO
     *
     * @return 空的场景列表DTO
     */
    private SceneListDTO createEmptySceneListDTO() {
        SceneListDTO result = new SceneListDTO();
        result.setSceneList(Collections.emptyList());
        return result;
    }

    /**
     * 创建空的标签列表DTO
     *
     * @return 空的标签列表DTO
     */
    private LabelListDTO createEmptyLabelListDTO() {
        LabelListDTO result = new LabelListDTO();
        result.setLabelList(Collections.emptyList());
        return result;
    }

    /**
     * 创建空的打标机器人信息DTO
     *
     * @return 空的打标机器人信息DTO
     */
    private LabelRobotInfoDTO createEmptyLabelRobotInfoDTO() {
        LabelRobotInfoDTO result = new LabelRobotInfoDTO();
        result.setInputParam(Collections.emptyList());
        result.setOutputParam(Collections.emptyList());
        return result;
    }

    /**
     * 将AidaSceneDTO转换为SceneInfo
     *
     * @param aidaSceneDTO 外部场景DTO
     * @return 内部场景信息
     */
    private SceneInfo convertToSceneInfo(AidaSceneDTO aidaSceneDTO) {
        if (Objects.isNull(aidaSceneDTO)) {
            return null;
        }

        SceneInfo sceneInfo = new SceneInfo();
        sceneInfo.setSceneId(aidaSceneDTO.getId());
        sceneInfo.setSceneName(aidaSceneDTO.getSceneName());
        return sceneInfo;
    }

    /**
     * 将InnerAppConfigDTO转换为LabelRobotInfoDTO
     *
     * @param appConfig 机器人配置DTO
     * @return 打标机器人信息DTO
     */
    private LabelRobotInfoDTO convertToLabelRobotInfoDTO(InnerAppConfigDTO appConfig) {
        if (Objects.isNull(appConfig)) {
            return createEmptyLabelRobotInfoDTO();
        }

        LabelRobotInfoDTO result = new LabelRobotInfoDTO();

        // 解析输入参数 - 处理UserInputForm的所有类型字段
        List<LabelRobotInfoDTO.ParamInfo> inputParams = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(appConfig.getUserInputFormList())) {
            for (InnerAppConfigDTO.UserInputForm form : appConfig.getUserInputFormList()) {
                LabelRobotInfoDTO.ParamInfo paramInfo = null;

                // 处理Text类型
                if (Objects.nonNull(form.getText())) {
                    paramInfo = new LabelRobotInfoDTO.ParamInfo();
                    paramInfo.setCode(form.getText().getVariable());
                    paramInfo.setName(form.getText().getLabel());
                }
                // 处理Select类型
                else if (Objects.nonNull(form.getSelect())) {
                    paramInfo = new LabelRobotInfoDTO.ParamInfo();
                    paramInfo.setCode(form.getSelect().getVariable());
                    paramInfo.setName(form.getSelect().getLabel());
                }
                // 处理Paragraph类型
                else if (Objects.nonNull(form.getParagraph())) {
                    paramInfo = new LabelRobotInfoDTO.ParamInfo();
                    paramInfo.setCode(form.getParagraph().getVariable());
                    paramInfo.setName(form.getParagraph().getLabel());
                }
                // 处理Photo类型
                else if (Objects.nonNull(form.getPhoto())) {
                    paramInfo = new LabelRobotInfoDTO.ParamInfo();
                    paramInfo.setCode(form.getPhoto().getVariable());
                    paramInfo.setName(form.getPhoto().getLabel());
                }

                if (Objects.nonNull(paramInfo)) {
                    inputParams.add(paramInfo);
                }
            }
        }
        result.setInputParam(inputParams);

        // 解析输出参数 - 从outputForm获取输出字段信息
        List<LabelRobotInfoDTO.ParamInfo> outputParams = new ArrayList<>();
        if (Objects.nonNull(appConfig.getOutputForm()) && Boolean.TRUE.equals(appConfig.getOutputForm().getEnabled()) && CollectionUtils.isNotEmpty(appConfig.getOutputForm().getParams())) {
            for (InnerAppConfigDTO.OutputParam outputParam : appConfig.getOutputForm().getParams()) {
                LabelRobotInfoDTO.ParamInfo paramInfo = new LabelRobotInfoDTO.ParamInfo();
                paramInfo.setCode(outputParam.getKey());
                paramInfo.setName(outputParam.getName());
                outputParams.add(paramInfo);
            }
        }
        result.setOutputParam(outputParams);

        return result;
    }

    @Override
    public List<NonCustomRobotDTO> queryNonCustomRobot(Long sceneId, List<String> labelIdList) {
        log.info("开始查询非定制机器人，场景ID：{}，标签ID列表：{}", sceneId, labelIdList);

        // 参数校验
        if (CollectionUtils.isEmpty(labelIdList)) {
            log.warn("标签ID列表为空，返回空结果");
            return Collections.emptyList();
        }

        List<NonCustomRobotDTO> result = new ArrayList<>();

        // 遍历标签ID列表，调用AidaLabelRemoteService获取机器人信息
        for (String labelId : labelIdList) {
            try {
                Long tagId = Long.parseLong(labelId);

                // 调用AidaLabelRemoteService获取机器人信息
                AidaResponse<List<AidaMarkBotInfoDTO>> response = aidaLabelRemoteService.listMarkBotsByTagIdAndUsage(tagId, UsageTypeEnum.DATASET_PROCESSING);

                if (Objects.isNull(response)) {
                    log.error("调用AidaLabelRemoteService#listMarkBotsByTagIdAndUsage查询机器人信息无结果，标签ID：{}", labelId);
                    continue;
                }

                if (Objects.isNull(response.getCode()) || response.getCode() != 0) {
                    log.error("调用AidaLabelRemoteService#listMarkBotsByTagIdAndUsage查询机器人信息失败，标签ID：{}，错误码：{}，错误信息：{}", labelId, response.getCode(), response.getMessage());
                    continue;
                }

                List<AidaMarkBotInfoDTO> markBotInfoList = response.getData();
                if (CollectionUtils.isEmpty(markBotInfoList)) {
                    log.warn("标签ID：{}，未查询到机器人信息", labelId);
                    continue;
                }

                // 转换为NonCustomRobotDTO
                NonCustomRobotDTO robotDTO = convertMarkBotInfoListToDTO(labelId, markBotInfoList);
                if (Objects.nonNull(robotDTO)) {
                    result.add(robotDTO);
                }

            } catch (NumberFormatException e) {
                log.error("标签ID格式错误，无法转换为Long类型，标签ID：{}", labelId, e);
            } catch (Exception e) {
                log.error("查询标签ID：{}的机器人信息异常", labelId, e);
            }
        }

        log.info("查询非定制机器人完成，共查询到{}个标签的机器人信息", result.size());
        return result;
    }

    @Override
    public void markData(DataMarkParam param) {
        // 1. 参数校验
        CheckUtil.paramCheck(Objects.nonNull(param), "打标参数不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getDatasetTaskId()), "数据处理任务ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getLabelType()), "标签类型不能为空");
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(param.getRobotList()), "机器人列表不能为空");

        // 2. 查询数据处理任务
        DataProcessTask dataProcessTask = dataProcessTaskRepository.getDataProcessTaskById(param.getDatasetTaskId());
        CheckUtil.paramCheck(Objects.nonNull(dataProcessTask), "数据处理任务不存在");

        // 3. 获取数据集版本锁
        boolean success = versionLockService.tryLock(dataProcessTask.getDatasetId(), dataProcessTask.getProcessDatasetVersionId(), "mark");
        CheckUtil.operationCheck(success, "当前数据正在进行别的操作，请等待其他操作完成");

        // 4. 保存打标信息
        DataProcessConfigWithBLOBs dataProcessConfig = saveDataProcessConfig(ProcessTypeEnum.DATA_TAGGING, JSONObject.toJSONString(param), param.getDatasetTaskId());

        // 5. 更新任务状态为打标中
        updateTaskStatus(param.getDatasetTaskId(), ProcessTaskStatusEnum.LABELING);

        // 6. 异步执行标注任务
        param.setMis(UserUtils.getUser().getLogin());
        CompletableFuture.runAsync(() -> {
            executeMarkingTask(param, dataProcessTask, dataProcessConfig.getId());
        }, RhinoThreadPoolConfig.DATA_PROCESS_TASK_THREAD_POOL.getExecutor());

        log.info("数据打标任务已启动, 任务ID: {}", param.getDatasetTaskId());
    }

    @Override
    public void deleteData(Long taskId) {
        CheckUtil.paramCheck(Objects.nonNull(taskId), "数据处理任务ID不能为空");
        DataProcessTask dataProcessTask = dataProcessTaskRepository.getDataProcessTaskById(taskId);
        CheckUtil.paramCheck(Objects.nonNull(dataProcessTask), "数据处理任务不存在");

        // 删除数据
        dataProcessTask.setIsDeleted(true);
        dataProcessTaskRepository.updateDataProcessTask(dataProcessTask);
    }

    @Override
    public List<RobotVO> getRobotList(Long dataSetId, Long versionId) {
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        DataSetVersion dataSetVersion = dataSetVersionRepository.getById(versionId);
        // 取十条样本数据
        DatasetEsQueryCondition datasetEsQueryCondition = dataSetInvokeEs.getDatasetEsQueryConditionWithPage(dataSet, dataSetVersion, FIRST_BATCH, PAGE_SIZE);
        DatasetPageResult datasetPageResult = datasetEsIndexService.pageQuery(datasetEsQueryCondition);
        if (datasetPageResult == null || CollectionUtils.isEmpty(datasetPageResult.getRecords())) {
            return Collections.emptyList();
        }
        List<DatasetEsIndex> records = datasetPageResult.getRecords();

        // 解析表头配置
        DatasetHeadConfig headConfig = JSON.parseObject(dataSetVersion.getHeadConfig(), DatasetHeadConfig.class);

        // 从robotInvokeInfo字段提取并去重appId
        Set<String> appIds = getAppIdsFromRecords(records, headConfig);

        // 根据appId调用aida接口获取机器人名字
        return getRobotDetails(appIds);
    }

    /**
     * 从 ES 记录中提取 App Ids
     *
     * @param records    ES 查询记录
     * @param headConfig 表头配置
     * @return AppId 集合
     */
    private Set<String> getAppIdsFromRecords(List<DatasetEsIndex> records, DatasetHeadConfig headConfig) {
        if (headConfig == null || CollectionUtils.isEmpty(headConfig.getHeadList())) {
            log.warn("数据集版本的表头配置为空，无法提取AppId");
            return Collections.emptySet();
        }

        // 提前获取字段类型和处理策略
        Map<String, Integer> columnTypeMap = headConfig.getHeadList().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(DataContentFieldDTO::getColumnName, DataContentFieldDTO::getFieldType, (v1, v2) -> v1));

        Integer fieldType = columnTypeMap.get(ROBOT_INVOKE_INFO_FIELD);
        if (fieldType == null) {
            log.warn("表头中未找到 {} 字段的类型定义", ROBOT_INVOKE_INFO_FIELD);
            return Collections.emptySet();
        }
        DataFieldStrategy strategy = dataFieldStrategyFactory.getStrategy(fieldType);
        if (strategy == null) {
            log.warn("未找到字段类型 {} 对应的处理策略", fieldType);
            return Collections.emptySet();
        }

        Set<String> appIds = new HashSet<>();
        for (DatasetEsIndex record : records) {
            try {
                Map<String, Object> rowData = DatasetFieldUtils.convertEsIndexToRowData(record, headConfig);
                Object robotInfoObj = rowData.get(ROBOT_INVOKE_INFO_FIELD);

                if (robotInfoObj == null) {
                    continue;
                }

                // 序列化再反序列化，确保数据类型一致性
                String strRobotInfo = strategy.serializeFieldValue(robotInfoObj);
                if (StringUtils.isBlank(strRobotInfo)) {
                    continue;
                }
                Object value = strategy.parseFieldValue(strRobotInfo);

                if (value instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject) value;
                    List<String> robotSignalTypeList = dataManagementLionConfig.getRobotList();

                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String appId = entry.getKey();
                        Object aidaRobotInfoValue = entry.getValue();

                        if (aidaRobotInfoValue == null) {
                            continue;
                        }

                        // 根据用户提供的数据格式，value 是一个需要再次解析的 JSON 字符串
                        try {
                            AidaRobotInfoParam aidaRobotInfoParam = JSON.parseObject(JSONObject.toJSONString(aidaRobotInfoValue), AidaRobotInfoParam.class);
                            if (aidaRobotInfoParam == null || StringUtils.isBlank(aidaRobotInfoParam.getSignalType())) {
                                continue;
                            }
                            if (robotSignalTypeList.contains(aidaRobotInfoParam.getSignalType())) {
                                appIds.add(appId);
                            }
                        } catch (Exception ex) {
                            log.warn("解析 AidaRobotInfoParam 失败, appId: {}, value: {}", appId, aidaRobotInfoValue, ex);
                        }
                    }

                }
            } catch (Exception e) {
                log.warn("解析robotInvokeInfo字段失败, recordId: {}, error: {}", record.getDocumentId(), e.getMessage());
            }
        }
        return appIds;
    }

    /**
     * 根据 App Ids 获取机器人详情
     *
     * @param appIds AppId 集合
     * @return 机器人视图对象列表
     */
    private List<RobotVO> getRobotDetails(Set<String> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Collections.emptyList();
        }

        // 根据appId调用aida接口获取机器人名字
        List<RobotVO> robotList = new ArrayList<>();
        for (String appId : appIds) {
            try {
                InnerAppConfigRequestParam configParam = new InnerAppConfigRequestParam();
                configParam.setAppId(appId);
                configParam.setSecret(TrainingCommonConstants.AIDA_SECRET);

                AidaBaseResponse<InnerAppConfigDTO> response = aidaConfigInnerRemoteService.getRobotConfigById(configParam);

                if (response != null && Objects.equals(response.getCode(), TrainingCommonConstants.AIDA_SUCC_CODE) && response.getData() != null) {
                    InnerAppConfigDTO appConfig = response.getData();
                    RobotVO robotVO = new RobotVO();
                    robotVO.setRobotId(appId);
                    robotVO.setRobotName(appConfig.getAppName());
                    robotList.add(robotVO);
                } else {
                    log.warn("获取机器人配置失败, appId: {}, response: {}", appId, JSON.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("获取机器人配置异常, appId: {}", appId, e);
            }
        }
        return robotList;
    }

    @Override
    public SignalRefreshRobotVersionInfo getRobotVersionList(String robotId) {
        SignalRefreshRobotVersionInfo result = new SignalRefreshRobotVersionInfo();
        InnerVersionQueryParam param = new InnerVersionQueryParam();
        param.setAppId(robotId);
        param.setSecret(TrainingCommonConstants.AIDA_SECRET);
        AidaBaseResponse<InnerAppVersionRespDTO> response = aidaConfigInnerRemoteService.getVersionsAndTokenByAppId(param);
        if (Objects.isNull(response) || !Objects.equals(response.getCode(), TrainingCommonConstants.AIDA_SUCC_CODE)) {
            log.error("调用AidaConfigInnerRemoteService#getVersionsAndTokenByAppId查询机器人版本失败，机器人ID：{}，错误码：{}，错误信息：{}", robotId, response.getCode(), response.getMessage());
            return result;
        }
        if (Objects.isNull(response.getData())) {
            log.warn("调用AidaConfigInnerRemoteService#getVersionsAndTokenByAppId查询机器人配置，未查询到配置信息，机器人ID：{}", robotId);
            return result;
        }
        InnerAppVersionRespDTO data = response.getData();
        result.setApiToken(data.getApiToken());
        List<InnerVersionDTO> modelConfigAndProcessVersions = data.getModelConfigAndProcessVersions();
        if (CollectionUtils.isNotEmpty(modelConfigAndProcessVersions)) {
            result.setVersionList(modelConfigAndProcessVersions.stream().map(version -> {
                RobotVersionInfo robotVersionInfo = new RobotVersionInfo();
                robotVersionInfo.setRobotVersionId(version.getVersionId());
                robotVersionInfo.setRobotVersionName(version.getVersionName());
                return robotVersionInfo;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * 执行打标任务
     *
     * @param param           打标参数
     * @param dataProcessTask 数据处理任务
     */
    private void executeMarkingTask(DataMarkParam param, DataProcessTask dataProcessTask, Long dataProcessConfigId) {
        log.info("开始执行打标任务, 任务ID: {}", param.getDatasetTaskId());

        try {
            // 构建标注任务参数
            StringBuilder scrollId = new StringBuilder();

            DataSetVersion dataSetVersion = dataSetVersionRepository.getById(dataProcessTask.getProcessDatasetVersionId());
            DatasetHeadConfig headConfig = JSON.parseObject(dataSetVersion.getHeadConfig(), DatasetHeadConfig.class);
            Map<String, DataContentFieldDTO> fieldMap = headConfig.getHeadList().stream().collect(Collectors.toMap(DataContentFieldDTO::getColumnName, Function.identity()));

            AidaLabelTaskRequestDTO labelTaskRequest = buildLabelingTaskParam(param, dataProcessTask, scrollId, fieldMap);

            // 调用标注服务创建标注任务
            AidaResponse<AidaLabelTaskResponseDTO> response = aidaLabelTaskRemoteService.createLabelTask(labelTaskRequest);
            if (Objects.isNull(response) || response.getCode() != 0) {
                log.error("调用标注服务创建标注任务失败, 任务ID: {}, 失败原因: {}", param.getDatasetTaskId(), response.getMessage());
                throw new RuntimeException(response.getMessage());
            }
            Long taskId = response.getData().getTaskId();
            labelTaskRequest.setTaskId(taskId);
            squirrelClient.set(MARKING_TASK_ID, taskId.toString(), dataProcessConfigId.toString(), 60 * 60 * 24 * 7);

            DataSetPageQueryParam esSearchParam = new DataSetPageQueryParam();
            esSearchParam.setDataSetId(dataProcessTask.getDatasetId());
            esSearchParam.setVersionId(dataProcessTask.getProcessDatasetVersionId());
            esSearchParam.setIsScrollSearch(true);
            esSearchParam.setScrollId(scrollId.toString());
            while (!labelTaskRequest.getIsEnd()) {
                DatasetQueryResultDTO result = caseAnalysisService.pageQueryDataSet(esSearchParam);
                if (Objects.isNull(result) || Objects.isNull(result.getPageData()) || CollectionUtils.isEmpty(result.getPageData().getData())) {
                    labelTaskRequest.setColumnValues(new ArrayList<>());
                    labelTaskRequest.setIsEnd(Boolean.TRUE);
                } else {
                    labelTaskRequest.setColumnValues(transColumnValues2LabelContent(result.getPageData().getData(), fieldMap));
                    if (Boolean.FALSE.equals(result.getHasNext())) {
                        labelTaskRequest.setIsEnd(Boolean.TRUE);
                    }
                }
                response = aidaLabelTaskRemoteService.createLabelTask(labelTaskRequest);
                if (Objects.isNull(response) || response.getCode() != 0) {
                    log.error("调用标注服务创建标注任务失败, 任务ID: {}, 失败原因: {}", param.getDatasetTaskId(), response.getMessage());
                    throw new AidaTrainingException(response.getMessage());
                }
                labelTaskRequest.setTaskId(response.getData().getTaskId());
            }
        } catch (Exception e) {
            log.error("执行打标任务异常, 任务ID: {}, 异常信息: {}", param.getDatasetTaskId(), e.getMessage(), e);
            // 更新任务状态为失败
            updateTaskStatus(param.getDatasetTaskId(), ProcessTaskStatusEnum.LABELING_FAILED);
            String message = String.format("数据处理任务「%s」执行打标失败，失败原因：%s", dataProcessTask.getTaskName(), e.getMessage());
            pushElephantService.pushElephant(message, dataProcessTask.getCreatorMis());
            versionLockService.releaseLock(dataProcessTask.getDatasetId(), dataProcessTask.getProcessDatasetVersionId(), "mark");
        }
    }

    /**
     * 构建标注任务参数
     *
     * @param param           打标参数
     * @param dataProcessTask 数据处理任务
     * @return 标注任务参数
     */
    private AidaLabelTaskRequestDTO buildLabelingTaskParam(DataMarkParam param, DataProcessTask dataProcessTask, StringBuilder scrollId, Map<String, DataContentFieldDTO> fieldMap) {
        AidaLabelTaskRequestDTO request = new AidaLabelTaskRequestDTO();
        // 构建打标机器人参数
        request.setBindReference(buildLabelTaskBindReferenceList(param));

        // 查询头批数据
        DataSetPageQueryParam esSearchParam = new DataSetPageQueryParam();
        buildBasicEsSearch(esSearchParam, dataProcessTask.getDatasetId(), dataProcessTask.getProcessDatasetVersionId());

        DatasetQueryResultDTO result = caseAnalysisService.pageQueryDataSet(esSearchParam);

        PageDataWithHead<DataFieldParam, DataSetRecordParam> pageData = result.getPageData();
        // 构建列名
        request.setColumnNames(pageData.getHeadList().stream().map(DataFieldParam::getColumnName).collect(Collectors.toList()));

        // 构建数据
        request.setColumnValues(transColumnValues2LabelContent(pageData.getData(), fieldMap));
        request.setSourceType(3);
        request.setIsEnd(!result.getHasNext());
        request.setMis(param.getMis());
        scrollId.append(result.getScrollId());
        return request;
    }

    /**
     * 转换列值到标注内容格式
     *
     * @param records 数据集记录
     * @return 标注数据内容列表
     */
    private List<AidaLabelDataContentDTO> transColumnValues2LabelContent(List<DataSetRecordParam> records, Map<String, DataContentFieldDTO> fieldMap) {
        List<AidaLabelDataContentDTO> dataList = new ArrayList<>();
        for (DataSetRecordParam record : records) {
            AidaLabelDataContentDTO data = new AidaLabelDataContentDTO();
            data.setDataId(record.getDataId());
            data.setDataContent(record.getContent().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue() == null ? StringUtils.EMPTY : fieldMap.containsKey(e.getKey()) ? dataFieldStrategyFactory.getStrategy(fieldMap.get(e.getKey()).getFieldType()).serializeFieldValue(e.getValue()) : e.getValue().toString())));
            dataList.add(data);
        }
        return dataList;
    }

    /**
     * 构建es查询参数
     *
     * @param param          查询参数
     * @param datasetId      数据集id
     * @param datasetVersion 数据集版本
     */
    private void buildBasicEsSearch(DataSetPageQueryParam param, Long datasetId, Long datasetVersion) {
        param.setIsScrollSearch(true);
        param.setDataSetId(datasetId);
        param.setVersionId(datasetVersion);
        param.setPageSize(100);
    }

    /**
     * 构建标注任务绑定引用列表
     *
     * @param param 打标参数
     * @return 标注任务绑定引用列表
     */
    private List<AidaLabelTaskBindReferenceDTO> buildLabelTaskBindReferenceList(DataMarkParam param) {
        List<AidaLabelTaskBindReferenceDTO> referenceList = new ArrayList<>();

        for (DataMarkParam.RobotConfig robotConfig : param.getRobotList()) {
            // 获取入参映射
            Map<String, String> inputParamMap = robotConfig.getInputParam().stream().collect(Collectors.toMap(DataMarkParam.ParamMapping::getColumn, DataMarkParam.ParamMapping::getName));
            if (DataMarkingTypeEnum.CUSTOMIZE.getCode().equals(param.getLabelType())) {
                if (CollectionUtils.isNotEmpty(robotConfig.getOutputParam())) {
                    for (DataMarkParam.ParamMapping outputParam : robotConfig.getOutputParam()) {
                        AidaLabelTaskBindReferenceDTO reference = new AidaLabelTaskBindReferenceDTO();
                        reference.setAppId(robotConfig.getRobotId());
                        reference.setAppVersion(robotConfig.getRobotVersionId());
                        reference.setInputsMap(inputParamMap);
                        reference.setOutputKey(outputParam.getCode());
                        reference.setTagId(0L);
                        reference.setTagName(outputParam.getName());
                        referenceList.add(reference);
                    }
                } else {
                    AidaLabelTaskBindReferenceDTO reference = new AidaLabelTaskBindReferenceDTO();
                    reference.setAppId(robotConfig.getRobotId());
                    reference.setAppVersion(robotConfig.getRobotVersionId());
                    reference.setInputsMap(inputParamMap);
                    reference.setTagId(0L);
                    reference.setTagName(robotConfig.getRobotVersionName() + "-完整输出");
                    referenceList.add(reference);
                }
            } else {
                AidaLabelTaskBindReferenceDTO reference = new AidaLabelTaskBindReferenceDTO();
                reference.setAppId(robotConfig.getRobotId());
                reference.setAppVersion(robotConfig.getRobotVersionId());
                reference.setInputsMap(inputParamMap);
                reference.setOutputKey(robotConfig.getOutputKey());
                reference.setTagId(robotConfig.getTagId());
                reference.setTagName(robotConfig.getTagName());
                referenceList.add(reference);
            }
        }
        return referenceList;
    }

    /**
     * 保存打标信息到任务的extraInfo字段
     *
     * @param processTypeEnum   处理类型枚举
     * @param ruleConfig        规则配置
     * @param dataProcessTaskId 数据处理任务ID
     */
    private DataProcessConfigWithBLOBs saveDataProcessConfig(ProcessTypeEnum processTypeEnum, String ruleConfig, Long dataProcessTaskId) {
        Date now = new Date();
        // 构建打标信息对象
        DataProcessConfigWithBLOBs dataProcessConfig = new DataProcessConfigWithBLOBs();
        dataProcessConfig.setCreatorMis(UserUtils.getUser().getLogin());
        dataProcessConfig.setCreatorName(UserUtils.getUser().getName());
        dataProcessConfig.setProcessType((byte) processTypeEnum.getCode());
        dataProcessConfig.setCreateTime(now);
        dataProcessConfig.setUpdateTime(now);
        dataProcessConfig.setStatus((byte) DataProcessConfigStatusEnum.PROCESSING.getCode());
        dataProcessConfig.setTaskId(dataProcessTaskId);
        dataProcessConfig.setRuleConfig(ruleConfig);

        dataProcessConfigRepository.createDataProcessConfig(dataProcessConfig);
        return dataProcessConfig;
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     */
    private void updateTaskStatus(Long taskId, ProcessTaskStatusEnum status) {
        try {
            Date now = new Date();
            DataProcessTask updateTask = new DataProcessTask();
            updateTask.setId(taskId);
            updateTask.setStatus(status.getCode());
            updateTask.setUpdateTime(now);

            dataProcessTaskRepository.updateDataProcessTask(updateTask);
            log.info("任务状态更新成功, 任务ID: {}, 状态: {}", taskId, status.getDesc());
        } catch (Exception e) {
            log.error("更新任务状态失败, 任务ID: {}, 状态: {}, 异常信息: {}", taskId, status.getDesc(), e.getMessage(), e);
        }
    }

    /**
     * 将AidaTagDTO转换为LabelInfo
     *
     * @param aidaTagDTO 外部标签DTO
     * @return 内部标签信息
     */
    private LabelInfo convertToLabelInfo(AidaDataTagDTO aidaTagDTO) {
        if (Objects.isNull(aidaTagDTO)) {
            return null;
        }

        LabelInfo labelInfo = new LabelInfo();
        labelInfo.setLabelId(String.valueOf(aidaTagDTO.getId()));
        labelInfo.setLabelName(aidaTagDTO.getTagName());
        return labelInfo;
    }

    /**
     * 将AidaMarkBotInfoDTO列表转换为NonCustomRobotDTO
     *
     * @param labelId         标签ID
     * @param markBotInfoList 机器人信息列表
     * @return NonCustomRobotDTO
     */
    private NonCustomRobotDTO convertMarkBotInfoListToDTO(String labelId, List<AidaMarkBotInfoDTO> markBotInfoList) {
        if (StringUtils.isBlank(labelId) || CollectionUtils.isEmpty(markBotInfoList)) {
            return null;
        }

        NonCustomRobotDTO robotDTO = new NonCustomRobotDTO();
        robotDTO.setLabelId(labelId);

        // 转换机器人配置列表
        List<NonCustomRobotDTO.RobotConfig> robotConfigs = markBotInfoList.stream()
                .filter(Objects::nonNull)
                .map(this::convertMarkBotInfoToDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        robotDTO.setRobotConfigList(robotConfigs);
        return robotDTO;
    }

    /**
     * 将AidaMarkBotInfoDTO转换为RobotConfig
     *
     * @param markBotInfo 机器人信息
     * @return RobotConfig
     */
    private NonCustomRobotDTO.RobotConfig convertMarkBotInfoToDTO(AidaMarkBotInfoDTO markBotInfo) {
        if (Objects.isNull(markBotInfo)) {
            return null;
        }

        VersionConfigDTO versionConfig = null;
        try {
            versionConfig = aidaRemoteService.versionConfigByVersionId(markBotInfo.getVersion());
        } catch (Exception e) {
            log.error("convertMarkBotInfoToDTO error, markBotInfo:{}", markBotInfo, e);
        }


        NonCustomRobotDTO.RobotConfig config = new NonCustomRobotDTO.RobotConfig();
        // 使用反射作为临时解决方案，实际使用时应该直接调用getter方法
        config.setRobotId(markBotInfo.getAppId());
        config.setRobotName(versionConfig != null ? versionConfig.getAppName() : "未获取到应用名称");
        config.setRobotVersionId(markBotInfo.getVersion());
        config.setRobotVersionName(versionConfig != null ? versionConfig.getVersionName() : "未获取到应用版本名称");
        config.setOutputKey(markBotInfo.getOutputKey());
        return config;
    }

    @Override
    public void updateSignal(SignalUpdateParam param) {
        checkSignalUpdateParam(param);
        DataProcessTask dataProcessTask = dataProcessTaskRepository.getDataProcessTaskById(param.getDatasetTaskId());
        CheckUtil.paramCheck(Objects.nonNull(dataProcessTask), "数据处理任务不存在");
        DataSet dataSet = dataSetRepository.getDataSetById(dataProcessTask.getDatasetId());
        CheckUtil.paramCheck(dataSet.getDataSource() == DataSourceEnum.ONLINE.getCode(), "只有数据来源为线上拉取的数据集才能执行信号回刷");

        // 获取锁
        boolean success = versionLockService.tryLock(dataProcessTask.getDatasetId(), dataProcessTask.getProcessDatasetVersionId(), "signal_refresh");
        CheckUtil.operationCheck(success, "当前数据正在进行别的操作，请等待其他操作完成");

        // 找到数据获取任务配置，解析配置的字段名称
        DataFetchTask dataFetchTask = dataFetchTaskRepository.getTaskById(dataSet.getFetchId());
        String columnName = getColumnName(param, dataFetchTask);
        // 创建数据处理配置
        DataProcessConfigWithBLOBs dataProcessConfig = saveDataProcessConfig(ProcessTypeEnum.SIGNAL_REFRESH, JSONObject.toJSONString(param), param.getDatasetTaskId());
        // 更新数据处理任务的状态
        updateTaskStatus(param.getDatasetTaskId(), ProcessTaskStatusEnum.SIGNAL_REFRESHING);

        param.setCreateMis(UserUtils.getUser().getLogin());
        param.setCreateName(UserUtils.getUser().getName());
        // 异步执行信号回刷
        SIGNAL_REFRESH_EXECUTOR.submit(() -> {
            try {
                refreshSignal(param, dataProcessTask, dataSet, columnName);
                // 更新数据处理任务状态
                updateTaskStatus(param.getDatasetTaskId(), ProcessTaskStatusEnum.SIGNAL_REFRESH_COMPLETE);
                // 更新数据配置状态
                dataProcessConfig.setStatus((byte) DataProcessConfigStatusEnum.PROCESSED.getCode());
                dataProcessConfigRepository.updateDataProcessConfig(dataProcessConfig);
                // 发送大象通知
                String url = Lion.getString(ConfigUtil.getAppkey(), LionConstant.DATA_PROCESS_TASK_MANAGE_URL);
                String message = String.format("数据处理任务【%s】信号回刷完成，[点击查看|%s]", dataProcessTask.getTaskName(), url);
                pushElephantService.pushElephant(message, param.getCreateMis());
            } catch (Exception e) {
                log.error("信号回刷异常, 任务ID: {}, 异常信息: {}", param.getDatasetTaskId(), e.getMessage(), e);
                // 更新数据处理任务的状态为信号回刷失败
                updateTaskStatus(param.getDatasetTaskId(), ProcessTaskStatusEnum.SIGNAL_REFRESH_FAILED);
                // 更新数据配置状态为失败
                dataProcessConfig.setStatus((byte) DataProcessConfigStatusEnum.PROCESS_FAILED.getCode());
                dataProcessConfigRepository.updateDataProcessConfig(dataProcessConfig);
                // 发送大象通知失败
                String message = String.format("数据处理任务【%s】信号回刷失败, 失败原因：%s", dataProcessTask.getTaskName(), e.getMessage());
                pushElephantService.pushElephant(message, param.getCreateMis());
            } finally {
                versionLockService.releaseLock(dataProcessTask.getDatasetId(), dataProcessTask.getProcessDatasetVersionId(), "signal_refresh");
            }
        });
    }

    /**
     * 检查信号更新参数
     *
     * @param param 参数对象
     */
    private void checkSignalUpdateParam(SignalUpdateParam param) {
        CheckUtil.paramCheck(Objects.nonNull(param), "参数不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getDatasetTaskId()), "数据集任务ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getRobotConfig()), "机器人配置不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getRobotConfig().getRobotId()), "机器人ID不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getRobotConfig().getRobotVersionId()), "机器人版本ID不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getRobotConfig().getApiToken()), "机器人API Token不能为空");
    }

    /**
     * 获取字段名称
     *
     * @param param         请求参数
     * @param dataFetchTask 数据获取任务
     * @return 字段名称
     */
    private String getColumnName(SignalUpdateParam param, DataFetchTask dataFetchTask) {
        String outputFields = dataFetchTask.getOutputFields();
        List<DataFetchTaskCreateOutputFieldDTO> outputFieldList = JSON.parseArray(outputFields, DataFetchTaskCreateOutputFieldDTO.class);
        Map<String, DataFetchTaskCreateOutputFieldDTO> outputFieldMap = outputFieldList.stream().collect(Collectors.toMap(DataFetchTaskCreateOutputFieldDTO::getRobotId, Function.identity()));
        if (!outputFieldMap.containsKey(param.getRobotConfig().getRobotId())) {
            return param.getRobotConfig().getRobotVersionName();
        }
        return outputFieldMap.get(param.getRobotConfig().getRobotId()).getFieldName();
    }

    private void refreshSignal(SignalUpdateParam param, DataProcessTask dataProcessTask, DataSet dataSet, String columnName) {
        // 查询数据集版本信息
        DataSetVersion dataSetVersion = dataSetVersionRepository.getById(dataProcessTask.getProcessDatasetVersionId());
        DatasetHeadConfig headConfig = JSON.parseObject(dataSetVersion.getHeadConfig(), DatasetHeadConfig.class);
        // 增加时间后缀表示新名字
        String newName = columnName + "-" + DateUtil.formatDate(new Date(), DateUtil.DATE_PATTERN_YYYY_MM_DD);

        DataSetPageQueryParam esSearchParam = new DataSetPageQueryParam();
        esSearchParam.setIsScrollSearch(true);
        esSearchParam.setDataSetId(dataProcessTask.getDatasetId());
        esSearchParam.setVersionId(dataProcessTask.getProcessDatasetVersionId());
        esSearchParam.setPageSize(100);
        // 分批查询并调用ai搭进行信号回刷
        while (true) {
            DatasetQueryResultDTO result = caseAnalysisService.pageQueryDataSet(esSearchParam);
            if (result != null && result.getPageData() != null && CollectionUtils.isNotEmpty(result.getPageData().getData())) {
                List<DataSetRecordParam> updateRecordList = new ArrayList<>();
                for (DataSetRecordParam record : result.getPageData().getData()) {
                    Map<String, Object> content = record.getContent();
                    Map<String, Object> paramMap = new HashMap<>();
                    List<MockRunParamDTO> mockRunParamList = new ArrayList<>();
                    if (content.containsKey(DataSetFieldConstants.FIELD_INPUTS)) {
                        Object inputs = content.get(DataSetFieldConstants.FIELD_INPUTS);
                        if (inputs != null) {
                            paramMap = JSONObject.parseObject(JSONObject.toJSONString(inputs));
                        }
                    }
                    String extraInfo = null;
                    if (content.containsKey(DataSetFieldConstants.FIELD_APPLICATION_INVOKE_INFO)) {
                        Object applicationInvokeInfo = content.get(DataSetFieldConstants.FIELD_APPLICATION_INVOKE_INFO);
                        if (applicationInvokeInfo != null) {
                            Map<String, Object> aidaRobotInfo = JSONObject.parseObject(JSONObject.toJSONString(applicationInvokeInfo));
                            if (aidaRobotInfo.containsKey(param.getRobotConfig().getRobotId())) {
                                Object robotInfo = aidaRobotInfo.get(param.getRobotConfig().getRobotId());
                                AidaRobotInfoParam aidaRobotInfoParam = JSONObject.parseObject(JSONObject.toJSONString(robotInfo), AidaRobotInfoParam.class);
                                if (StringUtils.isNotBlank(aidaRobotInfoParam.getExtraInfo())) {
                                    extraInfo = aidaRobotInfoParam.getExtraInfo();
                                }
                                if (CollectionUtils.isNotEmpty(aidaRobotInfoParam.getToolOutput())) {
                                    mockRunParamList = aidaRobotInfoParam.getToolOutput();
                                }
                            }
                        }
                    }
                    MockConfigDTO mockConfigDTO = new MockConfigDTO();
                    mockConfigDTO.setMockReturnOpen(false);
                    mockConfigDTO.setMockRunOpen(true);
                    mockConfigDTO.setMockRunParams(mockRunParamList);
                    AidaFinalRes response = aidaAppInvokeHelper.invokeAida(param.getRobotConfig().getRobotId(), param.getRobotConfig().getRobotVersionId(), param.getRobotConfig().getApiToken(), paramMap, extraInfo, mockConfigDTO);
                    if (response != null && StringUtils.isNotBlank(response.getAnswer())) {
                        DataSetRecordParam updateRecord = new DataSetRecordParam();
                        Map<String, Object> updateContent = new HashMap<>();
                        updateContent.put(newName, response.getAnswer());
                        updateRecord.setContent(updateContent);
                        updateRecord.setDataId(record.getDataId());
                        updateRecordList.add(updateRecord);
                    }
                }
                // 如果有执行结果，回刷信号
                if (CollectionUtils.isNotEmpty(updateRecordList)) {
                    // 构造新的表头
                    List<DataFieldParam> headList = new ArrayList<>();
                    DataFieldParam dataFieldParam = new DataFieldParam();
                    dataFieldParam.setColumnName(newName);
                    headList.add(dataFieldParam);
                    DatasetHeadConfig newHeadConfig = dataSetManagementService.handleHeadList(headConfig, headList, updateRecordList);
                    // 更新数据库中新表头
                    dataSetVersion.setHeadConfig(JSON.toJSONString(newHeadConfig));
                    dataSetVersionRepository.updateSelectiveById(dataSetVersion);
                    // 批量更新数据
                    List<DatasetEsIndex> esIndexList = dataSetManagementService.buildDatasetEsIndices(dataProcessTask.getDatasetId(), dataProcessTask.getProcessDatasetVersionId(), dataSet.getDataSource(), updateRecordList, newHeadConfig, null, new Date());
                    datasetEsIndexService.batchUpdateDatasets(esIndexList, DataSourceEnum.getValueByCode(dataSet.getDataSource()), dataSetVersion.getEsIndexName());
                }
            }
            if (result == null || result.getHasNext() == null || !result.getHasNext()) {
                break;
            }
            esSearchParam.setScrollId(result.getScrollId());
        }
    }
}
