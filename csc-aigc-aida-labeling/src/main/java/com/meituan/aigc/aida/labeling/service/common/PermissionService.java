package com.meituan.aigc.aida.labeling.service.common;

import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 权限服务
 * <AUTHOR>
 */
@Slf4j
@Component
public class PermissionService {

    @Autowired
    private LionConfig lionConfig;

    public List<String> listAllAdministrators() {
        return lionConfig.getAdministratorsList();
    }

    /**
     * 判断当前用户是否是管理员
     * @return true 是管理员，false 不是管理员
     */
    public boolean isAdminUser() {
        User user = UserUtils.getUser();
        if (Objects.nonNull(user)) {
            String loginMis = user.getLogin();
            // 获取所有管理员
            List<String> allAdmin = listAllAdministrators();
            // 判断用户是否是管理员
            if (allAdmin.contains(loginMis)) {
                log.info("当前用户 {} 是管理员", loginMis);
                return true;
            } else {
                log.info("当前用户 {} 不是管理员", loginMis);
                return false;
            }
        }
        return false;
    }
}
