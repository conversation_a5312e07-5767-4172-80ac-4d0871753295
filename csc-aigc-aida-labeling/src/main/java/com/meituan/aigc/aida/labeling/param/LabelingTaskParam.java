package com.meituan.aigc.aida.labeling.param;

import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelingTaskParam implements Serializable {

    private MultipartFile file;
    /**
     * 任务类型
     * {@link com.meituan.aigc.aida.labeling.common.enums.LabelingTaskType}
     */
    private Integer taskType;
    /**
     * 数据类型
     * {@link com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType}
     */
    private Integer dataType;
    /**
     * 任务来源
     * {@link com.meituan.aigc.aida.labeling.common.enums.TaskSourceEnum}
     */
    private Integer taskSource;
    /**
     * 标注负责人mis
     */
    private String responsiblePerson;
    /**
     * 标注负责人名称
     */
    private String responsiblePersonName;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 业务类型
     */
    private Long bizType;
    /**
     * 自定义标注项
     */
    private List<List<LabelResult>> labelResultList;
}
