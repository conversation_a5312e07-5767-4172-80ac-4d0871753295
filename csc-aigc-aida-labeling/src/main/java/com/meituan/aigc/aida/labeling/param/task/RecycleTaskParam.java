package com.meituan.aigc.aida.labeling.param.task;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RecycleTaskParam implements Serializable {
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 分组配置列表
     */
    private List<GroupConfig> groupConifgList;

    @Data
    public static class GroupConfig implements Serializable {
        /**
         * 分组ID
         */
        private Long groupId;
        /**
         * 子任务ID
         */
        private Long subTaskId;
    }
}
