package com.meituan.aigc.aida.benchmark.common.enums;

import com.meituan.aigc.aida.common.enums.base.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题长度标签枚举
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Getter
@AllArgsConstructor
public enum QuestionLengthEnum implements BaseEnum<Integer> {

    SHORT(0, "1-5轮"),
    MEDIUM(1, "6-10轮"),
    LONG(2, "10+轮");

    /**
     * 长度标签代码
     */
    private final Integer code;

    /**
     * 长度标签描述
     */
    private final String value;

    /**
     * 根据code获取value
     *
     * @param code 长度标签代码
     * @return 对应的长度标签描述，如果未找到则返回SHORT
     */
    public static String getValueByCode(Integer code) {
        for (QuestionLengthEnum lengthEnum : QuestionLengthEnum.values()) {
            if (lengthEnum.getCode().equals(code)) {
                return lengthEnum.getValue();
            }
        }
        return SHORT.value;
    }

    /**
     * 根据code获取枚举对象
     *
     * @param code 长度标签代码
     * @return 对应的枚举对象，如果未找到则返回SHORT
     */
    public static QuestionLengthEnum getByCode(Integer code) {
        for (QuestionLengthEnum lengthEnum : QuestionLengthEnum.values()) {
            if (lengthEnum.getCode().equals(code)) {
                return lengthEnum;
            }
        }
        return SHORT;
    }
} 