package com.meituan.aigc.aida.labeling.job.dashboard;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.cat.Cat;
import com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics;
import com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics;
import com.meituan.aigc.aida.labeling.dao.model.TaskStatistics;
import com.meituan.aigc.aida.labeling.dao.repository.*;
import com.meituan.aigc.aida.labeling.job.dashboard.param.StatisticalTaskAndLabelingAndCheckParam;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingStatisticsPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonQualityCheckStatisticsPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.TaskStatisticsPO;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/5/21 10:22
 * @Version: 1.0
 * 标注大盘数据统计定时任务
 */
@Slf4j
@CraneConfiguration
public class DashboardDataStatisticalJob {

    private static final String LOG_PRE = "【标注大盘每日数据统计】";

    @Resource
    private PersonLabelingStatisticsRepository personLabelingStatisticsRepository;

    @Resource
    private QualityCheckStatisticsRepository qualityCheckStatisticsRepository;

    @Resource
    private TaskStatisticsRepository taskStatisticsRepository;

    @Resource
    private LabelingDetailRepository labelingDetailRepository;

    @Resource
    private LabelingQualityCheckItemRepository labelingQualityCheckItemRepository;

    @Resource
    private LabelingTaskRepository labelingTaskRepository;


    /**
     * 统计每日标注数据定时任务 包括任务、人员标注数量、人员质检数量三个维度
     * @param param 可选填的参数，填入值时按填入的时间统计数据，未填入值时默认前一天的时间统计数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Crane("labeling.dashboard.dataStatisticalJob")
    public void statisticalTaskAndLabelingAndCheckData(StatisticalTaskAndLabelingAndCheckParam param){
        log.info("{}开始执行，入参：{}", LOG_PRE, JSON.toJSONString(param));
        try {
            // 确定统计时间区间
            Date startTime = null;
            Date endTime = null;
            Date statisticalTime = null;
            if (Objects.nonNull(param)
                    && DateUtil.isValidDateTime(param.getStartTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD)
                    && DateUtil.isValidDateTime(param.getEndTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD)){
                startTime = DateUtil.parseToDate(param.getStartTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
                endTime = DateUtil.parseToDate(param.getEndTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD);
                statisticalTime =startTime;
                if (DateUtil.daysBetween(startTime, endTime) != 1){
                    log.warn("{}起止日期间隔大于1天不可以执行", LOG_PRE);
                    return;
                }
            }
            // 入参没传默认取前一天的时间统计
            startTime = Objects.isNull(startTime) ? DateUtil.getYesterdayStartDate() : startTime;
            endTime = Objects.isNull(endTime) ? DateUtil.getYesterdayEndDate() : endTime;
            statisticalTime = Objects.isNull(statisticalTime) ? DateUtil.getYesterdayStartDate() : statisticalTime;
            log.info("{}开始统计，startTime={}, endTime={}, statisticalTime={}", LOG_PRE,  DateUtil.formatDate(startTime, DateUtil.DATE_PATTERN_YYYY_MM_DD),
                    DateUtil.formatDate(endTime, DateUtil.DATE_PATTERN_YYYY_MM_DD), DateUtil.formatDate(statisticalTime, DateUtil.DATE_PATTERN));
            // 标注数据统计
            statisticsLabelingData(startTime, endTime, statisticalTime);
            // 质检数据统计
            statisticsQualityCheckData(startTime, endTime, statisticalTime);
            // 任务数据统计
            statisticsTaskData(startTime, endTime, statisticalTime);
            log.info("{}执行结束", LOG_PRE);
        } catch (Exception e) {
            Cat.logErrorWithCategory("labeling.dashboard.dataStatisticalJob", e);
            log.error("{}执行异常", LOG_PRE, e);
            throw e;
        }

    }

    public void statisticsTaskData(Date startTime, Date endTime, Date statisticalTime) {
        List<TaskStatisticsPO> statisticsTaskRes = labelingTaskRepository.statisticsTaskData(startTime, endTime);
        List<TaskStatistics> taskStatisticsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(statisticsTaskRes)) {
            statisticsTaskRes.forEach(task -> {
                TaskStatistics taskStatistics = new TaskStatistics();
                taskStatistics.setStatDate(statisticalTime);
                taskStatistics.setBizType(task.getBizType());
                taskStatistics.setTaskType(task.getTaskType());
                taskStatistics.setDataType(task.getDataType());
                taskStatistics.setTaskId(task.getTaskId());
                taskStatistics.setTaskName(task.getTaskName());
                taskStatistics.setLabelingStatus(task.getLabelingStatus());
                taskStatistics.setQualityCheckStatus(task.getQualityCheckStatus());
                taskStatistics.setTotalSampleCount(task.getTotalSampleCount());
                taskStatistics.setCreateTime(new Date());
                taskStatistics.setUpdateTime(new Date());
                taskStatisticsList.add(taskStatistics);
            });
        }
        taskStatisticsRepository.batchInsert(taskStatisticsList);
    }

    public void statisticsQualityCheckData(Date startTime, Date endTime, Date statisticalTime) {
        List<PersonQualityCheckStatisticsPO> personQualityCheckStatistics = labelingQualityCheckItemRepository.statisticsPersonQualityCheckData(startTime, endTime);
        List<QualityCheckStatistics> qualityCheckStatisticsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(personQualityCheckStatistics)) {
            personQualityCheckStatistics.forEach(check -> {
                QualityCheckStatistics qualityCheckStatistics = new QualityCheckStatistics();
                qualityCheckStatistics.setStatDate(statisticalTime);
                qualityCheckStatistics.setBizType(check.getBizType());
                qualityCheckStatistics.setTaskType(check.getTaskType());
                qualityCheckStatistics.setDataType(check.getDataType());
                qualityCheckStatistics.setQualityCheckerName(check.getQualityCheckerName());
                qualityCheckStatistics.setQualityCheckerMis(check.getQualityCheckerMis());
                qualityCheckStatistics.setQualityCheckCount(check.getQualityCheckCount());
                qualityCheckStatistics.setQualityCheckCorrectCount(check.getQualityCheckCorrectCount());
                qualityCheckStatistics.setCreateTime(new Date());
                qualityCheckStatistics.setUpdateTime(new Date());
                qualityCheckStatisticsList.add(qualityCheckStatistics);
            });
        }
        qualityCheckStatisticsRepository.batchInsert(qualityCheckStatisticsList);
    }

    public void statisticsLabelingData(Date startTime, Date endTime, Date statisticalTime) {
        List<PersonLabelingStatisticsPO> statisticsPersonLabelingRes = labelingDetailRepository.statisticsPersonLabelingData(startTime, endTime);
        List<PersonLabelingStatistics> personLabelingStatisticsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(statisticsPersonLabelingRes)) {
            statisticsPersonLabelingRes.forEach(labeling -> {
                PersonLabelingStatistics personLabelingStatistics = new PersonLabelingStatistics();
                personLabelingStatistics.setStatDate(statisticalTime);
                personLabelingStatistics.setBizType(labeling.getBizType());
                personLabelingStatistics.setTaskType(labeling.getTaskType());
                personLabelingStatistics.setDataType(labeling.getDataType());
                personLabelingStatistics.setLabelerName(labeling.getLabelerName());
                personLabelingStatistics.setLabelerMis(labeling.getLabelerMis());
                personLabelingStatistics.setLabelingCount(labeling.getLabelingCount());
                personLabelingStatistics.setQcInspectedCount(labeling.getQcInspectedCount());
                personLabelingStatistics.setQcInspectedCorrectCount(labeling.getQcInspectedCorrectCount());
                personLabelingStatistics.setCreateTime(new Date());
                personLabelingStatistics.setUpdateTime(new Date());
                personLabelingStatisticsList.add(personLabelingStatistics);
            });
        }
        personLabelingStatisticsRepository.batchInsert(personLabelingStatisticsList);
    }


}
