package com.meituan.aigc.aida.labeling.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guowenhui
 * @Create: 2025/3/5 14:29
 * @Version: 1.0
 */
@Slf4j
public class LabelResultCompareUtil {

    /**
     * 枚举类型常量
     */
    public static final Integer ENUM_DATA_TYPE = CommonConstants.DataStatus.ENUM_DATA_TYPE;

    /**
     * 背对背一致率标注项
     */
    public static final Integer IS_COMPARE = CommonConstants.DataStatus.IS_COMPARE;

    public static boolean compareLabelValues(String labelResult1, String labelResult2, Boolean isCompare) {
        if (StringUtils.isBlank(labelResult1) || StringUtils.isBlank(labelResult2)) {
            return false;
        }
        return compareLabelValues(JSON.parseObject(labelResult1, new TypeReference<List<List<LabelResult>>>() {
                }),
                JSON.parseObject(labelResult2, new TypeReference<List<List<LabelResult>>>() {
                }), isCompare);
    }

    /**
     * 比较两个级联指标列表是否一致
     * 只比较枚举类型的值，任一项不同就返回false
     *
     * @param labelResultList1 第一个级联指标列表
     * @param labelResultList2 第二个级联指标列表
     *
     * @return 如果所有枚举类型的值都相同返回true，否则返回false
     */
    public static boolean compareLabelValues(List<List<LabelResult>> labelResultList1,
                                             List<List<LabelResult>> labelResultList2, Boolean isCompare) {
        try {
            // 空值检查
            if (labelResultList1 == null || labelResultList2 == null) {
                log.warn("比较的级联指标列表存在null值");
                return false;
            }

            // 长度检查
            if (labelResultList1.size() != labelResultList2.size()) {
                log.info("级联指标列表长度不同: {} vs {}", labelResultList1.size(), labelResultList2.size());
                return false;
            }

            // 如果两个列表都为空，则认为相等
            if (labelResultList1.isEmpty() && labelResultList2.isEmpty()) {
                return true;
            }

            // 预处理：过滤空组和无效组
            List<List<LabelResult>> filteredList1 = filterValidGroups(labelResultList1);
            List<List<LabelResult>> filteredList2 = filterValidGroups(labelResultList2);

            if (filteredList1.size() != filteredList2.size()) {
                log.info("有效标注项组数量不同: {} vs {}", filteredList1.size(), filteredList2.size());
                return false;
            }

            // 将两个列表转换为更合适的结构进行比较
            Map<String, List<LabelResult>> groupMap1 = convertToGroupMap(filteredList1);
            Map<String, List<LabelResult>> groupMap2 = convertToGroupMap(filteredList2);

            // 检查组数量是否一致
            if (groupMap1.size() != groupMap2.size()) {
                log.info("标注项组数量不同: {} vs {}", groupMap1.size(), groupMap2.size());
                return false;
            }

            // 比较每个组
            for (String groupKey : groupMap1.keySet()) {
                if (!groupMap2.containsKey(groupKey)) {
                    log.info("第二个列表中不存在组: {}", groupKey);
                    return false;
                }

                List<LabelResult> group1 = groupMap1.get(groupKey);
                List<LabelResult> group2 = groupMap2.get(groupKey);

                // 比较组内的枚举类型标注项
                if (!compareEnumItemsInGroup(group1, group2, isCompare)) {
                    return false;
                }
            }

            // 所有枚举类型的值都相同
            return true;
        } catch (Exception e) {
            log.error("比较级联指标时发生异常", e);
            return false;
        }
    }

    /**
     * 过滤有效的标注项组
     *
     * @param labelResultList 标注项组列表
     * @return 过滤后的列表
     */
    private static List<List<LabelResult>> filterValidGroups(List<List<LabelResult>> labelResultList) {
        return labelResultList.stream()
                .filter(group -> !CollectionUtils.isEmpty(group))
                .filter(group -> group.stream().anyMatch(item -> item != null && item.getId() != null))
                .collect(Collectors.toList());
    }

    /**
     * 比较组内的枚举类型标注项
     *
     * @param group1 第一个组
     * @param group2 第二个组
     * @return 如果所有枚举类型的值都相同返回true，否则返回false
     */
    private static boolean compareEnumItemsInGroup(List<LabelResult> group1, List<LabelResult> group2,
                                                   Boolean isCompare) {
        // 将组内标注项转换为Map，便于查找
        Map<Long, LabelResult> itemMap1 = group1.stream()
                .filter(item -> item != null && item.getId() != null)
                .collect(Collectors.toMap(LabelResult::getId, item -> item, (v1, v2) -> v1));

        Map<Long, LabelResult> itemMap2 = group2.stream()
                .filter(item -> item != null && item.getId() != null)
                .collect(Collectors.toMap(LabelResult::getId, item -> item, (v1, v2) -> v1));

        // 检查枚举类型标注项
        Set<Long> allItemIds = new HashSet<>();
        allItemIds.addAll(itemMap1.keySet());
        allItemIds.addAll(itemMap2.keySet());

        for (Long itemId : allItemIds) {
            // 检查标注项是否存在于两个列表中
            if (!itemMap1.containsKey(itemId)) {
                log.info("标注项ID {} 在第一个列表中不存在", itemId);
                return false;
            }
            if (!itemMap2.containsKey(itemId)) {
                log.info("标注项ID {} 在第二个列表中不存在", itemId);
                return false;
            }

            LabelResult item1 = itemMap1.get(itemId);
            LabelResult item2 = itemMap2.get(itemId);

            // 只对比背对背标注项
            if (Boolean.TRUE.equals(isCompare)) {
                if (!IS_COMPARE.equals(item1.getIsCompare())) {
                    continue;
                }
            } else {
                // 只比较枚举类型
                if (!ENUM_DATA_TYPE.equals(item1.getDataType())) {
                    continue;
                }
            }
            // 确保两边的数据类型一致
            if (!Objects.equals(item1.getDataType(), item2.getDataType())) {
                log.info("标注项[{}]({})的数据类型不同: {} vs {}",
                        item1.getName(), itemId, item1.getDataType(), item2.getDataType());
                return false;
            }

            // 比较枚举值
            if (!Objects.equals(item1.getValue(), item2.getValue())) {
                log.info("标注项[{}]({})的枚举值不同: {} vs {}",
                        item1.getName(), itemId, item1.getValue(), item2.getValue());
                return false;
            }

            // 如果是父指标，需要检查子指标
            if (item1.getParentId() == null || item1.getParentId() == -1L) {
                // 注意：这里不能直接返回，否则会导致其他父指标不被检查
                boolean childrenMatch = compareChildItems(group1, group2, itemId, item1.getValue(), item2.getValue(), isCompare);
                if (!childrenMatch) {
                    log.info("父标注项[{}]({})的子指标不一致", item1.getName(), itemId);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 比较子指标
     *
     * @param group1       第一个组
     * @param group2       第二个组
     * @param parentId     父指标ID
     * @param parentValue1 第一个父指标的值
     * @param parentValue2 第二个父指标的值
     * @param isCompare    是否只比较背靠背一致率标注项
     * @return 如果所有子指标的枚举值都相同返回true，否则返回false
     */
    private static boolean compareChildItems(List<LabelResult> group1, List<LabelResult> group2,
                                             Long parentId, String parentValue1, String parentValue2, Boolean isCompare) {
        // 获取所有子指标
        List<LabelResult> children1 = getChildrenByParentId(group1, parentId, parentValue1);
        List<LabelResult> children2 = getChildrenByParentId(group2, parentId, parentValue2);

        // 如果父指标没有选择枚举值，则不应该有子指标
        if (parentValue1 == null || parentValue2 == null) {
            return children1.isEmpty() && children2.isEmpty();
        }

        // 比较子指标数量
        if (children1.size() != children2.size()) {
            log.info("父标注项({})的子指标数量不同: {} vs {}",
                    parentId, children1.size(), children2.size());
            return false;
        }

        // 将子指标转换为Map
        Map<Long, LabelResult> childMap1 = children1.stream()
                .collect(Collectors.toMap(LabelResult::getId, c -> c, (v1, v2) -> v1));

        Map<Long, LabelResult> childMap2 = children2.stream()
                .collect(Collectors.toMap(LabelResult::getId, c -> c, (v1, v2) -> v1));

        // 检查所有子指标
        Set<Long> allChildIds = new HashSet<>();
        allChildIds.addAll(childMap1.keySet());
        allChildIds.addAll(childMap2.keySet());

        for (Long childId : allChildIds) {
            // 检查子指标是否存在于两个列表中
            if (!childMap1.containsKey(childId)) {
                log.info("子指标ID {} 在第一个列表中不存在", childId);
                return false;
            }
            if (!childMap2.containsKey(childId)) {
                log.info("子指标ID {} 在第二个列表中不存在", childId);
                return false;
            }

            LabelResult child1 = childMap1.get(childId);
            LabelResult child2 = childMap2.get(childId);

            // 根据比较模式决定是否需要比较当前子指标
            boolean shouldCompare = false;
            if (Boolean.TRUE.equals(isCompare)) {
                // 如果是背靠背比较模式，只比较isCompare=1的指标
                shouldCompare = IS_COMPARE.equals(child1.getIsCompare());
            } else {
                // 如果是普通比较模式，只比较枚举类型
                shouldCompare = ENUM_DATA_TYPE.equals(child1.getDataType());
            }

            if (shouldCompare) {
                // 比较子指标枚举值
                if (!Objects.equals(child1.getValue(), child2.getValue())) {
                    log.info("子指标[{}]({})的枚举值不同: {} vs {}",
                            child1.getName(), childId, child1.getValue(), child2.getValue());
                    return false;
                }
            }

            // 递归比较子指标的子指标（处理多层级级联指标）
            if (child1.getValue() != null && child2.getValue() != null) {
                boolean childrenMatch = compareChildItems(group1, group2, childId, child1.getValue(), child2.getValue(), isCompare);
                if (!childrenMatch) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 将级联指标列表转换为按组分类的Map
     *
     * @param labelResultList 级联指标列表
     * @return 按组分类的Map
     */
    private static Map<String, List<LabelResult>> convertToGroupMap(List<List<LabelResult>> labelResultList) {
        Map<String, List<LabelResult>> groupMap = new HashMap<>();

        for (int i = 0; i < labelResultList.size(); i++) {
            List<LabelResult> group = labelResultList.get(i);
            if (CollectionUtils.isEmpty(group)) {
                continue;
            }

            // 生成组标识
            String groupKey = generateGroupKey(group, i);
            groupMap.put(groupKey, new ArrayList<>(group));
        }

        return groupMap;
    }

    /**
     * 为标注项组生成唯一标识
     *
     * @param group 标注项组
     * @param index 组索引（作为备用标识）
     * @return 组标识
     */
    private static String generateGroupKey(List<LabelResult> group, int index) {
        // 尝试使用组内所有标注项ID的组合作为标识
        String idCombination = group.stream()
                .filter(item -> item != null && item.getId() != null)
                .map(item -> String.valueOf(item.getId()))
                .sorted() // 排序以确保相同ID集合生成相同的标识
                .collect(Collectors.joining("_"));

        if (!idCombination.isEmpty()) {
            return "group_" + idCombination;
        }

        // 如果无法生成基于ID的标识，使用索引作为备用
        return "group_index_" + index;
    }

    /**
     * 根据父指标ID和父指标枚举值获取子指标
     *
     * @param allLabels       所有标注项
     * @param parentId        父指标ID
     * @param parentEnumValue 父指标枚举值
     * @return 符合条件的子指标列表
     */
    private static List<LabelResult> getChildrenByParentId(List<LabelResult> allLabels, Long parentId,
                                                           String parentEnumValue) {
        if (parentEnumValue == null) {
            // 如果父指标没有选择枚举值，则不应该有子指标
            return Collections.emptyList();
        }

        return allLabels.stream()
                .filter(Objects::nonNull)
                .filter(label -> Objects.equals(label.getParentId(), parentId))
                .filter(label -> parentEnumValue.equals(label.getParentEnumValue()))
                .collect(Collectors.toList());
    }

    /**
     * 比较原始数据映射内容
     *
     * @param comparString   待比较字符串
     * @param rawData        原数据
     * @param labelingDetail 标注详情
     * @return 一致返回 true 不一致返回 false
     */
    public static boolean compareRawDataMappedContent(String comparString, LabelingTaskRawData rawData,
                                                      LabelingDetail labelingDetail) {
        if (StringUtils.isBlank(comparString) || Objects.isNull(labelingDetail)) {
            // 返回 true 表示操作成功或条件满足
            return true;
        }
        String compareContent = "";
        // 确定比较的内容来源
        if (StringUtils.isBlank(labelingDetail.getModifiedRawDataMappedContent())) {
            // 如果修改后内容为空，则与原始数据比较
            if (Objects.nonNull(rawData)) {
                compareContent = Optional.ofNullable(rawData.getRawDataMappedContent()).orElse("");
            }
        } else {
            // 否则与修改后内容比较
            compareContent = labelingDetail.getModifiedRawDataMappedContent();
        }
        // 只有当内容不一致时才更新
        return JsonCompareUtil.areEqual(comparString, compareContent);
    }

}