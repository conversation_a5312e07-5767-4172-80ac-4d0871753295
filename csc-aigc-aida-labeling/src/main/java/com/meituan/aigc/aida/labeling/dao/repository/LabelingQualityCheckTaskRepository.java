package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckTask;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckSampleCountPO;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckParam;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:06
 * @Version: 1.0
 */
public interface LabelingQualityCheckTaskRepository {

    /**
     * 插入质检任务
     */
    int insert(LabelingQualityCheckTask labelingQualityCheckTask);

    /**
     * 批量插入质检任务
     *
     * @param qualityCheckTasks 质检任务列表
     * @return 影响行数
     */
    int batchInsert(List<LabelingQualityCheckTask> qualityCheckTasks);

    /**
     * 根据质检任务id查询质检任务列表
     *
     * @param qualityCheckTaskId 质检任务id
     * @return 质检任务列表
     */
    List<LabelingQualityCheckTask> listByTaskId(Long qualityCheckTaskId);

    /**
     * 根据任务id更新质检任务状态
     *
     * @param taskId 任务id
     * @param status 质检状态
     */
    void updateStatusByTaskId(Long taskId, int status);

    /**
     * 根据主键查询
     *
     * @param qualityCheckTaskId 质检任务主键
     * @return 质检任务详情
     */
    LabelingQualityCheckTask getById(Long qualityCheckTaskId);

    /**
     * 根据标注任务id和状态统计数量
     *
     * @param taskId     任务id
     * @param statusList 状态集合
     * @return 数量
     */
    Integer countByTaskIdAndStatus(Long taskId, List<Integer> statusList);

    /**
     * 根据质检任务ID、质检任务名称和质检人查询质检任务列表
     *
     * @param param 质检任务查询参数
     * @return 质检任务列表
     */
    List<QualityCheckTaskPO> listByTaskIdAndNameAndQualityCheckerMis(QualityCheckParam param);

    /**
     * 更新质检任务状态
     *
     * @param id     质检任务id
     * @param status 质检状态
     */
    void updateStatusById(Long id, Integer status);

    /**
     * 根据主键更新质检任务
     *
     * @param labelingQualityCheckTask 质检任务
     */
    void updateById(LabelingQualityCheckTask labelingQualityCheckTask);

    /**
     * 批量更新质检任务
     *
     * @param labelingQualityCheckTasks 质检任务列表
     */
    void batchUpdateSelective(List<LabelingQualityCheckTask> labelingQualityCheckTasks);

    /**
     * 根据质检任务id统计质检样本数量
     *
     * @param taskId 质检任务id
     * @return 质检样本数量
     */
    QualityCheckSampleCountPO countSampleSizeByTaskId(Long taskId);
}
