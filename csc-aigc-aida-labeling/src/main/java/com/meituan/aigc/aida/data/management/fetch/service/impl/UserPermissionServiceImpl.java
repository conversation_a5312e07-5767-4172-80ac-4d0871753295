package com.meituan.aigc.aida.data.management.fetch.service.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.fetch.service.UserPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-07 16:29
 * @description
 */
@Service
@Slf4j
public class UserPermissionServiceImpl implements UserPermissionService {

    @Autowired
    private DataManagementLionConfig dataManagementLionConfig;
    @Override
    public List<String> getPermissions() {
        List<String> permissions = dataManagementLionConfig.getDataBuriedPointsPermissionList();
        if(CollectionUtils.isEmpty(permissions)){
            return Collections.emptyList();
        }
        return permissions;
    }
}
