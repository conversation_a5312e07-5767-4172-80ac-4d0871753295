### Consumer ###
mdp.mafka.consumer[0].appkey=com.sankuai.csccratos.aida.training
mdp.mafka.consumer[0].bgNameSpace=pingtai
mdp.mafka.consumer[0].topicName=ac.interface.invoke.log
mdp.mafka.consumer[0].subscribeGroup=csccratos.aida.training.consumer
mdp.mafka.consumer[0].listenerId=tpyWhiteListMisCallAcNotice

mdp.mafka.consumer[1].appkey=com.sankuai.csccratos.aida.training
mdp.mafka.consumer[1].bgNameSpace=xm
mdp.mafka.consumer[1].topicName=csc.haitun.event.service.message.event
mdp.mafka.consumer[1].subscribeGroup=aida.training.message.consumer
mdp.mafka.consumer[1].listenerId=chatEventMessageListener

mdp.mafka.consumer[2].appkey=com.sankuai.csccratos.aida.training
mdp.mafka.consumer[2].bgNameSpace=com.sankuai.mafka.castle.mtpingtai
mdp.mafka.consumer[2].topicName=aida.training.delay.trigger.robot
mdp.mafka.consumer[2].subscribeGroup=aida.training.message.consumer
mdp.mafka.consumer[2].listenerId=delayTriggerRobotListener


### Producer ###
mdp.mafka.producer[0].appkey=com.sankuai.csccratos.aida.training
mdp.mafka.producer[0].bgNameSpace=com.sankuai.mafka.castle.mtpingtai
mdp.mafka.producer[0].topicName=signal.logging.log
mdp.mafka.producer[0].producerName=signalLogToMafkaProducer

mdp.mafka.producer[1].appkey=com.sankuai.csccratos.aida.training
mdp.mafka.producer[1].bgNameSpace=com.sankuai.mafka.castle.mtpingtai
mdp.mafka.producer[1].topicName=aida.training.delay.trigger.robot
mdp.mafka.producer[1].producerName=delayTriggerRobotProducer
mdp.mafka.producer[1].delay=true

mdp.mafka.producer[2].appkey=com.sankuai.csccratos.aida.training
mdp.mafka.producer[2].bgNameSpace=com.sankuai.mafka.castle.mtpingtai
mdp.mafka.producer[2].topicName=manual.labeling.results
mdp.mafka.producer[2].producerName=manualLabelingMessageProducer