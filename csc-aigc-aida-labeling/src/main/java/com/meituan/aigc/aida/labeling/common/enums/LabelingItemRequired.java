package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-10 11:54
 * @description 是否必填
 */
@Getter
public enum LabelingItemRequired {
    YES(1, "是"),
    NO(2, "否");
    private final int code;
    private final String value;

    LabelingItemRequired(int code, String value) {
        this.code = code;
        this.value = value;
    }
    public static LabelingItemRequired getByCode(int code) {
        for (LabelingItemRequired value : LabelingItemRequired.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
