package com.meituan.aigc.aida.common.enums.base;

import java.util.EnumSet;
import java.util.Objects;

/**
 * 基础枚举接口
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
public interface BaseEnum<T> {

    /**
     * 获取对应的{@code code}
     *
     * @return {@link T}
     */
    T getCode();

    /**
     * 获取对应的{@code value}值
     *
     * @return {@link String}
     */
    String getValue();

    /**
     * @param code        key
     * @param clazz       类型
     * @param defaultEnum 默认枚举
     * @return 根据键获取枚举值, 如果找不到则返回默认枚举值
     */
    static <E extends Enum<E> & BaseEnum> E getByCode(Object code, Class<E> clazz, E defaultEnum) {
        EnumSet<E> all = EnumSet.allOf(clazz);
        return all.stream().filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(defaultEnum);
    }
}