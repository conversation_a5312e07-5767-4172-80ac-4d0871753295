package com.meituan.aigc.aida.data.management.dataset.dao.mapper;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DataSetVersionMapper {
    long countByExample(DataSetVersionExample example);

    int deleteByExample(DataSetVersionExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DataSetVersion record);

    int insertSelective(DataSetVersion record);

    List<DataSetVersion> selectByExample(DataSetVersionExample example);

    DataSetVersion selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DataSetVersion record, @Param("example") DataSetVersionExample example);

    int updateByExample(@Param("record") DataSetVersion record, @Param("example") DataSetVersionExample example);

    int updateByPrimaryKeySelective(DataSetVersion record);

    int updateByPrimaryKey(DataSetVersion record);
}