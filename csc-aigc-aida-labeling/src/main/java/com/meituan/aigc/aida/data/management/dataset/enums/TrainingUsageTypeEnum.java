package com.meituan.aigc.aida.data.management.dataset.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-30 17:28
 * @description 训练用途类型枚举
 */
@Getter
public enum TrainingUsageTypeEnum {

    TEXT_GENERATION(1, "文本生成"),
    IMAGE_GENERATION(2, "图像生成"),
    IMAGE_UNDERSTANDING(3, "图像理解");

    private int code;
    private String desc;

    TrainingUsageTypeEnum(int code,  String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TrainingUsageTypeEnum value : TrainingUsageTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return null;
    }
}

