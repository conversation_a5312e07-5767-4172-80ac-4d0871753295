package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.Data;

/**
 * 信号回刷请求参数
 */
@Data
public class SignalUpdateParam {
    /**
     * 任务ID
     */
    private Long datasetTaskId;

    /**
     * 机器人配置
     */
    private RobotConfig robotConfig;

    /**
     * 创建人mis
     */
    private String createMis;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 机器人配置信息
     */
    @Data
    public static class RobotConfig {
        /**
         * 机器人ID
         */
        private String robotId;

        /**
         * 机器人名称
         */
        private String robotName;

        /**
         * 机器人版本ID
         */
        private String robotVersionId;

        /**
         * 机器人版本名称
         */
        private String robotVersionName;

        /**
         * API令牌
         */
        private String apiToken;
    }
} 