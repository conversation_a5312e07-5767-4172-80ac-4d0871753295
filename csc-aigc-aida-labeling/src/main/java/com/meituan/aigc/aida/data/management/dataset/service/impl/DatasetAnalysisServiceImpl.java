package com.meituan.aigc.aida.data.management.dataset.service.impl;

import com.google.common.collect.Lists;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetFieldUtils;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetDistributeResponseDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFieldListResponseDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFilterPageResponseDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFieldPathInfo;
import com.meituan.aigc.aida.data.management.dataset.dto.FieldMappingDTO;
import com.meituan.aigc.aida.data.management.dataset.service.DatasetAnalysisService;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetQueryConditionUtils;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.data.management.es.query.DatasetPageResult;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetQueryCondition;
import com.meituan.aigc.aida.labeling.remote.common.enums.ConditionTypeEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetVersionService;
import com.meituan.aigc.aida.data.management.dataset.enums.DataSetVersionTypeEnum;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 数据集分析服务实现
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集分析相关业务逻辑实现
 */
@Slf4j
@Service
public class DatasetAnalysisServiceImpl implements DatasetAnalysisService {

    @Resource
    private DataSetVersionRepository dataSetVersionRepository;

    @Resource
    private DatasetEsIndexService datasetEsIndexService;

    @Resource
    private DataSetVersionService dataSetVersionService;

    private static final List<Integer> FLATTEN_FIELD_TYPE = Lists.newArrayList(FieldTypeEnum.JSON.getCode(), FieldTypeEnum.PROCESSED_SIGNAL.getCode());

    @MdpConfig("terms.buckets.size:100")
    private Integer termsBucketsSize;

    private static final String TEXT_PREFIX = "textData";
    private static final String ES_COMMON_DATA_PREFIX = "commonData";

    @Override
    public DatasetDistributeResponseDTO queryDatasetDistribute(DatasetDistributeQueryParam param) {
        log.info("开始查询数据集分布情况, 参数: {}", param);
        List<String> columnPathParts = new ArrayList<>();
        columnPathParts.add(param.getColumnName());

        try {
            // 1. 获取数据集版本信息
            DataSetVersion datasetVersion = getDatasetVersion(param.getDatasetId(), param.getDatasetVersionId());
            // 2. 获取需要统计的字段
            Map<String, FieldMappingDTO> fieldTypeMap = DatasetFieldUtils.convertHeadConfig2FieldMapping(datasetVersion.getHeadConfig());
            DatasetFieldPathInfo fieldPathInfo = DatasetFieldUtils.buildFieldPathInfo(columnPathParts, fieldTypeMap, null);
            DatasetEsQueryCondition datasetEsQueryCondition = new DatasetEsQueryCondition();

            // 3.构建基础查询参数
            buildBasicSearchCondition(datasetEsQueryCondition, datasetVersion, fieldPathInfo);

            // 调用ES查询服务
            DatasetPageResult pageResult = datasetEsIndexService.pageQuery(datasetEsQueryCondition);

            // 4. 解析聚合查询结果并组装返回数据
            DatasetDistributeResponseDTO responseDTO = parseAggregationResult(pageResult);
            log.info("查询数据集分布情况完成, 参数: {}, 结果项数: {}", param, responseDTO.getResultList().size());
            return responseDTO;

        } catch (Exception e) {
            log.error("查询数据集分布情况失败, 参数: {}", param, e);
            throw new RuntimeException("查询数据集分布情况失败", e);
        }
    }

    @Override
    public DatasetFieldListResponseDTO querySubFields(DatasetDistributeQueryParam param) {
        log.info("开始查询一级字段下的二级字段列表, 参数: {}", param);

        try {
            // 1. 获取数据集版本信息
            DataSetVersion datasetVersion = getDatasetVersion(param.getDatasetId(), param.getDatasetVersionId());
            if (Objects.isNull(datasetVersion) || StringUtils.isBlank(datasetVersion.getHeadConfig())) {
                log.error("数据集版本信息为空, DatasetDistributeQueryParam: {}", param);
                throw new RuntimeException("数据集版本信息为空");
            }

            // 2. 获取表头信息
            Map<String, FieldMappingDTO> fieldTypeMap = DatasetFieldUtils.convertHeadConfig2FieldMapping(datasetVersion.getHeadConfig());
            if (MapUtils.isEmpty(fieldTypeMap) || !fieldTypeMap.containsKey(param.getColumnName())) {
                log.error("表头信息为空或一级字段不存在, 参数: {}", param);
                throw new RuntimeException("表头信息为空或一级字段不存在");
            }

            // 3. 构建返回结果
            FieldMappingDTO fieldMappingDTO = fieldTypeMap.get(param.getColumnName());
            DatasetFieldListResponseDTO responseDTO = new DatasetFieldListResponseDTO();
            responseDTO.setFieldList(fieldMappingDTO.getFirstLevelJsonKey());

            log.info("查询一级字段下的二级字段列表完成, 参数: {}, 二级字段数量: {}", param, fieldMappingDTO.getFirstLevelJsonKey().size());
            return responseDTO;

        } catch (Exception e) {
            log.error("查询一级字段下的二级字段列表失败, 参数: {}", param, e);
            throw new RuntimeException("查询字段列表失败", e);
        }
    }

    @Override
    public DatasetFilterPageResponseDTO queryDatasetPage(DatasetFilterPageQueryParam param) {
        log.info("开始执行数据集筛选分页查询, 参数: {}", param);

        try {
            // 1. 获取数据集版本信息
            DataSetVersion datasetVersion = getDatasetVersion(param.getDatasetId(), param.getDatasetVersionId());

            // 2. 获取表头信息
            Map<String, FieldMappingDTO> fieldTypeMap = DatasetFieldUtils.convertHeadConfig2FieldMapping(datasetVersion.getHeadConfig());

            // 3. 构建ES查询条件
            DatasetEsQueryCondition condition = new DatasetEsQueryCondition();

            // 3.1 设置基本查询条件
            buildDataFilterSearchCondition(param, condition, fieldTypeMap, datasetVersion);

            // 3.2 设置返回字段
            configureReturnFields(condition, param.getColumnList(), fieldTypeMap);

            // 4. 执行ES查询
            DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);

            // 5. 构建返回结果
            DatasetFilterPageResponseDTO responseDTO = buildFilterPageResponse(pageResult, param);

            log.info("数据集筛选分页查询完成, 参数: {}, 返回数据条数: {}", param,
                    responseDTO.getData() != null ? responseDTO.getData().size() : 0);
            return responseDTO;

        } catch (Exception e) {
            log.error("数据集筛选分页查询失败, 参数: {}", param, e);
            throw new RuntimeException("数据集筛选分页查询失败", e);
        }
    }

    @Override
    public void saveDataset(DatasetSaveParam param) {
        log.info("开始保存数据集, 参数: {}", param);

        try {
            // 1. 参数校验
            if (StringUtils.isBlank(param.getNewVersionName())) {
                log.error("保存数据集失败: 新版本名称为空, 参数: {}", param);
                throw new RuntimeException("新版本名称不能为空");
            }

            // 2. 获取历史版本信息，验证版本是否存在
            DataSetVersion historyVersion = getDatasetVersion(param.getDatasetId(), param.getHistoryVersionId());

            // 3. 构建新增版本版本信息
            DataSetCopyVersionParam copyVersionParam = new DataSetCopyVersionParam();
            copyVersionParam.setDataSetId(param.getDatasetId());
            copyVersionParam.setVersionId(param.getHistoryVersionId());
            copyVersionParam.setVersionName(param.getNewVersionName());
            copyVersionParam.setVersionType(DataSetVersionTypeEnum.INSIGHT_EXPORT.getCode());
            copyVersionParam.setVersionDescription("洞察与分析导出版本");

            // 4. 设置筛选条件
            if (CollectionUtils.isNotEmpty(param.getConditionList())) {
                copyVersionParam.setConditionList(param.getConditionList());
                log.info("设置筛选条件, 条件数量: {}", param.getConditionList().size());
            }

            // 5. 设置保存的列
            if (CollectionUtils.isNotEmpty(param.getColumnList())) {
                copyVersionParam.setSavedColumnList(param.getColumnList());
                log.info("设置保存列, 列数量: {}", param.getColumnList().size());
            }

            // 6. 执行复制版本操作
            Long newVersionId = dataSetVersionService.copyVersion(copyVersionParam);

            log.info("保存数据集成功, datasetId: {}, historyVersionId: {}, newVersionId: {}, newVersionName: {}",
                    param.getDatasetId(), param.getHistoryVersionId(), newVersionId, param.getNewVersionName());

        } catch (Exception e) {
            log.error("保存数据集失败, 参数: {}", param, e);
            throw new RuntimeException("保存数据集失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建数据筛选查询条件
     *
     * @param condition      查询条件对象
     * @param fieldTypeMap   字段类型映射
     * @param datasetVersion 数据集版本信息
     */
    private void buildDataFilterSearchCondition(DatasetFilterPageQueryParam param, DatasetEsQueryCondition condition, Map<String, FieldMappingDTO> fieldTypeMap, DataSetVersion datasetVersion) {
        if (Objects.isNull(datasetVersion) || MapUtils.isEmpty(fieldTypeMap)) {
            return;
        }
        // 设置基本查询参数
        condition.setIndexName(datasetVersion.getEsIndexName());
        condition.addTermCondition(CommonConstants.BaseEsIndex.VERSION_ID, CommonConstants.BaseEsIndex.VERSION_ID, datasetVersion.getId().toString());
        condition.addTermCondition(CommonConstants.BaseEsIndex.DATASET_ID, CommonConstants.BaseEsIndex.DATASET_ID, datasetVersion.getDataSetId().toString());
        condition.addTermCondition(CommonConstants.BaseEsIndex.DATA_SOURCE, CommonConstants.BaseEsIndex.DATA_SOURCE, String.valueOf(DataSourceEnum.fromValue(datasetVersion.getVersionSource()).getCode()));

        // 构建筛选条件
        for (DataSetQueryCondition queryCondition : param.getConditionList()) {
            DatasetFieldPathInfo pathInfo = DatasetFieldUtils.buildFieldPathInfo(queryCondition.getColumnName(), fieldTypeMap, null);
            DatasetQueryConditionUtils.addQueryCondition(queryCondition, condition, pathInfo);
        }
    }


    /**
     * 获取数据集版本信息
     *
     * @param datasetId        数据集ID
     * @param datasetVersionId 数据集版本ID
     * @return 数据集版本信息
     */
    private DataSetVersion getDatasetVersion(Long datasetId, Long datasetVersionId) {
        if (Objects.isNull(datasetVersionId) || Objects.isNull(datasetId)) {
            log.error("getDatasetVersion-数据集版本信息为空");
            throw new RuntimeException("数据集信息为空");
        }

        DataSetVersion version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(datasetId, datasetVersionId);
        if (Objects.isNull(version) || StringUtils.isBlank(version.getHeadConfig())) {
            log.error("数据集版本信息为空, datasetId = {}, datasetVersionId = {}", datasetId, datasetVersionId);
            throw new RuntimeException("数据集版本信息为空");
        }
        return version;
    }

    /**
     * 解析ES聚合查询结果
     *
     * @param pageResult ES查询结果
     * @return 数据集分布响应DTO
     */
    private DatasetDistributeResponseDTO parseAggregationResult(DatasetPageResult pageResult) {
        DatasetDistributeResponseDTO responseDTO = new DatasetDistributeResponseDTO();
        List<DatasetDistributeResponseDTO.DistributeResultItem> resultList = new ArrayList<>();

        if (pageResult != null && pageResult.getTermsAggResult() != null && !pageResult.getTermsAggResult().isEmpty()) {
            Map<String, Map<String, Long>> termsAggResult = pageResult.getTermsAggResult();

            // 遍历聚合结果
            for (Map.Entry<String, Map<String, Long>> aggEntry : termsAggResult.entrySet()) {
                String aggKey = aggEntry.getKey();
                Map<String, Long> buckets = aggEntry.getValue();

                if (buckets != null && !buckets.isEmpty()) {
                    // 计算总数，用于计算占比
                    long totalCount = buckets.values().stream().mapToLong(Long::longValue).sum();

                    // 构建分组项列表
                    List<DatasetDistributeResponseDTO.DistributeGroupItem> groupList = buildDistributeGroupList(buckets, totalCount);

                    // 构建分布结果项
                    DatasetDistributeResponseDTO.DistributeResultItem resultItem =
                            new DatasetDistributeResponseDTO.DistributeResultItem(aggKey.substring(aggKey.lastIndexOf('.') + 1), groupList);
                    resultList.add(resultItem);
                }
            }
        }

        responseDTO.setResultList(resultList);
        return responseDTO;
    }

    /**
     * 构建分布分组项列表
     *
     * @param buckets    ES聚合桶数据
     * @param totalCount 总数量
     * @return 分组项列表
     */
    private List<DatasetDistributeResponseDTO.DistributeGroupItem> buildDistributeGroupList(Map<String, Long> buckets, long totalCount) {
        List<DatasetDistributeResponseDTO.DistributeGroupItem> groupList = new ArrayList<>();

        for (Map.Entry<String, Long> bucketEntry : buckets.entrySet()) {
            String value = bucketEntry.getKey();
            Long count = bucketEntry.getValue();

            // 计算占比，保留4位小数
            BigDecimal ratio = totalCount > 0 ? BigDecimal.valueOf(count).divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

            DatasetDistributeResponseDTO.DistributeGroupItem groupItem =
                    new DatasetDistributeResponseDTO.DistributeGroupItem(value, count, ratio.doubleValue());
            groupList.add(groupItem);
        }

        // 按count降序排序
        groupList.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));

        return groupList;
    }

    /**
     * 构建基础查询条件
     *
     * @param condition      查询条件对象
     * @param dataSetVersion 数据集版本信息
     */
    private void buildBasicSearchCondition(DatasetEsQueryCondition condition, DataSetVersion dataSetVersion, DatasetFieldPathInfo fieldPathInfo) {
        if (Objects.isNull(dataSetVersion)) {
            return;
        }
        // 设置基本查询参数
        condition.setIndexName(dataSetVersion.getEsIndexName());
        condition.addTermCondition(CommonConstants.BaseEsIndex.VERSION_ID, CommonConstants.BaseEsIndex.VERSION_ID, dataSetVersion.getId().toString());
        condition.addTermCondition(CommonConstants.BaseEsIndex.DATASET_ID, CommonConstants.BaseEsIndex.DATASET_ID, dataSetVersion.getDataSetId().toString());

        // 设置聚合查询参数
        if (fieldPathInfo.getFieldMappingDTO().getFieldType() != null && FLATTEN_FIELD_TYPE.contains(fieldPathInfo.getFieldMappingDTO().getFieldType().getCode())) {
            for (String path : fieldPathInfo.getFirstLevelJsonPath()) {
                condition.addTermsAggregation(path, path, termsBucketsSize);
            }
        } else {
            condition.addTermsAggregationNested(fieldPathInfo.getFieldName(), "textData", fieldPathInfo.getTextPath(), termsBucketsSize);
        }
    }

    /**
     * 配置返回字段
     */
    private void configureReturnFields(DatasetEsQueryCondition condition, List<String> columnList, Map<String, FieldMappingDTO> fieldTypeMap) {
        List<String> fieldPath = new ArrayList<>();
        // 构建筛选条件
        for (String column : columnList) {
            DatasetFieldPathInfo pathInfo = DatasetFieldUtils.buildFieldPathInfo(Collections.singletonList(column), fieldTypeMap, null);
            if (Objects.nonNull(pathInfo)) {
                fieldPath.add(pathInfo.getTextPath());
            }
        }
        condition.setFieldPath(fieldPath);
    }

    /**
     * 构建筛选分页响应结果
     *
     * @param pageResult ES查询结果
     * @param param      查询参数
     * @return 筛选分页响应DTO
     */
    private DatasetFilterPageResponseDTO buildFilterPageResponse(DatasetPageResult pageResult,
                                                                 DatasetFilterPageQueryParam param) {
        DatasetFilterPageResponseDTO responseDTO = new DatasetFilterPageResponseDTO();

        if (pageResult == null) {
            responseDTO.setTotalCount(0L);
            responseDTO.setData(new ArrayList<>());
            return responseDTO;
        }

        // 设置总记录数
        responseDTO.setTotalCount(pageResult.getTotal());

        // 转换数据列表
        List<DatasetFilterPageResponseDTO.DataItem> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
            for (DatasetEsIndex record : pageResult.getRecords()) {
                DatasetFilterPageResponseDTO.DataItem dataItem = convertToDataItem(record, param.getColumnList());
                dataList.add(dataItem);
            }
        }

        responseDTO.setData(dataList);
        return responseDTO;
    }

    /**
     * 转换ES记录为数据项
     *
     * @param record     ES索引记录
     * @param columnList 需要返回的列列表
     * @return 数据项
     */
    private DatasetFilterPageResponseDTO.DataItem convertToDataItem(DatasetEsIndex record, List<String> columnList) {
        DatasetFilterPageResponseDTO.DataItem dataItem = new DatasetFilterPageResponseDTO.DataItem();
        List<DatasetFilterPageResponseDTO.FieldResult> resultList = new ArrayList<>();

        // 如果没有指定列，返回所有可用字段
        if (CollectionUtils.isEmpty(columnList)) {
            // 处理文本数据
            if (record.getTextData() != null) {
                addFieldsFromMap(resultList, record.getTextData(), "text");
            }
            // 处理扁平化数据
            if (record.getFlattenedData() != null) {
                addFieldsFromMap(resultList, record.getFlattenedData(), "flattened");
            }
            // 处理公共数据
            if (record.getCommonData() != null) {
                addFieldsFromMap(resultList, record.getCommonData(), "common");
            }
        } else {
            // 根据指定列返回数据
            for (String column : columnList) {
                DatasetFilterPageResponseDTO.FieldResult fieldResult = extractFieldValue(record, column);
                resultList.add(fieldResult);
            }
        }

        dataItem.setResultList(resultList);
        return dataItem;
    }

    /**
     * 从Map中添加字段
     */
    private void addFieldsFromMap(List<DatasetFilterPageResponseDTO.FieldResult> resultList,
                                  Map<String, Object> dataMap, String fieldType) {
        if (dataMap == null) {
            return;
        }

        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            DatasetFilterPageResponseDTO.FieldResult fieldResult = new DatasetFilterPageResponseDTO.FieldResult();
            fieldResult.setName(entry.getKey());
            fieldResult.setFieldType(fieldType);
            fieldResult.setValue(entry.getValue());
            resultList.add(fieldResult);
        }
    }

    /**
     * 提取指定字段的值
     */
    private DatasetFilterPageResponseDTO.FieldResult extractFieldValue(DatasetEsIndex record, String column) {
        DatasetFilterPageResponseDTO.FieldResult fieldResult = new DatasetFilterPageResponseDTO.FieldResult();
        fieldResult.setName(column);

        // 尝试从不同的数据源中获取字段值
        Object value = null;
        String fieldType = "unknown";

        // 从文本数据中查找
        if (record.getTextData() != null && record.getTextData().containsKey(column)) {
            value = record.getTextData().get(column);
            fieldType = "text";
        }
        // 从扁平化数据中查找
        else if (record.getFlattenedData() != null && record.getFlattenedData().containsKey(column)) {
            value = record.getFlattenedData().get(column);
            fieldType = "flattened";
        }
        // 从公共数据中查找
        else if (record.getCommonData() != null && record.getCommonData().containsKey(column)) {
            value = record.getCommonData().get(column);
            fieldType = "common";
        }

        fieldResult.setFieldType(fieldType);
        fieldResult.setValue(value);
        return fieldResult;
    }
} 