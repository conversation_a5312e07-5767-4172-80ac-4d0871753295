package com.meituan.aigc.aida.labeling.convert;

import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelSubTaskDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-03-07 15:00
 * @description
 */
@Slf4j
@Data
public class LabelingSubTaskConvert {

    public static LabelSubTaskDTO convertLabelSubTaskDTO(LabelingSubTask labelingSubTask){
        if (Objects.isNull(labelingSubTask)) {
            return null;
        }
        LabelSubTaskDTO labelSubTaskDTO = new LabelSubTaskDTO();
        BeanUtils.copyProperties(labelingSubTask, labelSubTaskDTO);
        return labelSubTaskDTO;
    }
}
