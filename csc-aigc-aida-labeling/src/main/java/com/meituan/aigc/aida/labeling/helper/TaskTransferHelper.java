package com.meituan.aigc.aida.labeling.helper;

import com.meituan.aigc.aida.labeling.param.TaskTransferParam;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class TaskTransferHelper {

    /**
     * 任务转交
     *
     * @param transferIdList     任务ID列表
     * @param transferConfigList 转交配置列表
     * @return
     */
    public static Map<String, List<String>> transferTask(List<String> transferIdList, List<TaskTransferParam.TransferConfig> transferConfigList) {
        Collections.shuffle(transferIdList, ThreadLocalRandom.current());
        Map<String, List<String>> transferMisMap = new HashMap<>();
        int transferIndex = 0;
        for (TaskTransferParam.TransferConfig transferConfig : transferConfigList) {
            int curMisTransferNum = transferConfig.getTransferNum();
            List<String> curMisTransferIdList = new ArrayList<>();
            for (int i = 0; i < curMisTransferNum; i++) {
                curMisTransferIdList.add(transferIdList.get(transferIndex++));
            }
            transferMisMap.put(transferConfig.getTransferMis(), curMisTransferIdList);
        }
        return transferMisMap;
    }

}
