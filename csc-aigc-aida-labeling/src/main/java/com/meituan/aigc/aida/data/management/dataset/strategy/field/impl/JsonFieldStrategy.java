package com.meituan.aigc.aida.data.management.dataset.strategy.field.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * JSON字段处理策略
 */
@Slf4j
@Component
public class JsonFieldStrategy extends AbstractDataFieldStrategy {

    @Override
    public Integer getStrategyType() {
        return FieldTypeEnum.JSON.getCode();
    }

    @Override
    public void validateValue(String value) throws Exception {
        try {
            Object parsed = JSON.parse(value);
            // 校验必须是json格式，且暂不支持json数组
            if (parsed instanceof com.alibaba.fastjson.JSONArray) {
                throw new AidaTrainingCheckException("暂不支持JSON数组格式");
            }
            if (!(parsed instanceof JSONObject)) {
                throw new AidaTrainingCheckException("JSON格式不正确，必须是对象格式");
            }
        } catch (Exception e) {
            throw new AidaTrainingCheckException("JSON格式不正确：" + e.getMessage());
        }
    }

    @Override
    public void processFieldData(String columnName, String value,
                                Map<String, Object> textData,
                                Map<String, Object> flattenedData,
                                Map<String, Date> dateData,
                                Map<String, Object> nestedData) {
        // JSON类型：不做加工，直接存储为JSON对象
        try {
            JSONObject jsonObject = JSON.parseObject(value);
            flattenedData.put(columnName, jsonObject);
        } catch (Exception e) {
            // 如果解析失败，作为文本处理
            log.warn("JSON字段解析失败，作为文本处理：{}", e.getMessage());
            textData.put(columnName, value);
        }
    }
} 