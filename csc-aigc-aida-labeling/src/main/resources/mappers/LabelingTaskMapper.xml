<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.LabelingTaskMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.LabelingTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="data_set_id" jdbcType="BIGINT" property="dataSetId" />
    <result column="data_set_version" jdbcType="VARCHAR" property="dataSetVersion" />
    <result column="task_desc" jdbcType="VARCHAR" property="taskDesc" />
    <result column="labeling_config" jdbcType="CHAR" property="labelingConfig" />
    <result column="labeling_status" jdbcType="TINYINT" property="labelingStatus" />
    <result column="quality_check_status" jdbcType="TINYINT" property="qualityCheckStatus" />
    <result column="labeling_manager" jdbcType="VARCHAR" property="labelingManager" />
    <result column="labeling_manager_name" jdbcType="VARCHAR" property="labelingManagerName" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="task_source" jdbcType="TINYINT" property="taskSource" />
    <result column="upload_file_path" jdbcType="VARCHAR" property="uploadFilePath" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="biz_type" jdbcType="BIGINT" property="bizType" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="check_finish_time" jdbcType="TIMESTAMP" property="checkFinishTime" />
    <result column="labeling_finish_time" jdbcType="TIMESTAMP" property="labelingFinishTime" />
    <result column="assign_type" jdbcType="TINYINT" property="assignType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.meituan.aigc.aida.labeling.dao.model.LabelingTask">
    <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_name, data_set_id, data_set_version, task_desc, labeling_config, labeling_status, 
    quality_check_status, labeling_manager, labeling_manager_name, task_type, data_type, 
    task_source, upload_file_path, tenant_id, biz_type, is_deleted, creator_mis, create_time, update_time,
    check_finish_time, labeling_finish_time, assign_type
  </sql>
  <sql id="Blob_Column_List">
    extra_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from labeling_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from labeling_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from labeling_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from labeling_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskExample">
    delete from labeling_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTask" useGeneratedKeys="true" keyProperty="id">
    insert into labeling_task (id, task_name, data_set_id, 
      data_set_version, task_desc, labeling_config, 
      labeling_status, quality_check_status, labeling_manager, 
      labeling_manager_name, task_type, data_type, 
      task_source, upload_file_path, tenant_id, 
      biz_type, is_deleted, creator_mis, 
      create_time, update_time, check_finish_time, 
      labeling_finish_time, assign_type, extra_info
      )
    values (#{id,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{dataSetId,jdbcType=BIGINT}, 
      #{dataSetVersion,jdbcType=VARCHAR}, #{taskDesc,jdbcType=VARCHAR}, #{labelingConfig,jdbcType=CHAR}, 
      #{labelingStatus,jdbcType=TINYINT}, #{qualityCheckStatus,jdbcType=TINYINT}, #{labelingManager,jdbcType=VARCHAR}, 
      #{labelingManagerName,jdbcType=VARCHAR}, #{taskType,jdbcType=TINYINT}, #{dataType,jdbcType=TINYINT}, 
      #{taskSource,jdbcType=TINYINT}, #{uploadFilePath,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{bizType,jdbcType=BIGINT}, #{isDeleted,jdbcType=BIT}, #{creatorMis,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{checkFinishTime,jdbcType=TIMESTAMP}, 
      #{labelingFinishTime,jdbcType=TIMESTAMP}, #{assignType,jdbcType=TINYINT}, #{extraInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTask" useGeneratedKeys="true" keyProperty="id">
    insert into labeling_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="dataSetId != null">
        data_set_id,
      </if>
      <if test="dataSetVersion != null">
        data_set_version,
      </if>
      <if test="taskDesc != null">
        task_desc,
      </if>
      <if test="labelingConfig != null">
        labeling_config,
      </if>
      <if test="labelingStatus != null">
        labeling_status,
      </if>
      <if test="qualityCheckStatus != null">
        quality_check_status,
      </if>
      <if test="labelingManager != null">
        labeling_manager,
      </if>
      <if test="labelingManagerName != null">
        labeling_manager_name,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="taskSource != null">
        task_source,
      </if>
      <if test="uploadFilePath != null">
        upload_file_path,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creatorMis != null">
        creator_mis,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="checkFinishTime != null">
        check_finish_time,
      </if>
      <if test="labelingFinishTime != null">
        labeling_finish_time,
      </if>
      <if test="assignType != null">
        assign_type,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="dataSetId != null">
        #{dataSetId,jdbcType=BIGINT},
      </if>
      <if test="dataSetVersion != null">
        #{dataSetVersion,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null">
        #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="labelingConfig != null">
        #{labelingConfig,jdbcType=CHAR},
      </if>
      <if test="labelingStatus != null">
        #{labelingStatus,jdbcType=TINYINT},
      </if>
      <if test="qualityCheckStatus != null">
        #{qualityCheckStatus,jdbcType=TINYINT},
      </if>
      <if test="labelingManager != null">
        #{labelingManager,jdbcType=VARCHAR},
      </if>
      <if test="labelingManagerName != null">
        #{labelingManagerName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="taskSource != null">
        #{taskSource,jdbcType=TINYINT},
      </if>
      <if test="uploadFilePath != null">
        #{uploadFilePath,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="creatorMis != null">
        #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkFinishTime != null">
        #{checkFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="labelingFinishTime != null">
        #{labelingFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assignType != null">
        #{assignType,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskExample" resultType="java.lang.Long">
    select count(*) from labeling_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update labeling_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.dataSetId != null">
        data_set_id = #{record.dataSetId,jdbcType=BIGINT},
      </if>
      <if test="record.dataSetVersion != null">
        data_set_version = #{record.dataSetVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.taskDesc != null">
        task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.labelingConfig != null">
        labeling_config = #{record.labelingConfig,jdbcType=CHAR},
      </if>
      <if test="record.labelingStatus != null">
        labeling_status = #{record.labelingStatus,jdbcType=TINYINT},
      </if>
      <if test="record.qualityCheckStatus != null">
        quality_check_status = #{record.qualityCheckStatus,jdbcType=TINYINT},
      </if>
      <if test="record.labelingManager != null">
        labeling_manager = #{record.labelingManager,jdbcType=VARCHAR},
      </if>
      <if test="record.labelingManagerName != null">
        labeling_manager_name = #{record.labelingManagerName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=TINYINT},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.taskSource != null">
        task_source = #{record.taskSource,jdbcType=TINYINT},
      </if>
      <if test="record.uploadFilePath != null">
        upload_file_path = #{record.uploadFilePath,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.creatorMis != null">
        creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checkFinishTime != null">
        check_finish_time = #{record.checkFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.labelingFinishTime != null">
        labeling_finish_time = #{record.labelingFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.assignType != null">
        assign_type = #{record.assignType,jdbcType=TINYINT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update labeling_task
    set id = #{record.id,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      data_set_id = #{record.dataSetId,jdbcType=BIGINT},
      data_set_version = #{record.dataSetVersion,jdbcType=VARCHAR},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      labeling_config = #{record.labelingConfig,jdbcType=CHAR},
      labeling_status = #{record.labelingStatus,jdbcType=TINYINT},
      quality_check_status = #{record.qualityCheckStatus,jdbcType=TINYINT},
      labeling_manager = #{record.labelingManager,jdbcType=VARCHAR},
      labeling_manager_name = #{record.labelingManagerName,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=TINYINT},
      data_type = #{record.dataType,jdbcType=TINYINT},
      task_source = #{record.taskSource,jdbcType=TINYINT},
      upload_file_path = #{record.uploadFilePath,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      check_finish_time = #{record.checkFinishTime,jdbcType=TIMESTAMP},
      labeling_finish_time = #{record.labelingFinishTime,jdbcType=TIMESTAMP},
      assign_type = #{record.assignType,jdbcType=TINYINT},
      extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update labeling_task
    set id = #{record.id,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      data_set_id = #{record.dataSetId,jdbcType=BIGINT},
      data_set_version = #{record.dataSetVersion,jdbcType=VARCHAR},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      labeling_config = #{record.labelingConfig,jdbcType=CHAR},
      labeling_status = #{record.labelingStatus,jdbcType=TINYINT},
      quality_check_status = #{record.qualityCheckStatus,jdbcType=TINYINT},
      labeling_manager = #{record.labelingManager,jdbcType=VARCHAR},
      labeling_manager_name = #{record.labelingManagerName,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=TINYINT},
      data_type = #{record.dataType,jdbcType=TINYINT},
      task_source = #{record.taskSource,jdbcType=TINYINT},
      upload_file_path = #{record.uploadFilePath,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      check_finish_time = #{record.checkFinishTime,jdbcType=TIMESTAMP},
      labeling_finish_time = #{record.labelingFinishTime,jdbcType=TIMESTAMP},
      assign_type = #{record.assignType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTask">
    update labeling_task
    <set>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="dataSetId != null">
        data_set_id = #{dataSetId,jdbcType=BIGINT},
      </if>
      <if test="dataSetVersion != null">
        data_set_version = #{dataSetVersion,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null">
        task_desc = #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="labelingConfig != null">
        labeling_config = #{labelingConfig,jdbcType=CHAR},
      </if>
      <if test="labelingStatus != null">
        labeling_status = #{labelingStatus,jdbcType=TINYINT},
      </if>
      <if test="qualityCheckStatus != null">
        quality_check_status = #{qualityCheckStatus,jdbcType=TINYINT},
      </if>
      <if test="labelingManager != null">
        labeling_manager = #{labelingManager,jdbcType=VARCHAR},
      </if>
      <if test="labelingManagerName != null">
        labeling_manager_name = #{labelingManagerName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="taskSource != null">
        task_source = #{taskSource,jdbcType=TINYINT},
      </if>
      <if test="uploadFilePath != null">
        upload_file_path = #{uploadFilePath,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="creatorMis != null">
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkFinishTime != null">
        check_finish_time = #{checkFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="labelingFinishTime != null">
        labeling_finish_time = #{labelingFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assignType != null">
        assign_type = #{assignType,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTask">
    update labeling_task
    set task_name = #{taskName,jdbcType=VARCHAR},
      data_set_id = #{dataSetId,jdbcType=BIGINT},
      data_set_version = #{dataSetVersion,jdbcType=VARCHAR},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      labeling_config = #{labelingConfig,jdbcType=CHAR},
      labeling_status = #{labelingStatus,jdbcType=TINYINT},
      quality_check_status = #{qualityCheckStatus,jdbcType=TINYINT},
      labeling_manager = #{labelingManager,jdbcType=VARCHAR},
      labeling_manager_name = #{labelingManagerName,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=TINYINT},
      data_type = #{dataType,jdbcType=TINYINT},
      task_source = #{taskSource,jdbcType=TINYINT},
      upload_file_path = #{uploadFilePath,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      creator_mis = #{creatorMis,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      check_finish_time = #{checkFinishTime,jdbcType=TIMESTAMP},
      labeling_finish_time = #{labelingFinishTime,jdbcType=TIMESTAMP},
      assign_type = #{assignType,jdbcType=TINYINT},
      extra_info = #{extraInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTask">
    update labeling_task
    set task_name = #{taskName,jdbcType=VARCHAR},
      data_set_id = #{dataSetId,jdbcType=BIGINT},
      data_set_version = #{dataSetVersion,jdbcType=VARCHAR},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      labeling_config = #{labelingConfig,jdbcType=CHAR},
      labeling_status = #{labelingStatus,jdbcType=TINYINT},
      quality_check_status = #{qualityCheckStatus,jdbcType=TINYINT},
      labeling_manager = #{labelingManager,jdbcType=VARCHAR},
      labeling_manager_name = #{labelingManagerName,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=TINYINT},
      data_type = #{dataType,jdbcType=TINYINT},
      task_source = #{taskSource,jdbcType=TINYINT},
      upload_file_path = #{uploadFilePath,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      creator_mis = #{creatorMis,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      check_finish_time = #{checkFinishTime,jdbcType=TIMESTAMP},
      labeling_finish_time = #{labelingFinishTime,jdbcType=TIMESTAMP},
      assign_type = #{assignType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="statisticsTaskData" resultType="com.meituan.aigc.aida.labeling.job.dashboard.po.TaskStatisticsPO">
    SELECT
      task.biz_type AS bizType,
      task.data_type AS dataType,
      task.task_type AS taskType,
      task.id AS taskId,
      task.task_name AS taskName,
      task.labeling_status AS labelingStatus,
      task.quality_check_status AS qualityCheckStatus,
      (SELECT COUNT(id) FROM labeling_task_raw_data WHERE task_id = task.id) AS totalSampleCount
    FROM labeling_task AS task
    WHERE task.id IN(
      SELECT DISTINCT task_id FROM labeling_sub_task WHERE id IN(
        SELECT DISTINCT sub_task_id FROM labeling_detail
        WHERE first_labeling_time &gt;= #{startTime} AND first_labeling_time &lt;= #{endTime}
      )AND status != 8
    UNION
      SELECT task_id FROM labeling_quality_check_task WHERE id IN(
        SELECT DISTINCT quality_check_task_id FROM labeling_quality_check_item
        WHERE first_check_time &gt;= #{startTime} AND first_check_time &lt;= #{endTime}
      )
    )
  </select>
  <select id="listTaskByCreateTimeAndStatus" resultMap="ResultMapWithBLOBs">
    SELECT
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
    FROM labeling_task
    WHERE
      create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}
    AND labeling_status IN
    <foreach collection="labelingStatus" item="status" open="(" separator="," close=")">
      #{status}
    </foreach>
  </select>
</mapper>