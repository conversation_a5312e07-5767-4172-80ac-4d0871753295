package com.meituan.aigc.aida.labeling.pojo.vo.dashboard;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyBizStatisticsVO {
    /**
     * 业务类型id
     */
    private Long bizTypeId;
    
    /**
     * 日期
     */
    @JsonFormat(pattern = "MM-dd")
    private String date;

    /**
     * 标注/质检数量
     */
    private Integer dailyInspectedCount;
}
