package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 质检任务列表DTO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QualityCheckTaskListDTO implements Serializable {

    /**
     * 质检任务ID
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据来源
     */
    private String dataSetSource;

    /**
     * 总数据量
     */
    private Integer totalCount;

    /**
     * 样本数量
     */
    private Integer sampleCount;

    /**
     * 质检进度
     */
    private Integer qualityCheckProgress;

    /**
     * 质检状态
     */
    private String qualityCheckStatus;

    /**
     * 质检状态码
     */
    private Integer qualityCheckStatusCode;

    /**
     * 质检人mis
     */
    private String qualityChecker;

    /**
     * 质检人Mis
     */
    private String qualityCheckerMis;

    /**
     * 质检人名字
     */
    private String qualityCheckerName;

    /**
     * 标注任务类型 1:评测集标注 2:训练集标注 3:线上会话标注
     */
    private Integer taskType;

    /**
     * 数据类型 1:模型评测 2:Agent评测 3:有监督微调(SFT) 4:偏好对齐(DPO) 5:Session维度 6:Query维度
     */
    private Integer dataType;
}

