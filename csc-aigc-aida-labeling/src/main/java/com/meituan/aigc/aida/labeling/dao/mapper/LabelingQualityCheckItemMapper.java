package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItemExample;
import com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckItemPO;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonQualityCheckStatisticsPO;
import com.meituan.aigc.aida.labeling.param.quality.check.LabelingQualityCheckItemParam;
import com.meituan.aigc.aida.labeling.pojo.dto.PageQueryDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingDataCheckResultVO;
import com.meituan.aigc.aida.labeling.pojo.vo.QualityCheckModifiedDataCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface LabelingQualityCheckItemMapper {
    long countByExample(LabelingQualityCheckItemExample example);

    int deleteByExample(LabelingQualityCheckItemExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LabelingQualityCheckItem record);

    int insertSelective(LabelingQualityCheckItem record);

    List<LabelingQualityCheckItem> selectByExample(LabelingQualityCheckItemExample example);

    LabelingQualityCheckItem selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LabelingQualityCheckItem record, @Param("example") LabelingQualityCheckItemExample example);

    int updateByExample(@Param("record") LabelingQualityCheckItem record, @Param("example") LabelingQualityCheckItemExample example);

    int updateByPrimaryKeySelective(LabelingQualityCheckItem record);

    int updateByPrimaryKey(LabelingQualityCheckItem record);

    int batchInsert(@Param("list") List<LabelingQualityCheckItem> list);

    List<LabelingQualityCheckItemPO> listByQualityCheckTaskIdAndQualityCheckStatus(@Param("param") LabelingQualityCheckItemParam param);

    int deleteByQualityCheckTaskIds(@Param("list") List<Long> list);

    List<LabelingQualityCheckItemPO> listQualityCheckItemByTaskIdAndRawDataIds(@Param("qualityCheckTaskId") Long qualityCheckTaskId, @Param("rawDataIds") List<Long> rawDataIds);

    List<QualityCheckTaskPO> listByQualityCheckTaskId(@Param("qualityCheckTaskIds") List<Long> qualityCheckTaskIds);

    Integer countByCheckTaskIdAndStatus(@Param("qualityCheckTaskId") Long qualityCheckTaskId, @Param("qualityCheckDataStatus") Integer qualityCheckDataStatus);

    /**
     * 统计子任务下的数量：全部数量 已质检数量 未质检数量
     */
    PageQueryDTO<?> countByTaskIdAndLabelerMis(@Param("checkTaskId") Long checkTaskId, @Param("labelerMis") String labelerMis);

    List<LabelingDataCheckResultVO> countCheckDetailByCheckTaskIds(@Param("qualityCheckTaskIds") List<Long> qualityCheckTaskIds);

    Integer countRawDataNumByCheckTaskIdsAndStatus(@Param("qualityCheckTaskIds") List<Long> qualityCheckTaskIds, @Param("checkStatus") Integer checkStatus);

    Integer countCheckTaskNumByQualityCheckItemIds(@Param("checkItemIds") List<Long> checkItemIds);

    List<Long> listRawDataIdsByTaskAndRawDataIdAndStatus(@Param("qualityCheckTaskId") Long qualityCheckTaskId, @Param("rawDataId") Long rawDataId, @Param("checkAbleStatus") Integer checkAbleStatus, @Param("qualityStatus") Integer qualityStatus);

    Integer countRawDataIdsByTaskAndStatus(@Param("qualityCheckTaskId") Long qualityCheckTaskId, @Param("checkAbleStatus") Integer checkAbleStatus, @Param("qualityStatus") Integer qualityStatus);

    List<String> listSessionIdByTaskAndSessionIdAndStatus(@Param("qualityCheckTaskId") Long qualityCheckTaskId, @Param("sessionId") String sessionId, @Param("checkAbleStatus") Integer checkAbleStatus, @Param("qualityStatus") Integer qualityStatus);

    void batchUpdateByPrimaryKeySelective(@Param("labelingQualityCheckItemList") List<LabelingQualityCheckItem> labelingQualityCheckItemList);

    List<LabelingQualityCheckItemPO> listQualityCheckItemByTaskIdAndSessionIdList(@Param("qualityCheckTaskId") Long qualityCheckTaskId, @Param("sessionIdList") List<String> sessionIdList);

    QualityCheckModifiedDataCountVO countQualityCheckModifiedDataByTaskId(@Param("subTaskId") Long subTaskId);

    /**
     * 根据子任务id和状态查询会话数量
     *
     * @param subTaskId 子任务id
     * @param status    状态
     * @return 会话数量
     */
    int countSessionBySubTaskIdAndStatus(@Param("subTaskId") Long subTaskId, @Param("status") Integer status);

    /**
     * 根据子任务id和状态查询query数量
     *
     * @param subTaskId 子任务id
     * @param status    状态
     * @return query数量
     */
    int countQueryBySubTaskIdAndStatus(@Param("subTaskId") Long subTaskId, @Param("status") Integer status);

    List<PersonQualityCheckStatisticsPO> statisticsPersonQualityCheckData(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}