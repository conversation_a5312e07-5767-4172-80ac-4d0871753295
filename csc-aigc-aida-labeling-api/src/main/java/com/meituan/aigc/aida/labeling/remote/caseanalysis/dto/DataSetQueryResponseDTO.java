package com.meituan.aigc.aida.labeling.remote.caseanalysis.dto;

import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-26 11:17
 * @description 数据集查询结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetQueryResponseDTO implements Serializable {
    /**
     * 数据列信息
     */
    private List<DataFieldParam> headList;
    /**
     * 数据信息
     */
    private List<DataSetRecordParam> dataList;
}
