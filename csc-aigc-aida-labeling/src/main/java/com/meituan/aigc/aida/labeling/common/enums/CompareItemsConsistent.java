package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-10 11:54
 * @description 对比项一致
 */
@Getter
public enum CompareItemsConsistent {
    YES(1, "是"),
    NO(0, "否");
    private final int code;
    private final String value;

    CompareItemsConsistent(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static CompareItemsConsistent getByCode(int code) {
        for (CompareItemsConsistent value : CompareItemsConsistent.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }



}
