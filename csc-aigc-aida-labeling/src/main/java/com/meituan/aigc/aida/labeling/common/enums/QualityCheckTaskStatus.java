package com.meituan.aigc.aida.labeling.common.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 16:44
 * @Version: 1.0
 */
@Getter
public enum QualityCheckTaskStatus {
    CREATING(1, "创建中"),
    CHECKING(2, "质检中"),
    COMPLETED(3, "已质检"),
    FAILED(4, "失败"),
    PARTIAL(5, "部分质检"),
    ;

    private final int code;
    private final String value;

    QualityCheckTaskStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static QualityCheckTaskStatus getByCode(int code) {
        for (QualityCheckTaskStatus status : QualityCheckTaskStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 获取可分配质检任务的状态
     *
     * @return 状态
     */
    public static List<Integer> getDistributeStatus() {
        return Lists.newArrayList(FAILED.getCode(), CHECKING.getCode(), PARTIAL.getCode());
    }

}
