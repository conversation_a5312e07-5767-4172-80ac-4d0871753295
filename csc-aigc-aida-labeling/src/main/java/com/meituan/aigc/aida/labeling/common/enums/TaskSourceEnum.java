package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: wb_duaode
 * @Version: 1.0
 */
@Getter
public enum TaskSourceEnum {
    
    FETCH(1, "数据集创建"),
    UPLOAD(2, "文件导入"),
    CASE_ANALYSIS(3, "case分析工具");

    private final int code;
    private final String value;

    TaskSourceEnum(int code, String value) {
    this.code = code;
    this.value = value;
   }

    public static TaskSourceEnum getByCode(int code) {
        for (TaskSourceEnum taskSource : TaskSourceEnum.values()) {
            if (taskSource.getCode() == code) {
                return taskSource;
            }
        }
        return null;
    }
}