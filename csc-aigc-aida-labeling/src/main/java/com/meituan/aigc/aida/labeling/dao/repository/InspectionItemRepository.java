package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.InspectionItem;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:00
 * @Version: 1.0
 */
public interface InspectionItemRepository {

    /**
     * 根据模板id查询质检项
     * @param templateId 模板id
     * @return 质检项列表
     */
    List<InspectionItem> listByTemplateId(Long templateId);

    /**
     * 根据模板id列表查询质检项
     * @param templateIdList 模板id列表
     * @return 质检项列表
     */
    List<InspectionItem> listByTemplateIdList(List<Long> templateIdList);

    /**
     * 插入标注项
     * @param inspectionItem
     */
    void insertInspectionItem(InspectionItem inspectionItem);

    /**
     * 更新标注项
     * @param inspectionItem
     */
    void updateInspectionItem(InspectionItem inspectionItem);

    /**
     * 根据id查询标注项
     * @param id
     * @return
     */
    InspectionItem getById(Long id);

    /**
     * 批量更新标注项
     * @param inspectionItems
     */
    void batchUpdateItems(List<InspectionItem> inspectionItems);
}
