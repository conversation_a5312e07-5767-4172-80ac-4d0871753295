package com.meituan.aigc.aida.labeling.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;

/**
 * <AUTHOR>
 */
public interface UploadPlatformService {

    /**
     * 上传文件到S3并进行加密——文件输入
     *
     * @param multipartFile      文件
     * @param s3FileName         s3文件名
     * @param encryptionFileName 文枢文件名
     * @param fileType           文件类型
     * @param staffMis           下载员工Mis，文殊权限控制
     * @return 文件下载地址
     */
    String uploadS3AndEncryption(MultipartFile multipartFile, String s3FileName, String encryptionFileName, String fileType, String staffMis);

    /**
     * 上传文件到S3并进行加密——流输入
     *
     * @param inputStream        输入流
     * @param s3FileName         s3文件名
     * @param encryptionFileName 文枢文件名
     * @param fileType           文件类型
     * @param staffMis           下载员工Mis，文殊权限控制
     * @return 文件下载地址
     */
    String uploadS3AndEncryption(ByteArrayInputStream inputStream, String s3FileName, String encryptionFileName, String fileType, String staffMis);
}
