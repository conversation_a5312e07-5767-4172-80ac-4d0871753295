package com.meituan.aigc.aida.labeling.dao.model;

import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.common.enums.LabelingTaskStatus;
import com.meituan.aigc.aida.labeling.common.enums.LabelingTaskType;
import com.meituan.aigc.aida.labeling.common.enums.QualityCheckTaskStatus;
import lombok.Data;

import java.util.Date;

@Data
public class TaskStatistics {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 统计日期
     */
    private Date statDate;
    /**
     * 业务类型
     */
    private Long bizType;
    /**
     * 任务类型 {@link LabelingTaskType}
     */
    private Integer taskType;
    /**
     * 数据类型 {@link LabelTaskDataType}
     */
    private Integer dataType;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 标注状态 {@link LabelingTaskStatus}
     */
    private Integer labelingStatus;
    /**
     * 任务质检状态 {@link QualityCheckTaskStatus}
     */
    private Integer qualityCheckStatus;
    /**
     * 总样本量
     */
    private Integer totalSampleCount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}