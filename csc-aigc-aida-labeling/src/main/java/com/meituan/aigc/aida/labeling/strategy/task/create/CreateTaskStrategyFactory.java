package com.meituan.aigc.aida.labeling.strategy.task.create;

import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingTaskRawDataVO;
import com.meituan.aigc.aida.labeling.strategy.upload.FileProcessingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 17:25
 * @Version: 1.0
 * <p>
 * 导出策略工厂
 */
@Slf4j
@Component
public class CreateTaskStrategyFactory {


    @Autowired
    private List<CreateTaskStrategy> strategies;

    @Autowired
    private List<FileProcessingStrategy> fileProcessingStrategyList;

    private final Map<Integer, CreateTaskStrategy> strategyMap = new HashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void init() {
        for (CreateTaskStrategy strategy : strategies) {
            strategyMap.put(strategy.getTaskType(), strategy);
        }
    }

    /**
     * 获取指定任务类型的导出策略
     *
     * @param taskType 任务类型
     * @return 导出策略
     * @throws AidaTrainingCheckException 如果找不到对应的策略
     */
    public CreateTaskStrategy getStrategy(Integer taskType) {
        CreateTaskStrategy strategy = strategyMap.get(taskType);
        if (strategy == null) {
            log.error("导出任务未找到处理器，任务类型：{}", taskType);
            throw new AidaTrainingCheckException("不支持的任务类型: " + taskType);
        }
        return strategy;
    }

    /**
     * 执行导出方法
     *
     * @param taskType          任务类型
     * @param taskName          主任务ID
     * @param subTaskIds        子任务ID集合
     * @param labelDetailStatus 标注状态
     */
    public void runStrategy(Integer taskType, String taskName, List<Long> subTaskIds, Integer labelDetailStatus, String mis) {
        CreateTaskStrategy strategy = getStrategy(taskType);
        strategy.export(taskName, subTaskIds, labelDetailStatus, mis);
    }

    /**
     * 获取指定任务类型的导出策略
     *
     * @param taskDataType 任务类型
     * @return 导出策略
     */
    public CreateTaskStrategy getInstance(Integer taskDataType) {
        return strategyMap.get(taskDataType);
    }

    /**
     * 执行文件解析
     *
     * @param file 文件内容
     * @return 解析后结果
     */
    public LabelingTaskRawDataVO runStrategy(MultipartFile file) {
        if (CollectionUtils.isEmpty(fileProcessingStrategyList)) {
            log.error("文件处理未配置策略");
            throw new AidaTrainingCheckException("文件处理未配置策略");
        }
        String fileType = Optional.ofNullable(file.getOriginalFilename())
                .map(str -> str.substring(file.getOriginalFilename().lastIndexOf(".") + 1))
                .orElse("未获取到类型");

        for (FileProcessingStrategy fileProcessingStrategy : fileProcessingStrategyList) {
            if (fileProcessingStrategy.isMe(fileType)) {
                return fileProcessingStrategy.readFile(file);
            }
        }
        log.error("文件处理未匹配到处理规则,fileType={}", fileType);
        throw new AidaTrainingCheckException("文件处理未匹配到处理规则: " + fileType);
    }

    /**
     * 计算文件的内容行数（不包括表头）
     *
     * @param file 上传文件
     * @return 内容行数
     */
    public int countContentRows(MultipartFile file) {
        if (CollectionUtils.isEmpty(fileProcessingStrategyList)) {
            log.error("文件处理未配置策略");
            throw new AidaTrainingCheckException("文件处理未配置策略");
        }
        String fileType = Optional.ofNullable(file.getOriginalFilename()).map(str -> str.substring(file.getOriginalFilename().lastIndexOf(".") + 1)).orElse("未获取到类型");
        for (FileProcessingStrategy fileProcessingStrategy : fileProcessingStrategyList) {
            if (fileProcessingStrategy.isMe(fileType)) {
                return fileProcessingStrategy.countContentRows(file);
            }
        }
        log.error("文件处理未匹配到处理规则,fileType={}", fileType);
        throw new AidaTrainingCheckException("文件处理未匹配到处理规则: " + fileType);
    }

    /**
     * 获取文件表头
     *
     * @param file 上传文件
     * @return 内容行数
     */
    public Set<String> getFileHeader(MultipartFile file, Integer columns) throws IOException {
        if (CollectionUtils.isEmpty(fileProcessingStrategyList)) {
            log.error("文件处理未配置策略");
            throw new AidaTrainingCheckException("文件处理未配置策略");
        }
        String fileType = Optional.ofNullable(file.getOriginalFilename()).map(str -> str.substring(file.getOriginalFilename().lastIndexOf(".") + 1)).orElse("未获取到类型");
        for (FileProcessingStrategy fileProcessingStrategy : fileProcessingStrategyList) {
            if (fileProcessingStrategy.isMe(fileType)) {
                return fileProcessingStrategy.getFileHeader(file, columns);
            }
        }
        log.error("文件处理未匹配到处理规则,fileType={}", fileType);
        throw new AidaTrainingCheckException("文件处理未匹配到处理规则: " + fileType);
    }
}
