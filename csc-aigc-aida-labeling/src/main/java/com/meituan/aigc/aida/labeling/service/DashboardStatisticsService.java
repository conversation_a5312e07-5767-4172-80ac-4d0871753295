package com.meituan.aigc.aida.labeling.service;

import com.meituan.aigc.aida.labeling.param.BaseStatisticsRequestParam;
import com.meituan.aigc.aida.labeling.param.DashboardTaskListQueryParam;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.param.PersonStatisticsRequestParam;
import com.meituan.aigc.aida.labeling.param.DailyStatisticsRequestParam;
import com.meituan.aigc.aida.labeling.param.PersonnelDetailRequestParam;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.*;
import com.meituan.aigc.aida.labeling.pojo.vo.MetricVO;
import com.meituan.aigc.aida.labeling.pojo.vo.DailyTrendsVO;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 15:04
 * @Version: 1.0
 */
public interface DashboardStatisticsService {
    /**
     * 获取人员统计图表数据
     * <AUTHOR>
     * @param param 参数
     * @return 查询结果
     */
    List<MetricVO> getPersonStatisticalCharts(PersonStatisticsRequestParam param);

    /**
     * 获取每日标注趋势图表数据
     * @param param 参数
     * @return 每日趋势结果
     */
    DailyTrendsVO getDailyTrends(DailyStatisticsRequestParam param);

    /**
     * 获得业务的质检正确率/单个业务的该时间段的质检趋势
     * @param param 参数
     * @return 查询结果
     */
    BizQualityCheckRateVO getBizQualityCheckRate(BaseStatisticsRequestParam param);

    /**
     * 分页查询任务快照数据
     * @param param 参数
     * @return 查询结果
     */
    PageData<DashboardTaskItemVO> pageTasks(DashboardTaskListQueryParam param);

    /**
     * 查询业务场景和任务类型等数据
     * @return 结果
     */
    BizTypeAndTaskTypeQueryVO queryCondition();

    /**
     * 大盘总览数据统计
     * @param param 参数
     * @return 统计结果
     */
    DashboardOverviewStatsVO overviewStats(BaseStatisticsRequestParam param);

    /**
     * 获取人员详情数据
     * @param param 参数
     * @return 人员详情数据
     */
    PageData<PersonnelDetailVO> getPersonnelDetail(PersonnelDetailRequestParam param);

    /**
     * 查询生效的业务场景
     * @return 查询结果
     */
    public List<BaseNameCodeVO> getBizTypes();

    /**
     * 查询所有业务场景
     * @return 业务类型名称
     */
    public List<BaseNameCodeVO> getAllBizTypes();

}
