package com.meituan.aigc.aida.benchmark.pojo.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 考点统计类
 *
 * <AUTHOR>
 * @date 2025/6/26
 */

@Data
public class ExamStatisticsDTO {
    /**
     * 考点信息
     */
    private List<ExamItem> examItems;

    /**
     * 考点总分
     */
    private BigDecimal totalScore;

    /**
     * 总题数
     */
    private int totalCount;

    /**
     * 考点客观总分
     */
    private BigDecimal objectiveTotalScore;

    /**
     * 客观题数量
     */
    private int objectiveCount;

    /**
     * 考点主观总分
     */
    private BigDecimal subjectiveTotalScore;

    /**
     * 主观题数量
     */
    private int subjectiveCount;

    /**
     * 考点满分数量（3分）
     */
    private int perfectCount;

    /**
     * 考点可用数量（2分和3分）
     */
    private int usableCount;

    /**
     * 评测轮数
     */
    private int roundCount;

    public ExamStatisticsDTO() {
        this.examItems = Collections.synchronizedList(new ArrayList<>());
        this.perfectCount = 0;
        this.usableCount = 0;
        this.totalScore = BigDecimal.ZERO;
        this.objectiveTotalScore = BigDecimal.ZERO;
        this.subjectiveTotalScore = BigDecimal.ZERO;
        this.objectiveCount = 0;
        this.subjectiveCount = 0;
    }

    @Data
    public static class ExamItem {
        /**
         * 得分
         */
        private BigDecimal score;

        /**
         * 主客观
         */
        private String isObjective;

        /**
         * 是否强对抗
         */
        private String isStrong;

        /**
         * 难易程度
         */
        private String level;

        /**
         * 长度分级
         */
        private String lengthLevel;
    }

    /**
     * 增加客观题数量
     */
    public synchronized void incrementObjectiveCount() {
        this.objectiveCount++;
    }

    /**
     * 增加主观题数量
     */
    public synchronized void incrementSubjectiveCount() {
        this.subjectiveCount++;
    }

    /**
     * 增加满分数量
     */
    public synchronized void incrementPerfectCount() {
        this.perfectCount++;
    }

    /**
     * 增加可用数量
     */
    public synchronized void incrementUsableCount() {
        this.usableCount++;
    }

    /**
     * 增加评测轮数
     */
    public synchronized void incrementRoundCount() {
        this.roundCount++;
    }

    /**
     * 增加总题数
     */
    public synchronized void addTotalCount() {
        this.totalCount++;
    }

}
