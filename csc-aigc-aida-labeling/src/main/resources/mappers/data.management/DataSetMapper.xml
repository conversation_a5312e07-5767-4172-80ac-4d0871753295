<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.data.management.dataset.dao.mapper.DataSetMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="fetch_id" jdbcType="BIGINT" property="fetchId" />
    <result column="usage_category" jdbcType="TINYINT" property="usageCategory" />
    <result column="usage_type" jdbcType="TINYINT" property="usageType" />
    <result column="training_type" jdbcType="TINYINT" property="trainingType" />
    <result column="task_description" jdbcType="VARCHAR" property="taskDescription" />
    <result column="data_format" jdbcType="VARCHAR" property="dataFormat" />
    <result column="data_count" jdbcType="INTEGER" property="dataCount" />
    <result column="latest_version_id" jdbcType="BIGINT" property="latestVersionId" />
    <result column="data_set_status" jdbcType="TINYINT" property="dataSetStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="data_source" jdbcType="TINYINT" property="dataSource" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet">
    <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, description, creator_mis, creator_name, fetch_id, usage_category, usage_type,
    training_type, task_description, data_format, data_count, latest_version_id, data_set_status,
    create_time, update_time, is_deleted, data_source, file_path, file_name
  </sql>
  <sql id="Blob_Column_List">
    extra_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from data_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from data_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from data_set
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_set
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetExample">
    delete from data_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet">
    insert into data_set (id, name, description,
                          creator_mis, creator_name, fetch_id,
                          usage_category, usage_type, training_type,
                          task_description, data_format, data_count,
                          latest_version_id, data_set_status, create_time,
                          update_time, is_deleted, data_source,
                          file_path, file_name, extra_info
    )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
            #{creatorMis,jdbcType=VARCHAR}, #{creatorName,jdbcType=VARCHAR}, #{fetchId,jdbcType=BIGINT},
            #{usageCategory,jdbcType=TINYINT}, #{usageType,jdbcType=TINYINT}, #{trainingType,jdbcType=TINYINT},
            #{taskDescription,jdbcType=VARCHAR}, #{dataFormat,jdbcType=VARCHAR}, #{dataCount,jdbcType=INTEGER},
            #{latestVersionId,jdbcType=BIGINT}, #{dataSetStatus,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=BIT}, #{dataSource,jdbcType=TINYINT},
            #{filePath,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{extraInfo,jdbcType=LONGVARCHAR}
           )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet" useGeneratedKeys="true" keyProperty="id">
    insert into data_set
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="creatorMis != null">
        creator_mis,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="fetchId != null">
        fetch_id,
      </if>
      <if test="usageCategory != null">
        usage_category,
      </if>
      <if test="usageType != null">
        usage_type,
      </if>
      <if test="trainingType != null">
        training_type,
      </if>
      <if test="taskDescription != null">
        task_description,
      </if>
      <if test="dataFormat != null">
        data_format,
      </if>
      <if test="dataCount != null">
        data_count,
      </if>
      <if test="latestVersionId != null">
        latest_version_id,
      </if>
      <if test="dataSetStatus != null">
        data_set_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="dataSource != null">
        data_source,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="creatorMis != null">
        #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="fetchId != null">
        #{fetchId,jdbcType=BIGINT},
      </if>
      <if test="usageCategory != null">
        #{usageCategory,jdbcType=TINYINT},
      </if>
      <if test="usageType != null">
        #{usageType,jdbcType=TINYINT},
      </if>
      <if test="trainingType != null">
        #{trainingType,jdbcType=TINYINT},
      </if>
      <if test="taskDescription != null">
        #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="dataFormat != null">
        #{dataFormat,jdbcType=VARCHAR},
      </if>
      <if test="dataCount != null">
        #{dataCount,jdbcType=INTEGER},
      </if>
      <if test="latestVersionId != null">
        #{latestVersionId,jdbcType=BIGINT},
      </if>
      <if test="dataSetStatus != null">
        #{dataSetStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=TINYINT},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetExample" resultType="java.lang.Long">
    select count(*) from data_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update data_set
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorMis != null">
        creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.fetchId != null">
        fetch_id = #{record.fetchId,jdbcType=BIGINT},
      </if>
      <if test="record.usageCategory != null">
        usage_category = #{record.usageCategory,jdbcType=TINYINT},
      </if>
      <if test="record.usageType != null">
        usage_type = #{record.usageType,jdbcType=TINYINT},
      </if>
      <if test="record.trainingType != null">
        training_type = #{record.trainingType,jdbcType=TINYINT},
      </if>
      <if test="record.taskDescription != null">
        task_description = #{record.taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFormat != null">
        data_format = #{record.dataFormat,jdbcType=VARCHAR},
      </if>
      <if test="record.dataCount != null">
        data_count = #{record.dataCount,jdbcType=INTEGER},
      </if>
      <if test="record.latestVersionId != null">
        latest_version_id = #{record.latestVersionId,jdbcType=BIGINT},
      </if>
      <if test="record.dataSetStatus != null">
        data_set_status = #{record.dataSetStatus,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.dataSource != null">
        data_source = #{record.dataSource,jdbcType=TINYINT},
      </if>
      <if test="record.filePath != null">
        file_path = #{record.filePath,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update data_set
    set id = #{record.id,jdbcType=BIGINT},
    name = #{record.name,jdbcType=VARCHAR},
    description = #{record.description,jdbcType=VARCHAR},
    creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
    creator_name = #{record.creatorName,jdbcType=VARCHAR},
    fetch_id = #{record.fetchId,jdbcType=BIGINT},
    usage_category = #{record.usageCategory,jdbcType=TINYINT},
    usage_type = #{record.usageType,jdbcType=TINYINT},
    training_type = #{record.trainingType,jdbcType=TINYINT},
    task_description = #{record.taskDescription,jdbcType=VARCHAR},
    data_format = #{record.dataFormat,jdbcType=VARCHAR},
    data_count = #{record.dataCount,jdbcType=INTEGER},
    latest_version_id = #{record.latestVersionId,jdbcType=BIGINT},
    data_set_status = #{record.dataSetStatus,jdbcType=TINYINT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    is_deleted = #{record.isDeleted,jdbcType=BIT},
    data_source = #{record.dataSource,jdbcType=TINYINT},
    file_path = #{record.filePath,jdbcType=VARCHAR},
    file_name = #{record.fileName,jdbcType=VARCHAR},
    extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update data_set
    set id = #{record.id,jdbcType=BIGINT},
    name = #{record.name,jdbcType=VARCHAR},
    description = #{record.description,jdbcType=VARCHAR},
    creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
    creator_name = #{record.creatorName,jdbcType=VARCHAR},
    fetch_id = #{record.fetchId,jdbcType=BIGINT},
    usage_category = #{record.usageCategory,jdbcType=TINYINT},
    usage_type = #{record.usageType,jdbcType=TINYINT},
    training_type = #{record.trainingType,jdbcType=TINYINT},
    task_description = #{record.taskDescription,jdbcType=VARCHAR},
    data_format = #{record.dataFormat,jdbcType=VARCHAR},
    data_count = #{record.dataCount,jdbcType=INTEGER},
    latest_version_id = #{record.latestVersionId,jdbcType=BIGINT},
    data_set_status = #{record.dataSetStatus,jdbcType=TINYINT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    is_deleted = #{record.isDeleted,jdbcType=BIT},
    data_source = #{record.dataSource,jdbcType=TINYINT},
    file_path = #{record.filePath,jdbcType=VARCHAR},
    file_name = #{record.fileName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet">
    update data_set
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="creatorMis != null">
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="fetchId != null">
        fetch_id = #{fetchId,jdbcType=BIGINT},
      </if>
      <if test="usageCategory != null">
        usage_category = #{usageCategory,jdbcType=TINYINT},
      </if>
      <if test="usageType != null">
        usage_type = #{usageType,jdbcType=TINYINT},
      </if>
      <if test="trainingType != null">
        training_type = #{trainingType,jdbcType=TINYINT},
      </if>
      <if test="taskDescription != null">
        task_description = #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="dataFormat != null">
        data_format = #{dataFormat,jdbcType=VARCHAR},
      </if>
      <if test="dataCount != null">
        data_count = #{dataCount,jdbcType=INTEGER},
      </if>
      <if test="latestVersionId != null">
        latest_version_id = #{latestVersionId,jdbcType=BIGINT},
      </if>
      <if test="dataSetStatus != null">
        data_set_status = #{dataSetStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="dataSource != null">
        data_source = #{dataSource,jdbcType=TINYINT},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet">
    update data_set
    set name = #{name,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
        creator_name = #{creatorName,jdbcType=VARCHAR},
        fetch_id = #{fetchId,jdbcType=BIGINT},
        usage_category = #{usageCategory,jdbcType=TINYINT},
        usage_type = #{usageType,jdbcType=TINYINT},
        training_type = #{trainingType,jdbcType=TINYINT},
        task_description = #{taskDescription,jdbcType=VARCHAR},
        data_format = #{dataFormat,jdbcType=VARCHAR},
        data_count = #{dataCount,jdbcType=INTEGER},
        latest_version_id = #{latestVersionId,jdbcType=BIGINT},
        data_set_status = #{dataSetStatus,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=BIT},
        data_source = #{dataSource,jdbcType=TINYINT},
        file_path = #{filePath,jdbcType=VARCHAR},
        file_name = #{fileName,jdbcType=VARCHAR},
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet">
    update data_set
    set name = #{name,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
        creator_name = #{creatorName,jdbcType=VARCHAR},
        fetch_id = #{fetchId,jdbcType=BIGINT},
        usage_category = #{usageCategory,jdbcType=TINYINT},
        usage_type = #{usageType,jdbcType=TINYINT},
        training_type = #{trainingType,jdbcType=TINYINT},
        task_description = #{taskDescription,jdbcType=VARCHAR},
        data_format = #{dataFormat,jdbcType=VARCHAR},
        data_count = #{dataCount,jdbcType=INTEGER},
        latest_version_id = #{latestVersionId,jdbcType=BIGINT},
        data_set_status = #{dataSetStatus,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=BIT},
        data_source = #{dataSource,jdbcType=TINYINT},
        file_path = #{filePath,jdbcType=VARCHAR},
        file_name = #{fileName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>