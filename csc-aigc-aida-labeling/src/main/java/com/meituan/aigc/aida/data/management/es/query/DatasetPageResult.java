package com.meituan.aigc.aida.data.management.es.query;

import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 数据集分页查询结果
 *
 * <AUTHOR>
 * @date 2025/5/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DatasetPageResult {

    /**
     * 查询结果列表
     */
    private List<DatasetEsIndex> records;

    /**
     * 聚合查询结果
     */
    private Map<String, Map<String, Long>> termsAggResult;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码（从0开始）
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 查询耗时（毫秒）
     */
    private Long took;

    /**
     * 游标查询时间
     */
    private String scrollId;

    /**
     * 高亮结果
     * key: 文档ID, value: 高亮字段映射
     */
    private Map<String, Map<String, List<String>>> highlights;

    /**
     * 构建分页结果
     *
     * @param records  查询结果列表
     * @param total    总记录数
     * @param pageNum  当前页码
     * @param pageSize 每页大小
     * @param took     查询耗时
     * @param scrollId 游标ID
     * @return 分页结果
     */
    public static DatasetPageResult of(List<DatasetEsIndex> records, Long total, Integer pageNum, Integer pageSize, Long took, String scrollId) {
        // 滚动查询不分页
        if (StringUtils.isNotBlank(scrollId)) {
            return DatasetPageResult.builder()
                    .records(records)
                    .total(total)
                    .took(took)
                    .scrollId(scrollId)
                    .build();
        }

        // 计算总页数
        int totalPages = (int) Math.ceil((double) total / pageSize);

        return DatasetPageResult.builder()
                .records(records)
                .total(total)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .hasNext(pageNum < totalPages - 1)
                .hasPrevious(pageNum > 0)
                .took(took)
                .scrollId(scrollId)
                .build();
    }

    /**
     * 构建带高亮的分页结果
     *
     * @param records    查询结果列表
     * @param total      总记录数
     * @param pageNum    当前页码
     * @param pageSize   每页大小
     * @param took       查询耗时
     * @param highlights 高亮结果
     * @return 分页结果
     */
    public static DatasetPageResult ofWithHighlight(List<DatasetEsIndex> records, Long total, Integer pageNum,
                                                    Integer pageSize, Long took, Map<String, Map<String, List<String>>> highlights, String scrollId) {
        DatasetPageResult result = of(records, total, pageNum, pageSize, took, scrollId);
        result.setHighlights(highlights);
        return result;
    }

    /**
     * 是否为空结果
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取结果数量
     */
    public int getSize() {
        return records == null ? 0 : records.size();
    }
} 