package com.meituan.aigc.aida.labeling.strategy.dimension.impl;

import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.common.enums.CompareItemsConsistent;
import com.meituan.aigc.aida.labeling.common.enums.LabelingDetailStatus;
import com.meituan.aigc.aida.labeling.common.enums.sub.task.LabelingDetailSampleStatusEnum;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class CommonDimension {

    @Autowired
    private LionConfig lionConfig;

    /**
     * 构建标注详情
     *
     * @param labelingTask 任务对象
     * @param rawDataList  原始数据集
     * @param subTask      子任务对象
     * @return 标注详情列表
     */
    protected static List<LabelingDetail> getLabelingDetails(LabelingTask labelingTask,
                                                             List<LabelingTaskRawData> rawDataList, LabelingSubTask subTask) {
        List<LabelingDetail> details = new ArrayList<>();
        for (LabelingTaskRawData rawData : rawDataList) {
            LabelingDetail detail = new LabelingDetail();
            detail.setSubTaskId(subTask.getId());
            detail.setMessageId(rawData.getMessageId());
            detail.setSessionId(rawData.getSessionId());
            detail.setSessionTime(rawData.getSessionTime());
            detail.setRawDataId(rawData.getId());
            detail.setStatus(LabelingDetailStatus.WAITING_LABELING.getCode());
            detail.setSampleStatus(LabelingDetailSampleStatusEnum.NOT_SAMPLE.getCode());
            detail.setLabelingItemsResult(labelingTask.getLabelingConfig());
            detail.setComparItemsConsistent(CompareItemsConsistent.NO.getCode());
            detail.setLabelerMis(subTask.getLabelerMis());
            detail.setLabelerName(subTask.getLabelerName());
            Date date = new Date();
            detail.setCreateTime(date);
            detail.setUpdateTime(date);
            details.add(detail);
        }
        return details;
    }

    /**
     * 每批处理数
     *
     * @return 批次大小
     */
    protected Integer getBatchSize() {
        Integer batchSize = lionConfig.getPerBatchSize();
        if (batchSize == null) {
            // 默认1000
            return CommonConstants.Pagination.DEFAULT_BATCH_SIZE;
        }
        return batchSize;
    }
}
