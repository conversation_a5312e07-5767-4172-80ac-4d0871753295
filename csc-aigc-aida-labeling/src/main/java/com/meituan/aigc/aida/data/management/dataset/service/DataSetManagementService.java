package com.meituan.aigc.aida.data.management.dataset.service;

import java.util.Date;
import java.util.List;

import com.meituan.aigc.aida.data.management.dataset.dto.*;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.dataset.param.UpdateFieldParam;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;

/**
 * <AUTHOR>
 * @date 2025-05-23 14:42
 * @description 数据集管理服务
 */
public interface DataSetManagementService {

    /**
     * 根据数据获取任务id获取数据
     */
    DataFetchContent getDataFetchByTaskId(Long taskId);

    /**
     * 解析文件表头
     */
    List<DataFieldParam> parseFileHeader(DataSetParseHeadParam param);

    /**
     * 创建数据集
     *
     * @param param CreateDataSetRequestParam
     * @return 数据集id
     */
    DataSetCreateResponseParam createDataSet(DataSetCreateParam param);

    /**
     * 异步创建数据集——成功后大象通知
     *
     * @param param CreateDataSetRequestParam
     */
    void createDataSetAsync(DataSetCreateParam param);

    /**
     * 分页查询数据集列表
     */
    PageData<DataSetDTO> pageDataset(String name, Integer pageNum, Integer pageSize);

    /**
     * 分页查询数据集详情
     */
    PageData<DataSetRecordDTO> pageDataSetDetail(Long dataSetId, Long versionId, Integer pageNum, Integer pageSize);

    /**
     * 查询字段类型枚举
     */
    List<FieldEnumDTO> listFieldEnum();

    /**
     * 查询数据集字段信息
     */
    List<DataSourceField> listField(Long dataSetId, Long versionId);

    /**
     * 更新字段类型
     */
    void updateField(UpdateFieldParam updateFieldParam);


    /**
     * 导出数据集
     */
    void exportDataSet(Long dataSetId, Long versionId, Integer fileType);

    /**
     * 删除数据集
     */
    void deleteDataSet(Long dataSetId);

    /**
     * 发布数据集
     */
    void publishDataSet(Long dataSetId, Long versionId);

    /**
     * 处理数据集表头
     *
     * @param historyHeadConfig 历史表头配置
     * @param headList          表头列表
     * @param dataList          数据列表
     * @return DatasetHeadConfig
     */
    DatasetHeadConfig handleHeadList(DatasetHeadConfig historyHeadConfig, List<DataFieldParam> headList, List<DataSetRecordParam> dataList);

    /**
     * 构建数据集索引
     *
     * @param dataSetId  数据集id
     * @param versionId  版本id
     * @param dataSource 数据来源
     * @param dataList   数据列表
     * @param headConfig 表头配置
     * @param createTime 创建时间
     * @param updateTime 更新时间
     * @return 数据集索引列表
     */
    List<DatasetEsIndex> buildDatasetEsIndices(Long dataSetId, Long versionId, Integer dataSource, List<DataSetRecordParam> dataList, DatasetHeadConfig headConfig, Date createTime, Date updateTime);

    /**
     * 根据样本数据推断字段类型
     *
     * @param columnName 字段名称
     * @param dataList   样本数据列表
     * @return 推断出的字段类型
     */
    int inferFieldTypeFromSampleData(String columnName, List<DataSetRecordParam> dataList);
}
