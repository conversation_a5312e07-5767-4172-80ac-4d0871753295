package com.meituan.aigc.aida.labeling.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.labeling.common.enums.*;
import com.meituan.aigc.aida.labeling.dao.repository.PersonLabelingStatisticsRepository;
import com.meituan.aigc.aida.labeling.dao.repository.QualityCheckStatisticsRepository;
import com.meituan.aigc.aida.labeling.dao.repository.TaskStatisticsRepository;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingServiceException;
import com.meituan.aigc.aida.labeling.param.*;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.*;
import com.meituan.aigc.aida.labeling.pojo.vo.DailyTrendsVO;
import com.meituan.aigc.aida.labeling.pojo.vo.MetricDataPoint;
import com.meituan.aigc.aida.labeling.pojo.vo.MetricVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.*;
import com.meituan.aigc.aida.labeling.service.DashboardStatisticsService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.sankuai.csccratos.csc.aida.label.client.api.AidaSceneRemoteService;
import com.sankuai.csccratos.csc.aida.label.client.dto.common.AidaResponse;
import com.sankuai.csccratos.csc.aida.label.client.dto.scene.AidaSceneDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 15:05
 * @Version: 1.0
 */
@Slf4j
@Service
public class DashboardStatisticsServiceImpl implements DashboardStatisticsService {

    @Resource
    private PersonLabelingStatisticsRepository personLabelingStatisticsRepository;
    @Resource
    private QualityCheckStatisticsRepository qualityCheckStatisticsRepository;
    @Resource
    private TaskStatisticsRepository taskStatisticsRepository;
    @Resource
    private AidaSceneRemoteService aidaSceneRemoteService;

    /**
     * 质检人员排行榜统计图表
     * @param param 请求参数
     * @return 结果
     */
    @Override
    public List<MetricVO> getPersonStatisticalCharts(PersonStatisticsRequestParam param) {
        // 参数校验
        checkParamOfDate(param);
        // 查询数据
        PersonLabelingStatisticsDTO personLabelingStatisticsDTO = buildPersonLabelingStatisticsDTO(param);

        // 查询结果处理
        List<PersonLabelingQueryVO> stats = personLabelingStatisticsRepository.queryPersonLabelingStats(personLabelingStatisticsDTO);
        if (CollectionUtils.isEmpty(stats)) {
            return Collections.emptyList();
        }

        // 计算质检正确率
        List<MetricDataPoint> dataPoints = new ArrayList<>();
        for (PersonLabelingQueryVO stat : stats) {
            double accuracy = calculateAccuracyDouble(stat.getQcInspectedCount(), stat.getQcInspectedCorrectCount(), 1);
            dataPoints.add(new MetricDataPoint(stat.getLabelerName(), accuracy));
        }

        // 根据查询类型排序
        Integer queryType = param.getQueryType();
        // 只判断后10名，其他默认为前10名
        if (Objects.equals(OrderQueryTypeEnum.BOTTOM_TEN.getCode(), queryType)) {
            dataPoints = dataPoints.stream()
                    .sorted(Comparator.comparingDouble(MetricDataPoint::getValue))
                    .limit(10)
                    .collect(Collectors.toList());
        }else{
            dataPoints = dataPoints.stream()
                    .sorted(Comparator.comparingDouble(MetricDataPoint::getValue).reversed())
                    .limit(10)
                    .collect(Collectors.toList());
        }

        // 组装返回结果
        MetricVO metricVO = new MetricVO("质检正确率", dataPoints);
        return Collections.singletonList(metricVO);
    }

    /**
     * 每日标注/质检趋势
     * @param param 请求参数
     * @return 每日标注/质检趋势
     */
    @Override
    public DailyTrendsVO getDailyTrends(DailyStatisticsRequestParam param) {
        // 参数校验
        checkParamOfDate(param);
        // 如果前端传了参数，则按照前端传的参数查询
        if(param.getQueryType() == null || param.getQueryType() == 0){
            param.setQueryType(InspectedTypeEnum.LABELING.getCode());
        }
        // 构建查询
        DailyStatisticsDTO dailyStatisticsDTO = buildDailyStatisticsDTO(param);
        DailyStatisticsDTO lastDailyStatisticsDTO = buildDailyStatisticsDTO(param);
        // 日期格式转换
        lastDailyStatisticsDTO.setStartDate(DateUtil.getPreviousWeekDate(dailyStatisticsDTO.getStartDate()));
        lastDailyStatisticsDTO.setEndDate(DateUtil.getPreviousWeekDate(dailyStatisticsDTO.getEndDate()));
        // 预设返回值
        List<String> currentPeriodDates = DateUtil.getDateRangeList(param.getStartDate(), param.getEndDate());
        List<String> previousPeriodDates = DateUtil.getDateRangeList(DateUtil.formatDate(lastDailyStatisticsDTO.getStartDate()), DateUtil.formatDate(lastDailyStatisticsDTO.getEndDate()));
        DailyTrendsVO dailyTrendsVO = buildDailyTrendsVO(currentPeriodDates);
        // 获得本周和上周数据
        List<DailyBizStatisticsVO> curInspectedItems;
        List<DailyBizStatisticsVO> preInspectedItems;
        // 只判断是不是质检，其他都是标注
        if(Objects.equals(InspectedTypeEnum.QUALITY_INSPECTION.getCode(), param.getQueryType())){
            curInspectedItems = qualityCheckStatisticsRepository.queryDailyTrends(dailyStatisticsDTO);
            if (CollectionUtils.isEmpty(curInspectedItems)) {
                log.warn("每日趋势查询统计质检数据为空直接返回");
                return dailyTrendsVO;
            }
            lastDailyStatisticsDTO.setBizTypes(curInspectedItems.stream().map(DailyBizStatisticsVO::getBizTypeId).distinct().collect(Collectors.toList()));
            preInspectedItems = qualityCheckStatisticsRepository.queryDailyTrends(lastDailyStatisticsDTO);
        }else{
            curInspectedItems = personLabelingStatisticsRepository.queryDailyTrends(dailyStatisticsDTO);
            if (CollectionUtils.isEmpty(curInspectedItems)) {
                log.warn("每日趋势查询统计标注数据为空直接返回");
                return dailyTrendsVO;
            }
            lastDailyStatisticsDTO.setBizTypes(curInspectedItems.stream().map(DailyBizStatisticsVO::getBizTypeId).distinct().collect(Collectors.toList()));
            preInspectedItems = personLabelingStatisticsRepository.queryDailyTrends(lastDailyStatisticsDTO);
        }
        // 查询bizType映射关系
        Map<String, List<BaseNameCodeVO>> allBizTypesMap = getBizTypeIdNameMap();
        // 数据处理+计算周同比
        List<DailyTrendsVO.InspectedPoint> curInspectedPoints = mapItemToInspectedPoint(curInspectedItems, currentPeriodDates, allBizTypesMap);
        List<DailyTrendsVO.InspectedPoint> preInspectedPoints = mapItemToInspectedPoint(preInspectedItems, previousPeriodDates, allBizTypesMap);
        List<DailyTrendsVO.InspectedWeekPoint> weekOnWeekChanges = computeWeekOnWeekChange(curInspectedPoints, preInspectedPoints, currentPeriodDates);
        // 组装结果
        dailyTrendsVO.setSeries(curInspectedPoints);
        dailyTrendsVO.setLastWeekSeries(preInspectedPoints);
        dailyTrendsVO.setWeekOnWeekChange(weekOnWeekChanges);

        return dailyTrendsVO;
    }

    @NotNull
    private Map<String, List<BaseNameCodeVO>> getBizTypeIdNameMap() {
        List<BaseNameCodeVO> allBizTypes = getAllBizTypes();
        handleOtherType(allBizTypes);
        return allBizTypes.stream().collect(Collectors.groupingBy(BaseNameCodeVO::getCode));
    }

    /**
     * 获取业务质检正确率
     * @param param 请求参数
     * @return 业务质检正确率
     */
    @Override
    public BizQualityCheckRateVO getBizQualityCheckRate(BaseStatisticsRequestParam param) {
        // 参数校验
        checkParamOfDate(param);
        // 构建DTO
        BaseStatisticsDTO baseStatisticsDTO = buildBaseStatisticsDTO(param);
        // 返回结果
        BizQualityCheckRateVO bizQualityCheckRateVO = new BizQualityCheckRateVO();
        // 查询数据
        List<BizQualityCheckQueryVO> bizQualityCheckQueries;
        if(CollectionUtils.isNotEmpty(param.getBizTypes()) && param.getBizTypes().size() == 1){
            // 单个业务
            bizQualityCheckQueries = qualityCheckStatisticsRepository.querySingleBizQualityCheckRateTrends(baseStatisticsDTO);
            if (CollectionUtils.isEmpty(bizQualityCheckQueries)) {
                log.warn("单个业务查询业务质检正确率结果为空！");
                return bizQualityCheckRateVO;
            }
            bizQualityCheckQueries.forEach(bizQualityCheckQuery -> {
                bizQualityCheckQuery.setQualityRate(calculateAccuracyDouble(bizQualityCheckQuery.getTotalCount(), bizQualityCheckQuery.getTotalCorrectCount(), 1));
            });
            List<String> allDatesInRange = DateUtil.getDateRangeList(param.getStartDate(), param.getEndDate());
            // 日期补全
            bizQualityCheckQueries = repairQualityCheckQueries(bizQualityCheckQueries, allDatesInRange);
            // 组装返回结果
            bizQualityCheckRateVO.setDates(allDatesInRange);
            bizQualityCheckRateVO.setQualityRates(bizQualityCheckQueries.stream().map(BizQualityCheckQueryVO::getQualityRate).collect(Collectors.toList()));
        }else{
            // 多个业务
            bizQualityCheckQueries = qualityCheckStatisticsRepository.queryBizQualityCheckRate(baseStatisticsDTO);
            bizQualityCheckQueries.forEach(bizQualityCheckQuery -> {
                bizQualityCheckQuery.setQualityRate(calculateAccuracyDouble(bizQualityCheckQuery.getTotalCount(), bizQualityCheckQuery.getTotalCorrectCount(), 1));
            });
            if (CollectionUtils.isEmpty(bizQualityCheckQueries)) {
                log.warn("多个业务查询业务质检正确率结果为空！");
                return bizQualityCheckRateVO;
            }
            // 组装返回结果
            List<Long> bizTypeIds = bizQualityCheckQueries.stream().map(BizQualityCheckQueryVO::getBizType).collect(Collectors.toList());
            bizQualityCheckRateVO.setBizTypes(getNameListByCodeList(bizTypeIds));
            bizQualityCheckRateVO.setQualityRates(bizQualityCheckQueries.stream().map(BizQualityCheckQueryVO::getQualityRate).collect(Collectors.toList()));
        }

        return bizQualityCheckRateVO;
    }

    /**
     * 获取人员明细详情
     * @param param 请求参数
     * @return 人员明细详情
     */
    @Override
    public PageData<PersonnelDetailVO> getPersonnelDetail(PersonnelDetailRequestParam param) {
        // 设置默认值
        int pageNum = Objects.isNull(param.getPageNum()) || param.getPageNum() < 1 ? 1 : param.getPageNum();
        int pageSize = Objects.isNull(param.getPageSize()) || param.getPageSize() < 1 ? 5 : param.getPageSize();
        // 参数校验
        checkParamOfDate(param);
        // 构建DTO
        PersonnelDetailQuery personnelDetailQuery = buildPersonnelDetailQuery(param);
        if (Objects.isNull(param.getQueryType())) {
            personnelDetailQuery.setQueryType(InspectedTypeEnum.LABELING.getCode());
        }
        // 分页查询
        PageHelper.startPage(pageNum, pageSize);
        List<PersonnelDetailVO> personnelDetail;
        if(Objects.equals(personnelDetailQuery.getQueryType(), InspectedTypeEnum.QUALITY_INSPECTION.getCode())){
            personnelDetail = qualityCheckStatisticsRepository.queryPersonnelDetail(personnelDetailQuery);
        }else{
            personnelDetail = personLabelingStatisticsRepository.queryPersonnelDetail(personnelDetailQuery);
        }
        PageHelper.clearPage();
        if (CollectionUtils.isEmpty(personnelDetail)){
            return new PageData<>(0L, null);
        }
        // 处理业务类型
        handleBizType(personnelDetail);
        PageInfo<PersonnelDetailVO> personnelDetailVOPageInfo = new PageInfo<>(personnelDetail);
        long totalCount = personnelDetailVOPageInfo.getTotal();

        return new PageData<>(totalCount, personnelDetail);
    }

    private void handleBizType(List<PersonnelDetailVO> personnelDetail) {
        List<BaseNameCodeVO> allBizTypes = getAllBizTypes();
        handleOtherType(allBizTypes);
        Map<String, List<BaseNameCodeVO>> bizTypeMap = allBizTypes.stream().collect(Collectors.groupingBy(BaseNameCodeVO::getCode));
        personnelDetail.forEach(detail -> {
            if (CollectionUtils.isNotEmpty(bizTypeMap.get(detail.getBizType()))) {
                detail.setBizType(bizTypeMap.get(detail.getBizType()).get(0).getName());
            }
        });
    }

    @Override
    public PageData<DashboardTaskItemVO> pageTasks(DashboardTaskListQueryParam param) {
        // 设置默认值
        int pageNum = Objects.isNull(param.getPageNum()) || param.getPageNum() < 1 ? 1 : param.getPageNum();
        int pageSize = Objects.isNull(param.getPageSize()) || param.getPageSize() < 1 ? 10 : param.getPageSize();
        PageHelper.startPage(pageNum, pageSize);
        // 构建参数
        DashboardTaskListQuery dashboardTaskListQuery = buildDashboardTaskListQuery(param);
        // 查询数据
        List<DashboardTaskItemVO> pageData = taskStatisticsRepository.listTasks(dashboardTaskListQuery);
        handlePageTaskData(pageData);
        PageInfo<DashboardTaskItemVO> dashboardTaskItemPageInfo = new PageInfo<>(pageData);
        Long totalCount = dashboardTaskItemPageInfo.getTotal();
        PageHelper.clearPage();

        return new PageData<>(totalCount, pageData);
    }

    private void handlePageTaskData(List<DashboardTaskItemVO> pageData) {
        if (CollectionUtils.isNotEmpty(pageData)) {
            Map<String, List<BaseNameCodeVO>> bizTypeIdNameMap = getBizTypeIdNameMap();
            pageData.forEach(taskItem -> {
                if (Objects.nonNull(taskItem.getLabelingStatus())) {
                    LabelingTaskStatus labelingTaskStatus = LabelingTaskStatus.getByCode(taskItem.getLabelingStatus());
                    taskItem.setStatusName(Objects.nonNull(labelingTaskStatus) ? labelingTaskStatus.getValue() : "-");
                }
                if (Objects.nonNull(taskItem.getQualityCheckStatus())) {
                    QualityCheckTaskStatus qualityCheckTaskStatus = QualityCheckTaskStatus.getByCode(taskItem.getQualityCheckStatus());
                    taskItem.setQualityCheckStatusName(Objects.nonNull(qualityCheckTaskStatus) ? qualityCheckTaskStatus.getValue() : "-");
                }
                List<BaseNameCodeVO> bizNameCode = bizTypeIdNameMap.get(String.valueOf(taskItem.getBizType()));
                taskItem.setBizTypeName(CollectionUtils.isNotEmpty(bizNameCode) ? bizNameCode.get(0).getName() : String.valueOf(taskItem.getBizType()));
            });
        }
    }

    @Override
    public BizTypeAndTaskTypeQueryVO queryCondition() {
        BizTypeAndTaskTypeQueryVO bizTypeAndTaskType = new BizTypeAndTaskTypeQueryVO();
        List<BaseNameCodeVO> bizTypes = getBizTypes();
        handleOtherType(bizTypes);
        bizTypeAndTaskType.setBizTypes(bizTypes);
        bizTypeAndTaskType.setTaskTypes(getTaskTypes());
        return bizTypeAndTaskType;
    }

    @Override
    public DashboardOverviewStatsVO overviewStats(BaseStatisticsRequestParam param) {
        checkParamOfDate(param);
        // 本周查询条件
        DataStatisticalQuery query = buildDataStatisticalQuery(param);
        // 上周查询条件
        DataStatisticalQuery previousWeekQuery = buildPreviousWeekQuery(param);
        // 任务数据统计
        DashboardTaskCountVO dashboardTaskCountVO = buildDashboardTaskCountVO(query, previousWeekQuery);
        // 标注数据统计
        DashboardLabelingCountVO dashboardLabelingCountVO = buildDashboardLabelingCountVO(query, previousWeekQuery);
        // 质检数据统计
        DashboardQualityCheckCountVO dashboardQualityCheckCountVO = buildDashboardQualityCheckCountVO(query, previousWeekQuery);
        return new DashboardOverviewStatsVO(dashboardTaskCountVO, dashboardLabelingCountVO, dashboardQualityCheckCountVO);
    }

    @Override
    public List<BaseNameCodeVO> getBizTypes() {
        List<BaseNameCodeVO> bizTypes = new ArrayList<>();
        try {
            AidaResponse<List<AidaSceneDTO>> listAidaResponse = aidaSceneRemoteService.listAllScenes();
            if (Objects.isNull(listAidaResponse)) {
                log.error("调用AidaSceneRemoteService#listAllScenes查询业务场景列表无结果！");
                throw new AidaTrainingServiceException("查询业务场景列表无结果！");
            }
            if (Objects.isNull(listAidaResponse.getCode()) || listAidaResponse.getCode() != 0) {
                log.error("调用AidaSceneRemoteService#listAllScenes查询业务场景失败！");
                throw new AidaTrainingServiceException("查询业务场景列表失败！原因：" + listAidaResponse.getMessage());
            }
            List<AidaSceneDTO> allScenes = listAidaResponse.getData();
            if (CollectionUtils.isEmpty(allScenes)) {
                log.warn("调用AidaSceneRemoteService#listAllScenes查询业务场景，未查询到启用场景");
                return bizTypes;
            }
            allScenes.forEach(scene ->{
                BaseNameCodeVO baseNameCode = new BaseNameCodeVO();
                baseNameCode.setCode(String.valueOf(scene.getId()));
                baseNameCode.setName(scene.getSceneName());
                bizTypes.add(baseNameCode);
            });

        } catch (Exception e) {
            log.error("调用AidaSceneRemoteService#listAllScenes查询业务场景列表异常！", e);
        }
        return bizTypes;
    }

    @Override
    public List<BaseNameCodeVO> getAllBizTypes() {
        List<BaseNameCodeVO> bizTypes = new ArrayList<>();
        try {
            AidaResponse<List<AidaSceneDTO>> listAidaResponse = aidaSceneRemoteService.listAllScenesIncludeDisabled();
            if (Objects.isNull(listAidaResponse)) {
                log.error("调用AidaSceneRemoteService#listAllScenesIncludeDisabled查询所有业务场景列表无结果！");
                throw new AidaTrainingServiceException("查询所有业务场景列表无结果！");
            }
            if (Objects.isNull(listAidaResponse.getCode()) || listAidaResponse.getCode() != 0) {
                log.error("调用AidaSceneRemoteService#listAllScenesIncludeDisabled查询所有业务场景失败！");
                throw new AidaTrainingServiceException("查询所有业务场景列表失败！原因：" + listAidaResponse.getMessage());
            }
            List<AidaSceneDTO> allScenes = listAidaResponse.getData();
            if (CollectionUtils.isEmpty(allScenes)) {
                log.warn("调用AidaSceneRemoteService#listAllScenesIncludeDisabled查询所有业务场景，未查询到启用场景");
                return bizTypes;
            }
            allScenes.forEach(scene ->{
                BaseNameCodeVO baseNameCode = new BaseNameCodeVO();
                baseNameCode.setCode(String.valueOf(scene.getId()));
                baseNameCode.setName(scene.getSceneName());
                bizTypes.add(baseNameCode);
            });
        } catch (Exception e) {
            log.error("调用AidaSceneRemoteService#listAllScenesIncludeDisabled查询所有业务场景列表异常！", e);
        }
        return bizTypes;
    }
    
    public List<String> getNameListByCodeList(List<Long> bizTypeIds) {
        if (CollectionUtils.isEmpty(bizTypeIds)) {
            return Collections.emptyList();
        }
        List<BaseNameCodeVO> allBizTypes = getAllBizTypes();
        handleOtherType(allBizTypes);
        Map<String, BaseNameCodeVO> bizTypesMap = allBizTypes.stream().collect(Collectors.toMap(BaseNameCodeVO::getCode, Function.identity()));
        List<String> bizTypeNames = new ArrayList<>();
        bizTypeIds.forEach(bizType -> {
            BaseNameCodeVO baseNameCodeVO = bizTypesMap.get(String.valueOf(bizType));
            if (Objects.isNull(baseNameCodeVO)) {
                bizTypeNames.add("-");
            } else {
                bizTypeNames.add(baseNameCodeVO.getName());
            } 
        });
        return bizTypeNames;
    }

    private static void handleOtherType(List<BaseNameCodeVO> bizTypes) {
        // 特殊处理兼容历史数据
        BaseNameCodeVO other = new BaseNameCodeVO();
        other.setCode("-1");
        other.setName("其他");
        bizTypes.add(other);
    }
    
    @NotNull
    private DashboardQualityCheckCountVO buildDashboardQualityCheckCountVO(DataStatisticalQuery query, DataStatisticalQuery previousWeekQuery) {
        // 本周质检信息统计
        LabelingAndCheckCountVO checkCount = qualityCheckStatisticsRepository.statisticalQualityCheckData(query);
        // 上周质检信息统计
        LabelingAndCheckCountVO previousWeekCheckCount = qualityCheckStatisticsRepository.statisticalQualityCheckData(previousWeekQuery);
        // 计数质检指标
        DashboardQualityCheckCountVO dashboardQualityCheckCountVO = new DashboardQualityCheckCountVO();
        dashboardQualityCheckCountVO.setCheckedCount(checkCount.getQuantity());
        Double checkEfficiency = calculateHumanEffect(checkCount.getQuantity(), checkCount.getPeopleNum());
        dashboardQualityCheckCountVO.setCheckEfficiency(checkEfficiency);
        dashboardQualityCheckCountVO.setPeopleNum(checkCount.getPeopleNum());
        dashboardQualityCheckCountVO.setWowCheckedRate(calculateWeekOnWeekGrowthAsDouble(checkCount.getQuantity(), previousWeekCheckCount.getQuantity(), 1));
        Double previousWeekCheckEfficiency = calculateHumanEffect(previousWeekCheckCount.getQuantity(), previousWeekCheckCount.getPeopleNum());
        dashboardQualityCheckCountVO.setWowCheckEfficiency(calculateWeekOnWeekGrowthAsDouble(checkEfficiency, previousWeekCheckEfficiency, 1));
        dashboardQualityCheckCountVO.setWowPeopleRate(calculateWeekOnWeekGrowthAsDouble(checkCount.getPeopleNum(), previousWeekCheckCount.getPeopleNum(), 1));
        return dashboardQualityCheckCountVO;
    }

    @NotNull
    private DashboardLabelingCountVO buildDashboardLabelingCountVO(DataStatisticalQuery query, DataStatisticalQuery previousWeekQuery) {
        // 本周标注信息统计
        LabelingAndCheckCountVO labelingCount = personLabelingStatisticsRepository.statisticalLabelingData(query);
        // 上周标注信息统计
        LabelingAndCheckCountVO previousWeekLabelingCount = personLabelingStatisticsRepository.statisticalLabelingData(previousWeekQuery);
        // 计数标注指标
        DashboardLabelingCountVO dashboardLabelingCountVO = new DashboardLabelingCountVO();
        dashboardLabelingCountVO.setLabeledCount(labelingCount.getQuantity());
        Double labelingEfficiency = calculateHumanEffect(labelingCount.getQuantity(), labelingCount.getPeopleNum());
        dashboardLabelingCountVO.setLabelingEfficiency(labelingEfficiency);
        dashboardLabelingCountVO.setPeopleNum(labelingCount.getPeopleNum());
        dashboardLabelingCountVO.setWowLabeledRate(calculateWeekOnWeekGrowthAsDouble(labelingCount.getQuantity(), previousWeekLabelingCount.getQuantity(), 1));
        Double previousWeekLabelingEfficiency = calculateHumanEffect(previousWeekLabelingCount.getQuantity(), previousWeekLabelingCount.getPeopleNum());
        dashboardLabelingCountVO.setWowLabelingEfficiency(calculateWeekOnWeekGrowthAsDouble(labelingEfficiency, previousWeekLabelingEfficiency, 1));
        dashboardLabelingCountVO.setWowPeopleRate(calculateWeekOnWeekGrowthAsDouble(labelingCount.getPeopleNum(), previousWeekLabelingCount.getPeopleNum(), 1));
        return dashboardLabelingCountVO;
    }

    @NotNull
    private DashboardTaskCountVO buildDashboardTaskCountVO(DataStatisticalQuery query, DataStatisticalQuery previousWeekQuery) {
        // 本周任务数据统计
        TaskStatisticalVO dashboardTaskCount = taskStatisticsRepository.statisticalTaskData(query);
        // 上周任务数据统计
        TaskStatisticalVO previousWeekDashboardTaskCount = taskStatisticsRepository.statisticalTaskData(previousWeekQuery);
        // 计数任务指标
        DashboardTaskCountVO dashboardTaskCountVO = new DashboardTaskCountVO();
        dashboardTaskCountVO.setTotalTaskCount(dashboardTaskCount.getTotalTaskCount());
        dashboardTaskCountVO.setLabelingCompletedTaskCount(dashboardTaskCount.getLabelingCompletedTaskCount());
        dashboardTaskCountVO.setCheckCompletedTaskCount(dashboardTaskCount.getCheckCompletedTaskCount());
        dashboardTaskCountVO.setTotalSampleCount(dashboardTaskCount.getTotalSampleCount());
        dashboardTaskCountVO.setWowTotalTaskRate(calculateWeekOnWeekGrowthAsDouble(dashboardTaskCount.getTotalTaskCount(), previousWeekDashboardTaskCount.getTotalTaskCount(), 1));
        dashboardTaskCountVO.setWowCompletedTaskRate(calculateWeekOnWeekGrowthAsDouble(dashboardTaskCount.getLabelingCompletedTaskCount(), previousWeekDashboardTaskCount.getLabelingCompletedTaskCount(), 1));
        dashboardTaskCountVO.setWowCheckCompletedTaskRate(calculateWeekOnWeekGrowthAsDouble(dashboardTaskCount.getCheckCompletedTaskCount(), previousWeekDashboardTaskCount.getCheckCompletedTaskCount(), 1));
        dashboardTaskCountVO.setWowTotalSampleRate(calculateWeekOnWeekGrowthAsDouble(dashboardTaskCount.getTotalSampleCount(), previousWeekDashboardTaskCount.getTotalSampleCount(), 1));
        return dashboardTaskCountVO;
    }


    /**
     * 计数人效
     * @param completedNum 完成数量
     * @param peopleNum 人数
     * @return 人效
     */
    public Double calculateHumanEffect(Integer completedNum, Integer peopleNum) {
        if (Objects.isNull(peopleNum) || peopleNum == 0) {
            // 如果 completedNum 也为 null 或 0, 人效为 0.0
            if (Objects.isNull(completedNum) || completedNum == 0) {
                return 0.0d;
            }
            return Double.valueOf(completedNum);
        }
        if (Objects.isNull(completedNum) || completedNum == 0) {
            return 0.0d;
        }
        BigDecimal bdCompleted = BigDecimal.valueOf(completedNum);
        BigDecimal bdPeople = BigDecimal.valueOf(peopleNum);
        BigDecimal effectRatio = bdCompleted.divide(bdPeople, 4, RoundingMode.CEILING);
        // 将计算结果四舍五入到一位小数
        BigDecimal finalRoundedResult = effectRatio.setScale(1, RoundingMode.CEILING);

        return finalRoundedResult.doubleValue();
    }

    /**
     * 计算周同比增长率并返回Double类型
     *
     * @param currentValue 本周数值
     * @param previousValue 上周同期数值
     * @param scale 精度(小数位数)
     * @return 同比增长率(Double类型)
     */
    public static Double calculateWeekOnWeekGrowthAsDouble(Integer currentValue, Integer previousValue, int scale) {
        if (Objects.isNull(previousValue) || previousValue == 0) {
            if (Objects.isNull(currentValue) || currentValue == 0) {
                return 0.0d;
            }
            return 100.0d;
        }

        double growthRate = (double) (currentValue - previousValue) / previousValue * 100;
        BigDecimal bigDecimal = BigDecimal.valueOf(growthRate);
        bigDecimal = bigDecimal.setScale(scale, RoundingMode.HALF_UP);

        return bigDecimal.doubleValue();
    }

    /**
     * 计算周同比增长率并返回Double类型
     *
     * @param currentValue 本周数值
     * @param previousValue 上周同期数值
     * @param scale 精度(小数位数)
     * @return 同比增长率(Double类型)
     */
    public static Double calculateWeekOnWeekGrowthAsDouble(Double currentValue, Double previousValue, int scale) {
        if (Objects.isNull(previousValue) || previousValue == 0) {
            if (Objects.isNull(currentValue) || currentValue == 0) {
                return 0.0d;
            }
            return 100.0d;
        }

        double growthRate = (currentValue - previousValue) / previousValue * 100;
        BigDecimal bigDecimal = BigDecimal.valueOf(growthRate);
        bigDecimal = bigDecimal.setScale(scale, RoundingMode.HALF_UP);

        return bigDecimal.doubleValue();
    }

    public static Double calculateAccuracyDouble(Integer totalCount, Integer correctCount, int scale){
        if(Objects.isNull(totalCount) || totalCount == 0){
            if(Objects.isNull(correctCount) || correctCount == 0){
                return null;
            }
            return 100.0;
        }
        double accuracy = (double) correctCount / totalCount * 100;
        BigDecimal bigDecimal = BigDecimal.valueOf(accuracy);
        bigDecimal = bigDecimal.setScale(scale, RoundingMode.HALF_UP);

        return bigDecimal.doubleValue();
    }

    public static Double calculateAccuracyDouble(Long totalCount, Long correctCount, int scale){
        if(Objects.isNull(totalCount) || totalCount == 0){
            if(Objects.isNull(correctCount) || correctCount == 0){
                return null;
            }
            return 100.0;
        }
        double accuracy = (double) correctCount / totalCount * 100;
        BigDecimal bigDecimal = BigDecimal.valueOf(accuracy);
        bigDecimal = bigDecimal.setScale(scale, RoundingMode.HALF_UP);

        return bigDecimal.doubleValue();
    }


    @NotNull
    private static DataStatisticalQuery buildPreviousWeekQuery(BaseStatisticsRequestParam param) {
        DataStatisticalQuery previousWeekQuery = new DataStatisticalQuery();
        previousWeekQuery.setBizTypes(CollectionUtils.isEmpty(param.getBizTypes()) ? null : param.getBizTypes());
        previousWeekQuery.setTaskType(param.getTaskType());
        previousWeekQuery.setDataType(param.getDataType());
        previousWeekQuery.setStartDate(DateUtil.getPreviousWeekDate(DateUtil.parseToDate(param.getStartDate())));
        previousWeekQuery.setEndDate(DateUtil.getPreviousWeekDate(DateUtil.parseToDate(param.getEndDate())));
        return previousWeekQuery;
    }

    @NotNull
    private static DataStatisticalQuery buildDataStatisticalQuery(BaseStatisticsRequestParam param) {
        DataStatisticalQuery query = new DataStatisticalQuery();
        query.setBizTypes(CollectionUtils.isEmpty(param.getBizTypes()) ? null : param.getBizTypes());
        query.setTaskType(param.getTaskType());
        query.setDataType(param.getDataType());
        query.setStartDate(DateUtil.parseToDate(param.getStartDate()));
        query.setEndDate(DateUtil.parseToDate(param.getEndDate()));
        return query;
    }

    private List<BizTypeAndTaskTypeQueryVO.TaskTypeVO> getTaskTypes(){
        List<BizTypeAndTaskTypeQueryVO.TaskTypeVO> taskTypes = new ArrayList<>();
        BizTypeAndTaskTypeQueryVO.TaskTypeVO trainingSet = buildTrainingSet();
        BizTypeAndTaskTypeQueryVO.TaskTypeVO onlineSession = buildOnlineSession();
        taskTypes.add(trainingSet);
        taskTypes.add(onlineSession);
        return taskTypes;
    }

    private BizTypeAndTaskTypeQueryVO.TaskTypeVO buildOnlineSession() {
        BizTypeAndTaskTypeQueryVO.TaskTypeVO trainingSet = new BizTypeAndTaskTypeQueryVO.TaskTypeVO();
        trainingSet.setCode(String.valueOf(LabelingTaskType.ONLINE_SESSION_LABELING.getCode()));
        trainingSet.setName(LabelingTaskType.ONLINE_SESSION_LABELING.getValue());
        List<BaseNameCodeVO> onlineSessionDataTypes = buildOnlineSessionDataTypes();
        trainingSet.setDataTypes(onlineSessionDataTypes);
        return trainingSet;
    }

    private List<BaseNameCodeVO> buildOnlineSessionDataTypes(){
        List<BaseNameCodeVO> onlineSessionDataTypes = new ArrayList<>();
        BaseNameCodeVO session = new BaseNameCodeVO();
        session.setCode(String.valueOf(LabelTaskDataType.SESSION_ANNOTATION.getCode()));
        session.setName(LabelTaskDataType.SESSION_ANNOTATION.getValue());
        onlineSessionDataTypes.add(session);
        BaseNameCodeVO query = new BaseNameCodeVO();
        query.setCode(String.valueOf(LabelTaskDataType.QUERY_ANNOTATION.getCode()));
        query.setName(LabelTaskDataType.QUERY_ANNOTATION.getValue());
        onlineSessionDataTypes.add(query);
        return onlineSessionDataTypes;
    }


    private BizTypeAndTaskTypeQueryVO.TaskTypeVO buildTrainingSet() {
        BizTypeAndTaskTypeQueryVO.TaskTypeVO trainingSet = new BizTypeAndTaskTypeQueryVO.TaskTypeVO();
        trainingSet.setCode(String.valueOf(LabelingTaskType.TRAINING_SET_LABELING.getCode()));
        trainingSet.setName(LabelingTaskType.TRAINING_SET_LABELING.getValue());
        List<BaseNameCodeVO> trainingSetDataTypes = buildTrainingSetDataTypes();
        trainingSet.setDataTypes(trainingSetDataTypes);
        return trainingSet;
    }

    @NotNull
    private List<BaseNameCodeVO> buildTrainingSetDataTypes() {
        List<BaseNameCodeVO> trainingSetDataTypes = new ArrayList<>();
        BaseNameCodeVO sft = new BaseNameCodeVO();
        sft.setCode(String.valueOf(LabelTaskDataType.SFT_FINE_TUNING.getCode()));
        sft.setName(LabelTaskDataType.SFT_FINE_TUNING.getValue());
        trainingSetDataTypes.add(sft);
        BaseNameCodeVO dpo = new BaseNameCodeVO();
        dpo.setCode(String.valueOf(LabelTaskDataType.DPO_ALIGNMENT.getCode()));
        dpo.setName(LabelTaskDataType.DPO_ALIGNMENT.getValue());
        trainingSetDataTypes.add(dpo);
        return trainingSetDataTypes;
    }

    @NotNull
    private static DashboardTaskListQuery buildDashboardTaskListQuery(DashboardTaskListQueryParam param) {
        DashboardTaskListQuery dashboardTaskListQuery = new DashboardTaskListQuery();
        dashboardTaskListQuery.setBizTypes(CollectionUtils.isEmpty(param.getBizTypes()) ? null : param.getBizTypes());
        dashboardTaskListQuery.setTaskType(param.getTaskType());
        dashboardTaskListQuery.setDataType(param.getDataType());
        dashboardTaskListQuery.setStartDate(DateUtil.parseToDate(param.getStartDate()));
        dashboardTaskListQuery.setEndDate(DateUtil.parseToDate(param.getEndDate()));
        dashboardTaskListQuery.setQueryType(param.getQueryType());
        return dashboardTaskListQuery;
    }

    private <T extends BaseStatisticsRequestParam> void checkParamOfDate(T param) {
        CheckUtil.paramCheck(ObjectUtil.isNotNull(param.getStartDate()) && ObjectUtil.isNotNull(param.getEndDate()), "开始日期和结束日期不能为空");
        CheckUtil.paramCheck(!DateUtil.parseToDate(param.getStartDate()).after(DateUtil.parseToDate(param.getEndDate())), "开始日期不能大于结束日期");
    }

    /**
     * 将DailyBizStatisticsDTO列表转换为DailyTrendsVO.InspectedPoint列表，包含选中业务数据补全和选中日期数据补全
     * @param items 需要转换的DailyBizStatisticsDTO列表
     * @param referenceDates 参考日期列表
     */
    private List<DailyTrendsVO.InspectedPoint> mapItemToInspectedPoint(List<DailyBizStatisticsVO> items, List<String> referenceDates, Map<String, List<BaseNameCodeVO>> allBizTypesMap) {
        List<DailyTrendsVO.InspectedPoint> points = new ArrayList<>();
        if (CollectionUtils.isEmpty(items) || CollectionUtils.isEmpty(referenceDates)){
            log.warn("每日趋势查询统计数据为空，跳过处理items = {}", JSON.toJSONString(items));
            return points;
        }
        List<Long> bizTypeList = items.stream().map(DailyBizStatisticsVO::getBizTypeId).distinct().collect(Collectors.toList());
        // 按照业务类型分组
        Map<String, List<DailyBizStatisticsVO>> bizDailyStat = items.stream().collect(Collectors.groupingBy(
                dailyBizStatisticsVO -> dailyBizStatisticsVO.getBizTypeId() + "_" + dailyBizStatisticsVO.getDate()));
        // 组合每个业务每天的数据
        bizTypeList.forEach(bizType -> {
            String strBizType = String.valueOf(bizType);
            DailyTrendsVO.InspectedPoint inspectedPoint = new DailyTrendsVO.InspectedPoint();
            inspectedPoint.setName(CollectionUtils.isNotEmpty(allBizTypesMap.get(strBizType)) ? allBizTypesMap.get(strBizType).get(0).getName() : strBizType);
            List<Integer> dayDatas = new ArrayList<>();
            referenceDates.forEach(date -> {
                List<DailyBizStatisticsVO> dailyBizStatistics = bizDailyStat.get(bizType + "_" + date);
                if (CollectionUtils.isEmpty(dailyBizStatistics)) {
                    dayDatas.add(0);
                } else {
                    dayDatas.add(dailyBizStatistics.get(0).getDailyInspectedCount());
                }
            });
            inspectedPoint.setData(dayDatas);
            points.add(inspectedPoint);
        });
        return points;
    }

    /**
     * 计算周同比
     * @param curPoints 当前周的InspectedPoint列表
     * @param prePoints 上周的InspectedPoint列表
     * @return 周同比列表
     */
    private List<DailyTrendsVO.InspectedWeekPoint> computeWeekOnWeekChange(List<DailyTrendsVO.InspectedPoint> curPoints, List<DailyTrendsVO.InspectedPoint> prePoints, List<String> currentPeriodDates) {
        List<DailyTrendsVO.InspectedWeekPoint> weekOnWeekChanges = new ArrayList<>();
        // 上周业务数据为空时增长率为100%
        if (CollectionUtils.isEmpty(prePoints)){
            curPoints.forEach(curPoint -> {
                DailyTrendsVO.InspectedWeekPoint inspectedWeekPoint = new DailyTrendsVO.InspectedWeekPoint();
                inspectedWeekPoint.setName(curPoint.getName());
                inspectedWeekPoint.setData(getGrowth100(currentPeriodDates, curPoint.getData()));
                weekOnWeekChanges.add(inspectedWeekPoint);
            });
            return weekOnWeekChanges;
        }
        // 上周业务数据不为空时进行计算
        Map<String, List<DailyTrendsVO.InspectedPoint>> bizTypePreDataMap = prePoints.stream().collect(Collectors.groupingBy(DailyTrendsVO.InspectedPoint::getName));
        curPoints.forEach(curPoint -> {
            DailyTrendsVO.InspectedWeekPoint inspectedWeekPoint = new DailyTrendsVO.InspectedWeekPoint();
            inspectedWeekPoint.setName(curPoint.getName());
            List<DailyTrendsVO.InspectedPoint> inspectedPoints = bizTypePreDataMap.get(curPoint.getName());
            // 本周新增的业务上周没有数据，按0计算增长率
            List<Integer> prePointData = CollectionUtils.isEmpty(inspectedPoints)
                    || Objects.isNull(inspectedPoints.get(0)) ? Collections.emptyList() : inspectedPoints.get(0).getData();
            List<Integer> curPointData = curPoint.getData();
            List<Double> wowDataList = new ArrayList<>();
            for (int i = 0; i < curPointData.size(); i++) {
                Integer preData = 0;
                if (CollectionUtils.isNotEmpty(prePointData)) {
                    preData = prePointData.get(i);
                }
                double changePercentage = calculateWeekOnWeekGrowthAsDouble(curPointData.get(i), preData, 1);
                wowDataList.add(changePercentage);
            }
            inspectedWeekPoint.setData(wowDataList);
            weekOnWeekChanges.add(inspectedWeekPoint);
        });
        return weekOnWeekChanges;
    }

    @NotNull
    private static List<Double> getGrowth100(List<String> currentPeriodDates, List<Integer> currentDataList) {
        List<Double> wowDataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(currentDataList)) {
            // 补齐天数
            currentPeriodDates.forEach(date -> wowDataList.add(100.0d));
        } else {
            currentDataList.forEach(data -> {
                if (Objects.nonNull(data) && data > 0) {
                    wowDataList.add(100.0d);
                } else {
                    wowDataList.add(0.0d);
                } 
            });
        } 
        return wowDataList;
    }
    
    private List<BizQualityCheckQueryVO> repairQualityCheckQueries(List<BizQualityCheckQueryVO> bizQualityCheckQueries, List<String> allDatesInRange){
        List<BizQualityCheckQueryVO> filledBizQualityCheckQueries = new ArrayList<>();
        if (CollectionUtils.isEmpty(bizQualityCheckQueries) || CollectionUtils.isEmpty(allDatesInRange)){
            log.warn("查询各业务质检正确率结果为空，bizQualityCheckQueries：{}", JSON.toJSONString(bizQualityCheckQueries));
            return filledBizQualityCheckQueries;
        }
        Map<String, BizQualityCheckQueryVO> existingDataMap = bizQualityCheckQueries.stream()
            .collect(Collectors.toMap(BizQualityCheckQueryVO::getDate, bizQualityCheckQuery -> bizQualityCheckQuery));
        
        // 遍历完整日期列表并填充数据
        for (String currentDate : allDatesInRange) {
            BizQualityCheckQueryVO dataForDate = existingDataMap.get(currentDate);
            if (dataForDate != null) {
                filledBizQualityCheckQueries.add(dataForDate);
            } else {
                BizQualityCheckQueryVO newData = new BizQualityCheckQueryVO();
                newData.setDate(currentDate);
                newData.setQualityRate(null);
                filledBizQualityCheckQueries.add(newData);
            }
        }
        return filledBizQualityCheckQueries;
    }

    private PersonLabelingStatisticsDTO buildPersonLabelingStatisticsDTO(PersonStatisticsRequestParam param){
        PersonLabelingStatisticsDTO personLabelingStatisticsDTO = new PersonLabelingStatisticsDTO();
        personLabelingStatisticsDTO.setBizTypes(param.getBizTypes());
        personLabelingStatisticsDTO.setTaskType(param.getTaskType());
        personLabelingStatisticsDTO.setDataType(param.getDataType());
        personLabelingStatisticsDTO.setQueryType(param.getQueryType());
        personLabelingStatisticsDTO.setStartDate(DateUtil.parseToDate(param.getStartDate()));
        personLabelingStatisticsDTO.setEndDate(DateUtil.parseToDate(param.getEndDate()));
        return personLabelingStatisticsDTO;
    }

    private DailyStatisticsDTO buildDailyStatisticsDTO(DailyStatisticsRequestParam param){
        DailyStatisticsDTO dailyStatisticsDTO = new DailyStatisticsDTO();
        dailyStatisticsDTO.setBizTypes(param.getBizTypes());
        dailyStatisticsDTO.setTaskType(param.getTaskType());
        dailyStatisticsDTO.setDataType(param.getDataType());
        dailyStatisticsDTO.setQueryType(param.getQueryType());
        dailyStatisticsDTO.setStartDate(DateUtil.parseToDate(param.getStartDate()));
        dailyStatisticsDTO.setEndDate(DateUtil.parseToDate(param.getEndDate()));
        return dailyStatisticsDTO;
    }

    private DailyTrendsVO buildDailyTrendsVO(List<String> currentPeriodDates){
        DailyTrendsVO dailyTrendsVO = new DailyTrendsVO();
        dailyTrendsVO.setDates(currentPeriodDates);
        dailyTrendsVO.setSeries(Collections.emptyList());
        dailyTrendsVO.setLastWeekSeries(Collections.emptyList());
        dailyTrendsVO.setWeekOnWeekChange(Collections.emptyList());
        return dailyTrendsVO;
    }
    
    private BaseStatisticsDTO buildBaseStatisticsDTO(BaseStatisticsRequestParam param){
        BaseStatisticsDTO baseStatisticsDTO = new BaseStatisticsDTO();
        baseStatisticsDTO.setBizTypes(param.getBizTypes());
        baseStatisticsDTO.setTaskType(param.getTaskType());
        baseStatisticsDTO.setDataType(param.getDataType());
        baseStatisticsDTO.setStartDate(DateUtil.parseToDate(param.getStartDate()));
        baseStatisticsDTO.setEndDate(DateUtil.parseToDate(param.getEndDate()));
        return baseStatisticsDTO;
    }

    private PersonnelDetailQuery buildPersonnelDetailQuery(PersonnelDetailRequestParam param){
        PersonnelDetailQuery personnelDetailQuery = new PersonnelDetailQuery();
        personnelDetailQuery.setBizTypes(param.getBizTypes());
        personnelDetailQuery.setTaskType(param.getTaskType());
        personnelDetailQuery.setDataType(param.getDataType());
        personnelDetailQuery.setStartDate(DateUtil.parseToDate(param.getStartDate()));
        personnelDetailQuery.setEndDate(DateUtil.parseToDate(param.getEndDate()));
        personnelDetailQuery.setSortBy(param.getSortBy());
        personnelDetailQuery.setSortOrder(param.getSortOrder());
        personnelDetailQuery.setUserMis(param.getUserMis());
        personnelDetailQuery.setQueryType(param.getQueryType());
        personnelDetailQuery.setPageNum(param.getPageNum());
        personnelDetailQuery.setPageSize(param.getPageSize());
        return personnelDetailQuery;
    }

    private List<PersonnelDetailVO> buildPreviousPersonnelDetailVO(PersonnelDetailQuery personnelDetailQuery, List<PersonnelDetailVO> personnelDetail){
        List<PersonnelDetailVO> previousPersonnelDetail;
        PersonnelDetailQuery previousPersonnelDetailQuery = new PersonnelDetailQuery();
        previousPersonnelDetailQuery.setUserMisList(personnelDetail.stream().map(PersonnelDetailVO::getUserMis).collect(Collectors.toList()));
        previousPersonnelDetailQuery.setStartDate(DateUtil.getPreviousWeekDate(personnelDetailQuery.getStartDate()));
        previousPersonnelDetailQuery.setEndDate(DateUtil.getPreviousWeekDate(personnelDetailQuery.getEndDate()));
        if(Objects.equals(personnelDetailQuery.getQueryType(), InspectedTypeEnum.QUALITY_INSPECTION.getCode())){
            previousPersonnelDetail = qualityCheckStatisticsRepository.queryPersonnelDetail(previousPersonnelDetailQuery);
        }else{
            previousPersonnelDetail = personLabelingStatisticsRepository.queryPersonnelDetail(previousPersonnelDetailQuery);
        }
        return previousPersonnelDetail;
    }
}
