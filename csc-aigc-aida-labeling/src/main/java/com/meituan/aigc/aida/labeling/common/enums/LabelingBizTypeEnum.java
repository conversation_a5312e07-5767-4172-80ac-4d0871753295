package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: yangyi<PERSON>i
 * @Create: 2025/05/19 10:23
 * @Version: 1.0
 */
@Getter
public enum LabelingBizTypeEnum {
    WAI_KE_ONLINE(1, "外客在线"),
    PIN_HAO_FAN_ONLINE(2, "拼好饭在线"),
    RIDER_ONLINE(3, "骑手在线"),
    WAI_SHANG_ONLINE(4, "外商在线"),
    RIDER_PHONE(5, "骑手电话"),
    WAI_KE_PHONE(6, "外客电话"),
    OTHER(-1, "其他");
    
    private final Integer code;
    private final String value;

    LabelingBizTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public String toString() {
        return "LabelingBizTypeEnum{" +
                "code=" + code +
                ", value='" + value + '\'' +
                '}';
    }
    
    /**
     * 根据编码获取枚举
     * 
     * @param code 编码
     * @return 枚举值
     */
    public static LabelingBizTypeEnum getByCode(Integer code) {
        for (LabelingBizTypeEnum bizType : values()) {
            if (bizType.getCode() == (code)) {
                return bizType;
            }
        }
        return null;
    }
} 