package com.meituan.aigc.aida.labeling.strategy.task.create.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.common.constant.LionConstants;
import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.common.enums.SendTypeEnum;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.param.Context;
import com.meituan.aigc.aida.labeling.param.RawDataExtraInfo;
import com.meituan.aigc.aida.labeling.param.Signal;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategy;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class OnlineQueryCreateTask extends CommonCreateTask implements CreateTaskStrategy {

    private static final String LOG_FLAG = LabelTaskDataType.QUERY_ANNOTATION.getValue();

    /**
     * 自定义校验字段
     */
    private static final List<String> CUSTOM_FIELD_QUERY_TEMPLATE = Collections.singletonList("messageId");

    @Override
    public Integer getTaskType() {
        return LabelTaskDataType.QUERY_ANNOTATION.getCode();
    }

    @Override
    public void checkHeader(MultipartFile file) {
        checkHeader(file, null);
    }

    @Override
    public List<LabelingTaskRawData> processData(List<List<String>> rowData, List<String> headerList) {
        return processData(rowData, headerList, null);
    }

    @Override
    public void export(String taskName, List<Long> subTaskIds, Integer labelDetailStatus, String mis) {
        commonExport(taskName, subTaskIds, labelDetailStatus, LOG_FLAG, "会话数据", mis);
    }

    @Override
    protected int[] createHeaderAndFillData(SXSSFWorkbook workbook, Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData,
                                            List<Long> subTaskIds, Integer labelDetailStatus, String taskName) {
        // 创建表头行
        int colIndex = createHeaderRows(sheet, headerStyle, fieldData);

        // 设置列宽
        setupColumnWidths(sheet, colIndex);

        // 处理数据
        int totalCount = processSessionData(sheet, subTaskIds, labelDetailStatus, fieldData, taskName);

        return new int[]{colIndex, totalCount};
    }

    @Override
    public void createSecondHeaderRow(Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData) {
        List<InspectionItemData> labelingFields = fieldData.getLabelingFields();
        List<InspectionItemData> qualityCheckFields = fieldData.getQualityCheckFields();
        List<InspectionItemData> qualityCheckModifiedLabelingItems = fieldData.getQualityCheckModifiedLabelingItems();
        Row secondHeaderRow = sheet.createRow(1);
        int colIndex = 0;

        // 会话ID列
        colIndex = createCell(secondHeaderRow, colIndex, SESSION_ID, headerStyle);
        colIndex = createCell(secondHeaderRow, colIndex, TIME, headerStyle);
        colIndex = createCell(secondHeaderRow, colIndex, MESSAGE_ID, headerStyle);
        colIndex = createCell(secondHeaderRow, colIndex, MESSAGE_CONTENT, headerStyle);
        colIndex = createCell(secondHeaderRow, colIndex, CONTEXT, headerStyle);
        colIndex = createCell(secondHeaderRow, colIndex, SIGNAL, headerStyle);


        // 标注结果字段
        for (InspectionItemData field : labelingFields) {
            colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
        }

        // 质检结果字段
        for (InspectionItemData field : qualityCheckFields) {
            colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
        }

        // 标注结果字段 - 质检员修改后
        if (CollectionUtils.isNotEmpty(qualityCheckModifiedLabelingItems)) {
            for (InspectionItemData field : qualityCheckModifiedLabelingItems) {
                colIndex = createCell(secondHeaderRow, colIndex, field.getName(), headerStyle);
            }
        }

    }

    @Override
    protected boolean checkDuplicateSessionId(String sessionId, Set<String> duplicates, int rowIndex) {
        if (duplicates.contains(sessionId)) {
            log.info("数据中第【{}】行sessionId重复", rowIndex);
            return true;
        }
        duplicates.add(sessionId);
        return false;
    }

    /**
     * 创建表头行
     *
     * @param sheet       工作表
     * @param headerStyle 表头样式
     * @return 最终列索引
     */
    private int createHeaderRows(Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData) {
        // 创建第一行表头（主分类）
        int colIndex = createFirstHeaderRow(sheet, headerStyle, fieldData);

        // 创建第二行表头（具体字段）
        createSecondHeaderRow(sheet, headerStyle, fieldData);

        return colIndex;
    }

    /**
     * 创建第一行表头（主分类）
     *
     * @param sheet       工作表
     * @param headerStyle 表头样式
     * @return 最终列索引
     */
    private int createFirstHeaderRow(Sheet sheet, CellStyle headerStyle, ExportFieldData fieldData) {
        List<InspectionItemData> labelingFields = fieldData.getLabelingFields();
        List<InspectionItemData> qualityCheckFields = fieldData.getQualityCheckFields();
        List<InspectionItemData> qualityCheckModifiedLabelingItems = fieldData.getQualityCheckModifiedLabelingItems();
        Row firstHeaderRow = sheet.createRow(0);
        int colIndex = 0;

        // 会话信息列
        colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "会话信息", 6);

        // 标注结果列
        colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "标注结果", labelingFields.size());

        // 质检结果列
        colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "质检结果",
                qualityCheckFields.size());

        // 标注结果列-质检员修改后内容
        if (CollectionUtils.isNotEmpty(qualityCheckModifiedLabelingItems)) {
            colIndex = createMergedHeaderCell(sheet, firstHeaderRow, headerStyle, colIndex, "标注结果-质检员修改后", qualityCheckModifiedLabelingItems.size());
        }

        return colIndex;
    }

    /**
     * 设置列宽
     *
     * @param sheet    工作表
     * @param colIndex 列数
     */
    private void setupColumnWidths(Sheet sheet, int colIndex) {
        for (int i = 0; i < colIndex; i++) {
            // 约25个字符宽
            sheet.setColumnWidth(i, 5000);
        }
    }

    /**
     * 处理会话数据
     *
     * @param sheet             工作表
     * @param subTaskIds        子任务ID集合
     * @param labelDetailStatus 标注详情状态
     * @param taskName          任务名称
     * @return 处理的数据总数
     */
    private int processSessionData(Sheet sheet, List<Long> subTaskIds, Integer labelDetailStatus, ExportFieldData fieldData, String taskName) {
        // 创建数据处理器
        QueyDataProcessor processor = new QueyDataProcessor(fieldData.getLabelingFields(), fieldData.getQualityCheckFields(), fieldData.getQualityCheckModifiedLabelingItems());

        // 批量处理数据
        return processDataInBatches(sheet, 2, subTaskIds, labelDetailStatus, LOG_FLAG, taskName, processor);
    }

    /**
     * 会话数据处理器
     */
    private static class QueyDataProcessor implements ExportDataProcessor {
        private final List<InspectionItemData> labelingFields;
        private final List<InspectionItemData> qualityCheckFields;
        private final List<InspectionItemData> qualityCheckModifiedLabelingItems;

        public QueyDataProcessor(List<InspectionItemData> labelingFields, List<InspectionItemData> qualityCheckFields, List<InspectionItemData> qualityCheckModifiedLabelingItems) {
            this.labelingFields = labelingFields;
            this.qualityCheckFields = qualityCheckFields;
            this.qualityCheckModifiedLabelingItems = qualityCheckModifiedLabelingItems;
        }

        @Override
        public void processRow(Row row, LabelTaskDataExportVO data) {
            int cellIndex = 0;

            // 写入sessionId
            row.createCell(cellIndex++).setCellValue(data.getSessionId());


            String time = data.getSessionTime() != null
                    ? DateUtil.formatDate(data.getSessionTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD)
                    : "";
            // 写入会话时间
            row.createCell(cellIndex++).setCellValue(time);

            int maxLength = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.EXCEL_MAX_SIZE, 32767);
            // 写入大模型ID、大模型消息、上下文、信号
            if (StringUtils.isNotBlank(data.getExtraInfo())) {
                RawDataExtraInfo extraInfo = JSONObject.parseObject(data.getExtraInfo(), RawDataExtraInfo.class);
                if (extraInfo != null && StringUtils.isNotBlank(extraInfo.getLlmMessageId())) {
                    row.createCell(cellIndex++).setCellValue(extraInfo.getLlmMessageId());
                    row.createCell(cellIndex++).setCellValue(extraInfo.getMessageContent());
                    String context = buildContext(extraInfo.getContext());
                    if (context.length() >= maxLength) {
                        row.createCell(cellIndex++).setCellValue("");
                    } else {
                        row.createCell(cellIndex++).setCellValue(context);
                    }
                    String signal = buildSignal(extraInfo.getSignalList());
                    if (signal.length() >= maxLength) {
                        row.createCell(cellIndex++).setCellValue("");
                    } else {
                        row.createCell(cellIndex++).setCellValue(signal);
                    }
                } else {
                    row.createCell(cellIndex++).setCellValue("");
                    row.createCell(cellIndex++).setCellValue("");
                    row.createCell(cellIndex++).setCellValue("");
                    row.createCell(cellIndex++).setCellValue("");
                }
            } else {
                row.createCell(cellIndex++).setCellValue("");
                row.createCell(cellIndex++).setCellValue("");
                row.createCell(cellIndex++).setCellValue("");
                row.createCell(cellIndex++).setCellValue("");
            }


            // 使用公共方法处理标注结果和质检结果的写入
            CommonCreateTask.writeLabelingAndQualityCheckResults(row, data, cellIndex, labelingFields,
                    qualityCheckFields, qualityCheckModifiedLabelingItems);
        }

        /**
         * 构建上下文
         *
         * @param contextList 上下文列表
         * @return 上下文字符串
         */
        private String buildContext(List<Context> contextList) {
            if (CollectionUtils.isEmpty(contextList)) {
                return "";
            }
            StringBuilder sb = new StringBuilder();
            for (Context context : contextList) {
                if (isFilterContext(context)) {
                    continue;
                }
                if (context.getSenderType() == SendTypeEnum.CUSTOMER.getCode()) {
                    sb.append("用户：");
                } else {
                    sb.append("客服：");
                }
                sb.append(context.getMessage()).append("\n");
            }
            return sb.toString();
        }

        /**
         * 判断是否过滤上下文
         *
         * @param context 上下文对象
         * @return 是否过滤
         */
        private boolean isFilterContext(Context context) {
            if ("无法解析的消息类型".equals(context.getMessage())) {
                return true;
            }
            List<String> needMessageType = Lion.getList(ConfigUtil.getAppkey(), LionConstants.NEED_MESSAGE_TYPE, String.class);
            if (CollectionUtils.isEmpty(needMessageType)) {
                return false;
            }
            return !needMessageType.contains(context.getMessageType());
        }

        /**
         * 构建信号
         *
         * @param signalList 信号列表
         * @return 信号字符串
         */
        private String buildSignal(List<Signal> signalList) {
            if (CollectionUtils.isEmpty(signalList)) {
                return "";
            }
            StringBuilder sb = new StringBuilder();
            for (Signal signal : signalList) {
                sb.append(signal.getSignalName()).append(":").append(signal.getSignalValue().replace("\n", " ")).append("\n");
            }
            return sb.toString();
        }

    }
}
