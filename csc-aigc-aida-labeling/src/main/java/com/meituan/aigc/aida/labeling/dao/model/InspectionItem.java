package com.meituan.aigc.aida.labeling.dao.model;

import com.meituan.aigc.aida.labeling.common.enums.LabelingDataType;
import lombok.Data;

import java.util.Date;

/**
 * 检查项表(包含标注/质检类型)实体类
 */
@Data
public class InspectionItem {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 所属模版ID
     */
    private Long templateId;
    
    /**
     * 标注项名称
     */
    private String name;
    
    /**
     * 数据类型 1:文本/2:枚举
     * @see LabelingDataType
     */
    private Integer dataType;
    
    /**
     * 指标类型 1:标注/2:质检
     */
    private Integer itemType;
    
    /**
     * 枚举值列表
     */
    private String enumList;
    
    /**
     * 是否背靠背一致率标注项 1:是/2:否
     */
    private Integer isCompare;

    /**
     * 是否必填 1:是/2:否
     */
    private Integer isRequired;
    
    /**
     * 顺序
     */
    private Integer rankNum;
    
    /**
     * 父指标ID
     */
    private Long parentId;
    
    /**
     * 关联的父指标枚举
     */
    private String parentEnum;
    
    /**
     * 是否删除: 0-否, 1-是
     */
    private Boolean isDeleted;
    
    /**
     * 创建人MIS
     */
    private String createMis;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改人Mis
     */
    private String updateMis;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 