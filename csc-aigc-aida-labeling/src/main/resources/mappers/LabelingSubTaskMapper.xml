<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.LabelingSubTaskMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="labeler_mis" jdbcType="VARCHAR" property="labelerMis" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="total_data_count" jdbcType="INTEGER" property="totalDataCount" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="labeler_name" jdbcType="VARCHAR" property="labelerName" />
    <result column="assign_type" jdbcType="TINYINT" property="assignType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, group_id, name, labeler_mis, status, total_data_count, is_deleted, tenant_id, 
    creator_id, create_time, update_time, labeler_name, assign_type
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingSubTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from labeling_sub_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from labeling_sub_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from labeling_sub_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingSubTaskExample">
    delete from labeling_sub_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask" useGeneratedKeys="true" keyProperty="id">
    insert into labeling_sub_task (id, task_id, group_id,
      name, labeler_mis, status,
      total_data_count, is_deleted, tenant_id,
      creator_id, create_time, update_time,
      labeler_name, assign_type)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT},
      #{name,jdbcType=VARCHAR}, #{labelerMis,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{totalDataCount,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT}, #{tenantId,jdbcType=VARCHAR},
      #{creatorId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{labelerName,jdbcType=VARCHAR}, #{assignType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask">
    insert into labeling_sub_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="labelerMis != null">
        labeler_mis,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="totalDataCount != null">
        total_data_count,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="labelerName != null">
        labeler_name,
      </if>
      <if test="assignType != null">
        assign_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="labelerMis != null">
        #{labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="totalDataCount != null">
        #{totalDataCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="labelerName != null">
        #{labelerName,jdbcType=VARCHAR},
      </if>
      <if test="assignType != null">
        #{assignType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingSubTaskExample" resultType="java.lang.Long">
    select count(*) from labeling_sub_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update labeling_sub_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.labelerMis != null">
        labeler_mis = #{record.labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.totalDataCount != null">
        total_data_count = #{record.totalDataCount,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.labelerName != null">
        labeler_name = #{record.labelerName,jdbcType=VARCHAR},
      </if>
      <if test="record.assignType != null">
        assign_type = #{record.assignType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update labeling_sub_task
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      labeler_mis = #{record.labelerMis,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      total_data_count = #{record.totalDataCount,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      creator_id = #{record.creatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      labeler_name = #{record.labelerName,jdbcType=VARCHAR},
      assign_type = #{record.assignType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask">
    update labeling_sub_task
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="labelerMis != null">
        labeler_mis = #{labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="totalDataCount != null">
        total_data_count = #{totalDataCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="labelerName != null">
        labeler_name = #{labelerName,jdbcType=VARCHAR},
      </if>
      <if test="assignType != null">
        assign_type = #{assignType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask">
    update labeling_sub_task
    set task_id = #{taskId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      labeler_mis = #{labelerMis,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      total_data_count = #{totalDataCount,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=BIT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      labeler_name = #{labelerName,jdbcType=VARCHAR},
      assign_type = #{assignType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listByTaskIdAndNameAndLabelerMis" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskPO">
    select
    s.id as id,
    s.name as taskName,
    s.status as labelingStatus,
    s.total_data_count as dataTotalCount,
    s.labeler_mis as labelerMis,
    s.labeler_name as labelerName,
    s.create_time as createTime,
    s.update_time as updateTime,
    t.task_source as dataSetSource,
    t.task_type as taskType,
    t.data_type as dataType
    from labeling_sub_task s
    left join labeling_task t
    on s.task_id = t.id
    <where>
      s.is_deleted = 0
      and s.status != 1
      <if test="taskId != null">
        and s.task_id = #{taskId}
      </if>
      <if test="!isAdmin and labelerMis != null and labelerMis != ''">
        and (s.labeler_mis = #{labelerMis} or t.labeling_manager = #{labelerMis})
      </if>
      <if test="taskName != null">
        and s.name like concat("%",#{taskName},"%")
      </if>
      <if test="assignType != null">
        and s.assign_type = #{assignType}
      </if>
    </where>
    order by s.update_time desc
  </select>
  <insert id="batchInsertSubTasks" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO labeling_sub_task (
    task_id,
    group_id,
    name,
    labeler_mis,
    labeler_name,
    status,
    total_data_count,
    creator_id
    ) VALUES
    <foreach collection="subTaskList" item="item" separator=",">
      (
      #{item.taskId},
      #{item.groupId},
      #{item.name},
      #{item.labelerMis},
      #{item.labelerName},
      #{item.status},
      #{item.totalDataCount},
      #{item.creatorId}
      )
    </foreach>
  </insert>
  <!-- 统计子任务数量和抽检状态 -->
  <select id="countSubTask" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingSubtaskCountPo">
    SELECT
      s.group_id AS groupId,
      s.id AS subTaskId,
      COUNT(d.id) AS totalCount,
      Count(distinct d.session_id) AS totalSessionCount,
      SUM(CASE WHEN d.sample_status = 1 THEN 1 ELSE 0 END) AS inspectCount,
      COUNT(distinct CASE WHEN d.sample_status = 1 THEN d.session_id ELSE NULL END) AS inspectSessionCount
    FROM labeling_sub_task s
           LEFT JOIN labeling_detail d ON s.id = d.sub_task_id
    WHERE s.task_id = #{taskId}
    GROUP BY s.group_id, s.id
  </select>
  <select id="countGroupNumByQualityCheckItemIds" resultType="java.lang.Integer">
    SELECT
      COUNT(DISTINCT(group_id))
    FROM
      labeling_sub_task
    WHERE
      id IN(
        SELECT
          DISTINCT(labeling.sub_task_id)
        FROM
          labeling_quality_check_item AS quality
        LEFT JOIN
          labeling_detail AS labeling ON quality.labeling_data_id = labeling.id
        WHERE
          quality.id IN
          <foreach collection="checkItemIds" item="checkItemId" open="(" separator="," close=")">
            #{checkItemId}
          </foreach>
      )
  </select>
</mapper>