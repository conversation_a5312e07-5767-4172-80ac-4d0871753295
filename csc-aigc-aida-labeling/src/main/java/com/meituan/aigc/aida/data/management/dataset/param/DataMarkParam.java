package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.Data;

import java.util.List;

/**
 * 数据打标参数
 */
@Data
public class DataMarkParam {
    /**
     * 标签类型：1-自定义标签 2-非自定义标签
     */
    private Integer labelType;
    
    /**
     * 机器人列表
     */
    private List<RobotConfig> robotList;
    
    /**
     * 机器人配置
     */
    @Data
    public static class RobotConfig {
        /**
         * 空间ID
         */
        private String workspaceId;
        
        /**
         * 空间名称
         */
        private String workspaceName;
        
        /**
         * 机器人ID
         */
        private String robotId;
        
        /**
         * 机器人名称
         */
        private String robotName;
        
        /**
         * 机器人版本ID
         */
        private String robotVersionId;
        
        /**
         * 机器人版本名称
         */
        private String robotVersionName;
        
        /**
         * 输入参数
         */
        private List<ParamMapping> inputParam;
        
        /**
         * 输出参数
         */
        private List<ParamMapping> outputParam;
    }
    
    /**
     * 参数映射
     */
    @Data
    public static class ParamMapping {
        /**
         * 参数名称
         */
        private String name;
        
        /**
         * 参数code
         */
        private String code;
        
        /**
         * 映射的列名
         */
        private String column;
    }
} 