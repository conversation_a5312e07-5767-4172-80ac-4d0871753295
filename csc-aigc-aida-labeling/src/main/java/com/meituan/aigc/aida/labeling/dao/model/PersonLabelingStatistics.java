package com.meituan.aigc.aida.labeling.dao.model;

import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.common.enums.LabelingTaskType;
import lombok.Data;

import java.util.Date;

@Data
public class PersonLabelingStatistics {

    /**
     * 主键
     */
    private Long id;
    /**
     * 统计日期
     */
    private Date statDate;
    /**
     * 业务类型
     */
    private Long bizType;
    /**
     * 任务类型 {@link LabelingTaskType}
     */
    private Integer taskType;
    /**
     * 数据类型 {@link LabelTaskDataType}
     */
    private Integer dataType;
    /**
     * 标注员姓名
     */
    private String labelerName;
    /**
     * 标注员mis
     */
    private String labelerMis;
    /**
     * 标注量
     */
    private Integer labelingCount;
    /**
     * 被质检数量
     */
    private Integer qcInspectedCount;
    /**
     * 被质检正确数量
     */
    private Integer qcInspectedCorrectCount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}