package com.meituan.aigc.aida.data.management.dataset.param;

import com.meituan.aigc.aida.data.management.dataset.dto.DataSourceField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-23 16:17
 * @description
 */
@Data
public class UpdateFieldParam implements Serializable {
    /**
     * 数据集id
     */
    private Long dataSetId;
    /**
     * 版本id
     */
    private Long versionId;
    /**
     * 字段列表
     */
    private List<DataSourceField> headList;
}
