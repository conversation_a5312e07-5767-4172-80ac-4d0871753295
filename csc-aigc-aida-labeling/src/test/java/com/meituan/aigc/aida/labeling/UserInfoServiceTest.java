
// llm-auto-t-begin [poa= 7:2:1] 2023-08-11 11:22:03 8d87181fd3e01bdaf66a11f655faa5b4
package com.meituan.aigc.aida.labeling;

import com.meituan.aigc.aida.data.management.dataset.remote.CaseAnalysisRemoteServiceImpl;
import com.meituan.aigc.aida.data.management.dataset.service.CaseAnalysisService;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.api.CaseAnalysisRemoteService;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetCreateResponseDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.CreateDataSetRequestParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.UpdateDataSetRequestParam;
import com.meituan.aigc.aida.labeling.remote.common.dto.PageDataWithHead;
import com.meituan.aigc.aida.labeling.remote.common.dto.ServiceResponseDTO;
import com.meituan.aigc.aida.labeling.service.TaskService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
@RunWith(SpringRunner.class)
public class UserInfoServiceTest {

    @Autowired
    private TaskService taskService;

    @Test
    public void testSearchUserInfo() {
//        List<MisInfoDTO> empItems = taskService.getMisList("wb_duaode");
//        System.out.println(empItems);
    }

    public static void main(String[] args) {

    }

    @Autowired
    private CaseAnalysisService caseAnalysisService;

    @Test
    public void testQueryDataSetRecord() {
        CreateDataSetRequestParam param = new CreateDataSetRequestParam();
        param.setCreatorMis("yanxiao07");
        param.setCreatorName("闫啸");
        param.setDataSetName("测试数据集");
        param.setUsageCategory(3);
        param.setDataSource(3);
        param.setTrainingType(3);
        List<DataFieldParam> headList = new ArrayList<>();
        DataFieldParam dataFieldParam = new DataFieldParam();
        dataFieldParam.setColumnName("文本字段");
        dataFieldParam.setFieldType(1);
        headList.add(dataFieldParam);
        dataFieldParam = new DataFieldParam();
        dataFieldParam.setColumnName("JSON字段");
        dataFieldParam.setFieldType(2);
        headList.add(dataFieldParam);
        param.setHeadList(headList);
        List<DataSetRecordParam> dataList = new ArrayList<>();
        DataSetRecordParam dataSetRecordParam = new DataSetRecordParam();
        Map<String, Object> content = new HashMap<>();
        content.put("文本字段", "测试文本");
        content.put("JSON字段", "{\"key\":\"value\"}");
        dataSetRecordParam.setContent(content);
        dataList.add(dataSetRecordParam);
        param.setDataList(dataList);

        DataSetCreateResponseDTO responseDTO = caseAnalysisService.createDataSet(param);


        headList = new ArrayList<>();
        dataFieldParam = new DataFieldParam();
        dataFieldParam.setColumnName("文本字段");
        dataFieldParam.setFieldType(1);
        headList.add(dataFieldParam);
        dataFieldParam = new DataFieldParam();
        dataFieldParam = new DataFieldParam();
        dataFieldParam.setColumnName("文本字段2");
        dataFieldParam.setFieldType(1);
        headList.add(dataFieldParam);
        dataFieldParam = new DataFieldParam();
        dataFieldParam.setColumnName("JSON字段2");
        dataFieldParam.setFieldType(2);
        headList.add(dataFieldParam);
        param.setHeadList(headList);
        dataList = new ArrayList<>();
        dataSetRecordParam = new DataSetRecordParam();
        dataSetRecordParam.setDataId("JKrDFZcBgR01MEbriApY");
        content = new HashMap<>();
        content.put("文本字段", "测试文本");
        content.put("文本字段2", "测试文本2");
        content.put("JSON字段2", "{\"key2\":\"value2\"}");
        dataSetRecordParam.setContent(content);
        dataList.add(dataSetRecordParam);
        param.setDataList(dataList);

        UpdateDataSetRequestParam updateDataSetRequestParam = new UpdateDataSetRequestParam();
        updateDataSetRequestParam.setDataSetId(responseDTO.getDataSetId());
        updateDataSetRequestParam.setLockId(responseDTO.getLockId());
        updateDataSetRequestParam.setHeadList(headList);
        updateDataSetRequestParam.setDataList(dataList);
        caseAnalysisService.updateDataSet(updateDataSetRequestParam);


//        System.out.println(responseDTO);
    }


}