package com.meituan.aigc.aida.labeling.param;

import com.meituan.aigc.aida.labeling.common.enums.InspectedTypeEnum;
import lombok.Data;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 19:21
 * @Version: 1.0
 */
@Data
public class DashboardTaskListQueryParam extends BaseStatisticsRequestParam{

    /**
     * 当前页
     */
    private Integer pageNum;
    /**
     * 每页记录数
     */
    private Integer pageSize;

    /**
     * 查询类型 {@link InspectedTypeEnum}
     */
    private Integer queryType;

}
