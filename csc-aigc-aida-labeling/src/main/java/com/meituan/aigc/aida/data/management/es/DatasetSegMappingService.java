package com.meituan.aigc.aida.data.management.es;

import com.meituan.aigc.aida.config.EsLionConfig;
import com.meituan.aigc.aida.data.management.es.base.DatasetBaseEsIndex;
import com.meituan.aigc.aida.data.management.es.exception.TrainingEsException;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.rest.RestStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 数据集字段映射服务
 * 用于写入数据集字段映射关系
 *
 * <AUTHOR>
 * @date 2025/5/29
 */
@Slf4j
@Service
public class DatasetSegMappingService {

    /**
     * 数据集字段映射Es索引基础服务
     */
    @Resource
    private DatasetSegBaseIndexService datasetSegBaseIndexService;

    /**
     * Es相关Lion配置
     */
    @Resource
    private EsLionConfig esLionConfig;

    /**
     * 映射数据写入
     *
     * @param segMapping 映射信息
     * @return true表示插入成功，false表示插入失败
     */
    public boolean insertSegMapping(DatasetBaseEsIndex segMapping) {
        if (Objects.isNull(segMapping)) {
            log.warn("insertSegMapping failed, dataset is null");
            return true;
        }

        // 获取目标索引名称
        String indexName = esLionConfig.getDatasetFieldMappingIndex();

        log.info("Inserting segMapping to index: {}", indexName);

        try {
            IndexResponse response = datasetSegBaseIndexService.insert(segMapping, indexName);
            if (response != null && response.status() == RestStatus.CREATED) {
                log.info("Successfully inserted seg mapping to index: {}", indexName);
                return true;
            } else {
                log.error("Failed to insert seg mapping to index: {}, response: {}", indexName, response);
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to insert dataset to index: {}", indexName, e);
            throw new TrainingEsException("DatasetEsIndexService——插入异常", e);
        }
    }

    /**
     * 单条数据更新
     * 使用业务ID作为ES的doc_id进行更新
     *
     * @param segMapping 映射信息
     * @return true表示更新成功，false表示更新失败
     */
    public boolean updateSegMapping(DatasetBaseEsIndex segMapping) {
        if (Objects.isNull(segMapping)) {
            log.warn("updateSegMapping failed, segMapping is null");
            return false;
        }

        // 获取目标索引名称
        String indexName = esLionConfig.getDatasetFieldMappingIndex();

        log.info("Updating dataset with datasetId: {} to index: {}", segMapping.getDatasetId(), indexName);
        try {
            UpdateResponse updateResponse = datasetSegBaseIndexService.update(segMapping, segMapping.getDocumentId(), indexName);
            if (updateResponse != null && updateResponse.getResult() == DocWriteResponse.Result.UPDATED) {
                return true;
            }
            log.error("映射更新失败，response={}", updateResponse);
            return false;
        } catch (Exception e) {
            log.error("Failed to update dataset with datasetId: {} to index: {}", segMapping.getDatasetId(), indexName, e);
            throw new TrainingEsException("DatasetEsIndexService——更新映射异常", e);
        }
    }
}
