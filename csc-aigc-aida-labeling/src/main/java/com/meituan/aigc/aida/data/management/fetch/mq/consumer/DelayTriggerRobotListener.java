package com.meituan.aigc.aida.data.management.fetch.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meituan.aigc.aida.data.management.fetch.dto.BaseSingalTrackingBusParamDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.DelayTriggerRobotDTO;
import com.meituan.aigc.aida.data.management.fetch.helper.AidaSignalLoggingAppInvokeHelper;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: guowenhui
 * @Create: 2025/5/7 16:38
 * @Version: 1.0
 */
@Slf4j
@Component("delayTriggerRobotListener")
public class DelayTriggerRobotListener {

    private static final String LOG_PRE = "[DelayTriggerRobotListener]";

    @Resource
    private AidaSignalLoggingAppInvokeHelper aidaAppInvokeHelper;

    @MdpMafkaMsgReceive
    public ConsumeStatus consume(String msgBody) {
        log.info(LOG_PRE + "收到延迟执行机器人消息, msg:{}", msgBody);
        if (StringUtils.isBlank(msgBody)) {
            log.warn(LOG_PRE + "消息体为空");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            DelayTriggerRobotDTO delayTriggerRobotDTO = JSON.parseObject(msgBody, new TypeReference<DelayTriggerRobotDTO>() {
            });
            String robotId = delayTriggerRobotDTO.getRobotId();
            String acIdStr = delayTriggerRobotDTO.getAcIdStr();
            Map inputData = delayTriggerRobotDTO.getInputData();
            BaseSingalTrackingBusParamDTO busParamDTO = delayTriggerRobotDTO.getBusParamDTO();
            aidaAppInvokeHelper.invokeAidaAppAndLogging(robotId, inputData, busParamDTO, acIdStr);
        } catch (Exception e) {
            log.error(LOG_PRE + "消费延迟执行机器人消息异常, msgBody:{}", msgBody, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
