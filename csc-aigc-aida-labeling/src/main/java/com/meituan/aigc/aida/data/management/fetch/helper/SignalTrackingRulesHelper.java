package com.meituan.aigc.aida.data.management.fetch.helper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Maps;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.SignalTrackingRulesRepository;
import com.meituan.aigc.aida.data.management.fetch.dto.AidaAppCfgDTO;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;

import lombok.extern.slf4j.Slf4j;

/**
 * 信号埋点规则助手类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SignalTrackingRulesHelper {

    public static final String LOG_PREFIX = "[SignalTrackingRulesHelper]";

    /**
     * AidaAppCfgDTO中的实时触发机器人配置项的key
     */
    private static final String REAL_TIME_TRIGGER_ROBOT_KEY = "realTimeTriggerRobot";
    /**
     * AidaAppCfgDTO中的延迟触发机器人配置项的key
     */
    private static final String DELAY_TRIGGER_ROBOT_KEY = "delayTriggerRobot";

    /**
     * 同步延迟机器人延迟时间key
     */
    private static final String DELAY = "Delay";

    /**
     * 可刷新机器人配置key
     */
    private static final String REFRESHABLE_ROBOT_CONFIG_KEY = "refreshable.robot.config";

    @Resource
    private SignalTrackingRulesRepository signalTrackingRulesRepository;

    /**
     * 判断机器人配置是否可以刷新
     *
     * @param key 机器人配置key
     * @return 如果在Lion配置中存在，则返回true，否则返回false
     */
    public static boolean canRefresh(String key) {
        List<String> refreshableRobotConfigs = Lion.getList(REFRESHABLE_ROBOT_CONFIG_KEY);
        if (CollectionUtils.isEmpty(refreshableRobotConfigs)) {
            return false;
        }
        return refreshableRobotConfigs.contains(key);
    }

    /**
     * 获取去重后AI搭机器人ID集合
     * 1. 根据多标准问ID和多misID获取信号埋点规则
     * 2. 根据信号埋点规则中的机器人ID去重
     * 3. 返回机器人ID集合
     *
     * @param query 查询参数
     * @return 机器人ID集合
     */
    @NotNull
    public Set<String> getDistinctAidaAppIds(RobotListQuery query) {
        Set<String> robotIdSet = new HashSet<>();
        List<LlmSignalTrackingRules> allRules = getAllRulesByQuery(query);
        addAidaAppIdSet(allRules, robotIdSet);
        return robotIdSet;
    }

    /**
     * 获取AC接口与AI搭机器人ID的映射关系
     * 1. 根据多标准问ID和多misID获取信号埋点规则
     * 2. 解析每条规则中的AC接口和机器人ID
     * 3. 对相同AC接口的机器人ID进行去重合并
     * 4. 返回AC接口到机器人ID列表的映射
     *
     * @param query 查询参数
     * @return AC接口到机器人ID列表的映射 L 全部ac与全部机器人映射 M ac与延迟执行的机器人映射 R ac与延迟时间映射
     */
    @NotNull
    public Triple<Map<String, List<RobotConfig>>, Map<String, List<String>>, Map<String, Integer>> getAcInterfaceToAidaAppIdsMap(
            RobotListQuery query) {
        Map<String, List<RobotConfig>> acInterfaceToRobotIdsMap = Maps.newHashMap();
        Map<String, List<String>> acInterfaceToDelayRobotIdsMap = Maps.newHashMap();
        Map<String, Integer> acInterfaceToDelayTimeMap = Maps.newHashMap();
        if (StringUtils.isBlank(query.getMisList())) {
            log.warn("{} 查询参数为空 typicalQuestionIds={}, misList={}", LOG_PREFIX, query.getTypicalQuestionIds(),
                    query.getMisList());
            return new ImmutableTriple<>(acInterfaceToRobotIdsMap, acInterfaceToDelayRobotIdsMap,
                    acInterfaceToDelayTimeMap);
        }

        List<LlmSignalTrackingRules> allRules = getAllRulesByQuery(query);

        // 处理规则，构建映射关系
        for (LlmSignalTrackingRules rule : allRules) {
            String acInterface = rule.getAcInterface();
            String aidaAppCfg = rule.getAidaAppCfg();

            if (StringUtils.isBlank(acInterface) || StringUtils.isBlank(aidaAppCfg)) {
                log.warn("{} 规则中AC接口或aidaAppCfg为空 rule={}", LOG_PREFIX, rule);
                continue;
            }

            try {
                List<RobotConfig> robotConfigList = getRobotConfig(aidaAppCfg);
                if (CollectionUtils.isEmpty(robotConfigList)) {
                    log.warn("{} 规则中机器人ID为空 rule={}", LOG_PREFIX, rule);
                    continue;
                }
                // 获取延迟执行机器人Id 组装成acId 到延迟机器人ID的映射
                Pair<Set<String>, Integer> delayTriggerRobotIdAndTime = getDelayTriggerRobotIdAndTime(aidaAppCfg);
                Set<String> delayTriggerRobotIds = delayTriggerRobotIdAndTime.getLeft();
                List<String> delayRobotIds = acInterfaceToDelayRobotIdsMap.computeIfAbsent(acInterface, k -> new ArrayList<>());
                for (String delayRobotId : delayTriggerRobotIds) {
                    if (!delayRobotIds.contains(delayRobotId)) {
                        delayRobotIds.add(delayRobotId);
                    }
                }
                // 获取延迟执行时间
                acInterfaceToDelayTimeMap.put(acInterface, delayTriggerRobotIdAndTime.getRight());

                // 获取当前AC接口对应的机器人ID列表，如果不存在则创建新列表
                List<RobotConfig> existingRobotIds = acInterfaceToRobotIdsMap.computeIfAbsent(acInterface,
                        k -> new ArrayList<>());
                // 添加新的机器人ID并去重
                existingRobotIds.addAll(robotConfigList);
            } catch (Exception e) {
                log.error("解析aidaAppCfg失败，rule={}", rule, e);
            }
        }

        return new ImmutableTriple<>(acInterfaceToRobotIdsMap, acInterfaceToDelayRobotIdsMap,
                acInterfaceToDelayTimeMap);
    }

    /**
     * 获取延迟执行机器人Id和延迟时间
     *
     * @param aidaAppCfg 机器人配置
     * @return L 延迟机器人Id集合 R 延迟时间
     */
    public static Pair<Set<String>, Integer> getDelayTriggerRobotIdAndTime(String aidaAppCfg) {
        Set<String> robotIdSet = new HashSet<>();
        if (StringUtils.isBlank(aidaAppCfg)) {
            return new ImmutablePair<>(robotIdSet, 0);
        }
        AidaAppCfgDTO aidaAppCfgDTO = JSON.parseObject(aidaAppCfg, new TypeReference<AidaAppCfgDTO>() {
        });
        Map<String, String> delayTriggerRobot = aidaAppCfgDTO.getDelayTriggerRobot();
        if (CollectionUtils.isNotEmpty(delayTriggerRobot)) {
            robotIdSet.addAll(delayTriggerRobot.values());
        }
        Integer delayTime = aidaAppCfgDTO.getDelayTime();

        return new ImmutablePair<>(robotIdSet, delayTime);
    }

    /**
     * 从AIDA应用配置中解析并提取机器人ID集合。
     * <p>
     * 该方法会首先检查是否为兼容老数据的格式（即不包含 "realTimeTriggerRobot" 和 "delayTriggerRobot" 字段）。
     * 如果是老数据格式，则直接解析顶层JSON对象的值作为机器人ID。
     * 否则，它会解析 {@link AidaAppCfgDTO} 对象，并提取实时触发和延迟触发的机器人ID。
     * </p>
     *
     * @param aidaAppCfg AIDA应用的JSON配置字符串。如果为空或空白，将返回空集合。
     * @return 包含所有提取到的机器人ID的集合。如果无法解析或配置为空，则返回空集合。
     */
    public static Set<String> getRobotId(String aidaAppCfg) {
        Set<String> robotIdSet = new HashSet<>();
        if (StringUtils.isBlank(aidaAppCfg)) {
            return robotIdSet;
        }

        // 兼容老数据: 如果配置中不包含新版DTO的特定字段，则按老格式解析
        if (!aidaAppCfg.contains(REAL_TIME_TRIGGER_ROBOT_KEY) && !aidaAppCfg.contains(DELAY_TRIGGER_ROBOT_KEY)) {
            Map<String, String> cfgMap = JSON.parseObject(aidaAppCfg, new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isEmpty(cfgMap)) {
                log.error("{} 解析aidaAppCfg失败, aidaAppCfg={}", LOG_PREFIX, aidaAppCfg);
                return robotIdSet;
            }
            Collection<String> values = cfgMap.values();
            robotIdSet.addAll(values);
            return robotIdSet;
        }

        AidaAppCfgDTO aidaAppCfgDTO = JSON.parseObject(aidaAppCfg, new TypeReference<AidaAppCfgDTO>() {
        });
        // 如果 aidaAppCfgDTO 为 null (例如JSON格式错误但包含了关键字，虽然不太可能但作为防御性编程)
        if (aidaAppCfgDTO == null) {
            log.error("{} AIDA应用配置解析为null, aidaAppCfg={}", LOG_PREFIX, aidaAppCfg);
            return robotIdSet;
        }

        Map<String, String> realTimeTriggerRobot = aidaAppCfgDTO.getRealTimeTriggerRobot();
        if (CollectionUtils.isNotEmpty(realTimeTriggerRobot)) {
            robotIdSet.addAll(realTimeTriggerRobot.values());
        }
        Map<String, String> delayTriggerRobot = aidaAppCfgDTO.getDelayTriggerRobot();
        if (CollectionUtils.isNotEmpty(delayTriggerRobot)) {
            robotIdSet.addAll(delayTriggerRobot.values());
        }
        return robotIdSet;
    }

    /**
     * 从AIDA应用配置中解析并提取机器人配置列表。
     * <p>
     * 该方法会首先检查是否为兼容老数据的格式（即不包含 "realTimeTriggerRobot" 和 "delayTriggerRobot" 字段）。
     * 如果是老数据格式，则直接解析顶层JSON对象的值作为机器人ID。
     * 否则，它会解析 {@link AidaAppCfgDTO} 对象，并提取实时触发和延迟触发的机器人ID。
     * </p>
     *
     * @param aidaAppCfg AIDA应用的JSON配置字符串。如果为空或空白，将返回空集合。
     * @return 包含所有提取到的机器人配置的列表。如果无法解析或配置为空，则返回空列表。
     */
    public static List<RobotConfig> getRobotConfig(String aidaAppCfg) {
        List<RobotConfig> robotConfigList = new ArrayList<>();
        Set<String> robotIdSet = new HashSet<>();
        if (StringUtils.isBlank(aidaAppCfg)) {
            return robotConfigList;
        }

        // 兼容老数据: 如果配置中不包含新版DTO的特定字段，则按老格式解析
        if (!aidaAppCfg.contains(REAL_TIME_TRIGGER_ROBOT_KEY) && !aidaAppCfg.contains(DELAY_TRIGGER_ROBOT_KEY)) {
            Map<String, String> cfgMap = JSON.parseObject(aidaAppCfg, new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isEmpty(cfgMap)) {
                log.error("{} 解析aidaAppCfg失败, aidaAppCfg={}", LOG_PREFIX, aidaAppCfg);
                return robotConfigList;
            }
            return getRobotConfigByRobotId(cfgMap, robotIdSet);
        }

        AidaAppCfgDTO aidaAppCfgDTO = JSON.parseObject(aidaAppCfg, new TypeReference<AidaAppCfgDTO>() {
        });
        // 如果 aidaAppCfgDTO 为 null (例如JSON格式错误但包含了关键字，虽然不太可能但作为防御性编程)
        if (aidaAppCfgDTO == null) {
            log.error("{} AIDA应用配置解析为null, aidaAppCfg={}", LOG_PREFIX, aidaAppCfg);
            return robotConfigList;
        }

        // 处理实时触发机器人
        Map<String, String> realTimeTriggerRobot = aidaAppCfgDTO.getRealTimeTriggerRobot();
        if (CollectionUtils.isNotEmpty(realTimeTriggerRobot)) {
            List<RobotConfig> result = getRobotConfigByRobotId(realTimeTriggerRobot, robotIdSet);
            if (CollectionUtils.isNotEmpty(result)) {
                robotConfigList.addAll(result);
            }
        }

        // 处理延迟触发机器人
        Map<String, String> delayTriggerRobot = aidaAppCfgDTO.getDelayTriggerRobot();
        if (CollectionUtils.isNotEmpty(delayTriggerRobot)) {
            List<RobotConfig> result = getRobotConfigByRobotId(delayTriggerRobot, robotIdSet);
            if (CollectionUtils.isNotEmpty(result)) {
                robotConfigList.addAll(result);
            }
        }

        return robotConfigList;
    }

    /**
     * 根据机器人ID集合获取机器人配置列表。
     *
     * @param robotMap 机器人ID集合，键为机器人类型，值为机器人ID
     * @return 机器人配置列表
     */
    private static List<RobotConfig> getRobotConfigByRobotId(Map<String, String> robotMap, Set<String> robotIdSet) {
        return robotMap.entrySet().stream().filter(entry -> {
            if (robotIdSet.contains(entry.getValue())) {
                return false;
            }
            robotIdSet.add(entry.getValue());
            return true;
        }).map(entry -> {
            RobotConfig robotConfig = new RobotConfig();
            robotConfig.setAppId(entry.getValue());
            robotConfig.setSignalType(entry.getKey());
            return robotConfig;
        }).collect(Collectors.toList());
    }

    /**
     * 根据查询参数获取所有符合条件的信号埋点规则
     *
     * @param query 查询参数
     * @return 所有符合条件的信号埋点规则集合
     */
    public List<LlmSignalTrackingRules> getAllRulesByQuery(RobotListQuery query) {
        List<LlmSignalTrackingRules> allRules = new ArrayList<>();
        String[] misList = query.getMisList().split(CommonConstants.FieldName.ENGLISH_COMMA);
        Integer channel = query.getChannel();

        // 对每个mis，获取未配置typicalQuestionId的规则（等价于匹配所有标问ID）
        for (String mis : misList) {
            List<LlmSignalTrackingRules> universalRules = signalTrackingRulesRepository
                    .listEnableByMisIdsAndTypicalQuestionIdsIsBlank(mis, channel);
            if (!CollectionUtils.isEmpty(universalRules)) {
                allRules.addAll(universalRules);
            }
        }

        // 如果提供了标准问ID，再获取特定匹配这些标准问ID的规则
        if (StringUtils.isNotBlank(query.getTypicalQuestionIds())) {
            String[] typicalQuestionIds = query.getTypicalQuestionIds().split(CommonConstants.FieldName.ENGLISH_COMMA);
            for (String typicalQuestionId : typicalQuestionIds) {
                for (String mis : misList) {
                    // 注意：这里只获取特定匹配该标准问ID的规则，通用规则已在上面获取
                    List<LlmSignalTrackingRules> specificRules = signalTrackingRulesRepository
                            .listEnableByMisIdsAndTypicalQuestionIds(mis, typicalQuestionId, channel);
                    if (!CollectionUtils.isEmpty(specificRules)) {
                        allRules.addAll(specificRules);
                    }
                }
            }
        }

        return allRules;
    }

    /**
     * 添加信号埋点规则中的AI搭机器人ID
     *
     * @param llmSignalTrackingRules 信号埋点规则集合
     * @param robotIdSet             机器人ID集合
     */
    private void addAidaAppIdSet(List<LlmSignalTrackingRules> llmSignalTrackingRules, Set<String> robotIdSet) {
        if (CollectionUtils.isEmpty(llmSignalTrackingRules)) {
            return;
        }
        List<String> aidaAppCfg = llmSignalTrackingRules.stream().map(LlmSignalTrackingRules::getAidaAppCfg)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aidaAppCfg)) {
            return;
        }
        aidaAppCfg.forEach(cfg -> {
            Set<String> robotIds = getRobotId(cfg);
            robotIdSet.addAll(robotIds);
        });
    }

    @Data
    public static class RobotConfig {
        private String appId;
        private String signalType;
    }
}