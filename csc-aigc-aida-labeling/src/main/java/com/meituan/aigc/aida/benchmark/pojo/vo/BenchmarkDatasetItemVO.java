package com.meituan.aigc.aida.benchmark.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Benchmark数据集项VO
 * 
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BenchmarkDatasetItemVO {

    /**
     * 数据集ID
     */
    private Long id;

    /**
     * 数据集名称
     */
    private String name;

    /**
     * 评测对象
     */
    private String evalObject;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 数据集版本
     */
    private String version;

    /**
     * 数据量
     */
    private Integer dataCount;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
} 