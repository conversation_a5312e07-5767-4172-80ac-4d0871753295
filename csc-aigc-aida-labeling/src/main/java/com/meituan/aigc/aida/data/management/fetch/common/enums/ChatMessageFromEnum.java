package com.meituan.aigc.aida.data.management.fetch.common.enums;

import lombok.Getter;

/**
 * 会话消息来源
 *
 * <AUTHOR>
 */
@Getter
public enum ChatMessageFromEnum {
    STAFF("STAFF", "客服发送的消息"),
    CUSTOMER("CUSTOMER", "用户发送的消息");

    private final String code;
    private final String name;

    ChatMessageFromEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ChatMessageFromEnum findByCode(String code) {
        for (ChatMessageFromEnum skillType : ChatMessageFromEnum.values()) {
            if (skillType.getCode().equals(code)) {
                return skillType;
            }
        }
        return null;
    }
}
