package com.meituan.aigc.aida.data.management.fetch.mq.consumer;

import javax.annotation.Resource;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.meituan.aigc.aida.labeling.Application;
import com.meituan.mafka.client.consumer.ConsumeStatus;

import lombok.extern.log4j.Log4j;

/**
 * 监听海豚消息，执行信号埋点Workflow Listener测试
 * 
 * <AUTHOR> wangyi<PERSON>
 * @date : 2025/4/10
 */
@Log4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ChatEventMessageListenerTest {

    @Resource
    private ChatEventMessageListener chatEventMessageListener;


    @Before
    public void setUp() {
        // 设置消费开关为true
        ReflectionTestUtils.setField(chatEventMessageListener, "chatConsumerSwitch", true);
    }

    @Test
    public void testConsumeSuccess() {
        log.info("开始执行testConsumeSuccess测试");
        // 构造测试消息
        String msgBody = "{\"eventType\":\"staffMessageSendEvent\",\"event\":\"{\\\"serviceId\\\":457655430,\\\"staffId\\\":304291,\\\"customerId\\\":466486925,\\\"dialogId\\\":4407475368,\\\"skillId\\\":309,\\\"isSystem\\\":false,\\\"messageId\\\":9646862224,\\\"cscMessageId\\\":\\\"1816032202116251747\\\",\\\"extParam\\\":\\\"{\\\\\\\"smartBrokenLineTag\\\\\\\":\\\\\\\"pacific\\\\\\\"}\\\",\\\"msgContent\\\":\\\"您看是否可以先更换一下位置\\\",\\\"eventId\\\":\\\"7254a583-f47a-418c-a981-d0a4231cfc24\\\",\\\"occurredOn\\\":1721810792008}\"}";

        // 执行方法
        ConsumeStatus status = chatEventMessageListener.consume(msgBody);

        log.info("测试结果: " + status);
        // 验证执行结果
        assert status == ConsumeStatus.CONSUME_SUCCESS;
    }

    @Test
    public void testConsumeWithEmptyMessage() {
        log.info("开始执行testConsumeWithEmptyMessage测试");
        // 构造空消息
        String msgBody = "";

        // 执行方法
        ConsumeStatus status = chatEventMessageListener.consume(msgBody);

        log.info("测试结果: " + status);
        // 验证执行结果
        assert status == ConsumeStatus.CONSUME_SUCCESS;
    }

    @Test
    public void testConsumeWithSwitchOff() {
        log.info("开始执行testConsumeWithSwitchOff测试");
        // 设置消费开关为false
        ReflectionTestUtils.setField(chatEventMessageListener, "chatConsumerSwitch", false);

        // 构造测试消息
        String msgBody = "{\"eventId\":\"test-event-id\",\"eventType\":\"USER_MESSAGE_SEND\",\"eventObject\":{\"sessionId\":123456,\"contactId\":\"test-contact-id\",\"content\":\"测试消息内容\",\"fromType\":\"USER\"}}";

        // 执行方法
        ConsumeStatus status = chatEventMessageListener.consume(msgBody);

        log.info("测试结果: " + status);
        // 验证执行结果
        assert status == ConsumeStatus.CONSUME_SUCCESS;

        // 恢复消费开关为true，避免影响其他测试
        ReflectionTestUtils.setField(chatEventMessageListener, "chatConsumerSwitch", true);
    }

    @Test
    public void testConsumeWithException() {
        log.info("开始执行testConsumeWithException测试");
        // 构造测试消息
        String msgBody = "{\"eventId\":\"test-event-id\",\"eventType\":\"USER_MESSAGE_SEND\",\"eventObject\":{\"sessionId\":123456,\"contactId\":\"test-contact-id\",\"content\":\"测试消息内容\",\"fromType\":\"USER\"}}";

        try {
            // 执行方法
            ConsumeStatus status = chatEventMessageListener.consume(msgBody);

            log.info("测试结果: " + status);
            // 验证即使有异常，也返回成功状态
            assert status == ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            // 记录异常，但测试仍然通过，因为consume方法应该处理所有异常
            log.error("测试中捕获到异常: " + e.getMessage(), e);
            assert false : "consume方法应该处理所有异常，不应该抛出异常";
        }
    }
}