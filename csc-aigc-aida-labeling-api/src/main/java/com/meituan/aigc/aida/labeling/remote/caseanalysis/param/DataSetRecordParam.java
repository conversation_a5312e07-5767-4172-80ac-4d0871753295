package com.meituan.aigc.aida.labeling.remote.caseanalysis.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-05-26 10:41
 * @description 数据内容参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetRecordParam implements Serializable {

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 数据详情内容
     */
    private Map<String, Object> content;
}
