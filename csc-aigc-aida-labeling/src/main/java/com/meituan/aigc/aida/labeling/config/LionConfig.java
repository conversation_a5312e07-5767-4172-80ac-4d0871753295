package com.meituan.aigc.aida.labeling.config;

import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.meituan.aigc.aida.labeling.pojo.vo.TaskDataImportTemplateVO;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: guowenhui
 * @Create: 2025/3/6 19:22
 * @Version: 1.0
 */

@Slf4j
@Data
@Component
public class LionConfig {

    @MdpConfig("mis_page_size:20")
    private Integer misPageSize;

    @MdpConfig("per_batch_size:1000")
    private Integer perBatchSize;

    @MdpConfig("metric.enum.max.depth:20")
    private Integer metricEnumMaxDepth;

    @MdpConfig("aigc.aida.training.bucket:aida-eval-test-bucket")
    private String aigcAidaTrainingBucket;

    @MdpConfig("eval.service.s3.endpoint:s3plus-corp.sankuai.com")
    private String evalServiceS3Endpoint;

    @MdpConfig("administrators.list: [\"yanxiao07\"]")
    private String administratorsList;

    @MdpConfig("create.dataSet.notify.elephant.url")
    private String createDataSetNotifyElephantUrl;

    @MdpConfig("label.task.import.templates:[]")
    private String labelTaskImportTemplates;

    @MdpConfig("qualityCheck.distribute.notify.elephant.url")
    private String qualityCheckDistributeNotifyElephantUrl;

    @MdpConfig("max_file_rows:10000")
    private Integer maxFileRows;

    @MdpConfig("aida.training.sub.task.url")
    private String subTaskUrl;

    @MdpConfig("search.mis.switch:false")
    private Boolean searchMisSwitch;

    public List<String> getAdministratorsList() {
        try {
            return JSON.parseObject(administratorsList, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.error("获取{}配置异常：", administratorsList, e);
        }
        return Collections.emptyList();
    }

    public List<TaskDataImportTemplateVO> getLabelTaskImportTemplates() {
        try {
            return JSON.parseObject(labelTaskImportTemplates, new TypeReference<List<TaskDataImportTemplateVO>>() {
            });
        } catch (Exception e) {
            log.error("获取{}配置异常：", labelTaskImportTemplates, e);
        }
        return Collections.emptyList();
    }

    public Boolean isAdmin(String userMis) {
        return getAdministratorsList().contains(userMis);
    }

}
