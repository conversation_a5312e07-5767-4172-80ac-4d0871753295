package com.meituan.aigc.aida.data.management.dataset.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.alibaba.fastjson.TypeReference;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetFieldUtils;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.meituan.aigc.aida.data.management.dataset.dto.DataContentFieldDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFieldPathInfo;
import com.meituan.aigc.aida.data.management.dataset.dto.FieldMappingDTO;
import com.meituan.aigc.aida.data.management.dataset.param.DatasetHeadConfig;
import com.meituan.aigc.aida.data.management.dataset.service.CaseAnalysisService;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetManagementService;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.impl.JsonFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.impl.ProcessedSignalFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetQueryConditionUtils;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.labeling.common.constant.RedisConstant;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.data.management.es.query.DatasetPageResult;
import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetCreateResponseDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.CreateDataSetRequestParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.aigc.aida.labeling.remote.common.enums.*;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetTryLockDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.*;
import com.meituan.aigc.aida.labeling.remote.common.dto.PageDataWithHead;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

import java.util.regex.Pattern;

import org.elasticsearch.search.sort.SortOrder;

import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateResponseParam;

/**
 * Case分析服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-26 10:26
 * @description Case分析服务实现
 */
@Service
@Slf4j
public class CaseAnalysisServiceImpl implements CaseAnalysisService {

    @Autowired(required = false)
    @Qualifier("redisClient")
    private RedisStoreClient redisStoreClient;

    @Resource
    private DatasetEsIndexService datasetEsIndexService;

    @Resource
    private DataSetVersionRepository dataSetVersionRepository;

    @Resource
    private DataSetManagementService dataSetManagementService;

    @Autowired
    private JsonFieldStrategy jsonFieldStrategy;

    @Autowired
    private ProcessedSignalFieldStrategy processedSignalFieldStrategy;

    private static final String LION_FIELD_TYPE_REGEX_MAPPING_KEY = "aida.data.field.type.regex.mapping";

    // 新增常量
    private static final int SAMPLE_SIZE = 10;
    private static final int MIN_DATA_SIZE_FOR_MIDDLE_SAMPLE = 20;
    private static final String ES_COMMON_DATA_PREFIX = "commonData";
    private static final String FLATTEN_PREFIX = "flattenedData";
    private static final String TEXT_PREFIX = "textData";

    private static final List<Integer> FLATTEN_FIELD_TYPE = Lists.newArrayList(FieldTypeEnum.JSON.getCode(), FieldTypeEnum.PROCESSED_SIGNAL.getCode());

    @Resource
    private DataSetRepository dataSetRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataSetCreateResponseDTO createDataSet(CreateDataSetRequestParam param) {
        // 转换入参
        DataSetCreateParam createParam = new DataSetCreateParam();
        createParam.setDataSetId(param.getDataSetId());
        createParam.setLockId(param.getLockId());
        createParam.setIsEnd(param.getIsEnd());
        createParam.setCreatorMis(param.getCreatorMis());
        createParam.setCreatorName(param.getCreatorName());
        createParam.setDataSetName(param.getDataSetName());
        createParam.setDescription(param.getDescription());
        createParam.setUsageCategory(param.getUsageCategory());
        createParam.setTrainingType(param.getTrainingType());
        createParam.setDataSource(param.getDataSource());
        createParam.setHeadList(param.getHeadList());
        createParam.setDataList(param.getDataList());

        // 调用DataSetManagementService的createDataSet方法
        DataSetCreateResponseParam responseParam = dataSetManagementService.createDataSet(createParam);

        // 转换出参
        DataSetCreateResponseDTO responseDTO = new DataSetCreateResponseDTO();
        responseDTO.setDataSetId(responseParam.getDataSetId());
        responseDTO.setLockId(responseParam.getLockId());

        return responseDTO;
    }

    /**
     * 获取最新版本ID
     */
    private DataSetVersion getLatestVersion(Long dataSetId) {
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        return dataSetVersionRepository.getById(dataSet.getLatestVersionId());
    }

    /**
     * 根据数据值匹配数据类型
     * 优先级：Lion正则匹配、处理后信号、json格式，如果都不满足，则匹配为文本
     */
    private int matchFieldTypeByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return FieldTypeEnum.TEXT.getCode();
        }

        // 1. Lion正则匹配
        Integer lionMatchType = matchByLionRegex(value);
        if (lionMatchType != null) {
            return lionMatchType;
        }

        // 2. 处理后信号格式检查
        if (isProcessedSignalFormat(value)) {
            return FieldTypeEnum.PROCESSED_SIGNAL.getCode();
        }

        // 3. JSON格式检查
        if (isJsonFormat(value)) {
            return FieldTypeEnum.JSON.getCode();
        }

        // 4. 默认为文本
        return FieldTypeEnum.TEXT.getCode();
    }

    /**
     * 通过Lion配置的正则表达式匹配数据类型
     */
    private Integer matchByLionRegex(String value) {
        try {
            String configValue = Lion.getString(ConfigUtil.getAppkey(), LION_FIELD_TYPE_REGEX_MAPPING_KEY);
            if (StringUtils.isBlank(configValue)) {
                return null;
            }

            Map<String, String> regexMapping = JSON.parseObject(configValue, new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isEmpty(regexMapping)) {
                return null;
            }

            for (Map.Entry<String, String> entry : regexMapping.entrySet()) {
                String typeCode = entry.getKey();
                String regex = entry.getValue();

                if (StringUtils.isNotBlank(regex) && Pattern.matches(regex, value)) {
                    return Integer.parseInt(typeCode);
                }
            }
        } catch (Exception e) {
            log.warn("Lion配置正则匹配失败", e);
        }

        return null;
    }

    /**
     * 检查是否为处理后信号格式
     */
    private boolean isProcessedSignalFormat(String value) {
        try {
            processedSignalFieldStrategy.validateValue(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为JSON格式
     */
    private boolean isJsonFormat(String value) {
        try {
            jsonFieldStrategy.validateValue(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成锁ID
     */
    private String generateLockId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 验证锁
     */
    private void validateLock(Long dataSetId, String lockId) {
        if (StringUtils.isBlank(lockId)) {
            throw new AidaRpcException("锁ID不能为空");
        }

        StoreKey storeKey = new StoreKey(RedisConstant.DATASET_LOCK, dataSetId);
        String existingLockId = redisStoreClient.get(storeKey);
        log.info("验证数据集锁，dataSetId：{}，lockId：{}，existingLockId：{}", dataSetId, lockId, existingLockId);

        if (existingLockId == null || !existingLockId.equals(lockId)) {
            throw new AidaRpcException("数据集锁验证失败，请重新获取锁");
        }
    }

    /**
     * 释放锁
     */
    private void releaseLock(Long dataSetId, String lockId) {
        StoreKey storeKey = new StoreKey(RedisConstant.DATASET_LOCK, dataSetId);
        redisStoreClient.delete(storeKey);
        log.info("释放数据集锁成功，dataSetId：{}，lockId：{}", dataSetId, lockId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDataSet(UpdateDataSetRequestParam param) {
        // 校验锁和基础参数
        checkRequestParam(param);

        Long dataSetId = param.getDataSetId();

        // 获取最新版本ID
        DataSetVersion latestVersion = getLatestVersion(dataSetId);

        DataSetVersion currentVersion = dataSetVersionRepository.getById(latestVersion.getId());
        if (Objects.isNull(currentVersion)) {
            log.error("无法找到数据集的版本数据，dataSetId: {}", dataSetId);
            throw new AidaRpcException("数据集版本不存在，无法更新表头");
        }
        DatasetHeadConfig historyHeadConfig = JSON.parseObject(currentVersion.getHeadConfig(),
                new TypeReference<DatasetHeadConfig>() {
                });
        DatasetHeadConfig headConfig = new DatasetHeadConfig();

        // 处理 headList
        if (CollectionUtils.isNotEmpty(param.getHeadList())) {
            headConfig = dataSetManagementService.handleHeadList(historyHeadConfig, param.getHeadList(), param.getDataList());
            currentVersion.setHeadConfig(JSON.toJSONString(headConfig));
            currentVersion.setUpdateTime(new Date());
            dataSetVersionRepository.updateSelectiveById(currentVersion);
            log.info("数据集版本 headConfig 已更新，dataSetId: {}, versionId: {}", dataSetId, currentVersion.getId());
        }
        // 处理 dataList
        if (CollectionUtils.isNotEmpty(param.getDataList())) {
            List<DatasetEsIndex> datasetEsIndexList = dataSetManagementService.buildDatasetEsIndices(dataSetId, latestVersion.getId(), DataSourceEnum.CASE.getCode(), param.getDataList(), headConfig, null, new Date());
            datasetEsIndexService.batchUpdateDatasets(datasetEsIndexList, DataSourceEnum.CASE.getValue(), currentVersion.getEsIndexName());
        }
        // 则释放锁
        if (Boolean.TRUE.equals(param.getIsEnd())) {
            releaseLock(dataSetId, param.getLockId());
        }
    }

    @Override
    public DataSetTryLockDTO getLockIdByDataSetId(Long dataSetId) {
        if (Objects.isNull(dataSetId)) {
            throw new AidaRpcException("数据集id不能为空");
        }
        //生成唯一的锁
        String lockId = generateLockId();
        log.info("数据集Id:{} 获取锁存入缓存", dataSetId);
        int expireSeconds = Lion.getInt(ConfigUtil.getAppkey(), LionConstant.DATASET_LOCK_EXPIRE, 60 * 60 * 24);
        Boolean setNx = redisStoreClient.setnx(new StoreKey(RedisConstant.DATASET_LOCK, dataSetId), lockId,
                expireSeconds);
        if (!setNx) {
            throw new AidaRpcException("数据集锁已存在，请稍后重试");
        }
        return DataSetTryLockDTO.builder().lockId(lockId).build();
    }

    @Override
    public DatasetQueryResultDTO pageQueryDataSet(DataSetPageQueryParam param) {
        CheckUtil.paramCheck(Objects.nonNull(param), "请求参数不能为空");

        // 查询数据集版本
        DataSetVersion version = getDataSetVersion(param);
        // 构建查询条件
        DatasetEsQueryCondition condition = buildQueryCondition(param, version);

        // 如果没有指定数据集ID查询，则需要使用滚动查询功能
        if (Objects.isNull(param.getDataSetId())) {
            if (Objects.nonNull(param.getScrollId())) {
                condition.setScrollId(param.getScrollId());
            }
            condition.setScrollTime("10m");
        }
        // 调用ES查询服务
        DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);

        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
            DatasetQueryResultDTO result = new DatasetQueryResultDTO();
            result.setHasNext(false);
            return result;
        }

        // 将ES查询结果转换为PageDataWithHead格式
        DatasetQueryResultDTO result = convertToPageDataWithHead(version, pageResult);
        result.setScrollId(pageResult.getScrollId());
        result.setHasNext(pageResult.getHasNext());
        return result;
    }

    /**
     * 获取数据集版本
     *
     * @param param 查询参数
     * @return 数据集版本信息
     */
    private DataSetVersion getDataSetVersion(DataSetPageQueryParam param) {
        if (Objects.isNull(param.getDataSetId())) {
            return null;
        }
        DataSetVersion version;
        if (Objects.isNull(param.getVersionId())) {
            version = getLatestVersion(param.getDataSetId());
        } else {
            version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(param.getDataSetId(), param.getVersionId());
        }
        return version;
    }

    /**
     * 将ES查询结果转换为PageDataWithHead格式
     *
     * @param pageResult ES查询结果
     * @return PageDataWithHead格式的结果
     */
    private DatasetQueryResultDTO convertToPageDataWithHead(DataSetVersion version, DatasetPageResult pageResult) {
        DatasetQueryResultDTO datasetQueryResult = new DatasetQueryResultDTO();
        PageDataWithHead<DataFieldParam, DataSetRecordParam> result = new PageDataWithHead<>();
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
            result.setTotalCount(0);
            result.setData(Collections.emptyList());
            result.setHeadList(Collections.emptyList());
            datasetQueryResult.setPageData(result);
            return datasetQueryResult;
        }

        // 设置总数
        result.setTotalCount(pageResult.getTotal().intValue());

        DatasetHeadConfig datasetHeadConfig = null;
        if (Objects.nonNull(version)) {
            String headConfig = version.getHeadConfig();
            // 将headConfig转换为DatasetHeadConfig
            datasetHeadConfig = JSON.parseObject(headConfig,
                    new TypeReference<DatasetHeadConfig>() {
                    });
        }
        // 转换数据记录
        List<DataSetRecordParam> dataList = pageResult.getRecords().stream()
                .map(this::convertToDataSetRecordParam)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        result.setData(dataList);

        // 如果没有传数据ID，那么不返回表头
        // 转换表头字段
        if (Objects.nonNull(datasetHeadConfig)) {
            result.setHeadList(convertDataSetField(datasetHeadConfig, dataList));
        }
        datasetQueryResult.setPageData(result);
        return datasetQueryResult;
    }

    /**
     * 将数据集版本的headConfig转换为DataFieldParam列表
     *
     * @param datasetHeadConfig 数据集表头信息
     * @return DataFieldParam列表
     */
    private List<DataFieldParam> convertDataSetField(DatasetHeadConfig datasetHeadConfig, List<DataSetRecordParam> dataList) {
        try {
            List<DataFieldParam> dataFieldList = datasetHeadConfig.getHeadList().stream()
                    .filter(Objects::nonNull)
                    .map(dataContentFieldDTO -> convertToDataFieldParam(dataContentFieldDTO, dataList))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("成功转换headConfig，共{}个字段", dataFieldList.size());
            return dataFieldList;

        } catch (Exception e) {
            log.error("解析headConfig失败, datasetHeadConfig: {}", datasetHeadConfig, e);
            return Collections.emptyList();
        }
    }

    /**
     * 将DataContentFieldDTO转换为DataFieldParam
     *
     * @param dataContentField DataContentFieldDTO对象
     * @return DataFieldParam对象
     */
    private DataFieldParam convertToDataFieldParam(DataContentFieldDTO dataContentField, List<DataSetRecordParam> dataList) {
        if (dataContentField == null) {
            return null;
        }

        String columnName = dataContentField.getColumnName();
        Integer fieldType = dataContentField.getFieldType();

        if (StringUtils.isBlank(columnName)) {
            log.warn("字段名称为空，跳过该字段: {}", JSON.toJSONString(dataContentField));
            return null;
        }
        if (fieldType == null) {
            log.warn("字段类型为空，使用默认类型(文本), columnName: {}", columnName);
            fieldType = inferFieldTypeFromSampleData(columnName, dataList);
        }
        return new DataFieldParam(columnName, fieldType);
    }

    /**
     * 将DatasetEsIndex转换为DataSetRecordParam
     *
     * @param esIndex ES索引记录
     * @return DataSetRecordParam对象
     */
    private DataSetRecordParam convertToDataSetRecordParam(DatasetEsIndex esIndex) {
        if (esIndex == null) {
            return null;
        }
        DataSetRecordParam record = new DataSetRecordParam();
        Map<String, Object> content = new HashMap<>();
        // 构建数据集ES索引
        buildDataSetEsIndex(esIndex, content);
        // 设置数据ID（使用documentId或datasetId）
        buildDataId(esIndex, record);
        record.setContent(content);
        return record;
    }

    /**
     * 构建数据集ES索引
     *
     * @param esIndex ES索引记录
     * @param content 内容映射
     */
    private void buildDataSetEsIndex(DatasetEsIndex esIndex, Map<String, Object> content) {
        String fieldMapping = esIndex.getFieldMapping();
        DatasetHeadConfig datasetHeadConfig = JSON.parseObject(fieldMapping,
                new TypeReference<DatasetHeadConfig>() {
                });
        Map<String, String> columnNameMap = datasetHeadConfig.getHeadList().stream()
                .collect(Collectors.toMap(dataContentField -> dataContentField.getEsKey() + "." + dataContentField.getEsColumnName(), DataContentFieldDTO::getColumnName));

        // 转换文本数据
        if (esIndex.getTextData() != null) {
            esIndex.getTextData().forEach((key, value) -> {
                if (value != null) {
                    content.put(columnNameMap.getOrDefault(TEXT_PREFIX + "." + key, key), value);
                }
            });
        }

        // 转换平铺数据
        if (esIndex.getFlattenedData() != null) {
            esIndex.getFlattenedData().forEach((key, value) -> {
                if (value != null) {
                    content.put(columnNameMap.getOrDefault(FLATTEN_PREFIX + "." + key, key), value);
                }
            });
        }

        //转换嵌套数据
//        if (esIndex.getNestedData() != null) {
//            esIndex.getNestedData().forEach((key, value) -> {
//                if (value != null) {
//                    content.put(columnNameMap.getOrDefault(key, key), value);
//                }
//            });
//        }

        if (esIndex.getCommonData() != null) {
            esIndex.getCommonData().forEach((key, value) -> {
                if (value != null) {
                    content.put(key, value.toString());
                }
            });
        }

//        try {
//            // 转换日期数据
//            if (esIndex.getDateData() != null) {
//                esIndex.getDateData().forEach((key, value) -> {
//                    if (value != null) {
//                        content.put(columnNameMap.getOrDefault(key, key), DateUtil.formatDate(value, DateUtil.DATE_PATTERN_YYYY_MM_DD));
//                    }
//                });
//            }
//        } catch (Exception e) {
//            log.error("复杂索引日期转换失败，数据集id:{}", esIndex.getDatasetId(), e);
//        }
    }

    /**
     * 构建数据ID
     *
     * @param esIndex ES索引记录
     * @param record  数据记录
     */
    private void buildDataId(DatasetEsIndex esIndex, DataSetRecordParam record) {
        record.setDataId(esIndex.getDocumentId());
    }

    /**
     * 校验请求参数
     *
     * @param param 请求参数
     */
    private void checkRequestParam(UpdateDataSetRequestParam param) {
        if (Objects.isNull(param)) {
            throw new AidaRpcException("请求参数不能为空");
        }
        if (Objects.isNull(param.getDataSetId())) {
            throw new AidaRpcException("数据集id不能为空");
        }
        if (Objects.isNull(param.getLockId())) {
            throw new AidaRpcException("数据传输锁不能为空");
        }

        // 验证锁
        validateLock(param.getDataSetId(), param.getLockId());
    }

    /**
     * 构建查询条件
     *
     * @param param 查询参数
     * @return DatasetEsQueryCondition
     */
    private DatasetEsQueryCondition buildQueryCondition(DataSetPageQueryParam param, DataSetVersion version) {
        DatasetEsQueryCondition datasetEsQueryCondition = new DatasetEsQueryCondition();

        //设置基本信息
        handleBaseInfo(param, datasetEsQueryCondition, version);

        // 构建查询条件
        handleQueryCondition(param, datasetEsQueryCondition);

        // 设置默认的排序字段和顺序
        handleSortCondition(param, version, datasetEsQueryCondition);

        return datasetEsQueryCondition;
    }

    /**
     * 处理排序条件
     *
     * @param param                   查询参数
     * @param version                 数据集版本信息
     * @param datasetEsQueryCondition 查询条件对象
     */
    private void handleSortCondition(DataSetPageQueryParam param, DataSetVersion version, DatasetEsQueryCondition datasetEsQueryCondition) {
        if (StringUtils.isNotBlank(param.getSortField())) {
            if (param.getSortType() != null && param.getSortType().equals(SortTypeEnum.COMMON_DATA.getCode())) {
                datasetEsQueryCondition.setSortField(param.getSortField());
            } else {
                if (Objects.isNull(param.getDataSetId())) {
                    String path = ES_COMMON_DATA_PREFIX + "." + param.getSortField() + ".keyword";
                    datasetEsQueryCondition.addSortConditionNested(param.getSortField(), ES_COMMON_DATA_PREFIX, path, SortOrder.fromString(param.getSortOrder()));
                } else {
                    DatasetHeadConfig historyHeadConfig = JSON.parseObject(version.getHeadConfig(),
                            new TypeReference<DatasetHeadConfig>() {
                            });
                    Map<String, DataContentFieldDTO> historyHeadMap = historyHeadConfig.getHeadList().stream().collect(Collectors.toMap(DataContentFieldDTO::getColumnName, Function.identity()));
                    if (historyHeadMap.containsKey(param.getSortField())) {
                        DataContentFieldDTO dataContentField = historyHeadMap.get(param.getSortField());
                        if (FLATTEN_FIELD_TYPE.contains(dataContentField.getFieldType())) {
                            datasetEsQueryCondition.setSortField(FLATTEN_PREFIX + "." + dataContentField.getEsColumnName());
                        } else {
                            String path = TEXT_PREFIX + "." + dataContentField.getEsColumnName() + ".keyword";
                            datasetEsQueryCondition.addSortConditionNested(dataContentField.getEsColumnName(), TEXT_PREFIX, path, SortOrder.fromString(param.getSortOrder()));
                        }
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(param.getSortOrder())) {
            try {
                datasetEsQueryCondition.setSortOrder(SortOrder.valueOf(param.getSortOrder()));
            } catch (IllegalArgumentException e) {
                log.warn("无效的排序顺序: {}, 使用默认的降序排序", param.getSortOrder());
            }
        }
    }

    /**
     * 处理查询条件
     *
     * @param param                   查询参数
     * @param datasetEsQueryCondition 查询条件对象
     */
    private void handleQueryCondition(DataSetPageQueryParam param, DatasetEsQueryCondition datasetEsQueryCondition) {
        Map<String, FieldMappingDTO> fieldTypeMap = new HashMap<>();
        if (Objects.nonNull(param.getDataSetId())) {
            fieldTypeMap = getStringFieldTypeEnumMap(param);
        }
        Set<String> commonFields = getCommonFieldsFromLion();
        if (!CollectionUtils.isEmpty(param.getConditionList())) {
            for (DataSetQueryCondition condition : param.getConditionList()) {
                if (CollectionUtils.isEmpty(condition.getColumnName())) {
                    continue;
                }

                List<String> columnPathParts = condition.getColumnName();
                if (CollectionUtils.isEmpty(columnPathParts)) {
                    log.warn("从列路径部分生成了空的字段名: {} (查询值: {}). 跳过此条件.", columnPathParts, condition.getValue());
                    continue;
                }

                // 获取字段路径信息
                DatasetFieldPathInfo fieldPathInfo = DatasetFieldUtils.buildFieldPathInfo(columnPathParts, fieldTypeMap, commonFields);
                if (Objects.isNull(fieldPathInfo)) {
                    continue;
                }

                String queryValue = condition.getValue();
                Integer conditionType = condition.getConditionType();
                ConditionTypeEnum conditionTypeEnum = ConditionTypeEnum.getByCode(conditionType);
                // 根据不同的条件类型，将查询条件添加到不同的ES查询条件中
                if (conditionTypeEnum != null) {
                    DatasetQueryConditionUtils.addQueryCondition(condition, datasetEsQueryCondition, fieldPathInfo);
                } else {
                    // 如果没有指定条件类型，默认使用精确匹配
                    datasetEsQueryCondition.addTermCondition(fieldPathInfo.getFieldName(), fieldPathInfo.getFullPath(), queryValue);
                }
            }
        }
    }

    /**
     * 处理基本信息
     *
     * @param param     查询参数
     * @param condition 查询条件对象
     */
    private void handleBaseInfo(DataSetPageQueryParam param, DatasetEsQueryCondition condition, DataSetVersion version) {
        // 获取ES索引名称
        String esIndex = null;
        if (Objects.nonNull(version)) {
            esIndex = version.getEsIndexName();
            condition.addTermCondition(CommonConstants.BaseEsIndex.VERSION_ID, CommonConstants.BaseEsIndex.VERSION_ID, version.getId().toString());
        }
        condition.setIndexName(esIndex);

        // 设置分页参数
        if (param.getPageNum() != null) {
            condition.setPageNum(param.getPageNum());
        }
        if (param.getPageSize() != null) {
            condition.setPageSize(param.getPageSize());
        }
        // 设置数据来源
        if (param.getDataSource() != null) {
            condition.setDataSource(DataSourceEnum.getValueByCode(param.getDataSource()));
        }
        if (param.getDataSetId() != null) {
            condition.addTermCondition(CommonConstants.BaseEsIndex.DATASET_ID, CommonConstants.BaseEsIndex.DATASET_ID, param.getDataSetId().toString());
        }
    }

    private Map<String, FieldMappingDTO> getStringFieldTypeEnumMap(DataSetPageQueryParam param) {
        Map<String, FieldMappingDTO> fieldTypeMap = new HashMap<>();
        Long versionId;
        if (Objects.isNull(param.getVersionId())) {
            DataSetVersion latestVersion = getLatestVersion(param.getDataSetId());
            versionId = latestVersion.getId();
        } else {
            versionId = param.getVersionId();
        }
        try {
            // 获取数据集版本信息
            DataSetVersion version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(param.getDataSetId(), versionId);
            if (version != null && StringUtils.isNotBlank(version.getHeadConfig())) {
                fieldTypeMap.putAll(DatasetFieldUtils.convertHeadConfig2FieldMapping(version.getHeadConfig()));
            }
        } catch (Exception e) {
            log.warn("获取或解析数据集版本 {} (versionId: {}) 的 headConfig 失败: {}. 将按默认规则处理字段类型。",
                    param.getDataSetId(), param.getVersionId(), e.getMessage());
        }

        return fieldTypeMap;
    }


    /**
     * 从Lion获取通用字段配置
     *
     * @return 通用字段集合
     */
    private Set<String> getCommonFieldsFromLion() {
        try {
            String commonFieldsConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstant.LION_COMMON_FIELDS_KEY);
            if (StringUtils.isBlank(commonFieldsConfig)) {
                log.info("Lion配置[{}]为空，返回空的通用字段集合", LionConstant.LION_COMMON_FIELDS_KEY);
                return Collections.emptySet();
            }

            Set<String> commonFields = Arrays.stream(commonFieldsConfig.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            log.info("从Lion获取通用字段配置成功，字段数量：{}", commonFields.size());
            return commonFields;
        } catch (Exception e) {
            log.warn("从Lion获取通用字段配置失败", e);
            return Collections.emptySet();
        }
    }

    /**
     * 从采样数据推断字段类型
     *
     * @param columnName 字段名称
     * @param dataList   数据列表
     * @return 推断的字段类型
     */
    private int inferFieldTypeFromSampleData(String columnName, List<DataSetRecordParam> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("数据列表为空，字段[{}]使用默认文本类型", columnName);
            return FieldTypeEnum.TEXT.getCode();
        }

        // 获取采样数据
        List<String> sampleValues = getSampleValues(columnName, dataList);
        if (CollectionUtils.isEmpty(sampleValues)) {
            log.warn("字段[{}]采样数据为空，使用默认文本类型", columnName);
            return FieldTypeEnum.TEXT.getCode();
        }

        // 推断字段类型
        Set<Integer> fieldTypes = new HashSet<>();
        for (String value : sampleValues) {
            if (StringUtils.isNotBlank(value)) {
                int fieldType = matchFieldTypeByValue(value);
                fieldTypes.add(fieldType);
            }
        }

        // 如果所有采样数据的字段类型都相同，则使用该类型，否则使用文本类型
        if (fieldTypes.size() == 1) {
            Integer inferredType = fieldTypes.iterator().next();
            log.info("字段[{}]类型推断成功，采样数量：{}，推断类型：{}", columnName, sampleValues.size(), inferredType);
            return inferredType;
        } else {
            log.info("字段[{}]类型推断结果不一致，采样数量：{}，类型集合：{}，使用文本类型",
                    columnName, sampleValues.size(), fieldTypes);
            return FieldTypeEnum.TEXT.getCode();
        }
    }

    /**
     * 获取字段的采样值
     *
     * @param columnName 字段名称
     * @param dataList   数据列表
     * @return 采样值列表
     */
    private List<String> getSampleValues(String columnName, List<DataSetRecordParam> dataList) {
        List<String> allValues = dataList.stream()
                .map(DataSetRecordParam::getContent)
                .filter(Objects::nonNull)
                .map(content -> content.get(columnName))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allValues)) {
            return Collections.emptyList();
        }

        // 根据数据量决定采样策略
        if (allValues.size() <= MIN_DATA_SIZE_FOR_MIDDLE_SAMPLE) {
            // 数据量小于20条时，取前10条数据
            return allValues.stream()
                    .limit(SAMPLE_SIZE)
                    .collect(Collectors.toList());
        } else {
            // 数据量大于20条时，取中间10条数据
            int startIndex = (allValues.size() - SAMPLE_SIZE) / 2;
            int endIndex = startIndex + SAMPLE_SIZE;
            return allValues.subList(startIndex, Math.min(endIndex, allValues.size()));
        }
    }
}
