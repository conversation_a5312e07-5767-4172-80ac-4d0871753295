package com.meituan.aigc.aida.labeling.pojo.dto.dashboard;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.aigc.aida.labeling.common.enums.LabelTaskDataType;
import com.meituan.aigc.aida.labeling.common.enums.LabelingTaskType;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BaseStatisticsDTO {

    /**
     * 业务类型
     */
    private List<Long> bizTypes;
    /**
     * 任务类型 {@link LabelingTaskType}
     */
    private Integer taskType;
    /**
     * 数据类型 {@link LabelTaskDataType}
     */
    private Integer dataType;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
} 