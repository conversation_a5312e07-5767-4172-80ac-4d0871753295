package com.meituan.aigc.aida.data.management.dataset.param;

import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetQueryCondition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 数据集筛选分页查询参数
 *
 * <AUTHOR>
 * @date 2025-06-04
 * @description 数据集筛选分页查询请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetFilterPageQueryParam implements Serializable {

    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long datasetId;

    /**
     * 数据集版本ID
     */
    @NotNull(message = "数据集版本ID不能为空")
    private Long datasetVersionId;

    /**
     * 页码（从0开始）
     */
    private Integer pageNum = 0;

    /**
     * 页大小
     */
    private Integer pageSize = 20;

    /**
     * 查询条件列表
     */
    private List<DataSetQueryCondition> conditionList;

    /**
     * 需要返回的列列表
     */
    private List<String> columnList;
} 