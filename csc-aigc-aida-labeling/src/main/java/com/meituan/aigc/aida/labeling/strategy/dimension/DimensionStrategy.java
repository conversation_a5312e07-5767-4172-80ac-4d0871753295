package com.meituan.aigc.aida.labeling.strategy.dimension;

import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckDistributeParam;
import com.meituan.aigc.aida.labeling.pojo.vo.DistributionDimensionRequest;

import java.util.List;
import java.util.Map;


public interface DimensionStrategy {

    /**
     * 标注分配
     *
     * @param request 分配请求
     * @return 分配结果
     */
    int labelingDistribute(DistributionDimensionRequest request);

    /**
     * 随机分配数据详情到人员
     *
     * @param param        分配参数
     * @param labelingTask 标注任务
     * @param subTaskList  子任务列表
     * @return 分配结果
     */
    Map<String, List<LabelingDetail>> qualityCheckDistribute(QualityCheckDistributeParam param, LabelingTask labelingTask, List<LabelingSubTask> subTaskList);

    /**
     * 获取维度名称
     */
    Integer getDimensionType();
}
