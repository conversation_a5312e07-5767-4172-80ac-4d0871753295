package com.meituan.aigc.aida.data.management.dataset.strategy.task;

import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据集任务处理策略工厂
 */
@Component
public class DataSetProcessStrategyFactory {

    @Resource
    private List<DataSetProcessStrategy> dataSetProcessStrategyList;

    private Map<Integer, DataSetProcessStrategy> strategyMap;

    @PostConstruct
    public void init() {
        if (MapUtils.isEmpty(strategyMap)) {
            strategyMap = dataSetProcessStrategyList.stream()
                    .collect(Collectors.toMap(DataSetProcessStrategy::getStrategyType, Function.identity()));
        }
    }

    /**
     * 根据数据源类型获取对应的策略
     *
     * @param dataSource 数据源类型
     * @return 对应的策略实现
     */
    public DataSetProcessStrategy getStrategy(Integer dataSource) {
        DataSetProcessStrategy strategy = strategyMap.get(dataSource);
        CheckUtil.paramCheck(strategy != null, "数据集任务处理策略不存在");
        return strategy;
    }
}
