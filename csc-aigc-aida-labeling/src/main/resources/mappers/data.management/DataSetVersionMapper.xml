<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.data.management.dataset.dao.mapper.DataSetVersionMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_set_id" jdbcType="BIGINT" property="dataSetId" />
    <result column="version_name" jdbcType="VARCHAR" property="versionName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="head_config" jdbcType="CHAR" property="headConfig" />
    <result column="es_index_name" jdbcType="VARCHAR" property="esIndexName" />
    <result column="version_type" jdbcType="TINYINT" property="versionType" />
    <result column="version_source" jdbcType="VARCHAR" property="versionSource" />
    <result column="data_count" jdbcType="INTEGER" property="dataCount" />
    <result column="version_status" jdbcType="TINYINT" property="versionStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, data_set_id, version_name, description, head_config, es_index_name, version_type,
    version_source, data_count, version_status, create_time, update_time, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from data_set_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from data_set_version
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_set_version
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersionExample">
    delete from data_set_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion">
    insert into data_set_version (id, data_set_id, version_name,
                                  description, head_config, es_index_name,
                                  version_type, version_source, data_count,
                                  version_status, create_time, update_time,
                                  is_deleted)
    values (#{id,jdbcType=BIGINT}, #{dataSetId,jdbcType=BIGINT}, #{versionName,jdbcType=VARCHAR},
            #{description,jdbcType=VARCHAR}, #{headConfig,jdbcType=CHAR}, #{esIndexName,jdbcType=VARCHAR},
            #{versionType,jdbcType=TINYINT}, #{versionSource,jdbcType=VARCHAR}, #{dataCount,jdbcType=INTEGER},
            #{versionStatus,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
            #{isDeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion"  useGeneratedKeys="true" keyProperty="id">
    insert into data_set_version
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dataSetId != null">
        data_set_id,
      </if>
      <if test="versionName != null">
        version_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="headConfig != null">
        head_config,
      </if>
      <if test="esIndexName != null">
        es_index_name,
      </if>
      <if test="versionType != null">
        version_type,
      </if>
      <if test="versionSource != null">
        version_source,
      </if>
      <if test="dataCount != null">
        data_count,
      </if>
      <if test="versionStatus != null">
        version_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dataSetId != null">
        #{dataSetId,jdbcType=BIGINT},
      </if>
      <if test="versionName != null">
        #{versionName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="headConfig != null">
        #{headConfig,jdbcType=CHAR},
      </if>
      <if test="esIndexName != null">
        #{esIndexName,jdbcType=VARCHAR},
      </if>
      <if test="versionType != null">
        #{versionType,jdbcType=TINYINT},
      </if>
      <if test="versionSource != null">
        #{versionSource,jdbcType=VARCHAR},
      </if>
      <if test="dataCount != null">
        #{dataCount,jdbcType=INTEGER},
      </if>
      <if test="versionStatus != null">
        #{versionStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersionExample" resultType="java.lang.Long">
    select count(*) from data_set_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update data_set_version
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dataSetId != null">
        data_set_id = #{record.dataSetId,jdbcType=BIGINT},
      </if>
      <if test="record.versionName != null">
        version_name = #{record.versionName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.headConfig != null">
        head_config = #{record.headConfig,jdbcType=CHAR},
      </if>
      <if test="record.esIndexName != null">
        es_index_name = #{record.esIndexName,jdbcType=VARCHAR},
      </if>
      <if test="record.versionType != null">
        version_type = #{record.versionType,jdbcType=TINYINT},
      </if>
      <if test="record.versionSource != null">
        version_source = #{record.versionSource,jdbcType=VARCHAR},
      </if>
      <if test="record.dataCount != null">
        data_count = #{record.dataCount,jdbcType=INTEGER},
      </if>
      <if test="record.versionStatus != null">
        version_status = #{record.versionStatus,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update data_set_version
    set id = #{record.id,jdbcType=BIGINT},
    data_set_id = #{record.dataSetId,jdbcType=BIGINT},
    version_name = #{record.versionName,jdbcType=VARCHAR},
    description = #{record.description,jdbcType=VARCHAR},
    head_config = #{record.headConfig,jdbcType=CHAR},
    es_index_name = #{record.esIndexName,jdbcType=VARCHAR},
    version_type = #{record.versionType,jdbcType=TINYINT},
    version_source = #{record.versionSource,jdbcType=VARCHAR},
    data_count = #{record.dataCount,jdbcType=INTEGER},
    version_status = #{record.versionStatus,jdbcType=TINYINT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion">
    update data_set_version
    <set>
      <if test="dataSetId != null">
        data_set_id = #{dataSetId,jdbcType=BIGINT},
      </if>
      <if test="versionName != null">
        version_name = #{versionName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="headConfig != null">
        head_config = #{headConfig,jdbcType=CHAR},
      </if>
      <if test="esIndexName != null">
        es_index_name = #{esIndexName,jdbcType=VARCHAR},
      </if>
      <if test="versionType != null">
        version_type = #{versionType,jdbcType=TINYINT},
      </if>
      <if test="versionSource != null">
        version_source = #{versionSource,jdbcType=VARCHAR},
      </if>
      <if test="dataCount != null">
        data_count = #{dataCount,jdbcType=INTEGER},
      </if>
      <if test="versionStatus != null">
        version_status = #{versionStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion">
    update data_set_version
    set data_set_id = #{dataSetId,jdbcType=BIGINT},
        version_name = #{versionName,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        head_config = #{headConfig,jdbcType=CHAR},
        es_index_name = #{esIndexName,jdbcType=VARCHAR},
        version_type = #{versionType,jdbcType=TINYINT},
        version_source = #{versionSource,jdbcType=VARCHAR},
        data_count = #{dataCount,jdbcType=INTEGER},
        version_status = #{versionStatus,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>