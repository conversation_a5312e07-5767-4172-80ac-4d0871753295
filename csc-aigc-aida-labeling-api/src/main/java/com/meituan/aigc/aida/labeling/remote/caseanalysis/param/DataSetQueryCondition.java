package com.meituan.aigc.aida.labeling.remote.caseanalysis.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-26 11:14
 * @description 数据集查询条件
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetQueryCondition implements Serializable {
    /**
     * 字段名称列表，支持多级传入，如果只有一级，传入一级字段名称即可
     * 例如: ["日期"] 或者 ["原始信号","工单ID"] 表示检索日期或原始信号的工单ID字段
     */
    @NotEmpty
    private List<String> columnName;
    /**
     * 条件类型 1-等于 2-包含 3-大于 4-小于 5-大于等于 6-小于等于 7-为空
     * @see com.meituan.aigc.aida.labeling.remote.common.enums.ConditionTypeEnum
     */
    @NotNull
    private Integer conditionType;
    /**
     * 值
     */
    private String value;
}
