package com.meituan.aigc.aida.data.management.dataset.common;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.data.management.es.query.BatchResult;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.data.management.es.query.DatasetPageResult;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetQueryCondition;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-04 14:57
 * @description 调用ES的
 */
@Component
@Slf4j
public class DataSetInvokeEs {
    @Autowired
    private DatasetEsIndexService datasetEsIndexService;

    private static final int ES_DATA_QUERY_PAGE_SIZE = 1000;


    /**
     * 构建数据集ES查询条件（带分页）
     * @param dataSet 数据集信息
     * @param dataSetVersion 数据集版本信息
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 数据集ES查询条件
     */
    public DatasetEsQueryCondition getDatasetEsQueryConditionWithPage(DataSet dataSet, DataSetVersion dataSetVersion, int pageNum, int pageSize) {
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource(DataSourceEnum.getValueByCode(dataSet.getDataSource()))
                .indexName(dataSetVersion.getEsIndexName())
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();
        condition.addTermCondition(CommonConstants.BaseEsIndex.DATASET_ID, CommonConstants.BaseEsIndex.DATASET_ID, dataSet.getId().toString());
        condition.addTermCondition(CommonConstants.BaseEsIndex.VERSION_ID, CommonConstants.BaseEsIndex.VERSION_ID, dataSetVersion.getId().toString());
        condition.addSortCondition(CommonConstants.BaseEsIndex.UPDATE_TIME, CommonConstants.BaseEsIndex.UPDATE_TIME, SortOrder.DESC);
        return condition;
    }

    /**
     * 构建滚动查询条件
     *
     * @param dataSet       数据集对象
     * @param sourceVersion 源版本对象
     * @param conditionList 查询条件列表
     * @return 返回构建好的滚动查询条件
     */
    public DatasetEsQueryCondition buildScrollCondition(DataSet dataSet, DataSetVersion sourceVersion, List<DataSetQueryCondition> conditionList) {
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource(DataSourceEnum.getValueByCode(dataSet.getDataSource()))
                .indexName(sourceVersion.getEsIndexName())
                .scrollTime("10m")
                .pageSize(ES_DATA_QUERY_PAGE_SIZE)
                .build();
        condition.setDataSource(DataSourceEnum.getValueByCode(dataSet.getDataSource()));
        condition.addTermCondition(CommonConstants.BaseEsIndex.DATASET_ID, CommonConstants.BaseEsIndex.DATASET_ID, dataSet.getId().toString());
        condition.addTermCondition(CommonConstants.BaseEsIndex.VERSION_ID, CommonConstants.BaseEsIndex.VERSION_ID, sourceVersion.getId().toString());
        // TODO: 2025/6/5 增加入参的条件
        return condition;
    }

    /**
     * 删除版本的ES数据
     * @param dataSet 数据集对象
     * @param version 数据集版本对象
     */
    public void deleteVersionEsData(DataSet dataSet, DataSetVersion version) {
        String dataSource = DataSourceEnum.getValueByCode(dataSet.getDataSource());
        String indexName = version.getEsIndexName();

        if (StringUtils.isBlank(indexName)) {
            log.warn("数据集版本未关联ES索引，无需删除ES数据, dataSetId:{}, versionId:{}", dataSet.getId(), version.getId());
            return;
        }
        // 滚动查询
        DatasetEsQueryCondition condition = buildScrollCondition(dataSet, version, null);
        try {
            boolean hasMore = true;
            int totalDeletedCount = 0;
            // 构建ES查询条件
            while (hasMore) {
                DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);
                List<DatasetEsIndex> records = pageResult.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                    break;
                }

                // 批量删除ES数据
                BatchResult batchResult = datasetEsIndexService.batchDeleteDatasets(records, dataSource, indexName);
                if (!batchResult.isAllSuccess()) {
                    log.error("数据集版本数据删除ES失败，dataSetId:{}, versionId:{}", dataSet.getId(), version.getId());
                }
                totalDeletedCount += records.size();

                // 判断是否还有下一页
                hasMore = pageResult.getHasNext() != null && pageResult.getHasNext();
                if (hasMore) {
                    condition.setScrollId(pageResult.getScrollId());
                }
            }
            log.info("删除数据集版本ES数据完成，dataSetId:{}, versionId:{}, 总删除数据条数:{}",
                    dataSet.getId(), version.getId(), totalDeletedCount);
        } catch (Exception e) {
            log.error("删除数据集版本ES数据异常，dataSetId:{}, versionId:{}", dataSet.getId(), version.getId(), e);
            throw new RuntimeException("删除数据集版本ES数据失败", e);
        }
    }
}
