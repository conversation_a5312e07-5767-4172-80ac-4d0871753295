package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.meituan.aigc.aida.labeling.dao.mapper.LabelingTaskRawDataMapper;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawDataExample;
import com.meituan.aigc.aida.labeling.dao.po.LabelingRawDataCopyRelationPO;
import com.meituan.aigc.aida.labeling.dao.po.LabelingTaskRawDataPO;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskRawDataRepository;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingTaskCountDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:11
 * @Version: 1.0
 */
@Repository
public class LabelingTaskRawDataRepositoryImpl implements LabelingTaskRawDataRepository {

    @Resource
    private LabelingTaskRawDataMapper labelingTaskRawDataMapper;


    @Override
    public void batchInsert(List<LabelingTaskRawData> data) {
        labelingTaskRawDataMapper.batchInsert(data);
    }

    @Override
    public void updateByPrimaryKeySelective(LabelingTaskRawData data) {
        labelingTaskRawDataMapper.updateByPrimaryKeySelective(data);
    }

    @Override
    public List<LabelingTaskRawData> listByIds(List<Long> rawDataIds) {
        if (CollectionUtils.isEmpty(rawDataIds)) {
            return Collections.emptyList();
        }
        // 每批次处理的大小
        final int batchSize = 100;
        // 创建结果集合
        List<LabelingTaskRawData> labelingTaskRawDataList = new ArrayList<>();
        // 分批处理
        for (int i = 0; i < rawDataIds.size(); i += batchSize) {
            // 计算当前批次的结束索引
            int endIndex = Math.min(i + batchSize, rawDataIds.size());

            // 获取当前批次的ID列表
            List<Long> batchIds = rawDataIds.subList(i, endIndex);

            // 查询当前批次的数据
            List<LabelingTaskRawData> batchResult = labelingTaskRawDataMapper.listByIds(batchIds);

            // 将结果添加到总结果集合中
            labelingTaskRawDataList.addAll(batchResult);
        }
        return labelingTaskRawDataList;
    }

    @Override
    public List<LabelingTaskRawDataPO> getCountByTaskIds(List<Long> taskIds) {
        return labelingTaskRawDataMapper.getCountByTaskIds(taskIds);
    }

    @Override
    public List<LabelingTaskRawData> getRawDataByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return Collections.emptyList();
        }
        LabelingTaskRawDataExample example = new LabelingTaskRawDataExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        return labelingTaskRawDataMapper.selectByExample(example);
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return;
        }
        LabelingTaskRawDataExample example = new LabelingTaskRawDataExample();
        LabelingTaskRawDataExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        labelingTaskRawDataMapper.deleteByExample(example);
    }

    @Override
    public long countByTaskId(Long taskId) {
        LabelingTaskRawDataExample example = new LabelingTaskRawDataExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        return labelingTaskRawDataMapper.countByExample(example);
    }

    @Override
    public List<LabelingTaskRawData> pageQueryByTaskId(Long taskId, Integer offset, Integer limit) {
        return labelingTaskRawDataMapper.pageQueryByTaskIdWithOffset(taskId, offset, limit);
    }

    @Override
    public List<String> pageSessionByTaskId(Long taskId, Integer offset, Integer limit) {
        return labelingTaskRawDataMapper.pageSessionByTaskIdWithOffset(taskId, offset, limit);
    }

    @Override
    public LabelingTaskRawData getById(Long rawDataId) {
        if (Objects.isNull(rawDataId)) {
            return null;
        }
        return labelingTaskRawDataMapper.selectByPrimaryKey(rawDataId);
    }

    @Override
    public LabelingTaskCountDTO listCountByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }
        return labelingTaskRawDataMapper.listCountByTaskId(taskId);
    }

    @Override
    public List<LabelingTaskRawData> listByTaskIdAndSessionIdList(Long taskId, List<String> sessionIdList) {
        if (Objects.isNull(taskId) || CollectionUtils.isEmpty(sessionIdList)) {
            return Collections.emptyList();
        }
        LabelingTaskRawDataExample example = new LabelingTaskRawDataExample();
        example.createCriteria().andTaskIdEqualTo(taskId).andSessionIdIn(sessionIdList);
        return labelingTaskRawDataMapper.selectByExample(example);
    }

    @Override
    public List<LabelingRawDataCopyRelationPO> listCopyRelationByIds(List<Long> rawDataIds) {
        if (CollectionUtils.isEmpty(rawDataIds)) {
            return Collections.emptyList();
        }
        return labelingTaskRawDataMapper.listCopyRelationByIds(rawDataIds);
    }
}
