package com.meituan.aigc.aida.data.management.fetch.biz.pacific.butler;

import com.dianping.lion.Environment;
import com.dianping.pigeon.remoting.invoker.config.annotation.Reference;
import com.meituan.aigc.aida.common.util.ObjectConverter;
import com.sankuai.csc.pacific.common.tools.enums.CustomerEndEnum;
import com.sankuai.csc.workbench.bff.api.workbench.dto.InitButlerResDTO;
import com.sankuai.mdp.csc.pacific.context.api.base.Result;
import com.sankuai.mdp.csc.pacific.context.api.dto.request.ContextInvokerReqDto;
import com.sankuai.mdp.csc.pacific.context.api.dto.request.ContextQueryReqDto;
import com.sankuai.mdp.csc.pacific.context.api.dto.response.ContextInstanceParamRespDto;
import com.sankuai.mdp.csc.pacific.context.api.dto.response.ContextInstanceRespDto;
import com.sankuai.mdp.csc.pacific.context.api.enums.ChannelTypeEnum;
import com.sankuai.mdp.csc.pacific.context.api.enums.ContextCategoryEnum;
import com.sankuai.mdp.csc.pacific.context.api.enums.InvokeTypeEnum;
import com.sankuai.mdp.csc.pacific.context.api.remote.ContextRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 太平洋上下文解析 - 管家(新工作台)
 *
 * <AUTHOR>
 * @date 2025-03-10 PM6:01
 */
@Slf4j
@Component
public class ContextUtils {
    private static final String LOG_PREFIX = "[ContextUtils]";

    public static final String SPCODE_TAB = "tab";
    public static final String DATA_MODEL_CATEGORY = "dataModelCategory";

    @Autowired
    private ContextRemoteService contextRemoteService;

    public Pair<InitButlerResDTO, Map<String, String>> getContextInfo(String contactId, ChannelTypeEnum channelTypeEnum) {
        ContextInstanceRespDto contextInstanceRespDto = findContextByChannelFromMaster(contactId, channelTypeEnum.getCode());
        if (Objects.isNull(contextInstanceRespDto)) {
            log.warn("{}initButler contextInstanceRespDto is null", LOG_PREFIX);
            return Pair.of(null, new HashMap<>());
        }
        List<ContextInstanceParamRespDto> contextInstanceParamRespDtoList = contextInstanceRespDto.getContextInstanceParamRespDtoList();
        if (CollectionUtils.isEmpty(contextInstanceParamRespDtoList)) {
            return Pair.of(null, new HashMap<>());
        }
        Integer customerEndType = null;
        ContextInstanceParamRespDto customerEndTypeParam =
                contextInstanceParamRespDtoList.stream().filter(contextInstanceParamRespDto ->
                        Objects.equals(contextInstanceParamRespDto.getCategoryCode(), "pathParamCategory")
                                && Objects.equals(contextInstanceParamRespDto.getSpCode(), "customerEndType")).findAny().orElse(null);
        if (Objects.nonNull(customerEndTypeParam)) {
            customerEndType = Integer.valueOf(customerEndTypeParam.getSpValue());
        }
        // 云客服场景
        if (null != customerEndType && Objects.equals(customerEndType, CustomerEndEnum.CLOUD.getCode())) {
            log.info("{}, initButler, 云客服, contactId = [{}]", LOG_PREFIX, contactId);
            // 6.解析服务管家列表。如果未查到，需要初始化数据返给前端
            InitButlerResDTO initButlerResDTO = BffContextUtil.getCloudContextTabListValue(contextInstanceRespDto, ContextCategoryEnum.TAB_LIST_CATEGORY, SPCODE_TAB);
            if (CollectionUtils.isEmpty(contextInstanceParamRespDtoList)) {
                return Pair.of(initButlerResDTO, new HashMap<>());
            }
            Map<String, String> dtcMap =
                    contextInstanceParamRespDtoList.stream().filter(Objects::nonNull).filter(contextInstanceParamRespDto -> Objects.equals(contextInstanceParamRespDto.getCategoryCode(),
                            DATA_MODEL_CATEGORY)).collect(Collectors.toMap(ContextInstanceParamRespDto::getSpCode, ContextInstanceParamRespDto::getSpValue, (v1, v2) -> v1));
            return Pair.of(initButlerResDTO, dtcMap);
        }

        log.info("{}, initButler, 非云客服, contactId = [{}]", LOG_PREFIX, contactId);
        // 外客场景
        // 6.解析服务管家列表。如果未查到，需要初始化数据返给前端
        InitButlerResDTO initButlerResDTO = BffContextUtil.getContextTabListValue(contextInstanceRespDto, ContextCategoryEnum.TAB_LIST_CATEGORY, SPCODE_TAB);
        if (CollectionUtils.isEmpty(contextInstanceParamRespDtoList)) {
            return Pair.of(initButlerResDTO, new HashMap<>());
        }
        Map<String, String> dtcMap =
                contextInstanceParamRespDtoList.stream().filter(Objects::nonNull).filter(contextInstanceParamRespDto -> Objects.equals(contextInstanceParamRespDto.getCategoryCode(),
                        DATA_MODEL_CATEGORY)).collect(Collectors.toMap(ContextInstanceParamRespDto::getSpCode, ContextInstanceParamRespDto::getSpValue, (v1, v2) -> v1));
        return Pair.of(initButlerResDTO, dtcMap);
    }

    public ContextInstanceRespDto findContextByChannelFromMaster(String channelId, Integer channelType) {
        return findContextByChannel(channelId, channelType, true);

    }


    private ContextInstanceRespDto findContextByChannel(String channelId, Integer channelType, Boolean readMaster) {
        ContextQueryReqDto contextQueryReqDto = new ContextQueryReqDto();
        contextQueryReqDto.setChannelId(channelId);
        contextQueryReqDto.setChannelType(channelType);
        contextQueryReqDto.setReadMaster(readMaster);
        contextQueryReqDto.setContextInvokerReqDto(buildContextInvokerReqDto(InvokeTypeEnum.QUERY.getCode(), null));
        log.info("{}根据channelId查询上下文begin, contextQueryReqDto = [{}]", LOG_PREFIX, ObjectConverter.convertToJson(contextQueryReqDto));
        Result<ContextInstanceRespDto> rsp = contextRemoteService.queryContextByChannelId(contextQueryReqDto);
        log.info("{}根据channelId查询上下文end, rsp = [{}]", LOG_PREFIX, ObjectConverter.convertToJson(rsp));
        return null == rsp ? null : rsp.getData();
    }

    /**
     * 构建上下文"请求来源信息"
     */
    public static ContextInvokerReqDto buildContextInvokerReqDto(int type, Long staffId) {
        ContextInvokerReqDto contextInvokerReqDto = new ContextInvokerReqDto();
        contextInvokerReqDto.setEventCode("pacific_workbench_new");
        contextInvokerReqDto.setId(Environment.getAppName());
        contextInvokerReqDto.setName(Environment.getAppName());
        contextInvokerReqDto.setType(type);
        contextInvokerReqDto.setUpdaterId(staffId);
        return contextInvokerReqDto;
    }
}
