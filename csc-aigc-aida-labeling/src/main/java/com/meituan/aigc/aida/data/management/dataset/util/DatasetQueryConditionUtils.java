package com.meituan.aigc.aida.data.management.dataset.util;

import com.google.common.collect.Lists;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFieldPathInfo;
import com.meituan.aigc.aida.data.management.dataset.dto.FieldMappingDTO;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetQueryCondition;
import com.meituan.aigc.aida.labeling.remote.common.enums.ConditionTypeEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.meituan.aigc.aida.data.management.dataset.common.constants.DatasetCommonConstants.*;
import static com.meituan.aigc.aida.data.management.dataset.common.constants.DatasetCommonConstants.ES_KEYWORD_SUFFIX;

/**
 * 数据集查询条件构建工具类
 *
 * <AUTHOR>
 * @date 2025-06-03
 * @description 处理不同条件类型的ES查询条件构建逻辑
 */
@Slf4j
public class DatasetQueryConditionUtils {

    /**
     * 支持扁平化存储的字段类型
     */
    private static final List<Integer> FLATTEN_FIELD_TYPE = Lists.newArrayList(FieldTypeEnum.JSON.getCode(), FieldTypeEnum.PROCESSED_SIGNAL.getCode());

    /**
     * 添加查询条件到ES查询对象
     *
     * @param condition ES查询条件对象
     * @param pathInfo  字段路径信息
     */
    public static void addQueryCondition(DataSetQueryCondition condition, DatasetEsQueryCondition datasetEsQueryCondition,
                                         DatasetFieldPathInfo pathInfo) {
        if (Objects.isNull(condition)) {
            log.info("addQueryCondition-查询条件为空");
            return;
        }

        ConditionTypeEnum conditionType = ConditionTypeEnum.getByCode(condition.getConditionType());
        if (conditionType == null) {
            log.warn("addQueryCondition——查询的条件类型为空");
            return;
        }
        switch (conditionType) {
            case EQUAL:
                addEqualCondition(datasetEsQueryCondition, pathInfo, condition.getValue());
                break;
            case CONTAIN:
                addContainCondition(datasetEsQueryCondition, pathInfo, condition.getValue());
                break;
            case GREATER_THAN:
                addRangeCondition(datasetEsQueryCondition, pathInfo,
                        DatasetEsQueryCondition.RangeCondition.greaterThan(condition.getValue()));
                break;
            case LESS_THAN:
                addRangeCondition(datasetEsQueryCondition, pathInfo,
                        DatasetEsQueryCondition.RangeCondition.lessThan(condition.getValue()));
                break;
            case GREATER_THAN_OR_EQUAL:
                addRangeCondition(datasetEsQueryCondition, pathInfo,
                        DatasetEsQueryCondition.RangeCondition.greaterThanOrEqual(condition.getValue()));
                break;
            case LESS_THAN_OR_EQUAL:
                addRangeCondition(datasetEsQueryCondition, pathInfo,
                        DatasetEsQueryCondition.RangeCondition.lessThanOrEqual(condition.getValue()));
                break;
            case IS_NULL:
                addExistsCondition(datasetEsQueryCondition, pathInfo, false);
                break;
            default:
                log.warn("未处理的条件类型: {}, 使用默认的精确匹配查询", conditionType);
                addEqualCondition(datasetEsQueryCondition, pathInfo, condition.getValue());
                break;
        }
    }

    /**
     * 检查记录是否匹配所有内存过滤条件
     *
     * @param record                 数据记录
     * @param memoryFilterConditions 内存过滤条件列表
     * @param fieldTypeMap           字段类型映射
     * @return 是否匹配所有条件
     */
    public static boolean matchesMemoryFilterConditions(DatasetEsIndex record,
                                                        List<DataSetQueryCondition> memoryFilterConditions,
                                                        Map<String, FieldMappingDTO> fieldTypeMap) {
        if (CollectionUtils.isEmpty(memoryFilterConditions)) {
            return true;
        }

        // 所有条件都必须匹配（AND逻辑）
        for (DataSetQueryCondition condition : memoryFilterConditions) {
            if (!matchesMemoryFilterCondition(record, condition, fieldTypeMap)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 检查记录是否匹配单个内存过滤条件
     *
     * @param record       数据记录
     * @param condition    过滤条件
     * @param fieldTypeMap 字段类型映射
     * @return 是否匹配条件
     */
    private static boolean matchesMemoryFilterCondition(DatasetEsIndex record,
                                                        DataSetQueryCondition condition,
                                                        Map<String, FieldMappingDTO> fieldTypeMap) {
        try {
            Set<String> commonFields = DatasetFieldUtils.getCommonFieldsFromLion();
            // 构建字段路径信息
            DatasetFieldPathInfo pathInfo = DatasetFieldUtils.buildFieldPathInfo(condition.getColumnName(), fieldTypeMap, commonFields);
            if (pathInfo == null) {
                log.warn("无法构建字段路径信息，字段名: {}", condition.getColumnName());
                return false;
            }

            // 获取字段值
            Object fieldValue = getFieldValueFromRecord(record, pathInfo);
            if (fieldValue == null) {
                log.debug("字段值为空，字段名: {}", condition.getColumnName());
                return false;
            }

            // 转换为字符串进行比较
            String fieldValueStr = fieldValue.toString();
            String conditionValue = condition.getValue();

            if (StringUtils.isBlank(conditionValue)) {
                log.debug("条件值为空，字段名: {}", condition.getColumnName());
                return false;
            }

            // 执行CONTAIN匹配（忽略大小写）
            boolean matches = fieldValueStr.toLowerCase().contains(conditionValue.toLowerCase());

            log.debug("内存过滤匹配结果: 字段={}, 字段值={}, 条件值={}, 匹配={}",
                    condition.getColumnName(), fieldValueStr, conditionValue, matches);

            return matches;

        } catch (Exception e) {
            log.error("内存过滤条件匹配异常，字段名: {}, 条件值: {}",
                    condition.getColumnName(), condition.getValue(), e);
            return false;
        }
    }

    /**
     * 从记录中获取字段值
     *
     * @param record   数据记录
     * @param pathInfo 字段路径信息
     * @return 字段值
     */
    private static Object getFieldValueFromRecord(DatasetEsIndex record, DatasetFieldPathInfo pathInfo) {
        if (record == null || pathInfo == null) {
            return null;
        }

        try {
            // 根据字段类型从不同的数据源获取值
            if (DatasetQueryConditionUtils.isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
                // 从扁平化数据中获取
                Map<String, Object> flattenedData = record.getFlattenedData();
                if (flattenedData != null) {
                    // 构建扁平化字段路径（去掉前缀）
                    String flattenPath = pathInfo.getFlattenPath();
                    if (flattenPath.startsWith(ES_FLATTEN_PREFIX + POINT)) {
                        String fieldPath = flattenPath.substring((ES_FLATTEN_PREFIX + POINT).length());
                        return getNestedFieldValue(flattenedData, fieldPath);
                    }
                }
            } else {
                // 从文本数据中获取
                Map<String, Object> textData = record.getTextData();
                if (textData != null) {
                    String textPath = pathInfo.getTextPath();
                    if (textPath.startsWith(ES_TEXT_PREFIX + POINT)) {
                        String fieldPath = textPath.substring((ES_TEXT_PREFIX + POINT).length());
                        // 去掉.keyword后缀
                        if (fieldPath.endsWith(ES_KEYWORD_SUFFIX)) {
                            fieldPath = fieldPath.substring(0, fieldPath.length() - ES_KEYWORD_SUFFIX.length());
                        }
                        return getNestedFieldValue(textData, fieldPath);
                    }
                }
            }

            // 如果是公共字段，从公共数据中获取
            if (Boolean.TRUE.equals(pathInfo.getIsCommonField())) {
                Map<String, Object> commonData = record.getCommonData();
                if (commonData != null) {
                    String commonPath = pathInfo.getCommonDataKeywordPath();
                    if (commonPath.startsWith(ES_COMMON_DATA_PREFIX + POINT)) {
                        String fieldPath = commonPath.substring((ES_COMMON_DATA_PREFIX + POINT).length());
                        if (fieldPath.endsWith(ES_KEYWORD_SUFFIX)) {
                            fieldPath = fieldPath.substring(0, fieldPath.length() - ES_KEYWORD_SUFFIX.length());
                        }
                        return getNestedFieldValue(commonData, fieldPath);
                    }
                }
            }

        } catch (Exception e) {
            log.error("获取字段值异常，字段名: {}", pathInfo.getFieldName(), e);
        }

        return null;
    }

    /**
     * 从嵌套Map中获取字段值
     *
     * @param data      数据Map
     * @param fieldPath 字段路径（用.分隔的嵌套路径）
     * @return 字段值
     */
    private static Object getNestedFieldValue(Map<String, Object> data, String fieldPath) {
        if (data == null || StringUtils.isBlank(fieldPath)) {
            return null;
        }

        String[] pathParts = fieldPath.split("\\.");
        Object currentValue = data;

        for (String part : pathParts) {
            if (currentValue instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> currentMap = (Map<String, Object>) currentValue;
                currentValue = currentMap.get(part);
            } else {
                return null;
            }
        }

        return currentValue;
    }

    /**
     * 判断是否使用扁平化路径存储 - 基于字段类型代码
     */
    public static boolean isUseFlattenPath(Integer fieldTypeCode) {
        return fieldTypeCode != null && FLATTEN_FIELD_TYPE.contains(fieldTypeCode);
    }

    /**
     * 判断是否使用扁平化路径存储 - 基于字段映射对象
     */
    public static boolean isUseFlattenPath(FieldMappingDTO fieldMappingDTO) {
        return fieldMappingDTO != null &&
                fieldMappingDTO.getFieldType() != null &&
                isUseFlattenPath(fieldMappingDTO.getFieldType().getCode());
    }

    /**
     * 添加精确匹配条件
     */
    private static void addEqualCondition(DatasetEsQueryCondition condition, DatasetFieldPathInfo pathInfo, String searchValue) {
        if (pathInfo.getIsCommonField()) {
            condition.addTermConditionNested(pathInfo.getFieldName(), ES_COMMON_DATA_PREFIX, pathInfo.getCommonDataKeywordPath(), searchValue);
        } else if (isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
            condition.addTermCondition(pathInfo.getFieldName(), pathInfo.getFlattenPath(), searchValue);
        } else {
            condition.addTermConditionNested(pathInfo.getFieldName(), ES_TEXT_PREFIX, pathInfo.getTextPath(), searchValue);
        }
    }

    /**
     * 添加包含条件
     */
    private static void addContainCondition(DatasetEsQueryCondition condition, DatasetFieldPathInfo pathInfo, String queryValue) {
        String wildcardValue = "*" + queryValue + "*";
        if (pathInfo.getIsCommonField()) {
            condition.addWildcardConditionNested(pathInfo.getFieldName(), ES_COMMON_DATA_PREFIX, pathInfo.getCommonDataKeywordPath(), wildcardValue);
        } else if (isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
            condition.addWildcardCondition(pathInfo.getFieldName(), pathInfo.getFlattenPath(), wildcardValue);
        } else {
            condition.addWildcardConditionNested(pathInfo.getFieldName(), ES_TEXT_PREFIX, pathInfo.getTextPath(), wildcardValue);
        }
    }

    /**
     * 添加范围条件
     */
    private static void addRangeCondition(DatasetEsQueryCondition condition,
                                          DatasetFieldPathInfo pathInfo,
                                          DatasetEsQueryCondition.RangeCondition rangeCondition) {
        if (pathInfo.getIsCommonField()) {
            condition.addRangeConditionNested(pathInfo.getFieldName(), ES_COMMON_DATA_PREFIX, pathInfo.getCommonDataKeywordPath(), rangeCondition);
        } else if (isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
            condition.addRangeCondition(pathInfo.getFieldName(), pathInfo.getFlattenPath(), rangeCondition);
        } else {
            condition.addRangeConditionNested(pathInfo.getFieldName(), ES_TEXT_PREFIX, pathInfo.getTextPath(), rangeCondition);
        }
    }

    /**
     * 添加存在性条件
     */
    private static void addExistsCondition(DatasetEsQueryCondition condition,
                                           DatasetFieldPathInfo pathInfo,
                                           boolean exists) {
        if (pathInfo.getIsCommonField()) {
            condition.addExistsConditionNested(pathInfo.getFieldName(), ES_COMMON_DATA_PREFIX, pathInfo.getCommonDataKeywordPath(), exists);
        } else if (isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
            condition.addExistsCondition(pathInfo.getFieldName(), pathInfo.getFlattenPath(), exists);
        } else {
            condition.addExistsConditionNested(pathInfo.getFieldName(), ES_TEXT_PREFIX, pathInfo.getTextPath(), exists);
        }
    }


}