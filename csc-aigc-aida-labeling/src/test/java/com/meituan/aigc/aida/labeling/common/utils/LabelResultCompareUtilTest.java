package com.meituan.aigc.aida.labeling.common.utils;

import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertTrue;

/**
 * @Author: guowenhui
 * @Create: 2025/3/5 15:38
 * @Version: 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class LabelResultCompareUtilTest {

    @InjectMocks
    private LabelResultCompareUtil compareService;

    // 数据类型常量
    private static final Integer ENUM_TYPE = 1;
    private static final Integer TEXT_TYPE = 2;

    // 指标类型常量
    private static final Integer LABEL_TYPE = 1;
    private static final Integer QC_TYPE = 2;

    @Before
    public void setUp() {
        compareService = new LabelResultCompareUtil();
    }

    /**
     * 测试两个空列表比较
     */
    @Test
    public void testEmptyListsComparison() {
        List<List<LabelResult>> list1 = new ArrayList<>();
        List<List<LabelResult>> list2 = new ArrayList<>();

        assertTrue("两个空列表应该相等", LabelResultCompareUtil.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试完全相同的列表比较
     */
    @Test
    public void testIdenticalListsComparison() {
        List<List<LabelResult>> list1 = createBasicLabelResultList();
        List<List<LabelResult>> list2 = createBasicLabelResultList();

        assertTrue("完全相同的列表应该相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试枚举值不同的列表比较
     */
    @Test
    public void testDifferentEnumValuesComparison() {
        List<List<LabelResult>> list1 = createBasicLabelResultList();
        List<List<LabelResult>> list2 = createBasicLabelResultList();

        // 修改第二个列表中的枚举值
        list2.get(0).get(0).setValue("不同的值");

        assertFalse("枚举值不同的列表应该不相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试文本类型值不同但枚举值相同的列表比较
     */
    @Test
    public void testDifferentTextValuesComparison() {
        List<List<LabelResult>> list1 = createBasicLabelResultList();
        List<List<LabelResult>> list2 = createBasicLabelResultList();

        // 添加文本类型的标注项
        LabelResult textLabel1 = createLabelResult(3L, "文本标注", TEXT_TYPE, LABEL_TYPE, 2, null, null, "文本1", null);
        LabelResult textLabel2 = createLabelResult(3L, "文本标注", TEXT_TYPE, LABEL_TYPE, 2, null, null, "文本2", null);

        list1.get(0).add(textLabel1);
        list2.get(0).add(textLabel2);

        assertTrue("文本类型值不同但枚举值相同的列表应该相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试一个列表为null的情况
     */
    @Test
    public void testOneNullListComparison() {
        List<List<LabelResult>> list1 = createBasicLabelResultList();

        assertFalse("一个列表为null应该返回false", compareService.compareLabelValues(list1, null, false));
        assertFalse("一个列表为null应该返回false", compareService.compareLabelValues(null, list1, false));
    }

    /**
     * 测试两个列表长度不同的情况
     */
    @Test
    public void testDifferentLengthListsComparison() {
        List<List<LabelResult>> list1 = createBasicLabelResultList();
        List<List<LabelResult>> list2 = createBasicLabelResultList();

        // 添加一个额外的组到第二个列表
        list2.add(new ArrayList<>(Arrays.asList(
                createLabelResult(3L, "额外标注", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B"))
        )));

        assertFalse("长度不同的列表应该不相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试列表中包含null元素的情况
     */
    @Test
    public void testListWithNullElements() {
        List<List<LabelResult>> list1 = new ArrayList<>();
        list1.add(Arrays.asList(
                createLabelResult(1L, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")),
                null
        ));

        List<List<LabelResult>> list2 = new ArrayList<>();
        list2.add(Arrays.asList(
                createLabelResult(1L, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")),
                null
        ));

        assertTrue("包含null元素的列表应该正确比较", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试标注项ID为null的情况
     */
    @Test
    public void testLabelResultWithNullId() {
        List<List<LabelResult>> list1 = new ArrayList<>();
        list1.add(Arrays.asList(
                createLabelResult(null, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B"))
        ));

        List<List<LabelResult>> list2 = new ArrayList<>();
        list2.add(Arrays.asList(
                createLabelResult(null, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B"))
        ));

        assertTrue("ID为null的标注项应该被正确处理", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试空组的处理
     */
    @Test
    public void testEmptyGroups() {
        List<List<LabelResult>> list1 = new ArrayList<>();
        list1.add(new ArrayList<>());
        list1.add(Arrays.asList(
                createLabelResult(1L, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B"))
        ));

        List<List<LabelResult>> list2 = new ArrayList<>();
        list2.add(new ArrayList<>());
        list2.add(Arrays.asList(
                createLabelResult(1L, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B"))
        ));

        assertTrue("空组应该被正确处理", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试父子指标值相同的比较
     */
    @Test
    public void testParentChildWithSameValues() {
        List<List<LabelResult>> list1 = createCascadingLabelResultList();
        List<List<LabelResult>> list2 = createCascadingLabelResultList();

        assertTrue("父子指标值相同的列表应该相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试父指标值相同但子指标值不同的比较
     */
    @Test
    public void testParentSameChildDifferent() {
        List<List<LabelResult>> list1 = createCascadingLabelResultList();
        List<List<LabelResult>> list2 = createCascadingLabelResultList();

        // 修改第二个列表中的子指标值
        list2.get(0).get(1).setValue("子值B");

        assertFalse("父指标值相同但子指标值不同的列表应该不相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试父指标值不同的比较
     */
    @Test
    public void testParentDifferent() {
        List<List<LabelResult>> list1 = createCascadingLabelResultList();
        List<List<LabelResult>> list2 = createCascadingLabelResultList();

        // 修改第二个列表中的父指标值
        list2.get(0).get(0).setValue("值B");

        assertFalse("父指标值不同的列表应该不相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试组顺序不同但内容相同的比较
     */
    @Test
    public void testDifferentGroupOrder() {
        // 创建两个组
        List<LabelResult> group1 = new ArrayList<>();
        group1.add(createLabelResult(1L, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")));

        List<LabelResult> group2 = new ArrayList<>();
        group2.add(createLabelResult(2L, "标注2", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值C", Arrays.asList("值C", "值D")));

        // 创建两个顺序不同的列表
        List<List<LabelResult>> list1 = new ArrayList<>();
        list1.add(group1);
        list1.add(group2);

        List<List<LabelResult>> list2 = new ArrayList<>();
        list2.add(group2);
        list2.add(group1);

        // 由于组的顺序不同，比较结果可能取决于实现
        // 如果实现考虑了组的顺序，则应该不相等
        // 如果实现只关心组的内容，则应该相等
        // 这里假设实现考虑了组的顺序
        assertFalse("组顺序不同的列表应该不相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试组内标注项顺序不同但内容相同的比较
     */
    @Test
    public void testDifferentItemOrder() {
        // 创建两个顺序不同的组
        List<LabelResult> group1 = new ArrayList<>();
        group1.add(createLabelResult(1L, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")));
        group1.add(createLabelResult(2L, "标注2", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值C", Arrays.asList("值C", "值D")));

        List<LabelResult> group2 = new ArrayList<>();
        group2.add(createLabelResult(2L, "标注2", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值C", Arrays.asList("值C", "值D")));
        group2.add(createLabelResult(1L, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")));

        // 创建两个列表
        List<List<LabelResult>> list1 = new ArrayList<>();
        list1.add(group1);

        List<List<LabelResult>> list2 = new ArrayList<>();
        list2.add(group2);

        assertTrue("组内标注项顺序不同但内容相同的列表应该相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试多层级联指标的比较
     */
    @Test
    public void testMultiLevelCascading() {
        List<List<LabelResult>> list1 = createMultiLevelCascadingList();
        List<List<LabelResult>> list2 = createMultiLevelCascadingList();

        assertTrue("多层级联指标相同的列表应该相等", compareService.compareLabelValues(list1, list2, false));

        // 修改第二个列表中的最底层子指标值
        list2.get(0).get(2).setValue("孙值B");

        assertFalse("多层级联指标中有不同值的列表应该不相等", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试背靠背一致率标注项的比较
     */
    @Test
    public void testBackToBackCompareItems() {
        List<List<LabelResult>> list1 = new ArrayList<>();
        list1.add(Arrays.asList(
                createLabelResult(1L, "一致率标注", ENUM_TYPE, LABEL_TYPE, 1, null, null, "是", Arrays.asList("是", "否"))
        ));

        List<List<LabelResult>> list2 = new ArrayList<>();
        list2.add(Arrays.asList(
                createLabelResult(1L, "一致率标注", ENUM_TYPE, LABEL_TYPE, 1, null, null, "是", Arrays.asList("是", "否"))
        ));

        assertTrue("相同的背靠背一致率标注项应该相等", compareService.compareLabelValues(list1, list2, true));

        // 修改第二个列表中的一致率标注项值
        list2.get(0).get(0).setValue("否");

        assertFalse("不同的背靠背一致率标注项应该不相等", compareService.compareLabelValues(list1, list2, true));
    }

    /**
     * 测试异常处理
     */
    @Test
    public void testExceptionHandling() {
        // 创建一个会导致NullPointerException的标注项
        LabelResult badLabel = new LabelResult() {
            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public String getValue() {
                // 这里会抛出StringIndexOutOfBoundsException
                return "value".substring(10);
            }
        };

        List<List<LabelResult>> list1 = new ArrayList<>();
        list1.add(Collections.singletonList(badLabel));

        List<List<LabelResult>> list2 = new ArrayList<>();
        list2.add(Collections.singletonList(badLabel));

        // 应该捕获异常并返回false，而不是抛出异常
        assertFalse("异常情况应该被正确处理并返回false", compareService.compareLabelValues(list1, list2, false));
    }

    /**
     * 测试父指标值不同导致子指标不同的比较
     */
    @Test
    public void testParentValueChangeAffectsChildren() {
        // 创建第一个列表：父指标选择"值A"，对应子指标"子值A"
        List<List<LabelResult>> list1 = new ArrayList<>();
        List<LabelResult> group1 = new ArrayList<>();
        group1.add(createLabelResult(1L, "父标注", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")));
        group1.add(createLabelResult(2L, "子标注A", ENUM_TYPE, LABEL_TYPE, 2, 1L, "值A", "子值A", Arrays.asList("子值A", "子值B")));
        group1.add(createLabelResult(3L, "子标注B", ENUM_TYPE, LABEL_TYPE, 2, 1L, "值B", "子值C", Arrays.asList("子值C", "子值D")));
        list1.add(group1);

        // 创建第二个列表：父指标选择"值B"，对应子指标"子值C"
        List<List<LabelResult>> list2 = new ArrayList<>();
        List<LabelResult> group2 = new ArrayList<>();
        group2.add(createLabelResult(1L, "父标注", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值B", Arrays.asList("值A", "值B")));
        group2.add(createLabelResult(2L, "子标注A", ENUM_TYPE, LABEL_TYPE, 2, 1L, "值A", "子值A", Arrays.asList("子值A", "子值B")));
        group2.add(createLabelResult(3L, "子标注B", ENUM_TYPE, LABEL_TYPE, 2, 1L, "值B", "子值C", Arrays.asList("子值C", "子值D")));
        list2.add(group2);

        assertFalse("父指标值不同导致子指标不同的列表应该不相等", compareService.compareLabelValues(list1, list2, false));
    }

    private void assertFalse(String 父指标值不同导致子指标不同的列表应该不相等, boolean b) {
    }

    // 辅助方法：创建基本的标注结果列表
    private List<List<LabelResult>> createBasicLabelResultList() {
        List<List<LabelResult>> result = new ArrayList<>();

        List<LabelResult> group = new ArrayList<>();
        group.add(createLabelResult(1L, "标注1", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")));
        group.add(createLabelResult(2L, "标注2", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值C", Arrays.asList("值C", "值D")));

        result.add(group);
        return result;
    }

    // 辅助方法：创建带有级联关系的标注结果列表
    private List<List<LabelResult>> createCascadingLabelResultList() {
        List<List<LabelResult>> result = new ArrayList<>();

        List<LabelResult> group = new ArrayList<>();
        // 父指标
        group.add(createLabelResult(1L, "父标注", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")));
        // 子指标
        group.add(createLabelResult(3L, "子标注A", ENUM_TYPE, LABEL_TYPE, 2, 1L, "值A", "子值A", Arrays.asList("子值A", "子值B")));

        result.add(group);
        return result;
    }

    // 辅助方法：创建多层级联关系的标注结果列表
    private List<List<LabelResult>> createMultiLevelCascadingList() {
        List<List<LabelResult>> result = new ArrayList<>();

        List<LabelResult> group = new ArrayList<>();
        // 祖父指标
        group.add(createLabelResult(1L, "祖父标注", ENUM_TYPE, LABEL_TYPE, 2, null, null, "值A", Arrays.asList("值A", "值B")));
        // 父指标
        group.add(createLabelResult(3L, "父标注", ENUM_TYPE, LABEL_TYPE, 2, 1L, "值A", "子值A", Arrays.asList("子值A", "子值B")));
        // 子指标
        group.add(createLabelResult(5L, "子标注", ENUM_TYPE, LABEL_TYPE, 2, 3L, "子值A", "孙值A", Arrays.asList("孙值A", "孙值B")));

        result.add(group);
        return result;
    }

    // 辅助方法：创建标注结果对象
    private LabelResult createLabelResult(Long id, String name, Integer dataType, Integer itemType,
                                          Integer isCompare, Long parentId, String parentEnumValue,
                                          String value, List<String> enumList) {
        LabelResult result = new LabelResult();
        result.setId(id);
        result.setName(name);
        result.setDataType(dataType);
        result.setItemType(itemType);
        result.setIsCompare(isCompare);
        result.setParentId(parentId);
        result.setParentEnumValue(parentEnumValue);
        result.setValue(value);
        result.setEnumList(enumList);
        return result;
    }

}