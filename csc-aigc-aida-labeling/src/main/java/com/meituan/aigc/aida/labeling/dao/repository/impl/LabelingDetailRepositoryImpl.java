package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.common.enums.LabelingDetailStatus;
import com.meituan.aigc.aida.labeling.common.enums.sub.task.LabelingDetailSampleStatusEnum;
import com.meituan.aigc.aida.labeling.dao.mapper.LabelingDetailMapper;
import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.dao.model.LabelingDetailExample;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskUnLabelPO;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingDetailRepository;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingStatisticsPO;
import com.meituan.aigc.aida.labeling.param.LabelingDetailParam;
import com.meituan.aigc.aida.labeling.pojo.dto.DetailDataItemDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelDetailNumCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:05
 * @Version: 1.0
 */
@Repository
@Slf4j
public class LabelingDetailRepositoryImpl implements LabelingDetailRepository {

    @Resource
    private LabelingDetailMapper labelingDetailMapper;

    /**
     * 分页查询子任务下的标注详情
     *
     * @param stuTaskIds
     * @param status
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public List<LabelTaskDataExportVO> pageExportDataBySubTaskIdAndStatus(List<Long> stuTaskIds, Integer status, Integer pageNum, Integer pageSize) {
        if (Objects.isNull(pageNum) || pageNum < 1) {
            pageNum = 1;
        }
        if (Objects.isNull(pageSize) || pageSize < 1) {
            pageSize = 10;
        }
        Integer offset = (pageNum - 1) * pageSize;
        return labelingDetailMapper.pageExportDataBySubTaskIdAndStatus(stuTaskIds, status, offset, pageSize);
    }

    @Override
    public List<DetailDataItemDTO> pageDetailDataBySubTaskId(LabelingDetailParam param) {
        if (Objects.isNull(param) || Objects.isNull(param.getSubTaskId())) {
            return Collections.emptyList();
        }
        List<DetailDataItemDTO> detailDataItems = labelingDetailMapper.pageQueryBySubTaskId(param);
        if (CollectionUtils.isEmpty(detailDataItems)) {
            return Collections.emptyList();
        }
        return detailDataItems;
    }

    @Override
    public List<LabelingDetail> listByIds(List<Long> detailDataIds) {
        if (CollectionUtils.isEmpty(detailDataIds)) {
            return Collections.emptyList();
        }
        return labelingDetailMapper.listByIds(detailDataIds);
    }

    @Override
    public void batchUpdateLabelingDetail(List<LabelingDetail> updateLabelingDetails) {
        if (CollectionUtils.isEmpty(updateLabelingDetails)) {
            return;
        }
        labelingDetailMapper.batchUpdateLabelingDetail(updateLabelingDetails);
    }

    @Override
    public List<LabelingDetail> listBySubTaskIdRawDataIdAndStatus(List<Long> subTaskIds, Long rawDataId, Integer labelingDetailStatus) {
        return labelingDetailMapper.listBySubTaskIdRawDataIdAndStatus(subTaskIds, rawDataId, labelingDetailStatus);
    }

    @Override
    public List<LabelingSubTaskUnLabelPO> listBySubTaskId(List<Long> subTaskIds) {
        return labelingDetailMapper.listBySubTaskId(subTaskIds);
    }

    @Override
    public List<LabelingSubTaskUnLabelPO> listSessionCountBySubTaskId(List<Long> subTaskIds) {
        return labelingDetailMapper.listSessionCountBySubTaskId(subTaskIds);
    }

    @Override
    public void updateById(LabelingDetail labelingDetail) {
        labelingDetailMapper.updateByPrimaryKeySelective(labelingDetail);
    }

    @Override
    public void batchInsertDetail(List<LabelingDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        labelingDetailMapper.batchInsertDetail(details);
    }

    @Override
    public List<String> pageSessionBySubtaskAndSessionIdAndStatus(Long subTaskId, String sessionId, Integer labelStatus) {
        return labelingDetailMapper.pageSessionBySubtaskAndSessionIdAndStatus(subTaskId, sessionId, labelStatus);
    }

    @Override
    public List<LabelingDetail> listBySubTaskIdAndSessionIdList(Long subTaskId, List<String> sessionId, Integer status) {
        if (CollectionUtils.isEmpty(sessionId)) {
            return Collections.emptyList();
        }
        LabelingDetailExample example = new LabelingDetailExample();
        LabelingDetailExample.Criteria criteria = example.createCriteria();
        criteria.andSubTaskIdEqualTo(subTaskId).andSessionIdIn(sessionId);
        if (Objects.nonNull(status)) {
            if (status == 1) {
                criteria.andStatusEqualTo(new Byte(String.valueOf(LabelingDetailStatus.WAITING_LABELING.getCode())));
            } else {
                criteria.andStatusNotEqualTo(new Byte(String.valueOf(LabelingDetailStatus.WAITING_LABELING.getCode())));
            }
        }
        return labelingDetailMapper.selectByExample(example);
    }

    @Override
    public LabelDetailNumCountVO countNumBySubTask(Long subTaskId) {
        return labelingDetailMapper.countNumBySubTask(subTaskId);
    }

    @Override
    public Integer countConsistencyRateBySubTaskId(Long subTaskId) {
        return labelingDetailMapper.countConsistencyRateBySubTaskId(subTaskId);
    }

    @Override
    public List<LabelingDetail> listBySubTaskIdAndConsistency(Long subTaskId, Integer consistency) {
        LabelingDetailExample example = new LabelingDetailExample();
        LabelingDetailExample.Criteria criteria = example.createCriteria();
        criteria.andSubTaskIdEqualTo(subTaskId);
        if (Objects.nonNull(consistency)) {
            criteria.andComparItemsConsistentEqualTo((byte) consistency.intValue());
        }
        example.setOrderByClause("id asc");
        return labelingDetailMapper.selectByExample(example);
    }

    @Override
    public List<LabelingDetail> listBySubTaskIdListAndRawDataIdList(List<Long> subTaskIds, List<Long> rawDataIds) {
        LabelingDetailExample example = new LabelingDetailExample();
        LabelingDetailExample.Criteria criteria = example.createCriteria();
        if (CollectionUtils.isNotEmpty(subTaskIds)) {
            criteria.andSubTaskIdIn(subTaskIds);
        }
        if (CollectionUtils.isNotEmpty(rawDataIds)) {
            criteria.andRawDataIdIn(rawDataIds);
        }
        return labelingDetailMapper.selectByExample(example);
    }

    @Override
    public List<LabelingDetail> listAssignLabelingDetail(Long subTaskId) {
        LabelingDetailExample example = new LabelingDetailExample();
        LabelingDetailExample.Criteria criteria = example.createCriteria();
        criteria.andSubTaskIdEqualTo(subTaskId).andSampleStatusEqualTo(LabelingDetailSampleStatusEnum.NOT_SAMPLE.getCode());
        return labelingDetailMapper.selectByExample(example);
    }

    @Override
    public List<String> listSessionAssignLabelingDetail(Long subTaskId) {
        return labelingDetailMapper.listSessionAssignLabelingDetail(subTaskId);
    }

    @Override
    public List<LabelingDetail> listBysubTaskIdListAndRawDataIdList(List<Long> subTaskIds, List<Long> rawDataIds) {
        if (CollectionUtils.isEmpty(subTaskIds) || CollectionUtils.isEmpty(rawDataIds)) {
            return Collections.emptyList();
        }
        LabelingDetailExample example = new LabelingDetailExample();
        LabelingDetailExample.Criteria criteria = example.createCriteria();
        criteria.andSubTaskIdIn(subTaskIds).andRawDataIdIn(rawDataIds);
        return labelingDetailMapper.selectByExample(example);
    }

    @Override
    public void batchUpdateLabelerBySubTaskIdAndStatus(Long subTaskId, String labelerMis, String labelerName, Integer labelingStatus) {
        if (Objects.isNull(subTaskId) || StringUtils.isBlank(labelerMis) || StringUtils.isBlank(labelerName)) {
            return;
        }
        labelingDetailMapper.batchUpdateLabelerBySubTaskIdAndStatus(subTaskId, labelerMis, labelerName, labelingStatus);
    }

    @Override
    public LabelingDetail getById(Long labelingDataId) {
        if (Objects.isNull(labelingDataId)) {
            return null;
        }
        return labelingDetailMapper.selectByPrimaryKey(labelingDataId);
    }

    @Override
    public void updateViewStatusByIdList(List<Long> idList) {
        labelingDetailMapper.updateViewStatusByIdList(idList);
    }

    @Override
    public List<LabelingDetail> listBySubTaskId(Long subTaskId) {
        if (Objects.isNull(subTaskId)) {
            return new ArrayList<>();
        }
        LabelingDetailExample example = new LabelingDetailExample();
        LabelingDetailExample.Criteria criteria = example.createCriteria();
        criteria.andSubTaskIdEqualTo(subTaskId);
        return labelingDetailMapper.selectByExample(example);
    }

    @Override
    public int countSessionBySubTaskIdAndStatus(Long subTaskId, Integer status) {
        if (Objects.isNull(subTaskId)) {
            return 0;
        }
        return labelingDetailMapper.countSessionBySubTaskIdAndStatus(subTaskId, status);
    }

    @Override
    public int countQueryBySubTaskIdAndStatus(Long subTaskId, Integer status) {
        if (Objects.isNull(subTaskId)) {
            return 0;
        }
        return labelingDetailMapper.countQueryBySubTaskIdAndStatus(subTaskId, status);
    }

    @Override
    public List<LabelingDetail> listDataBySubTaskIdAndStatus(Long subTaskId, Integer status) {
        if (Objects.isNull(subTaskId) || Objects.isNull(status)) {
            return new ArrayList<>();
        }
        LabelingDetailExample example = new LabelingDetailExample();
        LabelingDetailExample.Criteria criteria = example.createCriteria();
        criteria.andSubTaskIdEqualTo(subTaskId).andStatusEqualTo(status.byteValue());
        return labelingDetailMapper.selectByExample(example);
    }
    @Override
    public List<PersonLabelingStatisticsPO> statisticsPersonLabelingData(Date startTime, Date endTime) {
        return labelingDetailMapper.statisticsPersonLabelingData(startTime, endTime);
    }

}
