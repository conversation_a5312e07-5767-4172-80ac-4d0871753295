package com.meituan.aigc.aida.labeling.remote.common.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 字段类型枚举
 *
 * <AUTHOR>
 * @date 2025/5/26
 */
@Getter
public enum FieldTypeEnum {
    /**
     * 文本类型
     */
    TEXT("text", 1, "文本", "能否方便告知一下是因为什么原因想要退款呢?"),

    /**
     * JSON类型
     */
    JSON("json", 2, "JSON", "{\"name\": \"张三\", \"age\": 30, \"city\": \"北京\"}"),

    /**
     * 处理后信号类型
     */
    PROCESSED_SIGNAL("processed_signal", 3, "处理后信号",
            "{\"key\": \"{现在时间:20250419 22:59:36}\\n{运单状态:已取消}\\n{运单配送类型:【外卖-快送】}\\n{骑手运力类型:畅跑}\"}"),

    /**
     * 标签类型
     */
    TAG("tag", 4, "标签", "[\"商家同意退款\", \"骑手重新配送\", \"用户已购买准时宝\"]"),

    /**
     * 标注项类型
     */
    LABELING_ITEM("labeling_item", 5, "标注项", "回复话术优秀"),

    /**
     * 质检项类型
     */
    QUALITY_CHECK_ITEM("quality_check_item", 6, "质检项", "正确"),

    /**
     * 枚举类型
     */
    ENUM("enum", 7, "枚举", "[\"已取消\", \"已完成\", \"已送达\"]");

    /**
     * 字段类型值
     */
    private final String value;

    /**
     * 枚举值
     */
    private final Integer code;

    /**
     * 字段类型描述
     */
    private final String description;

    /**
     * 数据格式示例
     */
    private final String example;

    FieldTypeEnum(String value, int code, String description, String example) {
        this.value = value;
        this.code = code;
        this.description = description;
        this.example = example;
    }

    /**
     * 根据value获取枚举
     *
     * @param value 字段类型值
     * @return 对应的枚举
     * @throws IllegalArgumentException 当value不匹配任何枚举时抛出
     */
    public static FieldTypeEnum fromValue(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Field type value cannot be null");
        }

        for (FieldTypeEnum type : FieldTypeEnum.values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown field type: " + value);
    }

    /**
     * 根据code获取枚举
     *
     * @param code 枚举值
     * @return 对应的枚举
     * @throws IllegalArgumentException 当code不匹配任何枚举时抛出
     */
    public static FieldTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FieldTypeEnum type : FieldTypeEnum.values()) {
            if (Objects.equals(type.code, code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断value是否为有效的字段类型
     *
     * @param value 字段类型值
     * @return true如果是有效的字段类型，否则false
     */
    public static boolean isValidFieldType(String value) {
        if (value == null) {
            return false;
        }

        for (FieldTypeEnum type : FieldTypeEnum.values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断code是否为有效的字段类型
     *
     * @param code 枚举值
     * @return true如果是有效的字段类型，否则false
     */
    public static boolean isValidFieldType(int code) {
        for (FieldTypeEnum type : FieldTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据code获取描述
     * @param code code值
     * @return 对应的描述
     */
    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FieldTypeEnum type : FieldTypeEnum.values()) {
            if (Objects.equals(type.code, code)) {
                return type.description;
            }
        }
        return null;
    }


    /**
     * 判断两个字段类型的优先级
     * 优先级：文本 > JSON  > 处理后信号类型
     */
    public static int compareTo(int code1, int code2) {
        if (code1 == code2) {
            return code1;
        }
        if (code1 == TEXT.getCode()) {
            return code1;
        }
        if (code2 == TEXT.getCode()) {
            return code2;
        }
        if (code1 == JSON.getCode()) {
            return code1;
        }
        if (code2 == JSON.getCode()) {
            return code2;
        }
        if (code1 == PROCESSED_SIGNAL.getCode()) {
            return code1;
        }
        return code2;
    }

}