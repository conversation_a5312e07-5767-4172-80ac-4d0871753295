package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.TaskStatistics;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DashboardTaskListQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DashboardTaskItemVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.TaskStatisticalVO;

import java.util.Date;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 14:54
 * @Version: 1.0
 */
public interface TaskStatisticsRepository {

    /**
     * 查询任务快照
     * @param dashboardTaskListQuery 参数
     * @return 结果
     */
    List<DashboardTaskItemVO> listTasks(DashboardTaskListQuery dashboardTaskListQuery);

    /**
     * 统计任务数量
     * @param query 查询条件
     * @return 结果
     */
    TaskStatisticalVO statisticalTaskData(DataStatisticalQuery query);

    /**
     * 批量插入
     * @param taskStatisticsList 数据
     */
    void batchInsert(List<TaskStatistics> taskStatisticsList);

    /**
     * 根据任务ID查询统计数据
     * @param taskId 任务ID
     * @return 结果
     */
    List<TaskStatistics> listByTaskId(Long taskId);

    /**
     * 根据任务ID和统计日期删除统计数据
     * @param taskId 任务ID
     * @param statDate 统计日期
     */
    void deleteByTaskIdAndStatDate(Long taskId, Date statDate);
}
