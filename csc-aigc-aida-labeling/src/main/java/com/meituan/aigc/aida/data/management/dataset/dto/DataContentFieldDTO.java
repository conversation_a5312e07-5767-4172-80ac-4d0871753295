package com.meituan.aigc.aida.data.management.dataset.dto;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-27 11:21
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataContentFieldDTO {
    /**
     * 字段名称
     */
    private String columnName;

    /**
     * es中存储的一级字段
     */
    private String esKey;

    /**
     * es中存储的二级列名
     */
    private String esColumnName;

    /**
     * 存储类型 text/flatten
     */
    private String storeType;

    /**
     * 数据类型
     * {@link FieldTypeEnum}
     */
    private Integer fieldType;

    /**
     * JSON-Key
     */
    private List<KeyNode> treeNodes;

    @Data
    public static class KeyNode {
        /**
         * 字段名称
         */
        private String key;

        /**
         * 子字段
         */
        private List<KeyNode> children;
    }
}
