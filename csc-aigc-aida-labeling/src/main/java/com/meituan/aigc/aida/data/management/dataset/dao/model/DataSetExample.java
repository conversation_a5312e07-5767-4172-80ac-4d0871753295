package com.meituan.aigc.aida.data.management.dataset.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DataSetExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DataSetExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNull() {
            addCriterion("creator_mis is null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNotNull() {
            addCriterion("creator_mis is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisEqualTo(String value) {
            addCriterion("creator_mis =", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotEqualTo(String value) {
            addCriterion("creator_mis <>", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThan(String value) {
            addCriterion("creator_mis >", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThanOrEqualTo(String value) {
            addCriterion("creator_mis >=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThan(String value) {
            addCriterion("creator_mis <", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThanOrEqualTo(String value) {
            addCriterion("creator_mis <=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLike(String value) {
            addCriterion("creator_mis like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotLike(String value) {
            addCriterion("creator_mis not like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIn(List<String> values) {
            addCriterion("creator_mis in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotIn(List<String> values) {
            addCriterion("creator_mis not in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisBetween(String value1, String value2) {
            addCriterion("creator_mis between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotBetween(String value1, String value2) {
            addCriterion("creator_mis not between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNull() {
            addCriterion("creator_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNotNull() {
            addCriterion("creator_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameEqualTo(String value) {
            addCriterion("creator_name =", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotEqualTo(String value) {
            addCriterion("creator_name <>", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThan(String value) {
            addCriterion("creator_name >", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("creator_name >=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThan(String value) {
            addCriterion("creator_name <", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanOrEqualTo(String value) {
            addCriterion("creator_name <=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLike(String value) {
            addCriterion("creator_name like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotLike(String value) {
            addCriterion("creator_name not like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIn(List<String> values) {
            addCriterion("creator_name in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotIn(List<String> values) {
            addCriterion("creator_name not in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameBetween(String value1, String value2) {
            addCriterion("creator_name between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotBetween(String value1, String value2) {
            addCriterion("creator_name not between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andFetchIdIsNull() {
            addCriterion("fetch_id is null");
            return (Criteria) this;
        }

        public Criteria andFetchIdIsNotNull() {
            addCriterion("fetch_id is not null");
            return (Criteria) this;
        }

        public Criteria andFetchIdEqualTo(Long value) {
            addCriterion("fetch_id =", value, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdNotEqualTo(Long value) {
            addCriterion("fetch_id <>", value, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdGreaterThan(Long value) {
            addCriterion("fetch_id >", value, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdGreaterThanOrEqualTo(Long value) {
            addCriterion("fetch_id >=", value, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdLessThan(Long value) {
            addCriterion("fetch_id <", value, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdLessThanOrEqualTo(Long value) {
            addCriterion("fetch_id <=", value, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdIn(List<Long> values) {
            addCriterion("fetch_id in", values, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdNotIn(List<Long> values) {
            addCriterion("fetch_id not in", values, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdBetween(Long value1, Long value2) {
            addCriterion("fetch_id between", value1, value2, "fetchId");
            return (Criteria) this;
        }

        public Criteria andFetchIdNotBetween(Long value1, Long value2) {
            addCriterion("fetch_id not between", value1, value2, "fetchId");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryIsNull() {
            addCriterion("usage_category is null");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryIsNotNull() {
            addCriterion("usage_category is not null");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryEqualTo(Integer value) {
            addCriterion("usage_category =", value, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryNotEqualTo(Integer value) {
            addCriterion("usage_category <>", value, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryGreaterThan(Integer value) {
            addCriterion("usage_category >", value, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("usage_category >=", value, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryLessThan(Integer value) {
            addCriterion("usage_category <", value, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("usage_category <=", value, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryIn(List<Integer> values) {
            addCriterion("usage_category in", values, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryNotIn(List<Integer> values) {
            addCriterion("usage_category not in", values, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryBetween(Integer value1, Integer value2) {
            addCriterion("usage_category between", value1, value2, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("usage_category not between", value1, value2, "usageCategory");
            return (Criteria) this;
        }

        public Criteria andUsageTypeIsNull() {
            addCriterion("usage_type is null");
            return (Criteria) this;
        }

        public Criteria andUsageTypeIsNotNull() {
            addCriterion("usage_type is not null");
            return (Criteria) this;
        }

        public Criteria andUsageTypeEqualTo(Integer value) {
            addCriterion("usage_type =", value, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeNotEqualTo(Integer value) {
            addCriterion("usage_type <>", value, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeGreaterThan(Integer value) {
            addCriterion("usage_type >", value, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("usage_type >=", value, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeLessThan(Integer value) {
            addCriterion("usage_type <", value, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeLessThanOrEqualTo(Integer value) {
            addCriterion("usage_type <=", value, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeIn(List<Integer> values) {
            addCriterion("usage_type in", values, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeNotIn(List<Integer> values) {
            addCriterion("usage_type not in", values, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeBetween(Integer value1, Integer value2) {
            addCriterion("usage_type between", value1, value2, "usageType");
            return (Criteria) this;
        }

        public Criteria andUsageTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("usage_type not between", value1, value2, "usageType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeIsNull() {
            addCriterion("training_type is null");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeIsNotNull() {
            addCriterion("training_type is not null");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeEqualTo(Integer value) {
            addCriterion("training_type =", value, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeNotEqualTo(Integer value) {
            addCriterion("training_type <>", value, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeGreaterThan(Integer value) {
            addCriterion("training_type >", value, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("training_type >=", value, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeLessThan(Integer value) {
            addCriterion("training_type <", value, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeLessThanOrEqualTo(Integer value) {
            addCriterion("training_type <=", value, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeIn(List<Integer> values) {
            addCriterion("training_type in", values, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeNotIn(List<Integer> values) {
            addCriterion("training_type not in", values, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeBetween(Integer value1, Integer value2) {
            addCriterion("training_type between", value1, value2, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTrainingTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("training_type not between", value1, value2, "trainingType");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionIsNull() {
            addCriterion("task_description is null");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionIsNotNull() {
            addCriterion("task_description is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionEqualTo(String value) {
            addCriterion("task_description =", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionNotEqualTo(String value) {
            addCriterion("task_description <>", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionGreaterThan(String value) {
            addCriterion("task_description >", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("task_description >=", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionLessThan(String value) {
            addCriterion("task_description <", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionLessThanOrEqualTo(String value) {
            addCriterion("task_description <=", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionLike(String value) {
            addCriterion("task_description like", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionNotLike(String value) {
            addCriterion("task_description not like", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionIn(List<String> values) {
            addCriterion("task_description in", values, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionNotIn(List<String> values) {
            addCriterion("task_description not in", values, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionBetween(String value1, String value2) {
            addCriterion("task_description between", value1, value2, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionNotBetween(String value1, String value2) {
            addCriterion("task_description not between", value1, value2, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andDataFormatIsNull() {
            addCriterion("data_format is null");
            return (Criteria) this;
        }

        public Criteria andDataFormatIsNotNull() {
            addCriterion("data_format is not null");
            return (Criteria) this;
        }

        public Criteria andDataFormatEqualTo(String value) {
            addCriterion("data_format =", value, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatNotEqualTo(String value) {
            addCriterion("data_format <>", value, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatGreaterThan(String value) {
            addCriterion("data_format >", value, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatGreaterThanOrEqualTo(String value) {
            addCriterion("data_format >=", value, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatLessThan(String value) {
            addCriterion("data_format <", value, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatLessThanOrEqualTo(String value) {
            addCriterion("data_format <=", value, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatLike(String value) {
            addCriterion("data_format like", value, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatNotLike(String value) {
            addCriterion("data_format not like", value, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatIn(List<String> values) {
            addCriterion("data_format in", values, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatNotIn(List<String> values) {
            addCriterion("data_format not in", values, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatBetween(String value1, String value2) {
            addCriterion("data_format between", value1, value2, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataFormatNotBetween(String value1, String value2) {
            addCriterion("data_format not between", value1, value2, "dataFormat");
            return (Criteria) this;
        }

        public Criteria andDataCountIsNull() {
            addCriterion("data_count is null");
            return (Criteria) this;
        }

        public Criteria andDataCountIsNotNull() {
            addCriterion("data_count is not null");
            return (Criteria) this;
        }

        public Criteria andDataCountEqualTo(Integer value) {
            addCriterion("data_count =", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountNotEqualTo(Integer value) {
            addCriterion("data_count <>", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountGreaterThan(Integer value) {
            addCriterion("data_count >", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_count >=", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountLessThan(Integer value) {
            addCriterion("data_count <", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountLessThanOrEqualTo(Integer value) {
            addCriterion("data_count <=", value, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountIn(List<Integer> values) {
            addCriterion("data_count in", values, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountNotIn(List<Integer> values) {
            addCriterion("data_count not in", values, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountBetween(Integer value1, Integer value2) {
            addCriterion("data_count between", value1, value2, "dataCount");
            return (Criteria) this;
        }

        public Criteria andDataCountNotBetween(Integer value1, Integer value2) {
            addCriterion("data_count not between", value1, value2, "dataCount");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdIsNull() {
            addCriterion("latest_version_id is null");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdIsNotNull() {
            addCriterion("latest_version_id is not null");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdEqualTo(Long value) {
            addCriterion("latest_version_id =", value, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdNotEqualTo(Long value) {
            addCriterion("latest_version_id <>", value, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdGreaterThan(Long value) {
            addCriterion("latest_version_id >", value, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("latest_version_id >=", value, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdLessThan(Long value) {
            addCriterion("latest_version_id <", value, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdLessThanOrEqualTo(Long value) {
            addCriterion("latest_version_id <=", value, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdIn(List<Long> values) {
            addCriterion("latest_version_id in", values, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdNotIn(List<Long> values) {
            addCriterion("latest_version_id not in", values, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdBetween(Long value1, Long value2) {
            addCriterion("latest_version_id between", value1, value2, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andLatestVersionIdNotBetween(Long value1, Long value2) {
            addCriterion("latest_version_id not between", value1, value2, "latestVersionId");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusIsNull() {
            addCriterion("data_set_status is null");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusIsNotNull() {
            addCriterion("data_set_status is not null");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusEqualTo(Byte value) {
            addCriterion("data_set_status =", value, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusNotEqualTo(Byte value) {
            addCriterion("data_set_status <>", value, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusGreaterThan(Byte value) {
            addCriterion("data_set_status >", value, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("data_set_status >=", value, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusLessThan(Byte value) {
            addCriterion("data_set_status <", value, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusLessThanOrEqualTo(Byte value) {
            addCriterion("data_set_status <=", value, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusIn(List<Byte> values) {
            addCriterion("data_set_status in", values, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusNotIn(List<Byte> values) {
            addCriterion("data_set_status not in", values, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusBetween(Byte value1, Byte value2) {
            addCriterion("data_set_status between", value1, value2, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andDataSetStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("data_set_status not between", value1, value2, "dataSetStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andDataSourceIsNull() {
            addCriterion("data_source is null");
            return (Criteria) this;
        }

        public Criteria andDataSourceIsNotNull() {
            addCriterion("data_source is not null");
            return (Criteria) this;
        }

        public Criteria andDataSourceEqualTo(Integer value) {
            addCriterion("data_source =", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotEqualTo(Integer value) {
            addCriterion("data_source <>", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceGreaterThan(Integer value) {
            addCriterion("data_source >", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_source >=", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLessThan(Integer value) {
            addCriterion("data_source <", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLessThanOrEqualTo(Integer value) {
            addCriterion("data_source <=", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceIn(List<Integer> values) {
            addCriterion("data_source in", values, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotIn(List<Integer> values) {
            addCriterion("data_source not in", values, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceBetween(Integer value1, Integer value2) {
            addCriterion("data_source between", value1, value2, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("data_source not between", value1, value2, "dataSource");
            return (Criteria) this;
        }

        public Criteria andFilePathIsNull() {
            addCriterion("file_path is null");
            return (Criteria) this;
        }

        public Criteria andFilePathIsNotNull() {
            addCriterion("file_path is not null");
            return (Criteria) this;
        }

        public Criteria andFilePathEqualTo(String value) {
            addCriterion("file_path =", value, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathNotEqualTo(String value) {
            addCriterion("file_path <>", value, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathGreaterThan(String value) {
            addCriterion("file_path >", value, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathGreaterThanOrEqualTo(String value) {
            addCriterion("file_path >=", value, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathLessThan(String value) {
            addCriterion("file_path <", value, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathLessThanOrEqualTo(String value) {
            addCriterion("file_path <=", value, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathLike(String value) {
            addCriterion("file_path like", value, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathNotLike(String value) {
            addCriterion("file_path not like", value, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathIn(List<String> values) {
            addCriterion("file_path in", values, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathNotIn(List<String> values) {
            addCriterion("file_path not in", values, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathBetween(String value1, String value2) {
            addCriterion("file_path between", value1, value2, "filePath");
            return (Criteria) this;
        }

        public Criteria andFilePathNotBetween(String value1, String value2) {
            addCriterion("file_path not between", value1, value2, "filePath");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNull() {
            addCriterion("file_name is null");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNotNull() {
            addCriterion("file_name is not null");
            return (Criteria) this;
        }

        public Criteria andFileNameEqualTo(String value) {
            addCriterion("file_name =", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotEqualTo(String value) {
            addCriterion("file_name <>", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThan(String value) {
            addCriterion("file_name >", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThanOrEqualTo(String value) {
            addCriterion("file_name >=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThan(String value) {
            addCriterion("file_name <", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThanOrEqualTo(String value) {
            addCriterion("file_name <=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLike(String value) {
            addCriterion("file_name like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotLike(String value) {
            addCriterion("file_name not like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameIn(List<String> values) {
            addCriterion("file_name in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotIn(List<String> values) {
            addCriterion("file_name not in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameBetween(String value1, String value2) {
            addCriterion("file_name between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotBetween(String value1, String value2) {
            addCriterion("file_name not between", value1, value2, "fileName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}