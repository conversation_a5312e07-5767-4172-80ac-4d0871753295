package com.meituan.aigc.aida.data.management.fetch.dao.model;

import lombok.Data;

import java.util.Date;

@Data
public class DataFetchTask {
    /**
     * ID
     */
    private Long id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 任务描述
     */
    private String description;
    /**
     * 数据类型：1-chat(人工侧对话)、2-log(大模型应用日志)
     */
    private Integer dataType;
    /**
     * 筛选条件，JSON格式
     */
    private String filterConditions;
    /**
     * 输出字段映射，JSON格式
     */
    private String outputFields;
    /**
     * 任务状态：1-processing(进行中)、2-completed(已完成)、3-failed(失败)、4-canceled(已取消)
     * {@link  com.meituan.aigc.aida.data.management.fetch.common.enums.DataFetchTaskStatus}
     */
    private Integer taskStatus;
    /**
     * 数据量
     */
    private Integer dataCount;
    /**
     * 创建人MIS
     */
    private String createMis;
    /**
     * 创建人名称
     */
    private String createName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除：0-否，1-是
     */
    private Boolean isDelete;


}