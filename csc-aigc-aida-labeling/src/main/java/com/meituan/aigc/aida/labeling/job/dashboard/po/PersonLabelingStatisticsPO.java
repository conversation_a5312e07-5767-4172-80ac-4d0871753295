package com.meituan.aigc.aida.labeling.job.dashboard.po;

import lombok.Data;

/**
 * @Author: guowen<PERSON>
 * @Create: 2025/5/21 14:38
 * @Version: 1.0
 */
@Data
public class PersonLabelingStatisticsPO {

    /**
     * 标注员mis
     */
    private String labelerMis;
    /**
     * 标注员名称
     */
    private String labelerName;
    /**
     * 标注数量
     */
    private Integer labelingCount;
    /**
     * 被质检数量
     */
    private Integer qcInspectedCount;
    /**
     * 被质检正确数量
     */
    private Integer qcInspectedCorrectCount;
    /**
     * 业务类型
     */
    private Long bizType;
    /**
     * 数据类型
     */
    private Integer dataType;
    /**
     * 任务类型
     */
    private Integer taskType;


}
