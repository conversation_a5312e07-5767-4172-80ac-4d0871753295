package com.meituan.aigc.aida.labeling.util;

import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-03-05 14:31
 * @description
 */
@Slf4j
public class DateUtil {

    public static final String DATE_PATTERN = CommonConstants.DatePattern.DATE;

    public static final String DATE_PATTERN_YYYY_MM_DD = CommonConstants.DatePattern.DATE_TIME;

    /**
     * 将dateStr类型转换成为Date类型
     */
    public static Date parseToDate(String dateStr) {
        return parseToDate(dateStr, DATE_PATTERN);
    }

    /**
     * 将指定dateStr类型转换成为Date类型
     *
     * @param dateStr
     * @param formatPattern
     * @return java.util.Date
     * <AUTHOR>
     * @date 2024/6/4
     **/
    public static Date parseToDate(String dateStr, String formatPattern) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateFormat dateFormat = new SimpleDateFormat(formatPattern);
        try {
            return dateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.error("DateUtil:parseToDate error, dateStr: {}, formatPattern: {}", dateStr, formatPattern, e);
            return null;
        }
    }

    /**
     * 将Date转换成为'yyyy-MM-dd hh:mm:ss'类型的字符串
     *
     * @return
     */
    public static String formatDate(Date date, String formatPattern) {
        if (null == date) {
            return null;
        }
        if (StringUtils.isEmpty(formatPattern)) {
            return formatDate(date);
        }
        DateFormat dateFormat = new SimpleDateFormat(formatPattern);
        return dateFormat.format(date);
    }

    /**
     * 将Date转换成为'yyyy-MM-dd'类型的字符串
     *
     * @return
     */
    public static String formatDate(Date date) {
        if (null == date) {
            return null;
        }
        DateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);
        return dateFormat.format(date);
    }

    /**
     * 获取time所包含的小时、分钟、秒
     * 04:00 --> [4,0,0], 21:35 --> [21,35,0], 05:23:24 --> [5,23,24]
     *
     * @param time 必须是 HH:mm:ss or HH:mm or HH
     * @return
     */
    public static int[] parseIntArrayFromTime(String time) throws IllegalArgumentException {
        String[] sArray = time.split(":");
        if (sArray.length < 2) {
            throw new IllegalArgumentException("param is illegal");
        }
        int[] result = new int[3];
        for (int i = 0; i < sArray.length; i++) {
            result[i] = Integer.parseInt(sArray[i]);
        }
        return result;
    }

    /**
     * 给data日期加上分钟，minute为负数则表示减去minute分钟
     *
     * @param date
     * @param minute
     * @return
     */
    public static Date addMinute(Date date, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }

    /**
     * 获取指定日期0点的Date对象。
     *
     * @param date 需要获取0点时间的Date对象
     * @return 0点的Date对象
     */
    public static Date getStartOfDay(Date date, Integer day) {
        if (date == null) {
            log.error("DateUtil:getStartOfDay date is null");
            return null;
        }
        if (day == null) {
            day = 0;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, day);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取到第二天0点的秒级时间差
     *
     * @param timestamp
     * @return
     */
    public static long getSecondsUntilNextDay(long timestamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp);
        // 第二天0点
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long nextDayStart = calendar.getTimeInMillis();
        // 秒级时间差
        return (nextDayStart - timestamp) / 1000;
    }

    /**
     * 获取两个日期的秒级差
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 秒差
     */
    public static Double diffDateSeconds(Date startTime, Date endTime) {
        long diffInMilliseconds = endTime.getTime() - startTime.getTime();
        return diffInMilliseconds / 1000.0;
    }

    /**
     * 给定某日期，计算该日期向前推移x天的日期
     *
     * @param currentDate 给定日期
     * @param x           向前推移x天
     * @return 得到计算日期
     */
    public static Date calculateBeforeDate(Date currentDate, int x) {
        if (Objects.isNull(currentDate)) {
            return null;
        }
        // 将Date转换为LocalDateTime(保留时分秒)
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(), ZoneId.systemDefault());
        // 只调整日期部分，保留时分秒
        LocalDateTime beforeDateTime = currentDateTime.minusDays(x);
        // 转换回Date类型
        return Date.from(beforeDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 校验日期合法性
     * @param dateTime 日期
     * @param format 格式
     * @return 结果
     */
    public static boolean isValidDateTime(String dateTime, String format) {
        if (StringUtils.isBlank(dateTime) || StringUtils.isBlank(format)) {
            return false;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            LocalDateTime.parse(dateTime, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 获取前一周的日期并返回Date类型
     */
    public static Date getPreviousWeekDate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        // 转换回Date
        return calculateBeforeDate(date, 7);
    }
    /**
     * 计算两个日期之间的天数
     *
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 两个日期之间的天数差(绝对值)
     */
    public static int daysBetween(Date date1, Date date2) {
        if (Objects.isNull(date1) || Objects.isNull(date2)) {
            return 0;
        }
        // 使用long避免整数溢出问题
        long diff = Math.abs(date1.getTime() - date2.getTime());
        return (int) Math.ceil((double) diff / (24 * 60 * 60 * 1000));
    }

    /**
     * 获取昨天开始时间
     * @return LocalDateTime
     */
    public static LocalDateTime getYesterdayStartLocalDateTime() {
        return LocalDate.now().minusDays(1).atStartOfDay();
    }

    /**
     * 获取昨天结束时间
     * @return LocalDateTime
     */
    public static LocalDateTime getYesterdayEndLocalDateTime() {
        return LocalDate.now().minusDays(1).atTime(23, 59, 59, 999999999);
    }

    /**
     * 获取昨天开始时间
     * @return date
     */
    public static Date getYesterdayStartDate() {
        LocalDateTime start = getYesterdayStartLocalDateTime();
        return Date.from(start.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取昨天结束时间
     * @return date
     */
    public static Date getYesterdayEndDate() {
        LocalDateTime end = getYesterdayEndLocalDateTime();
        return Date.from(end.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取给定日期的开始时间（当天00:00:00.000）
     * 使用Java 8+ API
     *
     * @param date 给定日期
     * @return 当天开始时间
     */
    public static Date getDayStartTime(Date date) {
        if (Objects.isNull(date)) {
            throw new AidaTrainingCheckException("日期不能为空");
        }
        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        LocalDateTime startOfDay = localDateTime.toLocalDate().atStartOfDay();

        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取给定日期的结束时间（当天23:59:59.999）
     *
     * @param date 给定日期
     * @return 当天结束时间
     */
    public static Date getDayEndTime(Date date) {
        if (Objects.isNull(date)) {
            throw new AidaTrainingCheckException("日期不能为空");
        }

        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        LocalDateTime endOfDay = localDateTime.toLocalDate().atTime(23, 59, 59, 999_999_999);

        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获得起止时间的日期列表(前包后包)
     * @return List<String> mm/dd格式
     */
    public static List<String> getDateRangeList(String startDateStr, String endDateStr) {
        List<String> dateList = new ArrayList<>();
        // Corresponds to "yyyy-MM-dd"
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("MM/dd");

        try {
            LocalDate startDate = LocalDate.parse(startDateStr, formatter);
            LocalDate endDate = LocalDate.parse(endDateStr, formatter);

            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                dateList.add(currentDate.format(outputFormatter));
                currentDate = currentDate.plusDays(1);
            }
        } catch (DateTimeParseException e) {
            log.error("获取俩个日期之间的日期异常：", e);
            return new ArrayList<>();
        }
        return dateList;
    }

    // 使用LocalDateTime
    public static LocalDateTime getCurrentTimePlus30Seconds() {
        return LocalDateTime.now().plusSeconds(30);
    }

    // 返回Date类型
    public static Date getCurrentTimePlus30SecondsAsDate() {
        return Date.from(LocalDateTime.now().plusSeconds(30)
                .atZone(java.time.ZoneId.systemDefault()).toInstant());
    }

}
