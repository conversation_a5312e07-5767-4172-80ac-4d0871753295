package com.meituan.aigc.aida.labeling.controller;

import com.meituan.aigc.aida.labeling.pojo.dto.ExternalMetricDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.meituan.aigc.aida.labeling.service.ExternalMetricService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-21 10:39
 * @description 外部指标
 */
@RestController
@RequestMapping("/api/v1/external-labeling-templates")
@Slf4j
public class ExternalMetricController {

    @Autowired
    private ExternalMetricService externalMetricService;

    /**
     * 获取外部指标列表
     *
     * @return 指标列表
     */
    @GetMapping("/list")
    public Result<List<ExternalMetricDTO>> listExternalMetric() {
        return Result.ok(externalMetricService.listExternalMetric());
    }

    /**
     * 获取外部指标详情
     *
     * @param metricId     指标ID
     * @param currentIndex 当前索引
     * @return 指标详情
     */
    @GetMapping("/detail")
    public Result<List<LabelResult>> getExternalMetricDetail(@RequestParam("metricId") Long metricId, @RequestParam("currentIndex") Long currentIndex) {
        return Result.ok(externalMetricService.getExternalMetricDetail(metricId, currentIndex));
    }
}
