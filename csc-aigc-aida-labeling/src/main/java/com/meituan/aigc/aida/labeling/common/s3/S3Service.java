package com.meituan.aigc.aida.labeling.common.s3;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.dianping.cat.Cat;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class S3Service {

    @Autowired
    private AmazonS3 amazonS3;

    @Autowired
    private LionConfig lionConfig;

    private static final String PATH_FORMAT = CommonConstants.PathFormat.COMMON_PATH;

    public String upload(MultipartFile multipartFile, String path) {
        if (StringUtils.isBlank(path)) {
            path = String.format(PATH_FORMAT, UUID.randomUUID().toString().replace("-", ""),
                    new SimpleDateFormat("yyyyMMdd").format(new Date()), multipartFile.getOriginalFilename());
        }
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(multipartFile.getSize());
            amazonS3.putObject(lionConfig.getAigcAidaTrainingBucket(), path, multipartFile.getInputStream(),
                    objectMetadata);
        } catch (IOException e) {
            log.error("multipartFile上传S3 文件异常！", e);
            Cat.logErrorWithCategory("s3 upload file error", multipartFile.getName(), e);
            return null;
        }

        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(
                lionConfig.getAigcAidaTrainingBucket(), path);
        generatePresignedUrlRequest.setExpiration(DateUtils.addYears(new Date(), 1));
        return amazonS3.generatePresignedUrl(generatePresignedUrlRequest).toExternalForm();
    }

    // llm-auto-x-begin [poa= 7:2:1] 2023-05-29 18:37:40
    // f86b6ce4e3437598858def53d3169564
    public String upload(ByteArrayInputStream inputStream, String path) {
        try {
            amazonS3.putObject(lionConfig.getAigcAidaTrainingBucket(), path, inputStream, new ObjectMetadata());
        } catch (Exception e) {
            log.error("inputStream上传S3 文件异常！", e);
            Cat.logErrorWithCategory("s3 upload file error", e);
            return null;
        }

        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(
                lionConfig.getAigcAidaTrainingBucket(), path);
        generatePresignedUrlRequest.setExpiration(DateUtils.addYears(new Date(), 1));
        return amazonS3.generatePresignedUrl(generatePresignedUrlRequest).toExternalForm();
    }
    // llm-auto-x-end f86b6ce4e3437598858def53d3169564

    /**
     * 从S3下载文件
     * @param fileUrl 文件URL
     * @return 文件字节数组
     */
    public byte[] download(String fileUrl) {
        try {
            // 解析URL
            URL url = new URL(fileUrl);
            String path = url.getPath();
            
            // 使用UTF-8解码URL路径
            path = java.net.URLDecoder.decode(path, "UTF-8");
            
            // 移除开头的斜杠
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            
            // 获取第一个斜杠之前的内容作为桶名
            int firstSlashIndex = path.indexOf('/');
            if (firstSlashIndex <= 0) {
                log.error("无效的S3 URL格式: {}", fileUrl);
                return null;
            }
            
            String bucketName = path.substring(0, firstSlashIndex);
            
            // 获取文件键名（桶名后面的所有部分，不包括查询参数）
            String key = path.substring(firstSlashIndex + 1);
            
            // 从S3下载对象
            S3Object s3Object = amazonS3.getObject(bucketName, key);
            try (S3ObjectInputStream inputStream = s3Object.getObjectContent()) {
                return IOUtils.toByteArray(inputStream);
            }
        } catch (Exception e) {
            log.error("从S3下载文件失败, url: {}", fileUrl, e);
            return null;
        }
    }
}
