package com.meituan.aigc.aida.labeling.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * 质检任务表实体类
 */
@Data
public class LabelingQualityCheckTask {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 质检任务名称
     */
    private String name;
    
    /**
     * 标注任务ID
     */
    private Long taskId;
    
    /**
     * 质检总数量
     */
    private Integer sampleSize;
    
    /**
     * 质检项配置
     */
    private String qualityCheckConfig;
    
    /**
     * 状态(1:创建中 2:质检中 3:已质检 4:失败)
     * {@link com.meituan.aigc.aida.labeling.common.enums.QualityCheckTaskStatus}
     */
    private Integer status;
    
    /**
     * 租户id
     */
    private String tenantId;
    
    /**
     * 质检人Mis
     */
    private String qualityCheckMis;

    /**
     * 质检人Mis名称
     */
    private String qualityCheckName;
    
    /**
     * 创建人MIS
     */
    private String createMis;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 抽样数量
     */
    private Integer rawDataSampleSize;

    /**
     * 分配方式 1:session维度分配 2:query维度分配'
     */
    private Integer assignType;
} 