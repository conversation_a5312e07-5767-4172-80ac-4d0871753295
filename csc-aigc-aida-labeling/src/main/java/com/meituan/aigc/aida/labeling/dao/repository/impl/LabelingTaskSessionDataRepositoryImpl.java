package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.meituan.aigc.aida.labeling.dao.mapper.LabelingTaskSessionDataMapper;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskSessionData;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskSessionDataExample;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskSessionDataRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class LabelingTaskSessionDataRepositoryImpl implements LabelingTaskSessionDataRepository {

    @Autowired
    private LabelingTaskSessionDataMapper labelingTaskSessionDataMapper;

    @Override
    public List<LabelingTaskSessionData> listByTaskIdAndSessionIdList(Long taskId, List<String> sessionIdList) {
        if (taskId == null || CollectionUtils.isEmpty(sessionIdList)) {
            return new ArrayList<>();
        }
        LabelingTaskSessionDataExample example = new LabelingTaskSessionDataExample();
        example.createCriteria().andTaskIdEqualTo(taskId).andSessionIdIn(sessionIdList);
        return labelingTaskSessionDataMapper.selectByExample(example);
    }

    @Override
    public List<LabelingTaskSessionData> listByTaskIdAndSessionIdListWithBlobs(Long taskId, List<String> sessionIdList) {
        if (taskId == null || CollectionUtils.isEmpty(sessionIdList)) {
            return new ArrayList<>();
        }
        LabelingTaskSessionDataExample example = new LabelingTaskSessionDataExample();
        example.createCriteria().andTaskIdEqualTo(taskId).andSessionIdIn(sessionIdList);
        return labelingTaskSessionDataMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public void insert(LabelingTaskSessionData labelingTaskSessionData) {
        labelingTaskSessionDataMapper.insert(labelingTaskSessionData);
    }

    @Override
    public void updateById(LabelingTaskSessionData labelingTaskSessionData) {
        labelingTaskSessionDataMapper.updateByPrimaryKeySelective(labelingTaskSessionData);
    }

}
