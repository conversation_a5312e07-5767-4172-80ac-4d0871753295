package com.meituan.aigc.aida.data.management.dataset.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.aigc.aida.data.management.dataset.dto.DataContentFieldDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFieldPathInfo;
import com.meituan.aigc.aida.data.management.dataset.dto.FieldMappingDTO;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetQueryCondition;
import com.meituan.aigc.aida.data.management.dataset.param.DatasetHeadConfig;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingException;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.aigc.aida.data.management.dataset.common.constants.DatasetCommonConstants.*;

/**
 * 数据集字段工具类
 * ES字段与数据集字段映射转换
 *
 * <AUTHOR>
 * @date 2025/6/3
 */
@Slf4j
public class DatasetFieldUtils {
    /**
     * 数据集字段转换为ES字段映射
     *
     * @param headConfig 数据集字段配置
     * @return 数据集与ES字段映射
     */
    public static Map<String, FieldMappingDTO> convertHeadConfig2FieldMapping(String headConfig) {
        Map<String, FieldMappingDTO> fieldTypeMap = new HashMap<>();

        try {
            DatasetHeadConfig datasetHeadConfig = parseHeadConfig(headConfig);
            if (datasetHeadConfig == null || CollectionUtils.isEmpty(datasetHeadConfig.getHeadList())) {
                return fieldTypeMap;
            }

            for (DataContentFieldDTO fieldDTO : datasetHeadConfig.getHeadList()) {
                if (isValidField(fieldDTO)) {
                    FieldMappingDTO fieldMappingDTO = buildFieldMapping(fieldDTO);
                    fieldTypeMap.put(fieldDTO.getColumnName(), fieldMappingDTO);
                }
            }

            return fieldTypeMap;
        } catch (Exception e) {
            log.error("数据集字段转换失败, headConfig: {}", headConfig, e);
            throw new AidaTrainingException("数据集字段转换失败");
        }
    }

    /**
     * 获取ES实际名称与字段名称映射
     *
     * @param headConfig 数据集字段配置
     * @return ES实际名称与字段名称映射
     */
    public static Map<String, DataContentFieldDTO> getEsFieldMapping(String headConfig) {
        if (StringUtils.isEmpty(headConfig)) {
            return Collections.emptyMap();
        }

        Map<String, DataContentFieldDTO> esFieldMapping = new HashMap<>();
        try {
            DatasetHeadConfig datasetHeadConfig = parseHeadConfig(headConfig);
            if (datasetHeadConfig == null || CollectionUtils.isEmpty(datasetHeadConfig.getHeadList())) {
                return esFieldMapping;
            }

            for (DataContentFieldDTO fieldDTO : datasetHeadConfig.getHeadList()) {
                if (isValidField(fieldDTO)) {
                    esFieldMapping.put(fieldDTO.getEsKey() + POINT + fieldDTO.getEsColumnName(), fieldDTO);
                }
            }
        } catch (Exception e) {
            log.error("获取ES字段与列名映射失败, headConfig: {}", headConfig, e);
            throw new RuntimeException("获取ES字段与列名映射失败");
        }
        return esFieldMapping;
    }

    /**
     * 从TreeNodes中提取一级JSON路径的key
     * 只有当节点没有子节点或子节点为空时，才认为是一级路径
     *
     * @param treeNodes 树形节点列表
     * @return 一级路径的key列表
     */
    public static List<String> extractFirstLevelKeys(List<DataContentFieldDTO.KeyNode> treeNodes) {
        List<String> firstLevelKeys = new ArrayList<>();

        if (CollectionUtils.isEmpty(treeNodes)) {
            return firstLevelKeys;
        }

        for (DataContentFieldDTO.KeyNode node : treeNodes) {
            if (isFirstLevelNode(node)) {
                firstLevelKeys.add(node.getKey());
                log.info("发现一级JSON路径key: {}", node.getKey());
            } else if (node != null) {
                log.info("跳过多级嵌套节点: {}, 子节点数量: {}",
                        node.getKey(),
                        node.getChildren() != null ? node.getChildren().size() : 0);
            }
        }

        return firstLevelKeys;
    }

    /**
     * 构建字段路径信息
     *
     * @param columnPathParts 列路径部分
     * @param fieldTypeMap    字段类型映射
     * @param commonFields    公共字段集合
     * @return 字段路径信息，如果路径无效则返回null
     */
    public static DatasetFieldPathInfo buildFieldPathInfo(List<String> columnPathParts, Map<String, FieldMappingDTO> fieldTypeMap, Set<String> commonFields) {

        if (CollectionUtils.isEmpty(columnPathParts)) {
            log.warn("列路径部分为空，跳过处理");
            return null;
        }

        DatasetFieldPathInfo pathInfo = new DatasetFieldPathInfo();

        // 处理字段映射
        FieldMappingDTO fieldMappingDTO = null;
        List<String> processedPathParts = new ArrayList<>(columnPathParts);

        // 如果存在字段映射，则替换第一个元素为映射的值
        if (CollectionUtils.isNotEmpty(processedPathParts) && fieldTypeMap.containsKey(processedPathParts.get(0))) {
            fieldMappingDTO = fieldTypeMap.get(processedPathParts.get(0));
            // 用ES映射字段替换原始字段名
            processedPathParts.set(0, fieldMappingDTO.getEsField());
        }

        // 获取字段名（路径的最后一部分）
        String fieldName = processedPathParts.get(processedPathParts.size() - 1);

        // 构建完整路径
        String fullPath = processedPathParts.stream()
                .filter(part -> part != null && !part.trim().isEmpty())
                .collect(Collectors.joining(POINT));

        // 构建各种类型的路径
        String flattenPath = ES_FLATTEN_PREFIX + POINT + fullPath;
        String textPath = ES_TEXT_PREFIX + POINT + fullPath + ES_KEYWORD_SUFFIX;
        String commonDataKeywordPath = ES_COMMON_DATA_PREFIX + POINT + fullPath + ES_KEYWORD_SUFFIX;

        // 判断是否为公共字段
        boolean isCommonField = commonFields != null &&
                !columnPathParts.isEmpty() &&
                commonFields.contains(columnPathParts.get(0));

        // 设置返回信息
        pathInfo.setFieldName(fieldName);
        pathInfo.setFullPath(fullPath);
        pathInfo.setFlattenPath(flattenPath);
        pathInfo.setTextPath(textPath);
        pathInfo.setCommonDataKeywordPath(commonDataKeywordPath);
        pathInfo.setFieldMappingDTO(fieldMappingDTO);
        pathInfo.setIsCommonField(isCommonField);
        pathInfo.setOriginFieldName(String.join(POINT, columnPathParts));
        if (Objects.nonNull(fieldMappingDTO) && CollectionUtils.isNotEmpty(fieldMappingDTO.getFirstLevelJsonKey())) {
            for (String jsonKey : fieldMappingDTO.getFirstLevelJsonKey()) {
                pathInfo.getFirstLevelJsonPath().add(flattenPath + POINT + jsonKey);
            }
        }

        return pathInfo;
    }

    /**
     * 解析数据集字段配置
     *
     * @param headConfig 字段配置JSON字符串
     * @return 解析后的配置对象，解析失败时返回null
     */
    private static DatasetHeadConfig parseHeadConfig(String headConfig) {
        if (StringUtils.isBlank(headConfig)) {
            return null;
        }

        return JSON.parseObject(headConfig, new TypeReference<DatasetHeadConfig>() {
        });
    }

    /**
     * 验证字段是否有效
     *
     * @param fieldDTO 字段DTO
     * @return 是否有效
     */
    private static boolean isValidField(DataContentFieldDTO fieldDTO) {
        return fieldDTO != null && StringUtils.isNotBlank(fieldDTO.getColumnName());
    }

    /**
     * 构建字段映射DTO
     *
     * @param fieldDTO 数据内容字段DTO
     * @return 字段映射DTO
     */
    private static FieldMappingDTO buildFieldMapping(DataContentFieldDTO fieldDTO) {
        FieldMappingDTO fieldMappingDTO = new FieldMappingDTO();

        // 设置字段类型
        FieldTypeEnum fieldType = determineFieldType(fieldDTO);
        fieldMappingDTO.setFieldType(fieldType);

        // 设置ES字段
        fieldMappingDTO.setEsField(fieldDTO.getEsColumnName());

        // 处理特殊类型字段的JSON路径
        handleJsonFieldPaths(fieldDTO, fieldType, fieldMappingDTO);

        return fieldMappingDTO;
    }

    /**
     * 确定字段类型
     *
     * @param fieldDTO 字段DTO
     * @return 字段类型枚举
     */
    private static FieldTypeEnum determineFieldType(DataContentFieldDTO fieldDTO) {
        if (Objects.nonNull(fieldDTO.getFieldType())) {
            return FieldTypeEnum.fromCode(fieldDTO.getFieldType());
        }
        return FieldTypeEnum.TEXT;
    }

    /**
     * 处理JSON和PROCESSED_SIGNAL类型字段的路径信息
     *
     * @param fieldDTO        字段DTO
     * @param fieldType       字段类型
     * @param fieldMappingDTO 字段映射DTO
     */
    private static void handleJsonFieldPaths(DataContentFieldDTO fieldDTO, FieldTypeEnum fieldType, FieldMappingDTO fieldMappingDTO) {
        if (!isJsonTypeField(fieldType) || CollectionUtils.isEmpty(fieldDTO.getTreeNodes())) {
            return;
        }

        List<String> firstLevelKeys = extractFirstLevelKeys(fieldDTO.getTreeNodes());
        if (CollectionUtils.isNotEmpty(firstLevelKeys)) {
            fieldMappingDTO.setFirstLevelJsonKey(firstLevelKeys);
            log.debug("字段 {} 的一级JSON路径: {}", fieldDTO.getColumnName(), firstLevelKeys);
        }
    }

    /**
     * 判断是否为JSON类型字段
     *
     * @param fieldType 字段类型
     * @return 是否为JSON类型字段
     */
    private static boolean isJsonTypeField(FieldTypeEnum fieldType) {
        return fieldType == FieldTypeEnum.JSON || fieldType == FieldTypeEnum.PROCESSED_SIGNAL;
    }


    /**
     * 判断是否为一级节点
     *
     * @param node 树形节点
     * @return 是否为一级节点
     */
    private static boolean isFirstLevelNode(DataContentFieldDTO.KeyNode node) {
        return node != null
                && StringUtils.isNotBlank(node.getKey())
                && CollectionUtils.isEmpty(node.getChildren());
    }

    /**
     * 将ES索引数据转换为行数据
     */
    public static Map<String, Object> convertEsIndexToRowData(DatasetEsIndex esIndex, DatasetHeadConfig headConfig) {
        Map<String, Object> rowData = new LinkedHashMap<>();
        List<DataContentFieldDTO> headList = headConfig.getHeadList();

        Map<String, String> columnNameMap = headList.stream()
                .collect(Collectors.toMap(
                        field -> field.getEsKey() + POINT + field.getEsColumnName(),
                        DataContentFieldDTO::getColumnName,
                        (v1, v2) -> v1 // 处理可能的重复键
                ));

        // 处理文本数据
        if (esIndex.getTextData() != null) {
            esIndex.getTextData().forEach((key, value) -> {
                if (value != null) {
                    String columnName = columnNameMap.getOrDefault(ES_TEXT_PREFIX + POINT + key, key);
                    rowData.put(columnName, value);
                }
            });
        }

        // 处理扁平化数据
        if (esIndex.getFlattenedData() != null) {
            esIndex.getFlattenedData().forEach((key, value) -> {
                if (value != null) {
                    String columnName = columnNameMap.getOrDefault(ES_FLATTEN_PREFIX + POINT + key, key);
                    rowData.put(columnName, value);
                }
            });
        }

        // 处理通用数据
        if (esIndex.getCommonData() != null) {
            esIndex.getCommonData().forEach((key, value) -> {
                if (value != null) {
                    rowData.put(key, value);
                }
            });
        }

        return rowData;
    }

    /**
     * 从Lion获取通用字段配置
     *
     * @return 通用字段集合
     */
    public static Set<String> getCommonFieldsFromLion() {
        try {
            String commonFieldsConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstant.LION_COMMON_FIELDS_KEY);
            if (StringUtils.isBlank(commonFieldsConfig)) {
                log.info("Lion配置[{}]为空，返回空的通用字段集合", LionConstant.LION_COMMON_FIELDS_KEY);
                return Collections.emptySet();
            }

            Set<String> commonFields = Arrays.stream(commonFieldsConfig.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            log.info("从Lion获取通用字段配置成功，字段数量：{}", commonFields.size());
            return commonFields;
        } catch (Exception e) {
            log.warn("从Lion获取通用字段配置失败", e);
            return Collections.emptySet();
        }
    }
}
