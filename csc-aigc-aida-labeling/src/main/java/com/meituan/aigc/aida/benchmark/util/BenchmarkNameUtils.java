package com.meituan.aigc.aida.benchmark.util;

import org.apache.calcite.common.StringUtils;

/**
 * Benchmark名称转换工具类
 *
 * <AUTHOR>
 * @date 2025/6/24
 */

public class BenchmarkNameUtils {

    /**
     * 根据标注任务名称获取模型名称
     *
     * @param taskName 标注任务名称
     * @return 模型名称
     */
    public static String getModeNameByTaskName(String taskName) {
        if (StringUtils.isBlank(taskName)) {
            return StringUtils.EMPTY;
        }

        // 按照"-"分割字符串
        String[] parts = taskName.split("-");

        // 检查分割后的数组长度，应该至少有5个部分
        if (parts.length < 5) {
            return StringUtils.EMPTY;
        }

        // 模型名称可能包含"-"，所以需要从第4个部分开始到倒数第2个部分结束
        StringBuilder modelName = new StringBuilder();
        for (int i = 3; i < parts.length - 1; i++) {
            if (i > 3) {
                modelName.append("-");
            }
            modelName.append(parts[i]);
        }

        return modelName.toString();
    }

    /**
     * 从任务名称中获取评测轮次
     *
     * @param taskName 任务名称
     * @return 评测轮次
     */
    public static String getBoByTaskName(String taskName) {
        if (StringUtils.isBlank(taskName)) {
            return StringUtils.EMPTY;
        }

        // 按照"-"分割字符串
        String[] parts = taskName.split("-");

        // 检查分割后的数组长度，应该至少有5个部分
        if (parts.length < 5) {
            return StringUtils.EMPTY;
        }

        return parts[parts.length - 1];
    }

    /**
     * 从任务名称中获取评测对象
     *
     * @param taskName 任务名称
     * @return 评测对象名称
     */
    public static String getEvalObjectNameByTaskName(String taskName) {
        if (StringUtils.isBlank(taskName)) {
            return StringUtils.EMPTY;
        }

        // 按照"-"分割字符串
        String[] parts = taskName.split("-");

        // 检查分割后的数组长度，应该至少有5个部分
        if (parts.length < 5) {
            return StringUtils.EMPTY;
        }

        // 返回评测对象部分（第2个部分，索引为1）
        return parts[1];
    }

    /**
     * 从任务名称中获取数据集版本
     *
     * @param taskName 任务名称
     * @return 数据集版本
     */
    public static String getVersionByTaskName(String taskName) {
        if (StringUtils.isBlank(taskName)) {
            return StringUtils.EMPTY;
        }

        // 按照"-"分割字符串
        String[] parts = taskName.split("-");

        // 检查分割后的数组长度，应该至少有5个部分
        if (parts.length < 5) {
            return StringUtils.EMPTY;
        }

        // 返回数据集版本部分（第3个部分，索引为2）
        return parts[2];
    }
}
