package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-11 14:15
 * @description
 */
@Getter
public enum LabelingItemTemplateType {

    LABEL(1, "标注模版"),
    QUALITY(2, "质检模版");

    private final int code;
    private final String value;

    LabelingItemTemplateType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LabelingItemTemplateType getByCode(int code) {
        for (LabelingItemTemplateType type : LabelingItemTemplateType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
