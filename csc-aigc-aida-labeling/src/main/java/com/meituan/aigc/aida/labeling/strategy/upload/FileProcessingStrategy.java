package com.meituan.aigc.aida.labeling.strategy.upload;

import com.meituan.aigc.aida.labeling.pojo.vo.LabelingTaskRawDataVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Set;

/**
 * 文件处理策略接口
 *
 * <AUTHOR>
 */
public interface FileProcessingStrategy {

    /**
     * 判断文件类型是否匹配
     * @param fileType 文件类型
     * @return true/false
     */
    boolean isMe(String fileType);

    /**
     * 读取文件内容
     * @param file 上传的文件
     * @return 文件内容转换为LabelingTaskRawDataVO对象
     */
    LabelingTaskRawDataVO readFile(MultipartFile file);

    /**
     * 统计文件内容行数
     * @param file 上传的文件
     * @return 文件行数
     */
    int countContentRows(MultipartFile file);

    /**
     * 获取文件表头
     * @param file 上传的文件
     * @param columns 列数
     * @return 文件表头
     * @throws IOException IO异常
     */
    Set<String> getFileHeader(MultipartFile file, Integer columns) throws IOException;
}
