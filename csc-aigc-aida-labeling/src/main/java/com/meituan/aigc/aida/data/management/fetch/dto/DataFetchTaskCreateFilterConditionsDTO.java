package com.meituan.aigc.aida.data.management.fetch.dto;

import lombok.Data;

/**
 * @Author: guo<PERSON><PERSON>
 * @Create: 2025/4/8 10:30
 * @Version: 1.0
 */
@Data
public class DataFetchTaskCreateFilterConditionsDTO {

    /**
     * 埋点触发开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String triggerTimeStart;
    /**
     * 埋点触发结束时间 yyyy-MM-dd HH:mm:ss
     */
    private String triggerTimeEnd;
    /**
     * 标准问ID集合，用","分割
     */
    private String typicalQuestionIds;
    /**
     * 客服mis号，用","分割
     */
    private String misList;
    /**
     * 数据采集时刻
     */
    private Integer dataCapturePoint;

}
