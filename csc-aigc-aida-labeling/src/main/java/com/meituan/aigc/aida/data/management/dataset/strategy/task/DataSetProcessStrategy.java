package com.meituan.aigc.aida.data.management.dataset.strategy.task;

import com.meituan.aigc.aida.data.management.dataset.dto.DataSourceField;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateResponseParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetParseHeadParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.dataset.param.DataSetProcessParam;

import java.util.List;

/**
 * 数据集任务处理策略接口
 */
public interface DataSetProcessStrategy {

    /**
     * 获取策略类型
     *
     * @return 策略类型值
     */
    Integer getStrategyType();

    /**
     * 创建数据集
     *
     * @param param 数据集处理参数
     * @return 处理结果
     */
    DataSetCreateResponseParam createDataSet(DataSetCreateParam param);

    /**
     * 解析表头
     *
     * @param param 解析参数
     * @return 表头字段列表
     */
    List<DataFieldParam> parseHeader(DataSetParseHeadParam param);
}
