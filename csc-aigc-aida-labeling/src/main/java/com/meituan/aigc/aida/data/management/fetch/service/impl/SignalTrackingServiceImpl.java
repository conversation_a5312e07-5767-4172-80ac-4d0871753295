package com.meituan.aigc.aida.data.management.fetch.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.zebra.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.common.squirrel.SquirrelClient;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.fetch.common.convertor.SignalTrackingConvertor;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.SignalTrackingRulesRepository;
import com.meituan.aigc.aida.data.management.fetch.dto.AidaAppCfgDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.SignalTrackingRuleDTO;
import com.meituan.aigc.aida.data.management.fetch.helper.SignalTrackingRulesHelper;
import com.meituan.aigc.aida.data.management.fetch.service.SignalTrackingService;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.common.constant.RedisConstant;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.csc.aigc.runtime.api.AidaConfigService;
import com.meituan.csc.aigc.runtime.dto.aida.AppConfigDTO;
import com.meituan.csc.aigc.runtime.dto.aida.param.AppConfigRequestParam;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-03 11:05
 * @description 信号埋点规则服务
 */
@Service
@Slf4j
public class SignalTrackingServiceImpl implements SignalTrackingService {

    @Resource
    private SignalTrackingRulesRepository signalTrackingRulesRepository;

    @Autowired
    private AidaConfigService aidaConfigService;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    @Resource
    private SquirrelClient squirrelClient;

    @Override
    public PageData<SignalTrackingRuleDTO> pageSignalTrackingRuleList(String name, Integer pageNum, Integer pageSize) {
        log.info("开始查询埋点规则列表,taskName:{},pageNum:{},pageSize:{}", name, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<LlmSignalTrackingRules> llmSignalTrackingRules = signalTrackingRulesRepository
                .listSignalTrackingRule(name);
        if (CollectionUtils.isEmpty(llmSignalTrackingRules)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<LlmSignalTrackingRules> pageInfo = new PageInfo<>(llmSignalTrackingRules);
        PageHelper.clearPage();
        List<SignalTrackingRuleDTO> signalTrackingRuleList = llmSignalTrackingRules.stream()
                .map(SignalTrackingConvertor::convertSignalTrackingRuleDTO)
                .collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), signalTrackingRuleList);
    }

    @Override
    public SignalTrackingRuleDTO getSignalTrackingRuleDetail(Long ruleId) {
        log.info("查询埋点规则详情,规则Id:{}", ruleId);
        LlmSignalTrackingRules signalTrackingRuleById = signalTrackingRulesRepository.getSignalTrackingRuleById(ruleId);
        if (Objects.isNull(signalTrackingRuleById)) {
            return null;
        }
        return SignalTrackingConvertor.convertSignalTrackingRuleDTO(signalTrackingRuleById);
    }

    @Override
    public void createSignalTrackingRule(SignalTrackingRuleDTO rule) {
        checkParam(rule);
        checkAppId(rule);
        LlmSignalTrackingRules llmSignalTrackingRules = SignalTrackingConvertor.convertLlmSignalTrackingRules(rule);
        if (Objects.isNull(llmSignalTrackingRules)) {
            return;
        }
        // 新增埋点规则
        if (Objects.isNull(rule.getId())) {
            // 仅新增时候设置创建人mis
            llmSignalTrackingRules.setCreatorMis(Optional.ofNullable(UserUtils.getUser()).map(User::getLogin)
                    .orElse(CommonConstants.FieldName.UNKNOWN));
            llmSignalTrackingRules.setCreatorName(Optional.ofNullable(UserUtils.getUser()).map(User::getName)
                    .orElse(llmSignalTrackingRules.getCreatorMis()));
            signalTrackingRulesRepository.insertSignalTrackingRule(llmSignalTrackingRules);
            log.info("新增埋点规则成功,ruleId:{}", llmSignalTrackingRules.getId());
        } else {
            // 更新埋点规则
            llmSignalTrackingRules.setUpdateTime(new Date());
            signalTrackingRulesRepository.updateSignalTrackingRule(llmSignalTrackingRules);
            log.info("更新埋点规则成功,ruleId:{}", llmSignalTrackingRules.getId());
        }
        // 清除弹窗AC ID缓存
        clearVerifyExistPopupAcIdCache();
    }

    @Override
    public void deleteSignalTrackingRule(Long ruleId) {
        log.info("开始删除埋点规则,规则Id:{}", ruleId);
        CheckUtil.paramCheck(Objects.nonNull(ruleId), "埋点规则Id不能为空");
        LlmSignalTrackingRules signalTrackingRule = signalTrackingRulesRepository.getSignalTrackingRuleById(ruleId);
        CheckUtil.paramCheck(Objects.nonNull(signalTrackingRule), "埋点规则不存在");
        signalTrackingRule.setIsDelete(Boolean.TRUE);
        signalTrackingRule.setUpdateTime(new Date());
        signalTrackingRulesRepository.updateSignalTrackingRule(signalTrackingRule);
        log.info("埋点规则删除成功,规则Id:{}", ruleId);
        // 清除弹窗AC ID缓存
        clearVerifyExistPopupAcIdCache();
    }

    @Override
    public void updateStatus(Long ruleId, Integer status) {
        log.info("开始更新埋点规则状态,规则Id:{},状态:{}", ruleId, status);
        CheckUtil.paramCheck(Objects.nonNull(ruleId), "埋点规则Id不能为空");
        CheckUtil.paramCheck(Objects.nonNull(status), "埋点规则状态不能为空");
        signalTrackingRulesRepository.updateStatus(ruleId, status);
        // 清除弹窗AC ID缓存
        clearVerifyExistPopupAcIdCache();
    }

    @Override
    public void clearVerifyExistPopupAcIdCache() {
        try {
            log.info("清除弹窗AC ID缓存");
            squirrelClient.delete(RedisConstant.VERIFY_EXIST_POPUP_AC_ID, "");
        } catch (Exception e) {
            log.error("清除弹窗AC ID缓存失败", e);
        }
    }

    @Override
    public String getSignalTrackingRobotConfig() {
        return dataManagementLionConfig.getSignalTrackingRobotConfig();
    }

    /**
     * 参数校验
     *
     * @param rule
     */
    private void checkParam(SignalTrackingRuleDTO rule) {
        if (Objects.isNull(rule)) {
            throw new AidaTrainingCheckException("入参不能为空");
        }
        if (StringUtils.isBlank(rule.getName())) {
            throw new AidaTrainingCheckException("埋点规则名称不能为空");
        }
        if (StringUtils.isBlank(rule.getMisIds())) {
            throw new AidaTrainingCheckException("客服mis号不能为空");
        }
        if (StringUtils.isBlank(rule.getAcInterface())) {
            throw new AidaTrainingCheckException("太平洋弹屏AC接口ID不能为空");
        }
        if (StringUtils.isBlank(rule.getAidaAppCfg())) {
            throw new AidaTrainingCheckException("请至少添加一个机器人");
        }
        AidaAppCfgDTO aidaAppCfgDTO = JSON.parseObject(rule.getAidaAppCfg(), new TypeReference<AidaAppCfgDTO>() {
        });
        if (CollectionUtils.isNotEmpty(aidaAppCfgDTO.getDelayTriggerRobot()) && Objects.isNull(aidaAppCfgDTO.getDelayTime())){
            throw new AidaTrainingCheckException("请填写延迟执行时间");
        }
        if (Objects.nonNull(aidaAppCfgDTO.getDelayTime()) && (aidaAppCfgDTO.getDelayTime() < 5 || aidaAppCfgDTO.getDelayTime() >3600)){
            throw new AidaTrainingCheckException("延迟执行时间范围5-3600秒");
        }
    }

    /**
     * 校验appId
     *
     * @param rule 埋点规则
     */
    private void checkAppId(SignalTrackingRuleDTO rule) {
        String aidaAppCfg = rule.getAidaAppCfg();
        Set<String> robotIdSet = SignalTrackingRulesHelper.getRobotId(aidaAppCfg);
        if (CollectionUtils.isEmpty(robotIdSet)) {
            throw new AidaTrainingCheckException("埋点机器人不能为空");
        }
        for (String robotId : robotIdSet) {
            try {
                AppConfigRequestParam param = new AppConfigRequestParam();
                param.setAppId(robotId);
                param.setAuthorization(String.format("Bearer %s", aidaConfigService.getAppToken(robotId)));
                param.setUser(Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse(""));
                AppConfigDTO appConfigNoToken = aidaConfigService.getAppConfigNoToken(param);
                if (Objects.isNull(appConfigNoToken)) {
                    throw new AidaTrainingCheckException("埋点机器人不存在: " + robotId);
                }
            } catch (Exception e) {
                log.error("获取机器人信息失败，robotId={}，错误信息：{}", robotId, e.getMessage(), e);
                throw new AidaTrainingCheckException(e.getMessage() + "，appId=" + robotId);
            }
        }
    }
}
