package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem;
import com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckItemPO;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonQualityCheckStatisticsPO;
import com.meituan.aigc.aida.labeling.param.quality.check.LabelingQualityCheckItemParam;
import com.meituan.aigc.aida.labeling.pojo.dto.PageQueryDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingDataCheckResultVO;
import com.meituan.aigc.aida.labeling.pojo.vo.QualityCheckModifiedDataCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingQualityCheckInfoVO;

import java.util.Date;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:06
 * @Version: 1.0
 */
public interface LabelingQualityCheckItemRepository {

    /**
     * 批量插入质检项
     *
     * @param qualityCheckItems 质检项列表
     * @return 插入数量
     */
    int batchInsert(List<LabelingQualityCheckItem> qualityCheckItems);

    /**
     * 根据质检任务id和质检状态查询质检项详情
     *
     * @param param 质检任务id和质检状态
     * @return 质检项详情列表
     */
    List<LabelingQualityCheckItemPO> listByQualityCheckTaskIdAndQualityCheckStatus(LabelingQualityCheckItemParam param);

    /**
     * 根据质检任务id删除质检项
     *
     * @param qualityCheckTaskIds 质检任务id列表
     */
    void deleteByQualityCheckTaskIds(List<Long> qualityCheckTaskIds);

    /**
     * 根据质检任务id查询质检项列表
     *
     * @param qualityCheckTaskId 质检任务id
     * @return 质检项列表
     */
    List<LabelingQualityCheckItemPO> listQualityCheckItemByTaskIdAndRawDataIds(Long qualityCheckTaskId, List<Long> rawDataIds);

    /**
     * 根据质检任务id查询质检任务列表
     *
     * @param qualityCheckTaskIds 质检任务id
     * @return 质检任务列表
     */
    List<QualityCheckTaskPO> listByQualityCheckTaskId(List<Long> qualityCheckTaskIds);

    LabelingQualityCheckItem getById(Long id);

    /**
     * 更新质检结果
     *
     * @param checkItem 质检详情
     */
    void updateByIdSelective(LabelingQualityCheckItem checkItem);

    Integer countByCheckTaskIdAndStatus(Long qualityCheckTaskId, Integer qualityCheckDataStatus);

    /**
     * 统计子任务下的数量：全部数量 已质检数量 未质检数量
     */
    PageQueryDTO<?> countByTaskIdAndLabelerMis(Long checkTaskId, String labelerMis);

    /**
     * 标注员维度统计质检任务质检情况
     *
     * @param qualityCheckTaskIds 质检任务ID集合
     * @return 统计结果
     */
    List<LabelingDataCheckResultVO> countCheckDetailByCheckTaskIds(List<Long> qualityCheckTaskIds);

    /**
     * 根据质检任务ID和状态计算完成质检的原数据数量
     *
     * @param qualityCheckTaskIds 质检任务ID集合
     * @return 完成质检的原数据数量
     */
    Integer countRawDataNumByCheckTaskIdsAndStatus(List<Long> qualityCheckTaskIds, Integer checkStatus);

    /**
     * 根据质检详情ID查询所属质检任务数量
     *
     * @param checkItemIds 质检详情id
     * @return 质检任务数量
     */
    Integer countCheckTaskNumByQualityCheckItemIds(List<Long> checkItemIds);

    /**
     * 根据质检任务ID和可检状态查询rawDataId集合
     *
     * @param qualityCheckTaskId 质检任务ID
     * @param rawDataId          原始数据ID
     * @param qualityCheckTaskId 质检任务第
     * @param checkableStatus    可检状态
     * @return rawDataId集合
     */
    List<Long> listRawDataIdsByTaskAndRawDataIdAndStatus(Long qualityCheckTaskId, Long rawDataId, Integer checkableStatus, Integer qualityStatus);

    /**
     * 根据状态统计质检任务抽检到的原数据数量
     *
     * @param qualityCheckTaskId 质检任务ID
     * @param checkableStatus    可检状态
     * @param qualityStatus      质检状态
     * @return 原数据数量
     */
    Integer countRawDataIdsByTaskAndStatus(Long qualityCheckTaskId, Integer checkableStatus, Integer qualityStatus);

    /**
     * 根据质检详情ID查询质检详情
     *
     * @param labelingDetailIdList 质检详情ID列表
     * @return 质检详情列表
     */
    List<LabelingQualityCheckItem> listByDetailIdList(List<Long> labelingDetailIdList);

    /**
     * 根据主键批量更新质检详情
     *
     * @param labelingQualityCheckItemList 质检详情集合
     */
    void batchUpdateById(List<LabelingQualityCheckItem> labelingQualityCheckItemList);

    /**
     * 根据质检任务ID和状态查询会话ID集合
     *
     * @param qualityCheckTaskId 质检任务ID
     * @param sessionId          会话ID
     * @param checkableStatus    可检状态
     * @param qualityStatus      质检状态
     * @return 会话ID集合
     */
    List<String> listSessionIdByTaskAndSessionIdAndStatus(Long qualityCheckTaskId, String sessionId, Integer checkableStatus, Integer qualityStatus);

    /**
     * 根据质检任务ID和会话ID集合查询质检详情
     *
     * @return 质检详情列表
     */
    List<LabelingQualityCheckItemPO> listQualityCheckItemByTaskIdAndSessionIdList(Long qualityCheckTaskId, List<String> sessionIdList);

    /**
     * 根据任务Id统计质检员修改标注数据数量（根据子任务Id查询出主任务Id，再统计质检详情）
     *
     * @param subTaskId 子任务Id
     * @return 结果
     */
    QualityCheckModifiedDataCountVO countQualityCheckModifiedDataByTaskId(Long subTaskId);

    /**
     * 更新质检结果
     *
     * @param labelingQualityCheckItem 质检详情
     */
    void updateById(LabelingQualityCheckItem labelingQualityCheckItem);

    /**
     * 根据质检任务id和会话id查询已查看的原始数据id
     *
     * @param qualityCheckTaskId 质检任务id
     * @param sessionId          会话id
     * @return 已查看的原始数据id列表
     */
    List<LabelingQualityCheckItem> listDetailIdByTaskIdAndSessionId(Long qualityCheckTaskId, String sessionId);

    /**
     * 根据质检任务id和会话id查询已查看的原始数据id
     *
     * @param qualityCheckTaskId 质检任务id
     * @param rawDataId          原始数据ID
     * @return 已查看的原始数据id列表
     */
    List<LabelingQualityCheckItem> listDetailIdByTaskIdAndRawDataId(Long qualityCheckTaskId, Long rawDataId);


    /**
     * 根据子任务id和状态查询会话数量
     *
     * @param subTaskId 子任务id
     * @param status    状态
     * @return 会话数量
     */
    int countSessionBySubTaskIdAndStatus(Long subTaskId, Integer status);

    /**
     * 根据子任务id和状态查询query数量
     *
     * @param subTaskId 子任务id
     * @param status    状态
     * @return query数量
     */
    int countQueryBySubTaskIdAndStatus(Long subTaskId, Integer status);

    /**
     * 根据质检任务id和质检状态查询质检项列表
     *
     * @param qualityCheckTaskId 质检任务id
     * @param qualityCheckStatus 质检状态
     * @return 质检项列表
     */
    List<LabelingQualityCheckItem> listDataByQualityCheckTaskIdAndQualityCheckStatus(Long qualityCheckTaskId, Integer qualityCheckStatus);

    /**
     * 人员维度统计某一时间段内的质检数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 结果
     */
    List<PersonQualityCheckStatisticsPO> statisticsPersonQualityCheckData(Date startTime, Date endTime);

    /**
     * 统计某一个任务在某段自己内的质检量
     * @param taskId 主任务ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 结果
     */
    Integer countByTaskIdAndFirstCheckTime(Long taskId, Date startTime, Date endTime);

    /**
     * 统计一段时间内的标注数据统计需要回刷的质检信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 结果
     */
    List<LabelingQualityCheckInfoVO> getLabelingQualityCheckInfo(Date startTime, Date endTime);

    /**
     * 查询一段时间内的质检数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<LabelingQualityCheckItem> listQualityCheckedItemByDateRange(Date startTime, Date endTime);
}
