package com.meituan.aigc.aida.data.management.dataset.dao.mapper;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DataProcessTaskMapper {
    long countByExample(DataProcessTaskExample example);

    int deleteByExample(DataProcessTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DataProcessTask record);

    int insertSelective(DataProcessTask record);

    List<DataProcessTask> selectByExampleWithBLOBs(DataProcessTaskExample example);

    List<DataProcessTask> selectByExample(DataProcessTaskExample example);

    DataProcessTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DataProcessTask record, @Param("example") DataProcessTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") DataProcessTask record, @Param("example") DataProcessTaskExample example);

    int updateByExample(@Param("record") DataProcessTask record, @Param("example") DataProcessTaskExample example);

    int updateByPrimaryKeySelective(DataProcessTask record);

    int updateByPrimaryKeyWithBLOBs(DataProcessTask record);

    int updateByPrimaryKey(DataProcessTask record);
}