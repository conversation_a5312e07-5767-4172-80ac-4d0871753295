package com.meituan.aigc.aida.labeling.remote.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConditionTypeEnum {

    /**
     * 1-等于 2-包含 3-大于 4-小于 5-大于等于 6-小于等于 7-为空
     */
    EQUAL(1, "等于"),
    CONTAIN(2, "包含"),
    GREATER_THAN(3, "大于"),
    LESS_THAN(4, "小于"),
    GREATER_THAN_OR_EQUAL(5, "大于等于"),
    LESS_THAN_OR_EQUAL(6, "小于等于"),
    IS_NULL(7, "为空"),
    ;

    private final Integer code;
    private final String desc;


    public static ConditionTypeEnum getByCode(Integer code) {
        for (ConditionTypeEnum conditionTypeEnum : ConditionTypeEnum.values()) {
            if (conditionTypeEnum.getCode().equals(code)) {
                return conditionTypeEnum;
            }
        }
        return null;
    }
}
