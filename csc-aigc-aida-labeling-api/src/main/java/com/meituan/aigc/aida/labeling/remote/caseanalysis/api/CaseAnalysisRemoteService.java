package com.meituan.aigc.aida.labeling.remote.caseanalysis.api;

import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetCreateResponseDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetTryLockDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.*;
import com.meituan.aigc.aida.labeling.remote.common.dto.PageDataWithHead;
import com.meituan.aigc.aida.labeling.remote.common.dto.ServiceResponseDTO;

/**
 * <AUTHOR>
 * @date 2025-05-26 10:21
 * @description case分析
 */
public interface CaseAnalysisRemoteService {

    /**
     * 创建数据集
     *
     * @param param CreateDataSetRequestParam
     * @return 数据集id
     */
    ServiceResponseDTO<DataSetCreateResponseDTO> createDataSet(CreateDataSetRequestParam param);

    /**
     * 更新数据集
     *
     * @param param UpdateDataSetRequestParam
     * @return void
     */
    ServiceResponseDTO<?> updateDataSet(UpdateDataSetRequestParam param);

    /**
     * 获取数据集锁id
     *
     * @param dataSetId 数据集id
     * @return DataSetTryLockDTO
     */
    ServiceResponseDTO<DataSetTryLockDTO> getLockIdByDataSetId(Long dataSetId);

    /**
     * 分页查询数据集
     *
     * @param param DataSetPageQueryParam
     * @return PageRemoteData
     */
    ServiceResponseDTO<DatasetQueryResultDTO> pageQueryDataSet(DataSetPageQueryParam param);
}
