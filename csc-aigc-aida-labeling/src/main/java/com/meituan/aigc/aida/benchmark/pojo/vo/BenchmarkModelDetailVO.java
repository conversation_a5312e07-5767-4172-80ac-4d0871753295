package com.meituan.aigc.aida.benchmark.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Benchmark模型详细评测结果响应VO
 *
 * <AUTHOR>
 * @date 2025/6/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BenchmarkModelDetailVO implements Serializable {

    /**
     * 综合分数
     */
    private Double totalScore;

    /**
     * 综合排名
     */
    private Integer rank;

    /**
     * 可用率
     */
    private Double availability;

    /**
     * 满分率
     */
    private Double fullScoreRatio;

    /**
     * 表头列表
     */
    private List<String> headList;

    /**
     * 数据列表
     */
    private List<List<String>> dataList;
} 