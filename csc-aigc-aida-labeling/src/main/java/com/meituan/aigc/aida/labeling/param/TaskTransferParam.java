package com.meituan.aigc.aida.labeling.param;

import com.meituan.aigc.aida.labeling.common.enums.TransferTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TaskTransferParam implements Serializable {

    /**
     * 子任务id
     */
    private Long subTaskId;

    /**
     * 转交类型
     *
     * @see TransferTypeEnum
     */
    private Integer transferType;

    /**
     * 转交配置
     */
    private List<TransferConfig> transferConfig;

    @Data
    public static class TransferConfig implements Serializable {
        /**
         * 转交人mis
         */
        private String transferMis;
        /**
         * 转交人姓名
         */
        private String transferName;
        /**
         * 转交数量
         */
        private Integer transferNum;
    }
}
