package com.meituan.aigc.aida.data.management.dataset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据处理任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum ProcessTaskStatusEnum {
    /**
     * 任务状态: 1-创建中 2-待处理 3-打标中 4-打标完成 5-打标失败 6-创建失败 7-信号回刷中 8-信号回刷完成 9-信号回刷失败
     */
    CREATING(1, "创建中"),
    WAITING(2, "待处理"),
    LABELING(3, "打标中"),
    LABELING_COMPLETE(4, "打标完成"),
    LABELING_FAILED(5, "打标失败"),
    CREATE_FAILED(6, "创建失败"),
    SIGNAL_REFRESHING(7, "信号回刷中"),
    SIGNAL_REFRESH_COMPLETE(8, "信号回刷完成"),
    SIGNAL_REFRESH_FAILED(9, "信号回刷失败"),
    ;

    private final Integer code;
    private final String desc;
} 