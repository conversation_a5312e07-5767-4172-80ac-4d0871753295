package com.meituan.aigc.aida.data.management.dataset.service.impl;

import com.meituan.aigc.aida.data.management.dataset.dto.DataProcessTaskDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.LabelRobotInfoDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.NonCustomLabelInfoDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.NonCustomRobotDTO;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.dataset.service.DataProcessService;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetVersionService;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.service.common.PermissionService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataProcessTaskRepository;
import com.meituan.aigc.aida.data.management.dataset.common.DataSetInvokeEs;
import com.meituan.aigc.aida.data.management.dataset.enums.DataSetVersionTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.ProcessTaskStatusEnum;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.util.stream.Collectors;

/**
 * 数据处理服务实现类
 */
@Slf4j
@Service
public class DataProcessServiceImpl implements DataProcessService {

    @Autowired
    private DataSetRepository dataSetRepository;

    @Autowired
    private DataSetVersionRepository dataSetVersionRepository;

    @Autowired
    private DataProcessTaskRepository dataProcessTaskRepository;

    @Autowired
    private DataSetInvokeEs dataSetInvokeEs;

    @Autowired
    private DatasetEsIndexService datasetEsIndexService;

    @Autowired
    private DataSetVersionService dataSetVersionService;

    @Autowired
    private PermissionService permissionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createProcessTask(DataProcessCreateTaskParam param) {
        // 1. 校验参数
        DataSet dataSet = validateCreateTaskParams(param);

        // 2. 复制一个新的数据集版本
        DataSetCopyVersionParam dataSetCopyVersionParam = new DataSetCopyVersionParam();
        dataSetCopyVersionParam.setDataSetId(dataSet.getId());
        dataSetCopyVersionParam.setVersionId(param.getVersionId());
        dataSetCopyVersionParam.setVersionName(param.getProcessDatasetVersionName());
        dataSetCopyVersionParam.setVersionType(DataSetVersionTypeEnum.DATA_PROCESS.getCode());
        dataSetCopyVersionParam.setVersionDescription("由数据处理任务创建: " + param.getTaskName());
        Long newVersionId = dataSetVersionService.copyVersion(dataSetCopyVersionParam);

        // 3. 创建数据处理任务
        createDataProcessTask(param, newVersionId);
    }

    /**
     * 校验创建任务的参数
     *
     * @param param 创建任务参数
     * @return 数据集对象
     */
    private DataSet validateCreateTaskParams(DataProcessCreateTaskParam param) {
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getTaskName()), "任务名称不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getDatasetId()), "数据集ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(param.getVersionId()), "数据集版本ID不能为空");
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getProcessDatasetVersionName()), "清洗后数据集版本名称不能为空");

        // 查询数据集是否存在
        DataSet dataSet = dataSetRepository.getDataSetById(param.getDatasetId());
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        // 查询数据集版本是否存在
        DataSetVersion sourceVersion = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(
                param.getDatasetId(), param.getVersionId());
        CheckUtil.paramCheck(Objects.nonNull(sourceVersion), "数据集版本不存在");

        return dataSet;
    }

    /**
     * 创建数据处理任务
     *
     * @param param        任务参数
     * @param newVersionId 新版本ID
     */
    private void createDataProcessTask(DataProcessCreateTaskParam param, Long newVersionId) {
        Date now = new Date();

        DataProcessTask task = new DataProcessTask();
        task.setTaskName(param.getTaskName());
        task.setDatasetId(param.getDatasetId());
        task.setProcessDatasetVersionId(newVersionId);
        task.setOriginalDatasetVersionId(param.getVersionId());
        task.setStatus(ProcessTaskStatusEnum.CREATING.getCode().byteValue());
        task.setTotalCount(0); // 初始设置为0，待处理完成后更新
        // 设置创建者信息
        task.setCreatorMis(UserUtils.getUser().getLogin());
        task.setCreatorName(UserUtils.getUser().getName());
        // 设置创建和更新时间
        task.setCreateTime(now);
        task.setUpdateTime(now);

        // 插入任务数据
        dataProcessTaskRepository.createDataProcessTask(task);
    }

    @Override
    public PageData<DataProcessTaskDTO> listProcessTask(DataProcessTaskPageParam param) {
        // 1. 校验参数，设置默认值
        if (param.getPageNum() == null || param.getPageNum() <= 0) {
            param.setPageNum(1);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(10);
        }

        // 2. 使用PageHelper进行数据库分页
        log.info("开始分页查询数据处理任务，参数：taskName={}, datasetId={}, pageNum={}, pageSize={}",
                param.getTaskName(), param.getDatasetId(), param.getPageNum(), param.getPageSize());

        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        // 3. 查询符合条件的数据处理任务
        List<DataProcessTask> tasks;

        // 获取当前登录用户
        String currentUserMis = UserUtils.getUser().getLogin();
        boolean isAdmin = permissionService.isAdminUser();

        // 根据用户权限决定是否按创建人过滤
        if (isAdmin) {
            // 管理员可以查看所有任务
            log.info("当前用户{}是管理员，可查看所有任务", currentUserMis);
            tasks = dataProcessTaskRepository.listDataProcessTaskByNameAndDatasetId(param.getTaskName(), param.getDatasetId());
        } else {
            // 非管理员只能查看自己创建的任务
            log.info("当前用户{}非管理员，只能查看自己创建的任务", currentUserMis);
            tasks = dataProcessTaskRepository.listDataProcessTaskByNameAndDatasetIdAndCreator(
                    param.getTaskName(), param.getDatasetId(), currentUserMis);
        }

        if (CollectionUtils.isEmpty(tasks)) {
            PageHelper.clearPage();
            return new PageData<>(0L, Collections.emptyList());
        }

        // 4. 获取分页信息
        PageInfo<DataProcessTask> pageInfo = new PageInfo<>(tasks);
        PageHelper.clearPage();

        // 5. 转换为DTO
        List<DataProcessTaskDTO> taskDTOs = tasks.stream()
                .map(this::convertToDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return new PageData<>(pageInfo.getTotal(), taskDTOs);
    }

    /**
     * 将数据处理任务实体转换为DTO
     *
     * @param task 数据处理任务实体
     * @return 数据处理任务DTO
     */
    private DataProcessTaskDTO convertToDTO(DataProcessTask task) {
        if (Objects.isNull(task)) {
            return null;
        }

        DataProcessTaskDTO dto = new DataProcessTaskDTO();
        dto.setId(task.getId());
        dto.setTaskName(task.getTaskName());
        dto.setTaskStatus(task.getStatus().intValue());
        dto.setSourceDatasetId(task.getDatasetId());
        dto.setProcessedDatasetVersionId(task.getProcessDatasetVersionId());
        dto.setSourceDatasetVersionId(task.getOriginalDatasetVersionId());
        dto.setCreatorName(task.getCreatorName());
        dto.setCreateTime(task.getCreateTime() != null ?
                task.getCreateTime().toString() : null);

        // 查询数据集信息
        DataSet dataSet = dataSetRepository.getDataSetById(task.getDatasetId());
        if (Objects.nonNull(dataSet)) {
            dto.setSoucreDatasetName(dataSet.getName());
        }

        // 查询数据集版本信息
        DataSetVersion sourceVersion = dataSetVersionRepository.getById(task.getOriginalDatasetVersionId());
        if (Objects.nonNull(sourceVersion)) {
            dto.setSoucreDatasetVersionName(sourceVersion.getVersionName());
        }

        DataSetVersion processedVersion = dataSetVersionRepository.getById(task.getProcessDatasetVersionId());
        if (Objects.nonNull(processedVersion)) {
            dto.setProcessedDatasetVersionName(processedVersion.getVersionName());
        }

        return dto;
    }

    @Override
    public LabelRobotInfoDTO getLabelRobotInfo(LabelRobotParam param) {
        // TODO: 实现获取打标机器人信息
        return null;
    }

    @Override
    public NonCustomLabelInfoDTO getNonCustomLabelInfo() {
        // TODO: 实现获取非自定义标签信息
        return null;
    }

    @Override
    public List<NonCustomRobotDTO> queryNonCustomRobot(NonCustomRobotQueryParam param) {
        // TODO: 实现查询非自定义打标机器人
        return null;
    }

    @Override
    public void markData(DataMarkParam param) {
        // TODO: 实现数据打标
    }
} 