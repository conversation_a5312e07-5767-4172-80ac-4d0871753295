package com.meituan.aigc.aida.common.squirrel;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.dianping.squirrel.client.StoreClient;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.core.StoreCallback;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.squirrel.client.impl.redis.StoreScanResult;
import com.dianping.squirrel.client.impl.redis.StoreTuple;
import com.dianping.squirrel.client.impl.redis.pipeline.RedisPipeline;

/**
 * Created by kevin on 2017/5/6.
 */
@Component
public class SquirrelClient {

    @Resource(name = "redisClient")
    private RedisStoreClient redisStoreClient;

    private StoreKey createStoreKey(String category, String key) {
        return new StoreKey(category, key);
    }

    /**
     * @return 是否存在该key，存在返回true，不存在返回false
     */
    public Boolean exists(String category, String key) {
        return redisStoreClient.exists(this.createStoreKey(category, key));
    }

    /**
     * @param category category
     * @param key      key
     * @return 返回该key的类型
     */
    public String type(String category, String key) {
        return redisStoreClient.type(this.createStoreKey(category, key));
    }

    /**
     * 带有过期时间的incr操作
     *
     * @param category        category
     * @param key
     * @param amount          要增加的值
     * @param expireInSeconds 初始化 key 的过期时间
     * @param defaultValue    初始化 key 的默认值
     * @return 增长后 key 的值,如果 Key 不存在，会创建这个 Key，且值为 defaultValue,然后再增加amount, 过期时间为
     *         defaultExpire
     */
    public Long incrBy(String category, String key, long amount, int expireInSeconds, int defaultValue) {
        return redisStoreClient.incrBy(this.createStoreKey(category, key), amount, expireInSeconds, defaultValue);
    }

    /**
     * 带有过期时间的incr操作
     *
     * @param category        category
     * @param key
     * @param amount          要增加的值
     * @param expireInSeconds 初始化 key 的过期时间
     * @return 增长后 key 的值,如果 Key 不存在，会创建这个 Key : 值=amount ,过期时间为 defaultExpir
     */
    public Long incrBy(String category, String key, long amount, int expireInSeconds) {
        return redisStoreClient.incrBy(this.createStoreKey(category, key), amount, expireInSeconds);
    }

    /**
     * 自增函数, 默认过期时间为category上配置的过期时间
     *
     * @param category category
     * @param key
     * @param amount   要增加的值
     * @return 增长后 key 的值,如果 Key 不存在，会创建这个 Key : 值=amount，返回值 amount,过期时间为category
     *         的配置时间
     */
    public Long incrBy(String category, String key, long amount) {
        return redisStoreClient.incrBy(this.createStoreKey(category, key), amount);
    }

    /**
     * @param category category
     * @param key
     * @param amount   要增加的值
     * @return 增长后 key 的值,如果 Key 不存在，会创建这个 Key，且值为0，然后再增加, 注意:该值无过期时间
     */
    public Double incrByFloat(String category, String key, double amount) {
        return redisStoreClient.incrByFloat(this.createStoreKey(category, key), amount);
    }

    /**
     * 带有过期时间的自减操作
     *
     * @param category        category
     * @param key
     * @param amount          要减少的值
     * @param expireInSeconds 初始化 key 的过期时间
     * @param defaultValue    初始化 key 的默认值
     * @return 减少后的值，如果 Key 不存在，会创建这个 Key，且值为 defaultValue,过期时间为 defaultExpire，然后再减少
     */
    public Long decrBy(String category, String key, long amount, int expireInSeconds, int defaultValue) {
        return redisStoreClient.decrBy(this.createStoreKey(category, key), amount, expireInSeconds, defaultValue);
    }

    /**
     * 带有过期时间的自减操作
     *
     * @param category        category
     * @param key
     * @param amount          要减少的值
     * @param expireInSeconds 初始化 key 的过期时间
     * @return 减少后的值，如果 Key 不存在，会创建这个 Key : 值= -amount，返回值
     *         -amount,过期时间为expireInSeconds
     */
    public Long decrBy(String category, String key, long amount, int expireInSeconds) {
        return redisStoreClient.decrBy(this.createStoreKey(category, key), amount, expireInSeconds);
    }

    /**
     * 自减函数, 默认过期时间为category上配置的过期时间
     *
     * @param category category
     * @param key
     * @param amount   要减少的值
     * @return 减少后的值，如果 Key 不存在，会创建这个 Key : 值= -amount，返回值 -amount,过期时间为 category
     *         的配置时间
     */
    public Long decrBy(String category, String key, long amount) {
        return redisStoreClient.decrBy(this.createStoreKey(category, key), amount);
    }

    /**
     * @param category        category
     * @param key
     * @param expireInSeconds 超时时间 , 如果过期时间小于等于 0 ,该key直接过期
     * @return 设置成功返回true，当key不存在或者不能为key设置生存时间时返回false
     */
    public Boolean expire(String category, String key, int expireInSeconds) {
        return redisStoreClient.expire(this.createStoreKey(category, key), expireInSeconds);
    }

    /**
     * @param category      category
     * @param key
     * @param expireInMills 超时时间（毫秒）
     * @return 设置成功返回true，当key不存在或者不能为key设置生存时间时返回false
     */
    public Boolean pexpire(String category, String key, int expireInMills) {
        return redisStoreClient.pexpire(this.createStoreKey(category, key), expireInMills);
    }

    /**
     * @return 以秒为单位，返回给定 key 的剩余生存时间<br>
     *         当 key 不存在时，返回 -2 。<br>
     *         当 key 存在但没有设置剩余生存时间时，返回 -1 。<br>
     */
    public Long ttl(String category, String key) {
        return redisStoreClient.ttl(this.createStoreKey(category, key));
    }

    /**
     * 移除给定 key 的生存时间，将这个key从 易失的(带生存时间key) 转换成 持久的(一个不带生存时间、永不过期的key)
     *
     * @return 当生存时间移除成功时，返回 true <br>
     *         如果 key 不存在或 key 没有设置生存时间，返回 false <br>
     */
    public Boolean persist(String category, String key) {
        return redisStoreClient.persist(this.createStoreKey(category, key));
    }

    /**
     * 如果 key 已经存在并且是一个字符串， APPEND 命令将 value 追加到 key 原来的值的末尾。<br>
     * 如果 key 不存在， APPEND 就简单地将给定 key 设为 value ，就像执行 SET key value 一样。<br>
     *
     * @return 追加 value 之后， key 中字符串的长度。
     */
    public Long append(String category, String key, String value) {
        return redisStoreClient.append(this.createStoreKey(category, key), value);
    }

    /**
     * 将给定 key 的值设为 value ，并返回 key 的旧值(old value)。<br>
     * 当 key 存在但不是字符串类型时，返回一个错误。
     *
     * @return 返回给定 key 的旧值。当 key 不存在时，返回 null 。
     */
    public <T> T getSet(String category, String key, Object value) {
        return redisStoreClient.getSet(this.createStoreKey(category, key), value);
    }

    /**
     * 同 {@link RedisStoreClient#getSet(StoreKey, Object)}, 可设置过期时间(lua 实现的原子命令).
     *
     * @param category        category
     * @param key             StoreKey
     * @param value           设置的value
     * @param expireInSeconds 过期时间
     * @return 旧值
     */
    public <T> T getSet(String category, String key, Object value, int expireInSeconds) {
        return redisStoreClient.getSet(this.createStoreKey(category, key), value, expireInSeconds);
    }

    /**
     * 对 key 所储存的字符串值，获取指定偏移量上的位(bit)。 <br>
     * 当 offset 比字符串值的长度大，或者 key 不存在时，返回 0 。<br>
     *
     * @param category category
     * @param key
     * @param offset   偏移
     * @return 字符串值指定偏移量上的位(bit)如果是1返回true，否则返回false
     */
    public Boolean getBit(String category, String key, long offset) {
        return redisStoreClient.getBit(this.createStoreKey(category, key), offset);
    }

    /**
     * 对 key 所储存的字符串值，设置或清除指定偏移量上的位(bit)。位的设置或清除取决于 value 参数，可以是 0 也可以是 1 。<br>
     * 当 key 不存在时，自动生成一个新的字符串值。字符串会进行伸展(grown)以确保它可以将 value 保存在指定的偏移量上。<br>
     * 当字符串值进行伸展时，空白位置以 0 填充。offset 参数必须大于或等于 0 ，小于 2^32 (bit 映射被限制在 512 MB 之内)。<br>
     *
     * @param category category
     * @param key
     * @param offset   偏移
     * @param value    设置的值
     * @return 指定偏移量原来储存的值
     */
    public Boolean setBit(String category, String key, long offset, boolean value) {
        return redisStoreClient.setBit(this.createStoreKey(category, key), offset, value);
    }

    /**
     * 计算给定字符串中，被设置为 1 的比特位的数量。
     *
     * @param category category
     * @param key
     * @param start    起始位置
     * @param end      结束位置
     * @return 字符串中设置为 1 的比特位的数量
     */
    public Long bitCount(String category, String key, long start, long end) {
        return redisStoreClient.bitCount(this.createStoreKey(category, key), start, end);
    }

    /**
     * 计算给定字符串中，被设置为 1 的比特位的数量。
     *
     * @param category category
     * @param key
     * @return 字符串中设置为 1 的比特位的数量
     */
    public Long bitCount(String category, String key) {
        return redisStoreClient.bitCount(this.createStoreKey(category, key));
    }

    /**
     * 设置 Key 对应的值为 Value，并设置过期时间expire(默认不需要这个,category自带过期时间), 如果 Key 不存在则添加，如果
     * Key 已经存在则覆盖
     *
     * @param category        category
     * @param key
     * @param value
     * @param expireInSeconds 单位 秒
     * @return 如果成功，返回 true<br>
     *         如果失败，返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public Boolean set(String category, String key, Object value, int expireInSeconds) {
        return redisStoreClient.set(this.createStoreKey(category, key), value, expireInSeconds);
    }

    /**
     * 如果当前key 存在并且与 expect 相同, 则设置该key 的值为 newValue,并设置过期时间 expireInSeconds
     *
     * @param category        category
     * @param key
     * @param expect          期望的value, 可以为 null ,表示不存在
     * @param newValue        如果key符合期望,则设置该key 为 setValue, 不能为null
     * @param expireInSeconds 设置newValue的同时设置过期时间 小于0 则不过期. 单位:秒
     * @return
     */
    public Boolean compareAndSet(String category, String key, Object expect, Object newValue, int expireInSeconds) {
        return redisStoreClient.compareAndSet(this.createStoreKey(category, key), expect, newValue, expireInSeconds);
    }

    /**
     * 如果当前key 存在并且与 expect 相同, 则设置该key 的值为 newValue,过期时间根据category的配置而定
     *
     * @param category category
     * @param key
     * @param expect   期望的value
     * @param newValue 如果key符合期望,则设置该key 为 setValue
     * @return
     */
    public Boolean compareAndSet(String category, String key, Object expect, Object newValue) {
        return redisStoreClient.compareAndSet(this.createStoreKey(category, key), expect, newValue);
    }

    /**
     * 如果当前key 存在并且 value 与 expect 相同,则删除该key.
     *
     * @param category category
     * @param key
     * @param expect   期望的value
     * @return
     */
    public Boolean compareAndDelete(String category, String key, Object expect) {
        return redisStoreClient.compareAndDelete(this.createStoreKey(category, key), expect);
    }

    /**
     * 设置 Key 对应的值为 Value(当且仅当key不存在),并设置过期时间expire(默认不需要这个,category自带过期时间)
     *
     * @param category        category
     * @param key
     * @param value
     * @param expireInSeconds 单位 秒
     * @return 如果成功，返回 true<br>
     *         如果失败，返回 false
     */
    public Boolean add(String category, String key, Object value, int expireInSeconds) {
        return redisStoreClient.add(this.createStoreKey(category, key), value, expireInSeconds);
    }

    /**
     * 添加 Key 对应的值为 Value，只有当 Key 不存在时才添加，如果 Key 已经存在，不改变现有的值
     * {@link RedisStoreClient#add(StoreKey, Object, int)}
     *
     * @param category        category
     * @param key             要添加的 Key
     * @param value           要添加的 Value
     * @param expireInSeconds 过期时间
     * @return 如果 Key 不存在且添加成功，返回 true<br>
     *         如果 Key 已经存在，返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public Boolean setnx(String category, String key, Object value, int expireInSeconds) {
        return redisStoreClient.setnx(this.createStoreKey(category, key), value, expireInSeconds);
    }

    /**
     * 设置 Key 对应的值为 Value，此value不会进行序列化,以原始的方式存入redis
     *
     * @param category        category
     * @param key
     * @param value
     * @param expireInSeconds
     * @return
     */
    public Boolean setRaw(String category, String key, String value, int expireInSeconds) {
        return redisStoreClient.setRaw(this.createStoreKey(category, key), value, expireInSeconds);
    }

    /**
     * 返回 Key 对应 原始值，此value不会进行反序列化
     *
     * @param category category
     * @param key
     * @return string 类型
     */
    public String getRaw(String category, String key) {
        return redisStoreClient.getRaw(this.createStoreKey(category, key));
    }

    /**
     * 设置 Key 对应的值为 Value，此value不会进行序列化,以原始的方式存入redis
     *
     * @param category        category
     * @param key
     * @param value
     * @param expireInSeconds
     * @return {@code true} 设置成功 {@code false} 设置失败
     */
    public Boolean setBytes(String category, String key, byte[] value, int expireInSeconds) {
        return redisStoreClient.setBytes(this.createStoreKey(category, key), value, expireInSeconds);
    }

    /**
     * 设置 Key 对应的值为 Value，此value不会进行序列化,以原始的方式存入redis
     * 过期时间为 category 配置的过期时间
     *
     * @param category category
     * @param key
     * @param value
     * @return {@code true} 设置成功 {@code false} 设置失败
     */
    public Boolean setBytes(String category, String key, byte[] value) {
        return redisStoreClient.setBytes(this.createStoreKey(category, key), value);
    }

    /**
     * 返回 Key 对应 原始值，此value不会进行反序列化
     *
     * @param category category
     * @param key
     * @return byte[]
     */
    public byte[] getBytes(String category, String key) {
        return redisStoreClient.getBytes(this.createStoreKey(category, key));
    }

    /**
     * 返回pipeline接口
     *
     * @return RedisPipeline
     */
    public RedisPipeline pipelined() {
        return redisStoreClient.pipelined();
    }

    /**
     * 获取指定 Key 的值
     *
     * @param key 要获取的 Key
     * @return Key 对应的值，如果 Key 不存在，返回 null
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     */
    public <T> T get(String category, String key) {
        return redisStoreClient.get(this.createStoreKey(category, key));
    }

    /**
     * 设置 Key 对应的值为 Value，如果 Key 不存在则添加，如果 Key 已经存在则覆盖
     *
     * @param key   要设置的 Key
     * @param value 要设置的 Value
     * @return 如果成功，返回 true<br>
     *         如果失败，返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public Boolean set(String category, String key, Object value) {
        return redisStoreClient.set(this.createStoreKey(category, key), value);
    }

    /**
     * 添加 Key 对应的值为 Value，只有当 Key 不存在时才添加，如果 Key 已经存在，不改变现有的值
     * {@link StoreClient#setnx(StoreKey, Object)}
     *
     * @param key   要添加的 Key
     * @param value 要添加的 Value
     * @return 如果 Key 不存在且添加成功，返回 true<br>
     *         如果 Key 已经存在，返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public Boolean add(String category, String key, Object value) {
        return redisStoreClient.add(this.createStoreKey(category, key), value);
    }

    /**
     * 添加 Key 对应的值为 Value，只有当 Key 不存在时才添加，如果 Key 已经存在，不改变现有的值
     * {@link StoreClient#add(StoreKey, Object)}
     *
     * @param key   要添加的 Key
     * @param value 要添加的 Value
     * @return 如果 Key 不存在且添加成功，返回 true<br>
     *         如果 Key 已经存在，返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public Boolean setnx(String category, String key, Object value) {
        return redisStoreClient.setnx(this.createStoreKey(category, key), value);
    }

    /**
     * 删除指定 Key
     *
     * @param key 要删除的 Key
     * @return 如果 Key 存在且被删除，返回 true<br>
     *         如果 Key 不存在，返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public Boolean delete(String category, String key) {
        return redisStoreClient.delete(this.createStoreKey(category, key));
    }

    /**
     * 异步获取指定 Key 的值
     *
     * @param key 要获取的 Key
     * @return 返回 Future 对象<br>
     *         如果操作成功，Future 返回 Key 对应的值<br>
     *         如果 Key 不存在，Future 返回 null
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     */
    public <T> Future<T> asyncGet(String category, String key) {
        return redisStoreClient.asyncGet(this.createStoreKey(category, key));
    }

    /**
     * 异步设置 Key 对应的值为 Value，如果 Key 不存在则添加，如果 Key 已经存在则覆盖
     *
     * @param key   要设置的 Key
     * @param value 要设置的 Value
     * @return 返回 Future 对象<br>
     *         如果操作成功，Future 返回 true<br>
     *         如果操作失败，Future 返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     */
    public Future<Boolean> asyncSet(String category, String key, Object value) {
        return redisStoreClient.asyncSet(this.createStoreKey(category, key), value);
    }

    /**
     * 异步添加 Key 对应的值为 Value，只有当 Key 不存在时才添加，如果 Key 已经存在，不改变现有的值
     *
     * @param key   要添加的 Key
     * @param value 要添加的 Value
     * @return 返回 Future 对象<br>
     *         如果 Key 不存在且添加成功，Future 返回 true<br>
     *         如果 Key 已经存在，Future 返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     */
    public Future<Boolean> asyncAdd(String category, String key, Object value) {
        return redisStoreClient.asyncAdd(this.createStoreKey(category, key), value);
    }

    /**
     * 异步删除指定 Key
     *
     * @param key 要删除的 Key
     * @return 返回 Future 对象<br>
     *         如果 Key 存在且被删除，Future 返回 true<br>
     *         如果 Key 不存在，Future 返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     */
    public Future<Boolean> asyncDelete(String category, String key) {
        return redisStoreClient.asyncDelete(this.createStoreKey(category, key));
    }

    /**
     * 异步获取指定 Key 的值
     *
     * @param key      要获取的 Key
     * @param callback 操作完成时的回调函数
     * @return 返回 null<br>
     *         如果操作成功，会调用 callback 的 onSuccess 方法，参数为 Key 对应的值<br>
     *         如果 Key 不存在，会调用 callback 的 onSuccess 方法，参数为 null<br>
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     */
    public <T> Void asyncGet(String category, String key, StoreCallback<T> callback) {
        return redisStoreClient.asyncGet(this.createStoreKey(category, key), callback);
    }

    /**
     * 异步设置 Key 对应的值为 Value，如果 Key 不存在则添加，如果 Key 已经存在则覆盖
     *
     * @param key      要设置的 Key
     * @param value    要设置的 Value
     * @param callback 操作完成时的回调函数
     * @return 返回 null<br>
     *         如果操作成功，会调用 callback 的 onSuccess 方法，参数为 true<br>
     *         如果操作失败，会调用 callback 的 onSuccess 方法，参数为 false<br>
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     */
    public Void asyncSet(String category, String key, Object value, StoreCallback<Boolean> callback) {
        return redisStoreClient.asyncSet(this.createStoreKey(category, key), value, callback);
    }

    /**
     * 异步添加 Key 对应的值为 Value，只有当 Key 不存在时才添加，如果 Key 已经存在，不改变现有的值
     *
     * @param key      要添加的 Key
     * @param value    要添加的 Value
     * @param callback 操作完成时的回调函数
     * @return 返回 null<br>
     *         如果 Key 不存在且添加成功，会调用 callback 的 onSuccess 方法，参数为 true<br>
     *         如果 Key 已经存在，会调用 callback 的 onSuccess 方法，参数为 false<br>
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     */
    public Void asyncAdd(String category, String key, Object value, StoreCallback<Boolean> callback) {
        return redisStoreClient.asyncAdd(this.createStoreKey(category, key), value, callback);
    }

    /**
     * 异步删除指定 Key
     *
     * @param key      要删除的 Key
     * @param callback 操作完成时的回调函数
     * @return 返回 null<br>
     *         如果 Key 存在且被删除，会调用 callback 的 onSuccess 方法，参数为 true<br>
     *         如果 Key 不存在，会调用 callback 的 onSuccess 方法，参数为 false<br>
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     */
    public Void asyncDelete(String category, String key, StoreCallback<Boolean> callback) {
        return redisStoreClient.asyncDelete(this.createStoreKey(category, key), callback);
    }

    /**
     * 增加指定 Key 的值
     * 注意 : redis中直接使用 increase 操作会创建一个不过期的key
     *
     * @param key    要增加的 Key
     * @param amount 要增加的值
     * @return 增加后的值，如果 Key 不存在，会创建这个 Key，且值为0，然后再增加
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public Long increase(String category, String key, int amount) {
        return redisStoreClient.increase(this.createStoreKey(category, key), amount);
    }

    /**
     * 减少指定 Key 的值
     * 注意 : redis中直接使用 decrease 操作会创建一个不过期的key
     *
     * @param key    要减少的 Key
     * @param amount 要减少的值
     * @return 减少后的值，如果 Key 不存在，会创建这个 Key，且值为0，然后再减少
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。</br>
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public Long decrease(String category, String key, int amount) {
        return redisStoreClient.decrease(this.createStoreKey(category, key), amount);
    }

    /**
     * 批量获取指定 Key 的值。默认的超时时间是100毫秒，默认的送达策略是 DeliverOption.BEST_EFFORT_DELIVER </br>
     * multiGet的使用请参考文档：
     *
     * @param keys 要获取的 Key 列表
     * @return 返回 Key <-> Value Map, 根据送达策略返回值
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     * @see <a href=
     *      "http://wiki.sankuai.com/pages/viewpage.action?pageId=528021922">multiGet使用文档</a>
     */
    public <T> Map<StoreKey, T> multiGet(List<StoreKey> keys) {
        return redisStoreClient.multiGet(keys);
    }

    /**
     * 批量获取指定 Key 的值。默认的超时时间是100毫秒，默认的送达策略是 DeliverOption.BEST_EFFORT_DELIVER </br>
     * multiGet的使用请参考文档：
     *
     * @param keys    要获取的 Key 列表
     * @param timeout 这组key的超时时间，区别于单个key设置的超时时间，单位是毫秒。
     * @return 返回 Key <-> Value Map, 根据送达策略返回值
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     * @see <a href=
     *      "http://wiki.sankuai.com/pages/viewpage.action?pageId=528021922">multiGet使用文档</a>
     */
    public <T> Map<StoreKey, T> multiGet(List<StoreKey> keys, long timeout) {
        return redisStoreClient.multiGet(keys, timeout);
    }

    /**
     * 批量获取指定 Key 的值。默认的超时时间是100毫秒，默认的送达策略是 DeliverOption.BEST_EFFORT_DELIVER </br>
     * multiGet的使用请参考文档：
     *
     * @param keys          要获取的 Key 列表
     * @param deliverOption 返回值的送达策略，可以要求是尽最大努力在超时时间内返回；也可以是要求必须全部返回。
     * @return 返回 Key <-> Value Map, 根据送达策略返回值
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     * @see <a href=
     *      "http://wiki.sankuai.com/pages/viewpage.action?pageId=528021922">multiGet使用文档</a>
     */
    public <T> Map<StoreKey, T> multiGet(List<StoreKey> keys, StoreClient.DeliverOption deliverOption) {
        return redisStoreClient.multiGet(keys, deliverOption);
    }

    /**
     * 批量获取指定 Key 的值。默认的超时时间是100毫秒，默认的送达策略是
     * {@link DeliverOption.BEST_EFFORT_DELIVER} </br>
     * multiGet的使用请参考文档：
     *
     * @param keys          要获取的 Key 列表
     * @param timeout       这组key的超时时间，区别于单个key设置的超时时间，单位是毫秒。
     * @param deliverOption 返回值的送达策略，可以要求是尽最大努力在超时时间内返回；也可以是要求必须全部返回。
     * @return 返回 Key <-> Value Map, 根据送达策略返回值
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     * @see <a href=
     *      "http://wiki.sankuai.com/pages/viewpage.action?pageId=528021922">multiGet使用文档</a>
     */
    public <T> Map<StoreKey, T> multiGet(List<StoreKey> keys, long timeout, StoreClient.DeliverOption deliverOption) {
        return redisStoreClient.multiGet(keys, timeout, deliverOption);
    }

    /**
     * 同 {@link StoreClient#multiGet(List, long, DeliverOption)}
     * 返回bytes结果的map
     *
     * @param keys          要获取的 Key 列表
     * @param timeout       这组key的超时时间，区别于单个key设置的超时时间，单位是毫秒。
     * @param deliverOption 返回值的送达策略，可以要求是尽最大努力在超时时间内返回；也可以是要求必须全部返回。
     * @return 返回 Key <-> Value Map, 根据送达策略返回值
     */
    public Map<StoreKey, byte[]> multiGetBytes(List<StoreKey> keys, long timeout,
            StoreClient.DeliverOption deliverOption) {
        return redisStoreClient.multiGetBytes(keys, timeout, deliverOption);
    }

    /**
     * 异步批量获取指定 Key 的值。默认的超时时间是100毫秒，默认的送达策略是 DeliverOption.BEST_EFFORT_DELIVER <br>
     * multiGet的使用请参考文档：
     *
     * @param keys     要获取的 Key 列表
     * @param callback 操作完成时的回调函数
     * @return 返回 null<br>
     *         如果操作成功，会调用 callback 的 onSuccess 方法，参数为 Key - Value Map
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     * @see <a href=
     *      "http://wiki.sankuai.com/pages/viewpage.action?pageId=528021922">multiGet使用文档</a>
     */
    public <T> Void asyncMultiGet(List<StoreKey> keys, StoreCallback<Map<StoreKey, T>> callback) {
        return redisStoreClient.asyncMultiGet(keys, callback);
    }

    public <T> Future<Map<StoreKey, T>> asyncMultiGet(List<StoreKey> keys) {
        return redisStoreClient.asyncMultiGet(keys);
    }

    /**
     * 异步批量获取指定 Key 的值
     *
     * @param keys     要获取的 Key 列表
     * @param callback 操作完成时的回调函数
     * @param timeout  这组key的超时时间，区别于单个key设置的超时时间，单位是毫秒。
     * @return 返回 null<br>
     *         如果操作成功，会调用 callback 的 onSuccess 方法，参数为 Key - Value Map
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     */
    public <T> Void asyncMultiGet(List<StoreKey> keys, StoreCallback<Map<StoreKey, T>> callback, long timeout) {
        return redisStoreClient.asyncMultiGet(keys, callback, timeout);
    }

    public <T> Future<Map<StoreKey, T>> asyncMultiGet(List<StoreKey> keys, long timeout) {
        return redisStoreClient.asyncMultiGet(keys, timeout);
    }

    /**
     * 异步批量获取指定 Key 的值
     *
     * @param keys          要获取的 Key 列表
     * @param callback      操作完成时的回调函数
     * @param deliverOption 返回值的送达策略，可以要求是尽最大努力在超时时间内返回；也可以是要求必须全部返回。
     * @return 返回 null
     *         如果操作成功，会调用 callback 的 onSuccess 方法，参数为 Key - Value Map
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     */
    public <T> Void asyncMultiGet(List<StoreKey> keys, StoreCallback<Map<StoreKey, T>> callback,
            StoreClient.DeliverOption deliverOption) {
        return redisStoreClient.asyncMultiGet(keys, callback, deliverOption);
    }

    public <T> Future<Map<StoreKey, T>> asyncMultiGet(List<StoreKey> keys, StoreClient.DeliverOption deliverOption) {
        return redisStoreClient.asyncMultiGet(keys, deliverOption);
    }

    /**
     * 异步批量获取指定 Key 的值
     *
     * @param keys          要获取的 Key 列表
     * @param callback      操作完成时的回调函数
     * @param timeout       这组key的超时时间，区别于单个key设置的超时时间，单位是毫秒。
     * @param deliverOption 返回值的送达策略，可以要求是尽最大努力在超时时间内返回；也可以是要求必须全部返回。
     * @return 返回 null
     *         如果操作成功，会调用 callback 的 onSuccess 方法，参数为 Key - Value Map
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     */
    public <T> Void asyncMultiGet(List<StoreKey> keys, StoreCallback<Map<StoreKey, T>> callback, long timeout,
            StoreClient.DeliverOption deliverOption) {
        return redisStoreClient.asyncMultiGet(keys, callback, timeout, deliverOption);
    }

    public <T> Future<Map<StoreKey, T>> asyncMultiGet(List<StoreKey> keys, long timeout,
            StoreClient.DeliverOption deliverOption) {
        return redisStoreClient.asyncMultiGet(keys, timeout, deliverOption);
    }

    /**
     * 批量设置指定的 Key 到指定的 Value,所有 key 的过期时间默认使用Key列表中第一个key 的category配置的过期时间
     *
     * @param keys   要设置的 Key 列表
     * @param values 要设置的 Value 列表
     * @return 如果操作成功，返回 true
     *         如果操作失败，返回 false
     * @throws com.dianping.squirrel.common.exception.StoreException 异常都是
     *                                                               StoreException
     *                                                               的子类且是
     *                                                               RuntimeException，可以根据需要捕获相应异常。
     *                                                               如：如果需要捕获超时异常，可以捕获
     *                                                               StoreTimeoutException
     */
    public <T> Boolean multiSet(List<StoreKey> keys, List<T> values) {
        return redisStoreClient.multiSet(keys, values);
    }

    /**
     * 同 {@link StoreClient#multiSet(List, List)} , 可自定义过期时间
     *
     * @param expireInSeconds 过期时间
     */
    public <T> Boolean multiSet(List<StoreKey> keys, List<T> values, int expireInSeconds) {
        return redisStoreClient.multiSet(keys, values, expireInSeconds);
    }

    /**
     * @param map             k-v map to be stored
     * @param expireInSeconds 过期时间, 单位 s, 小于 0 则不过期
     */
    public <T> Boolean multiSet(Map<StoreKey, T> map, int expireInSeconds) {
        return redisStoreClient.multiSet(map, expireInSeconds);
    }

    /**
     * @param map             k-v map to be stored
     * @param expireInSeconds 过期时间, 单位 s, 小于 0 则不过期
     */
    public Boolean multiSetBytes(Map<StoreKey, byte[]> map, int expireInSeconds) {
        return redisStoreClient.multiSetBytes(map, expireInSeconds);
    }

    /**
     * 异步批量设置指定的 Key 到指定的 Value, 所有 key 的过期时间默认使用Key列表中第一个key 的category配置的过期时间.
     *
     * @param keys     要设置的 Key 列表
     * @param values   要设置的 Value 列表
     * @param callback 操作完成时的回调函数
     * @return 返回 null
     *         如果操作成功，会调用 callback 的 onSuccess 方法，参数为 true<br>
     *         如果操作失败，会调用 callback 的 onSuccess 方法，参数为 false<br>
     *         如果发生异常，会调用 callback 的 onFailure 方法，参数为异常
     */
    public <T> Void asyncMultiSet(List<StoreKey> keys, List<T> values, StoreCallback<Boolean> callback) {
        return redisStoreClient.asyncMultiSet(keys, values, callback);
    }

    /**
     * 同 {@link StoreClient#asyncMultiSet(List, List, StoreCallback)} , 可自定义过期时间
     *
     * @param expireInSeconds 过期时间
     */
    public <T> Void asyncMultiSet(List<StoreKey> keys, List<T> values, StoreCallback<Boolean> callback,
            int expireInSeconds) {
        return redisStoreClient.asyncMultiSet(keys, values, callback, expireInSeconds);
    }

    /**
     * @param map             k-v pairs
     * @param callback        操作完成时的回调函数
     * @param expireInSeconds 过期时间
     */
    public <T> Void asyncMultiSet(Map<StoreKey, T> map, StoreCallback<Boolean> callback, int expireInSeconds) {
        return redisStoreClient.asyncMultiSet(map, callback, expireInSeconds);
    }

    /**
     * 批量删除key
     *
     * @param keys 待删除key列表
     * @return 删除key 的个数
     */
    public Long multiDelete(List<StoreKey> keys) {
        return redisStoreClient.multiDelete(keys);
    }

    /**
     * 批量删除异步回调接口
     *
     * @param keys     待删除key列表
     * @param callback 操作完成时的回调函数
     * @return
     */
    public Void asyncMultiDelete(List<StoreKey> keys, StoreCallback<Long> callback) {
        return redisStoreClient.asyncMultiDelete(keys, callback);
    }

    /**
     * 获取最终拼接而成的key
     *
     * @param category
     * @param key
     * @return 获取最终拼接而成的key
     */
    public String getFinalKey(String category, String key) {
        return redisStoreClient.getFinalKey(this.createStoreKey(category, key));
    }

    /**
     * @return 返回是否是分布式缓存，目前，除了ehcache之外，其余包括memcached,redis,dcache均是分布式缓存
     */
    public boolean isDistributed() {
        return redisStoreClient.isDistributed();
    }

    /**
     * @return 返回缓存类型
     */
    public String getScheme() {
        return redisStoreClient.getScheme();
    }

    /**
     * 将哈希表 key 中的域 field 的值设为 value。如果 key 不存在，一个新的哈希表被创建并进行 HSET 操作。<br>
     * 如果域 field 已经存在于哈希表中，旧值将被覆盖。<br>
     *
     * @param key   StoreKey
     * @param field map 中的field
     * @param value 需要设置的value
     * @return 如果 field 是哈希表中的一个新建域，并且值设置成功，返回 1 。
     *         如果哈希表中域 field 已经存在且旧值已被新值覆盖，返回 0 。
     */
    public Long hset(String category, String key, String field, Object value) {
        return redisStoreClient.hset(this.createStoreKey(category, key), field, value);
    }

    /**
     * 将哈希表 key 中的域 field 的值设为 value。如果 key 不存在，一个新的哈希表被创建并进行 HSET 操作。<br>
     * 如果域 field 已经存在于哈希表中，旧值将被覆盖。 并设置该hash过期时间<br>
     *
     * @param key             StoreKey
     * @param field           map 中的field
     * @param value           需要设置的value
     * @param expireInSeconds 设置整个 hash 的过期时间
     * @return 如果 field 是哈希表中的一个新建域，并且值设置成功，返回 1 。
     *         如果哈希表中域 field 已经存在且旧值已被新值覆盖，返回 0 。
     */
    public Long hset(String category, String key, String field, Object value, int expireInSeconds) {
        return redisStoreClient.hset(this.createStoreKey(category, key), field, value, expireInSeconds);
    }

    /**
     * 对比哈希表 key 中的域 field 的值,如果是expect 则更新为 update。
     *
     * @param key             StoreKey
     * @param field           map 中的field
     * @param expect          可以为 null, 代表该field不存在
     * @param update          满足条件则设置该值
     * @param expireInSeconds 该过期时间是作用于整个 hash ,而不是单个field
     * @return 如果更新成功则返回 true。 更新失败则返回 false;
     */
    public Boolean hcompareAndSet(String category, String key, String field, Object expect, Object update,
            int expireInSeconds) {
        return redisStoreClient.hcompareAndSet(this.createStoreKey(category, key), field, expect, update,
                expireInSeconds);
    }

    /**
     * 返回哈希表 key 中给定域 field 的值。
     *
     * @param key   StoreKey
     * @param field map 中的field
     * @return 给定域的值。当给定域不存在或是给定 key 不存在时，返回 nil 。
     */
    public <T> T hget(String category, String key, String field) {
        return redisStoreClient.hget(this.createStoreKey(category, key), field);
    }

    /**
     * 返回哈希表 key 中，一个或多个给定域的值。如果给定的域不存在于哈希表，那么返回一个 nil 值。 <br>
     * 因为不存在的 key 被当作一个空哈希表来处理，所以对一个不存在的 key 进行 HMGET 操作将返回一个只带有 nil 值的表。<br>
     *
     * @param key    StoreKey
     * @param fields map 中的field
     * @return 一个包含多个给定域的关联值的表，表值的排列顺序和给定域参数的请求顺序一样。
     */
    public <T> List<T> hmget(String category, String key, String... fields) {
        return redisStoreClient.hmget(this.createStoreKey(category, key), fields);
    }

    /**
     * 返回哈希表 key 中，一个或多个给定域的值.
     *
     * @param key    StoreKey
     * @param fields map 中的field
     * @return 一个包含多个给定域的关联值的Map
     */
    public <T> Map<String, T> hmget(String category, String key, List<String> fields) {
        return redisStoreClient.hmget(this.createStoreKey(category, key), fields);
    }

    /**
     * 同时将多个 field-value (域-值)对设置到哈希表 key 中。此命令会覆盖哈希表中已存在的域。
     * 如果 key 不存在，一个空哈希表被创建并执行 HMSET 操作。
     *
     * @param key      StoreKey
     * @param valueMap map 中的field
     * @return 如果命令执行成功，返回true. 否则,返回false。
     */
    public <T> Boolean hmset(String category, String key, Map<String, T> valueMap) {
        return redisStoreClient.hmset(this.createStoreKey(category, key), valueMap);
    }

    /**
     * 删除哈希表 key 中的一个或多个指定域，不存在的域将被忽略。
     *
     * @param key   StoreKey
     * @param field map 中的field
     * @return 被成功移除的域的数量，不包括被忽略的域。
     */
    public Long hdel(String category, String key, String... field) {
        return redisStoreClient.hdel(this.createStoreKey(category, key), field);
    }

    /**
     * 返回哈希表 key 中的所有域。
     *
     * @param key StoreKey
     * @return 一个包含哈希表中所有域的表。当 key 不存在时，返回一个空表。
     */
    public Set<String> hkeys(String category, String key) {
        return redisStoreClient.hkeys(this.createStoreKey(category, key));
    }

    /**
     * 返回哈希表 key 中所有域的值。
     *
     * @param key StoreKey
     * @return 一个包含哈希表中所有值的表。当 key 不存在时，返回一个空表。
     */
    public <T> List<T> hvals(String category, String key) {
        return redisStoreClient.hvals(this.createStoreKey(category, key));
    }

    /**
     * 返回哈希表 key 中，所有的域和值。
     *
     * @param key StoreKey
     * @return 返回整个Map
     */
    public <T> Map<String, T> hgetAll(String category, String key) {
        return redisStoreClient.hgetAll(this.createStoreKey(category, key));
    }

    /**
     * 返回哈希表 key 中，所有的域和值。
     * 利用 hscan 实现的 hgetall , 每次 scan 100个字段
     *
     * @param key StoreKey
     * @return 返回Map
     */
    public <T> Map<String, T> hgetAllByScan(String category, String key) {
        return redisStoreClient.hgetAllByScan(this.createStoreKey(category, key));
    }

    /**
     * 返回哈希表 key 中，所有的域和值。
     * 利用 hscan 实现的 hgetall , 每次 scan 个数由 count 决定
     *
     * @param key   StoreKey
     * @param count 每次scan 哈希表中键值对的个数
     * @return
     */
    public <T> Map<String, T> hgetAllByScan(String category, String key, int count) {
        return redisStoreClient.hgetAllByScan(this.createStoreKey(category, key), count);
    }

    /**
     * 为哈希表 key 中的域 field 的值加上增量 increment 。增量也可以为负数，相当于对给定域进行减法操作。 <br>
     * 如果 key 不存在，一个新的哈希表被创建并执行 HINCRBY 命令。如果域 field 不存在，那么在执行命令前，域的值被初始化为 0 。<br>
     * 对一个储存字符串值的域 field 执行 HINCRBY 命令将造成一个错误。本操作的值被限制在 64 位(bit)有符号数字表示之内。<br>
     *
     * @param key    StoreKey
     * @param field  map 中的field
     * @param amount 增加的值
     * @return执行 HINCRBY 命令之后，哈希表 key 中域 field 的值。
     */
    public Long hincrBy(String category, String key, String field, int amount) {
        return redisStoreClient.hincrBy(this.createStoreKey(category, key), field, amount);
    }

    /**
     * 将哈希表 key 中的域 field 的值设置为 value ，当且仅当域 field 不存在。 <br>
     * 若域 field 已经存在，该操作无效。如果 key 不存在，一个新哈希表被创建并执行 HSETNX 命令。 <br>
     *
     * @param key   StoreKey
     * @param field map 中的field
     * @param value value
     * @return 设置成功，返回 true 。如果给定域已经存在且没有操作被执行，返回 false 。
     */
    public Boolean hsetnx(String category, String key, String field, Object value) {
        return redisStoreClient.hsetnx(this.createStoreKey(category, key), field, value);
    }

    /**
     * 返回哈希表 key 中域的数量。
     *
     * @param key StoreKey
     * @return 哈希表中域的数量。当 key 不存在时，返回 0 。
     */
    public Long hlen(String category, String key) {
        return redisStoreClient.hlen(this.createStoreKey(category, key));
    }

    /**
     * 查看哈希表 key 中，给定域 field 是否存在。
     *
     * @param key   StoreKey
     * @param field map 中的field
     * @return 如果哈希表含有给定域，返回true。如果哈希表不含有给定域，或 key 不存在，返回false。
     */
    public Boolean hExists(String category, String key, String field) {
        return redisStoreClient.hExists(this.createStoreKey(category, key), field);
    }

    /**
     * redis 中hscan 命令,迭代哈希键中的键值对
     *
     * @param key          需要遍历的 StoreKey
     * @param fieldPattern 需要匹配的 hash 中的 field 模式, null 或者 * 代表全部匹配
     * @return 返回遍历所得的 field-value
     */
    public <T> Iterable<Map<String, T>> hscan(String category, String key, String fieldPattern) {
        return redisStoreClient.hscan(this.createStoreKey(category, key), fieldPattern);
    }

    /**
     * 将任意数量的元素添加到指定的 HyperLogLog 里面。
     *
     * @param storeKey HyperLogLog 键
     * @param elements 任意字符串元素
     * @return 如果 HyperLogLog 的内部储存被修改了， 那么返回 true， 否则返回 false。
     */
    public Boolean pfadd(String category, String key, String... elements) {
        return redisStoreClient.pfadd(this.createStoreKey(category, key), elements);
    }

    /**
     * 将任意数量的元素添加到指定的 HyperLogLog 里面。
     *
     * @param storeKey HyperLogLog 键
     * @param expire   当key不存在时,设置初始过期时间
     * @param elements 任意字符串元素
     * @return 如果 HyperLogLog 的内部储存被修改了， 那么返回 true， 否则返回 false。
     */
    public Boolean pfadd(String category, String key, int expire, String... elements) {
        return redisStoreClient.pfadd(this.createStoreKey(category, key), expire, elements);
    }

    /**
     * 返回储存在给定键的 HyperLogLog 的近似基数， 如果键不存在， 那么返回 0 。
     *
     * @param storeKey HyperLogLog
     * @return 返回给定 HyperLogLog 的近似基数,
     */
    public Long pfcount(String category, String key) {
        return redisStoreClient.pfcount(this.createStoreKey(category, key));
    }

    /**
     * * 指定某个category进行scan，返回一个用来遍历所有与给定模式相匹配的key的迭代器。
     *
     * @param patternKey 用来匹配key的正则表达式。其中模板参数可以填 * 来匹配,
     *                   (eg, new StoreKey(category, "*") : 匹配该category下所有key
     *                   new StoreKey(category,"abc*") : 匹配模板参数以abc开头的key
     *                   ，最后由StoreKey来生成redis的pattern
     * @param count      count取值范围为{0,100}，表示每次迭代返回的key的数目
     * @return 返回 scan 迭代器
     */
    public Iterable<List<StoreKey>> scan(String category, String key, int count) {
        return redisStoreClient.scan(this.createStoreKey(category, key), count);
    }

    /**
     * 将一个或多个值 value 插入到列表 key 的表尾(最右边)。如果有多个 value 值， 那么各个 value<br>
     * 值按从左到右的顺序依次插入到表尾：比如对一个空列表 mylist 执行 RPUSH mylist a b c,得出的结果列表为 a b c<br>
     * 等同于执行命令 RPUSH mylist a 、 RPUSH mylist b 、 RPUSH mylist c 。 <br>
     * 如果 key 不存在，一个空列表会被创建并执行 RPUSH 操作。当 key 存在但不是列表类型时，返回一个错误。<br>
     *
     * @return 执行 RPUSH 操作后，表的长度。
     */
    public Long rpush(String category, String key, Object... value) {
        return redisStoreClient.rpush(this.createStoreKey(category, key), value);
    }

    /**
     * 将一个或多个值 value 插入到列表 key 的表头。如果有多个 value 值，<br>
     * 那么各个 value 值按从左到右的顺序依次插入到表头： 比如说，对空列表 mylist 执行命令 LPUSH mylist a b c <br>
     * 列表的值将是 c b a ，这等同于原子性地执行 LPUSH mylist a、LPUSH mylist b和LPUSH mylist c
     * 三个命令。<br>
     * 如果 key 不存在，一个空列表会被创建并执行 LPUSH 操作。当 key 存在但不是列表类型时，返回一个错误。<br>
     *
     * @return 执行 LPUSH 命令后，列表的长度。
     */
    public Long lpush(String category, String key, Object... value) {
        return redisStoreClient.lpush(this.createStoreKey(category, key), value);
    }

    /**
     * 移除并返回列表 key 的头元素。
     *
     * @return 列表的头元素。当 key 不存在时，返回 nil 。
     */
    public <T> T lpop(String category, String key) {
        return redisStoreClient.lpop(this.createStoreKey(category, key));
    }

    /**
     * LPOP 命令的阻塞版本，当给定列表内没有任何元素可供弹出的时候，连接将被 BLPOP 命令阻塞，直到等待超时或发现可弹出元素为止。<br>
     *
     * @param timeoutInSeconds 等待超时时间, 单位 秒 . (最大可设置等待时长 5 分钟)
     * @return 如果队列有元素, 则返回列表的头元素, 当 key 不存在时并且等待超过 timeoutInSeconds 返回 nil
     */
    public <T> T blpop(String category, String key, int timeoutInSeconds) {
        return redisStoreClient.blpop(this.createStoreKey(category, key), timeoutInSeconds);
    }

    /**
     * 移除并返回列表 key 的尾元素。
     *
     * @return 列表的尾元素。当 key 不存在时，返回 nil 。
     */
    public <T> T rpop(String category, String key) {
        return redisStoreClient.rpop(this.createStoreKey(category, key));
    }

    /**
     * RPOP 命令的阻塞版本，当给定列表内没有任何元素可供弹出的时候，连接将被 BRPOP 命令阻塞，直到等待超时或发现可弹出元素为止。<br>
     *
     * @param timeoutInSeconds 等待超时时间, 单位 秒 . (最大可设置等待时长 5 分钟)
     * @return 如果队列有元素, 则返回列表的尾元素, 当 key 不存在时并且等待超过 timeoutInSeconds 返回 nil
     */
    public <T> T brpop(String category, String key, int timeoutInSeconds) {
        return redisStoreClient.brpop(this.createStoreKey(category, key), timeoutInSeconds);
    }

    /**
     * 返回列表 key 中，下标为 index 的元素。下标(index)参数 start 和 stop 都以 0 为底，<br>
     * 也就是说，以 0 表示列表的第一个元素，以 1 表示列表的第二个元素，以此类推。<br>
     * 你也可以使用负数下标，以 -1 表示列表的最后一个元素， -2 表示列表的倒数第二个元素，以此类推。<br>
     * 如果 key 不是列表类型，返回一个错误。<br>
     *
     * @return 列表中下标为 index 的元素。如果 index 参数的值不在列表的区间范围内(out of range)，返回 nil 。
     */
    public <T> T lindex(String category, String key, long index) {
        return redisStoreClient.lindex(this.createStoreKey(category, key), index);
    }

    /**
     * 将列表 key 下标为 index 的元素的值设置为 value 。<br>
     * 当 index 参数超出范围，或对一个空列表( key 不存在)进行 LSET 时，返回错误。 <br>
     *
     * @return 操作成功返回 true ，操作失败返回false，返回错误是抛出异常。
     */
    public Boolean lset(String category, String key, long index, Object value) {
        return redisStoreClient.lset(this.createStoreKey(category, key), index, value);
    }

    /**
     * 返回列表 key 的长度。如果 key 不存在，则 key 被解释为一个空列表，返回 0 .
     * 如果 key 不是列表类型，返回一个错误。
     *
     * @return 列表 key 的长度。当发生错误时，抛出异常
     */
    public Long llen(String category, String key) {
        return redisStoreClient.llen(this.createStoreKey(category, key));
    }

    /**
     * 返回列表 key 中指定区间内的元素，区间以偏移量 start 和 stop 指定。 <br>
     * 下标(index)参数 start 和 stop 都以 0 为底，也就是说，以 0 表示列表的第一个元素，以 1 表示列表的第二个元素，以此类推。<br>
     * 你也可以使用负数下标，以 -1 表示列表的最后一个元素， -2 表示列表的倒数第二个元素，以此类推。<br>
     *
     * @param start 起始位置
     * @param end   结束位置
     * @return 一个列表，包含指定区间内的元素。
     */
    public <T> List<T> lrange(String category, String key, long start, long end) {
        return redisStoreClient.lrange(this.createStoreKey(category, key), start, end);
    }

    /**
     * 对一个列表进行修剪(trim)，就是说，让列表只保留指定区间内的元素，不在指定区间之内的元素都将被删除。
     * 当 key 不是列表类型时，返回一个错误。
     *
     * @param start 起始位置,下标以 0 表示第一个元素
     * @param end   结束位置
     * @return 命令执行成功时，返回true，否则返回false。如果发生错误抛出异常。
     */
    public Boolean ltrim(String category, String key, long start, long end) {
        return redisStoreClient.ltrim(this.createStoreKey(category, key), start, end);
    }

    /**
     * 根据参数 count 的值，移除列表中与参数 value 相等的元素。
     * count 的值可以是以下几种：
     * count > 0 : 从表头开始向表尾搜索，移除与 value 相等的元素，数量为 count 。
     * count < 0 : 从表尾开始向表头搜索，移除与 value 相等的元素，数量为 count 的绝对值。
     * count = 0 : 移除表中所有与 value 相等的值。
     *
     * @param count 需要移除元素的个数
     * @param value 需要移除的元素值
     * @return 被移除元素的数量。因为不存在的 key 被视作空表(empty list)，所以当 key 不存在时， LREM 命令总是返回 0 。
     */
    public Long lrem(String category, String key, long count, Object value) {
        return redisStoreClient.lrem(this.createStoreKey(category, key), count, value);
    }

    /**
     * 将值 value 插入到列表 key 的表头，当且仅当 key 存在并且是一个列表。
     * 和 LPUSH 命令相反，当 key 不存在时， LPUSHX 命令什么也不做。
     *
     * @return LPUSHX 命令执行之后，表的长度。
     */
    public Long lpushx(String category, String key, Object string) {
        return redisStoreClient.lpushx(this.createStoreKey(category, key), string);
    }

    /**
     * 将值 value 插入到列表 key 的表尾，当且仅当 key 存在并且是一个列表。
     * 和 RPUSH 命令相反，当 key 不存在时， RPUSHX 命令什么也不做。
     *
     * @return RPUSHX 命令执行之后，表的长度。
     */
    public Long rpushx(String category, String key, Object string) {
        return redisStoreClient.rpushx(this.createStoreKey(category, key), string);
    }

    /**
     * 将一个或多个 member 元素加入到集合 key 当中，已经存在于集合的 member 元素将被忽略。 <br>
     * 假如 key 不存在，则创建一个只包含 member 元素作成员的集合。当 key 不是集合类型时，返回一个错误。<br>
     *
     * @param key    StoreKey
     * @param member 需要添加的member (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及
     *               hashcode 方法)
     * @return 被添加到集合中的新元素的数量，不包括被忽略的元素。
     */
    public Long sadd(String category, String key, Object... member) {
        return redisStoreClient.sadd(this.createStoreKey(category, key), member);
    }

    /**
     * {@link RedisSetCommands#sadd(StoreKey, Object...)} 每次只添加一个member, 可设置该set
     * 的过期时间
     *
     * @param key             StoreKey
     * @param member          需要添加的member
     * @param expireInSeconds 可设置过期时间, 每次调用都会更新
     * @return
     */
    public Long sadd(String category, String key, Object member, int expireInSeconds) {
        return redisStoreClient.sadd(this.createStoreKey(category, key), member, expireInSeconds);
    }

    /**
     * 移除集合 key 中的一个或多个 member 元素，不存在的 member 元素会被忽略。当 key 不是集合类型，返回一个错误。
     *
     * @param key    StoreKey
     * @param member (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及 hashcode 方法)
     * @return 被成功移除的元素的数量，不包括被忽略的元素。
     */
    public Long srem(String category, String key, Object... member) {
        return redisStoreClient.srem(this.createStoreKey(category, key), member);
    }

    /**
     * 返回集合 key 中的所有成员。不存在的 key 被视为空集合。
     *
     * @param key StoreKey
     * @return 集合中的所有成员。
     */
    public <T> Set<T> smembers(String category, String key) {
        return redisStoreClient.smembers(this.createStoreKey(category, key));
    }

    /**
     * 返回集合 key 的基数(集合中元素的数量)。
     *
     * @param key StoreKey
     * @return 集合的基数。当 key 不存在时，返回 0 。
     */
    public Long scard(String category, String key) {
        return redisStoreClient.scard(this.createStoreKey(category, key));
    }

    /**
     * 判断 member 元素是否集合 key 的成员。
     *
     * @param key    StoreKey
     * @param member (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及 hashcode 方法)
     * @return 如果 member 元素是集合的成员，返回true。如果 member 元素不是集合的成员，或 key 不存在，返回false。
     */
    public Boolean sismember(String category, String key, Object member) {
        return redisStoreClient.sismember(this.createStoreKey(category, key), member);
    }

    /**
     * 移除并返回集合中的一个随机元素。
     * 如果只想获取一个随机元素，但不想该元素从集合中被移除的话，可以使用 SRANDMEMBER 命令。
     *
     * @param key StoreKey
     * @return 被移除的随机元素。当 key 不存在或 key 是空集时，返回 nil 。
     */
    public <T> T spop(String category, String key) {
        return redisStoreClient.spop(this.createStoreKey(category, key));
    }

    /**
     * 返回集合中的一个随机元素。
     *
     * @param key StoreKey
     * @return 只提供 key 参数时，返回一个元素；如果集合为空，返回 nil 。
     */
    public <T> T srandmember(String category, String key) {
        return redisStoreClient.srandmember(this.createStoreKey(category, key));
    }

    /**
     * 如果count 为正数，且小于集合基数，那么命令返回一个包含 count 个元素的数组，
     * 数组中的元素各不相同。如果 count 大于等于集合基数，那么返回整个集合。
     * 如果 count 为负数，那么命令返回一个数组，数组中的元素可能会重复出现多次，而数组的长度为 count 的绝对值。
     *
     * @param key   StoreKey
     * @param count 最多返回元素个数
     * @return 那么返回一个数组；如果集合为空，返回空数组。
     */
    public <T> List<T> srandmember(String category, String key, int count) {
        return redisStoreClient.srandmember(this.createStoreKey(category, key), count);
    }

    /**
     * 将一个 member 元素及其 score 值加入到有序集 key 当中。 <br>
     * 如果某个 member 已经是有序集的成员，那么更新这个 member 的 score 值，并通过重新插入这个 member 元素，来保证该 member
     * 在正确的位置上。<br>
     * score 值可以是整数值或双精度浮点数。如果 key 不存在，则创建一个空的有序集并执行 ZADD 操作。当 key
     * 存在但不是有序集类型时，抛出一个异常。 <br>
     *
     * @param key    StoreKey
     * @param score  score值
     * @param member 对象 (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及 hashcode 方法)
     * @return 被成功添加的新成员的数量，不包括那些被更新的、已经存在的成员。
     */
    public Long zadd(String category, String key, double score, Object member) {
        return redisStoreClient.zadd(this.createStoreKey(category, key), score, member);
    }

    public Long zadd(String category, String key, double score, Object member, int expireInSeconds) {
        return redisStoreClient.zadd(this.createStoreKey(category, key), score, member, expireInSeconds);
    }

    /**
     * 将 多个 member 元素及其 score 值加入到有序集 key 当中。 <br>
     * 如果某个 member 已经是有序集的成员，那么更新这个 member 的 score 值，并通过重新插入这个 member 元素，来保证该 member
     * 在正确的位置上。<br>
     * score 值可以是整数值或双精度浮点数。如果 key 不存在，则创建一个空的有序集并执行 ZADD 操作。当 key
     * 存在但不是有序集类型时，抛出一个异常。 <br>
     *
     * @param key StoreKey
     * @return 被成功添加的新成员的数量，不包括那些被更新的、已经存在的成员。
     */
    public <T> Long zadd(String category, String key, Map<T, Double> scoreMembers) {
        return redisStoreClient.zadd(this.createStoreKey(category, key), scoreMembers);
    }

    /**
     * 移除有序集 key 中的一个或多个成员，不存在的成员将被忽略。当 key 存在但不是有序集类型时，抛出一个异常。
     *
     * @param key     StoreKey
     * @param members (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及 hashcode 方法)
     * @return 被成功移除的成员的数量，不包括被忽略的成员。
     */
    public Long zrem(String category, String key, Object... members) {
        return redisStoreClient.zrem(this.createStoreKey(category, key), members);
    }

    /**
     * 为有序集 key 的成员 member 的 score 值加上增量 increment 。<br>
     * 可以通过传递一个负数值 increment ，让 score 减去相应的值，比如 ZINCRBY key -5 member ，就是让 member 的
     * score 值减去 5 。<br>
     * 当 key 不存在，或 member 不是 key 的成员时， ZINCRBY key increment member 等同于 ZADD key
     * increment member 。<br>
     * 当 key 不是有序集类型时，抛出一个异常。score 值可以是整数值或双精度浮点数。<br>
     *
     * @param key    StoreKey
     * @param score  score值
     * @param member (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及 hashcode 方法)
     * @return member 成员的新 score 值，以字符串形式表示。
     */
    public Double zincrby(String category, String key, double score, Object member) {
        return redisStoreClient.zincrby(this.createStoreKey(category, key), score, member);
    }

    /**
     * 返回有序集 key 中成员 member 的排名。其中有序集成员按 score 值递增(从小到大)顺序排列。<br>
     * 排名以 0 为底，也就是说， score 值最小的成员排名为 0 。<br>
     * 使用 ZREVRANK 命令可以获得成员按 score 值递减(从大到小)排列的排名。<br>
     *
     * @param key    StoreKey
     * @param member member (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及 hashcode
     *               方法)
     * @return 如果 member 是有序集 key 的成员，返回 member 的排名。如果 member 不是有序集 key 的成员，返回null 。
     */
    public Long zrank(String category, String key, Object member) {
        return redisStoreClient.zrank(this.createStoreKey(category, key), member);
    }

    /**
     * 返回有序集 key 中成员 member 的排名。其中有序集成员按 score 值递减(从大到小)排序。<br>
     * 排名以 0 为底，也就是说， score 值最大的成员排名为 0 。<br>
     * 使用 ZRANK 命令可以获得成员按 score 值递增(从小到大)排列的排名。<br>
     *
     * @param key    StoreKey
     * @param member member (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及 hashcode
     *               方法)
     * @return 如果 member 是有序集 key 的成员，返回 member 的排名。 如果 member 不是有序集 key 的成员，返回 nil
     *         。
     */
    public Long zrevrank(String category, String key, Object member) {
        return redisStoreClient.zrevrank(this.createStoreKey(category, key), member);
    }

    /**
     * 返回有序集 key 的基数。当 key 存在且是有序集类型时，返回有序集的基数。当 key 不存在时，返回 0 。
     *
     * @param key StoreKey
     * @return 集合基数
     */
    public Long zcard(String category, String key) {
        return redisStoreClient.zcard(this.createStoreKey(category, key));
    }

    /**
     * 返回有序集 key 中，成员 member 的 score 值。果 member 元素不是有序集 key 的成员，或 key 不存在，返回 null 。
     *
     * @param key    StoreKey
     * @param member (注意: member 是否相同是判断序列化后的值,而不是根据对象的 equals 以及 hashcode 方法)
     * @return member 成员的 score 值
     */
    public Double zscore(String category, String key, Object member) {
        return redisStoreClient.zscore(this.createStoreKey(category, key), member);
    }

    /**
     * 返回有序集 key 中， score 值在 min 和 max 之间(默认包括 score 值等于 min 或 max )的成员的数量。<br>
     * 于参数 min 和 max 的详细使用方法，请参考 ZRANGEBYSCORE 命令。<br>
     *
     * @param key StoreKey
     * @param min 较小的 score
     * @param max 较大的 score
     * @return score 值在 min 和 max 之间的成员的数量
     */
    public Long zcount(String category, String key, double min, double max) {
        return redisStoreClient.zcount(this.createStoreKey(category, key), min, max);
    }

    /**
     * 返回有序集 key 中，指定区间内的成员。其中成员的位置按 score 值递增(从小到大)来排序。<br>
     * 具有相同 score 值的成员按字典序(lexicographical biz_data )来排列。如果你需要成员按 score
     * 值递减(从大到小)来排列，请使用 ZREVRANGE 命令。<br>
     * 下标参数 start 和 stop 都以 0 为底，也就是说，以 0 表示有序集第一个成员，以 1 表示有序集第二个成员，以此类推。<br>
     * 你也可以使用负数下标，以 -1 表示最后一个成员， -2 表示倒数第二个成员，以此类推。<br>
     * 超出范围的下标并不会引起错误。
     *
     * @param key   StoreKey
     * @param start 区间起始位置
     * @param end   区间结束位置
     * @return 指定区间内的有序集成员的列表。
     */
    public <T> Set<T> zrange(String category, String key, long start, long end) {
        return redisStoreClient.zrange(this.createStoreKey(category, key), start, end);
    }

    /**
     * 同 {@link #zrange(StoreKey, long, long)}
     *
     * @return 指定区间内，带有 score 的有序集成员的列表。
     */
    public Set<StoreTuple> zrangeWithScore(String category, String key, long start, long end) {
        return redisStoreClient.zrangeWithScore(this.createStoreKey(category, key), start, end);
    }

    /**
     * 返回有序集 key 中，所有 score 值介于 min 和 max 之间(包括等于 min 或 max )的成员。有序集成员按 score
     * 值递增(从小到大)次序排列。<br>
     * 具有相同 score 值的成员按字典序(lexicographical biz_data)来排列(该属性是有序集提供的，不需要额外的计算)。<br>
     * 可选的 LIMIT 参数指定返回结果的数量及区间(就像SQL中的 SELECT LIMIT offset, count )，注意当 offset
     * 很大时，定位 offset 的操作<br>
     * 可能需要遍历整个有序集，此过程最坏复杂度为 O(N) 时间。<br>
     * 可选的 WITHSCORES 参数决定结果集是单单返回有序集的成员，还是将有序集成员及其 score 值一起返回。<br>
     *
     * @param key StoreKey
     * @param min score 较小值
     * @param max score 较大值
     * @return 指定区间内的有序集成员的列表
     */
    public <T> Set<T> zrangeByScore(String category, String key, double min, double max) {
        return redisStoreClient.zrangeByScore(this.createStoreKey(category, key), min, max);
    }

    /**
     * 同 {@link #zrangeByScore(StoreKey, double, double)}
     *
     * @return 指定区间内，带有 score 值的有序集成员的列表
     */
    public Set<StoreTuple> zrangeByScoreWithScore(String category, String key, double min, double max) {
        return redisStoreClient.zrangeByScoreWithScore(this.createStoreKey(category, key), min, max);
    }

    /**
     * 同 {@link #zrangeByScore(StoreKey, double, double)}
     *
     * @param offset 限制结果集合中的开始位置
     * @param count  返回的个数限制
     * @return 指定区间内的有序集成员的列表
     */
    public <T> Set<T> zrangeByScore(String category, String key, double min, double max, int offset, int count) {
        return redisStoreClient.zrangeByScore(this.createStoreKey(category, key), min, max, offset, count);
    }

    /**
     * 返回有序集 key 中，指定区间内的成员。其中成员的位置按 score 值递减(从大到小)来排列。<br>
     * 具有相同 score 值的成员按字典序的逆序(reverse lexicographical biz_data)排列。<br>
     * 除了成员按 score 值递减的次序排列这一点外， ZREVRANGE 命令的其他方面和 ZRANGE 命令一样。<br>
     *
     * @param key   StoreKey
     * @param start 区间起始位置
     * @param end   区间结束位置
     * @return 指定区间内的有序集成员的列表
     */
    public <T> Set<T> zrevrange(String category, String key, long start, long end) {
        return redisStoreClient.zrevrange(this.createStoreKey(category, key), start, end);
    }

    /**
     * 同{@link #zrevrange(StoreKey, long, long)}
     *
     * @return 指定区间内，带有 score 值的有序集成员的列表
     */
    public Set<StoreTuple> zrevrangeWithScore(String category, String key, long start, long end) {
        return redisStoreClient.zrevrangeWithScore(this.createStoreKey(category, key), start, end);
    }

    /**
     * 返回有序集 key 中， score 值介于 max 和 min 之间(默认包括等于 max 或 min )的所有的成员。有序集成员按 score
     * 值递减(从大到小)的次序排列。<br>
     * 具有相同 score 值的成员按字典序的逆序(reverse lexicographical biz_data )排列。<br>
     * 除了成员按 score 值递减的次序排列这一点外， ZREVRANGEBYSCORE 命令的其他方面和 ZRANGEBYSCORE 命令一样。<br>
     *
     * @param key StoreKey
     * @param max 分数区间较大值
     * @param min 分数区间较小值
     * @return 指定区间内的有序集成员的列表
     */
    public <T> Set<T> zrevrangeByScore(String category, String key, double max, double min) {
        return redisStoreClient.zrevrangeByScore(this.createStoreKey(category, key), max, min);
    }

    /**
     * 同 {@link #zrevrangeByScore(StoreKey, double, double)}
     *
     * @param offset 指定返回集合的起始位置
     * @param count  限制返回个数
     * @return 指定区间内的有序集成员的列表
     */
    public <T> Set<T> zrevrangeByScore(String category, String key, double max, double min, int offset, int count) {
        return redisStoreClient.zrevrangeByScore(this.createStoreKey(category, key), max, min, offset, count);
    }

    /**
     * 移除有序集 key 中，指定排名(rank)区间内的所有成员。区间分别以下标参数 start 和 stop 指出，包含 start 和 stop
     * 在内。<br>
     * 下标参数 start 和 stop 都以 0 为底，也就是说，以 0 表示有序集第一个成员，以 1 表示有序集第二个成员，以此类推。<br>
     * 你也可以使用负数下标，以 -1 表示最后一个成员， -2 表示倒数第二个成员，以此类推。<br>
     *
     * @param key   StoreKey
     * @param start 排名起始位置
     * @param end   排名结束位置
     * @return 被移除成员的数量。
     */
    public Long zremrangeByRank(String category, String key, long start, long end) {
        return redisStoreClient.zremrangeByRank(this.createStoreKey(category, key), start, end);
    }

    /**
     * 移除有序集 key 中，所有 score 值介于 start 和 end 之间(包括等于 start 或 end )的成员。
     *
     * @param key   StoreKey
     * @param start score起始区间值
     * @param end   score结束区间值
     * @return 被移除成员的数量。
     */
    public Long zremrangeByScore(String category, String key, double start, double end) {
        return redisStoreClient.zremrangeByScore(this.createStoreKey(category, key), start, end);
    }

    /**
     * scan zset 集合中的元素
     *
     * @param key    StoreKey
     * @param cursor 游标
     * @return 返回集合中的部分结果
     */
    public StoreScanResult<StoreTuple> zscan(String category, String key, String cursor) {
        return redisStoreClient.zscan(this.createStoreKey(category, key), cursor);
    }

    public String locate(String category, String key) {
        return redisStoreClient.locate(this.createStoreKey(category, key));
    }

    public String locate(String finalKey) {
        return redisStoreClient.locate(finalKey);
    }
}
