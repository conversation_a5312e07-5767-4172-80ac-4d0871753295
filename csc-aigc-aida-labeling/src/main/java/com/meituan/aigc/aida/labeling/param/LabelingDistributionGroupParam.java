package com.meituan.aigc.aida.labeling.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-06 16:20
 * @description 标注任务分配入参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelingDistributionGroupParam implements Serializable {

    /**
     * 父分组ID(复标或部分回收的时候传入)
     */
    private Long parentId;
    /**
     * 任务id
     */
    @NotNull(message = "任务id不能为空")
    private Long taskId;
    /**
     * 分配的数量
     */
    @NotNull(message = "分配数量不能为空")
    private Integer dataCount;
    /**
     *  key是标注人mis，value是标注人姓名
     */
    @NotNull(message = "分配标注人不能为空")
    private Map<String, String> labelers;

}
