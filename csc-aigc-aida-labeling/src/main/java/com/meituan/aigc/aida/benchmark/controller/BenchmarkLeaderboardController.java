package com.meituan.aigc.aida.benchmark.controller;

import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkLeaderboardRankingVO;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkModelDetailVO;
import com.meituan.aigc.aida.benchmark.service.LeaderboardService;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Benchmark排行榜Controller
 *
 * <AUTHOR>
 * @description Benchmark排行榜相关接口
 * @date 2025/6/24
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1/benchmark/leaderboard")
public class BenchmarkLeaderboardController {

    @Resource
    private LeaderboardService leaderboardService;

    /**
     * 获取排行榜排名列表
     *
     * @param versionId benchmark版本ID
     * @return 排行榜排名数据
     */
    @GetMapping("/ranking/list")
    public Result<BenchmarkLeaderboardRankingVO> getRankingList(@RequestParam("versionId") Long versionId) {
        log.info("接收到获取排行榜排名列表请求，版本ID: {}", versionId);

        BenchmarkLeaderboardRankingVO result = leaderboardService.getRankingList(versionId);

        log.info("排行榜排名列表查询完成，版本ID: {}, 模型数量: {}", versionId, result.getTotal());
        return Result.ok(result);
    }

    /**
     * 获取模型详细评测结果
     *
     * @param versionId benchmark版本ID
     * @param modelName 模型名称
     * @return 模型详细评测结果
     */
    @GetMapping("/ranking/detail")
    public Result<BenchmarkModelDetailVO> getModelDetail(@RequestParam("versionId") Long versionId,
                                                         @RequestParam("modelName") String modelName) {
        log.info("接收到获取模型详细评测结果请求，版本ID: {}, 模型名称: {}", versionId, modelName);

        BenchmarkModelDetailVO result = leaderboardService.getModelDetail(versionId, modelName);

        log.info("模型详细评测结果查询完成，版本ID: {}, 模型: {}, 综合得分: {}, 排名: {}",
                versionId, modelName, result.getTotalScore(), result.getRank());
        return Result.ok(result);
    }
}
