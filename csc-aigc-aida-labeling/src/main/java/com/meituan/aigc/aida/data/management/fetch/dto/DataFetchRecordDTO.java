package com.meituan.aigc.aida.data.management.fetch.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-08 11:19
 * @description 数据拉取记录
 */
@Data
public class DataFetchRecordDTO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 智能侧SessionId
     */
    private String sessionId;
    /**
     * 联络ID
     */
    private String contactId;

    /**
     * 客服mis
     */
    private String staffMis;

    /**
     * 客服发送的消息内容
     */
    private String staffMessageContent;

    /**
     * 消息Id
     */
    private String messageId;

    /**
     * 信号数据内容
     */
    private String signalDataContent;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 用户发送的消息内容
     */
    private String customerMessageContent;
    /**
     * 消息来源: STAFF: 客服发送的消息, CUSTOMER: 用户发送的消息
     */
    private String chatMessageFromType;
    /**
     * 联系类型，online: 在线渠道, call: 电话渠道
     */
    private String contactType;
    /**
     * 标问ID列表
     */
    private String typicalQuestionIds;
    /**
     * 弹屏AC ID
     */
    private String popupAcId;
    /**
     * 埋点通用信息
     */
    private String commonInfo;
    /**
     * 应用入参
     */
    private String inputs;

}
