<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.data.management.fetch.dao.mapper.DataFetchTaskMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="filter_conditions" jdbcType="CHAR" property="filterConditions" />
    <result column="output_fields" jdbcType="CHAR" property="outputFields" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="data_count" jdbcType="INTEGER" property="dataCount" />
    <result column="create_mis" jdbcType="VARCHAR" property="createMis" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, description, data_type, filter_conditions, output_fields, task_status, 
    data_count, create_mis, create_name, create_time, update_time, is_delete
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from data_fetch_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_fetch_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_fetch_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTaskExample">
    delete from data_fetch_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask" useGeneratedKeys="true" keyProperty="id">
    insert into data_fetch_task (id, name, description, 
      data_type, filter_conditions, output_fields, 
      task_status, data_count, create_mis, 
      create_name, create_time, update_time, 
      is_delete)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{dataType,jdbcType=TINYINT}, #{filterConditions,jdbcType=CHAR}, #{outputFields,jdbcType=CHAR}, 
      #{taskStatus,jdbcType=TINYINT}, #{dataCount,jdbcType=INTEGER}, #{createMis,jdbcType=VARCHAR}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask" useGeneratedKeys="true" keyProperty="id">
    insert into data_fetch_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="filterConditions != null">
        filter_conditions,
      </if>
      <if test="outputFields != null">
        output_fields,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="dataCount != null">
        data_count,
      </if>
      <if test="createMis != null">
        create_mis,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="filterConditions != null">
        #{filterConditions,jdbcType=CHAR},
      </if>
      <if test="outputFields != null">
        #{outputFields,jdbcType=CHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="dataCount != null">
        #{dataCount,jdbcType=INTEGER},
      </if>
      <if test="createMis != null">
        #{createMis,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTaskExample" resultType="java.lang.Long">
    select count(*) from data_fetch_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update data_fetch_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.filterConditions != null">
        filter_conditions = #{record.filterConditions,jdbcType=CHAR},
      </if>
      <if test="record.outputFields != null">
        output_fields = #{record.outputFields,jdbcType=CHAR},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=TINYINT},
      </if>
      <if test="record.dataCount != null">
        data_count = #{record.dataCount,jdbcType=INTEGER},
      </if>
      <if test="record.createMis != null">
        create_mis = #{record.createMis,jdbcType=VARCHAR},
      </if>
      <if test="record.createName != null">
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update data_fetch_task
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=TINYINT},
      filter_conditions = #{record.filterConditions,jdbcType=CHAR},
      output_fields = #{record.outputFields,jdbcType=CHAR},
      task_status = #{record.taskStatus,jdbcType=TINYINT},
      data_count = #{record.dataCount,jdbcType=INTEGER},
      create_mis = #{record.createMis,jdbcType=VARCHAR},
      create_name = #{record.createName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask">
    update data_fetch_task
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="filterConditions != null">
        filter_conditions = #{filterConditions,jdbcType=CHAR},
      </if>
      <if test="outputFields != null">
        output_fields = #{outputFields,jdbcType=CHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="dataCount != null">
        data_count = #{dataCount,jdbcType=INTEGER},
      </if>
      <if test="createMis != null">
        create_mis = #{createMis,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask">
    update data_fetch_task
    set name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=TINYINT},
      filter_conditions = #{filterConditions,jdbcType=CHAR},
      output_fields = #{outputFields,jdbcType=CHAR},
      task_status = #{taskStatus,jdbcType=TINYINT},
      data_count = #{dataCount,jdbcType=INTEGER},
      create_mis = #{createMis,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>