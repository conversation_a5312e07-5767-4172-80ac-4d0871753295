package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.OperationLogs;
import com.meituan.aigc.aida.labeling.dao.model.OperationLogsExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OperationLogsMapper {
    long countByExample(OperationLogsExample example);

    int deleteByExample(OperationLogsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(OperationLogs record);

    int insertSelective(OperationLogs record);

    List<OperationLogs> selectByExampleWithBLOBs(OperationLogsExample example);

    List<OperationLogs> selectByExample(OperationLogsExample example);

    OperationLogs selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") OperationLogs record, @Param("example") OperationLogsExample example);

    int updateByExampleWithBLOBs(@Param("record") OperationLogs record, @Param("example") OperationLogsExample example);

    int updateByExample(@Param("record") OperationLogs record, @Param("example") OperationLogsExample example);

    int updateByPrimaryKeySelective(OperationLogs record);

    int updateByPrimaryKeyWithBLOBs(OperationLogs record);

    int updateByPrimaryKey(OperationLogs record);
}