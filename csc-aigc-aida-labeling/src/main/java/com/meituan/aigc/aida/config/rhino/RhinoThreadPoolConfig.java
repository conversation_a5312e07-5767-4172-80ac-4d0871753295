package com.meituan.aigc.aida.config.rhino;

import com.dianping.rhino.Rhino;
import lombok.extern.slf4j.Slf4j;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * Rhino线程池工具类
 *
 * <AUTHOR>
 * @date 2025/6/12
 */
@Slf4j
public class RhinoThreadPoolConfig {

    /**
     * 数据线程池
     */
    public static final ThreadPool DATA_PROCESS_TASK_THREAD_POOL = Rhino.newThreadPool("data-process-thread-pool",
            DefaultThreadPoolProperties.Setter().withCoreSize(10)
                    .withMaxSize(40)
                    .withMaxQueueSize(400)
                    .withRejectHandler(new ThreadPoolExecutor.AbortPolicy()));

    /**
     * 指标计算线程池
     */
    public static final ThreadPool INDICATOR_CALCULATE_TASK_THREAD_POOL = Rhino.newThreadPool("indicator-calculate-thread-pool",
            DefaultThreadPoolProperties.Setter().withCoreSize(10)
                    .withMaxSize(40)
                    .withMaxQueueSize(400)
                    .withRejectHandler(new ThreadPoolExecutor.AbortPolicy()));
}
