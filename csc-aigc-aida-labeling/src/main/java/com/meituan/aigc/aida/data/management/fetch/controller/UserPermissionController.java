package com.meituan.aigc.aida.data.management.fetch.controller;

import com.meituan.aigc.aida.data.management.fetch.service.UserPermissionService;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-07 16:45
 * @description 用户权限控制层
 */
@Validated
@RestController
@RequestMapping("/api/v1/permissions")
@Slf4j
public class UserPermissionController {

    @Autowired
    private UserPermissionService userPermissionService;
    @GetMapping("/getUserPermissions")
    public Result<List<String>> getPermissions() {
        return Result.ok(userPermissionService.getPermissions());
    }
}
