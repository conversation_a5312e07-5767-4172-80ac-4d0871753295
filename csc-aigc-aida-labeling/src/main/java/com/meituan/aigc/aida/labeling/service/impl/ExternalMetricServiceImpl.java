package com.meituan.aigc.aida.labeling.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.meituan.aigc.aida.labeling.common.enums.LabelingDataType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.common.enums.LabelingItemType;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.pojo.dto.ExternalMetricDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import com.meituan.aigc.aida.labeling.service.ExternalMetricService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.csc.aigc.eval.remote.training.api.TrainingCascadeMetricService;
import com.meituan.csc.aigc.eval.remote.training.dto.ChildMetricDTO;
import com.meituan.csc.aigc.eval.remote.training.dto.EnumMetricDTO;
import com.meituan.csc.aigc.eval.remote.training.dto.ManualAnnotationDTO;
import com.meituan.csc.aigc.eval.remote.training.dto.MetricItemDTO;
import com.meituan.csc.aigc.eval.remote.training.dto.TrainingMetricDTO;
import com.meituan.csc.aigc.eval.remote.training.dto.TrainingMetricDetailDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-04-21 16:23
 * @description 外部指标服务
 */
@Service
@Slf4j
public class ExternalMetricServiceImpl implements ExternalMetricService {

    @Autowired
    private TrainingCascadeMetricService trainingCascadeMetricService;

    @Autowired
    private LionConfig lionConfig;

    @Override
    public List<ExternalMetricDTO> listExternalMetric() {
        log.info("查询外部指标模版");
        List<TrainingMetricDTO> trainingMetricList = trainingCascadeMetricService.getTrainingMetric();
        if (CollectionUtils.isEmpty(trainingMetricList)) {
            return Collections.emptyList();
        }
        return trainingMetricList.stream()
                .filter(Objects::nonNull)
                .map(this::convertMetricDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelResult> getExternalMetricDetail(Long metricId, Long currentIndex) {
        log.info("查询外部指标项数据,指标Id:{}", metricId);
        CheckUtil.paramCheck(Objects.nonNull(metricId), "指标Id不能为空");
        TrainingMetricDetailDTO trainingMetricDetail = trainingCascadeMetricService.getTrainingMetricDetail(metricId);
        if (Objects.isNull(trainingMetricDetail)) {
            return Collections.emptyList();
        }
        return convertMetricDetailDTO(trainingMetricDetail, currentIndex);
    }

    /**
     * 转换指标详情DTO
     *
     * @param trainingMetricDetail 训练指标详情DTO
     * @param currentIndex         当前索引
     * @return 返回转换后的标签结果列表
     */
    private List<LabelResult> convertMetricDetailDTO(TrainingMetricDetailDTO trainingMetricDetail, Long currentIndex) {
        log.info("转换指标详情DTO, dto:{},当前索引:{}", JSONObject.toJSONString(trainingMetricDetail),currentIndex);
        List<LabelResult> labelResultList = new ArrayList<>();
        if (Objects.isNull(trainingMetricDetail)) {
            return Collections.emptyList();
        }
        LabelResult labelResult = new LabelResult();
        labelResult.setId(++currentIndex);
        ManualAnnotationDTO manualAnnotationDTO = Optional.ofNullable(trainingMetricDetail.getMetricItem())
                .map(MetricItemDTO::getManualAnnotation).orElse(null);
        if (Objects.isNull(manualAnnotationDTO)) {
            labelResultList.add(labelResult);
            return labelResultList;
        }
        buildManualAnnotation(labelResult, manualAnnotationDTO, -1L, null);
        labelResultList.add(labelResult);
        currentIndex = convertEnumMetric(currentIndex, labelResult.getId(), manualAnnotationDTO.getEnumValueList(),
                labelResultList, 0);

        log.info("转换完成，共生成 {} 个 LabelResult, 最后一个索引是 {}", labelResultList.size(), currentIndex);
        return labelResultList;
    }

    /**
     * 构建人工标注
     *
     * @param labelResult         标签结果对象
     * @param manualAnnotationDTO 人工标注数据传输对象
     * @param parentId            父级ID
     * @param parentEnumValue     父级枚举值
     */
    private void buildManualAnnotation(LabelResult labelResult,
            ManualAnnotationDTO manualAnnotationDTO,
            Long parentId,
            String parentEnumValue) {
        labelResult.setName(manualAnnotationDTO.getAnnotationName());
        labelResult.setDataType(LabelingDataType.getCodeByName(manualAnnotationDTO.getAnnotationType()));
        labelResult.setItemType(LabelingItemType.LABEL.getCode());
        labelResult.setParentId(parentId == -1L ? null : parentId);
        labelResult.setParentEnumValue(parentEnumValue);
        List<String> rootEnumValues = Optional.ofNullable(manualAnnotationDTO.getEnumValueList())
                .orElse(Collections.emptyList())
                .stream().map(EnumMetricDTO::getEnumValue).collect(Collectors.toList());
        labelResult.setEnumList(rootEnumValues);
    }

    /**
     * 转换枚举指标（带递归深度控制）
     *
     * @param currentIndex    当前索引
     * @param parentId        父级ID
     * @param enumValueList   枚举值列表
     * @param labelResultList 标签结果列表
     * @param depth           当前递归深度
     * @return 返回转换后的最后一个索引
     */
    private Long convertEnumMetric(Long currentIndex, Long parentId, List<EnumMetricDTO> enumValueList,
            List<LabelResult> labelResultList, int depth) {
        log.info("转换枚举指标,currentIndex:{}",currentIndex);
        // 递归深度检查，使用Lion配置的最大深度限制
        Integer maxDepth = lionConfig.getMetricEnumMaxDepth();
        CheckUtil.paramCheck(depth < maxDepth, String.format("枚举递归深度超过最大限制: %s", maxDepth));

        if (CollectionUtils.isEmpty(enumValueList)) {
            return currentIndex;
        }
        Long lastIndex = currentIndex;
        for (EnumMetricDTO enumMetricDTO : enumValueList) {
            if (Objects.isNull(enumMetricDTO)) {
                continue;
            }
            if (CollectionUtils.isEmpty(enumMetricDTO.getMetrics())) {
                continue;
            }
            for (ChildMetricDTO childMetric : enumMetricDTO.getMetrics()) {
                LabelResult labelResult = new LabelResult();
                labelResult.setId(++lastIndex);
                ManualAnnotationDTO childManualAnnotation = childMetric.getChildManualAnnotation();
                if (Objects.isNull(childManualAnnotation)) {
                    labelResult.setParentId(parentId);
                    labelResult.setParentEnumValue(enumMetricDTO.getEnumValue());
                    labelResult.setItemType(LabelingItemType.LABEL.getCode());
                    labelResultList.add(labelResult);
                    continue;
                }
                buildManualAnnotation(labelResult, childManualAnnotation, parentId, enumMetricDTO.getEnumValue());
                labelResultList.add(labelResult);
                lastIndex = convertEnumMetric(lastIndex, labelResult.getId(), childManualAnnotation.getEnumValueList(),
                        labelResultList, depth + 1);
            }
        }
        return lastIndex;
    }

    /**
     * 转换指标
     *
     * @param trainingMetricDTO 训练指标数据传输对象
     * @return 返回转换后的外部指标数据传输对象
     */
    private ExternalMetricDTO convertMetricDTO(TrainingMetricDTO trainingMetricDTO) {
        if (Objects.isNull(trainingMetricDTO)) {
            return null;
        }
        ExternalMetricDTO externalMetricDTO = new ExternalMetricDTO();
        externalMetricDTO.setId(trainingMetricDTO.getId());
        externalMetricDTO.setMetricName(trainingMetricDTO.getMetricName());
        externalMetricDTO.setAnnotationType(trainingMetricDTO.getAnnotationType());
        externalMetricDTO.setIsTreeAnnotation(trainingMetricDTO.getIsTreeAnnotation());
        externalMetricDTO.setEnumValueList(trainingMetricDTO.getEnumValueList());
        return externalMetricDTO;
    }
}
