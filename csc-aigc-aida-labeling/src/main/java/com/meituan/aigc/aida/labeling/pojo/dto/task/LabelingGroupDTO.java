package com.meituan.aigc.aida.labeling.pojo.dto.task;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LabelingGroupDTO implements Serializable {
    /**
     * 数据ID
     */
    private Long id;
    /**
     * 背靠背一致率标注项
     */
    private List<LabelingGroupItem> compareLabelingItems;

    /**
     * 非背靠背一致率标注项
     */
    private List<LabelingGroupItem> noCompareLabelingItems;

    /**
     * 是否一致
     */
    private Boolean isConsistent;

    /**
     * 原始数据
     */
    private String rawDataContent;

    /**
     * 原始数据映射内容
     */
    private String rawDataMappedContent;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 会话时间
     */
    private String sessionTime;


    @Data
    public static class LabelingGroupItem implements Serializable {
        /**
         * 标注人姓名
         */
        private String labelerName;
        /**
         * 标注时间
         */
        private String labelingTime;
        /**
         * 标注结果
         */
        private List<LabelingResult> labelingItemResult;
    }

    @Data
    public static class LabelingResult implements Serializable {
        /**
         * 标注项名称
         */
        private String name;
        /**
         * 标注项值
         */
        private String value;
    }
}


