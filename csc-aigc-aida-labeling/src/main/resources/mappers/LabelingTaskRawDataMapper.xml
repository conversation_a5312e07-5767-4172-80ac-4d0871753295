<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.LabelingTaskRawDataMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="session_time" jdbcType="TIMESTAMP" property="sessionTime" />
    <result column="raw_data_content" jdbcType="CHAR" property="rawDataContent" />
    <result column="raw_data_headers" jdbcType="CHAR" property="rawDataHeaders" />
    <result column="raw_data_mapped_content" jdbcType="CHAR" property="rawDataMappedContent" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, session_id, session_time, raw_data_content, raw_data_headers, raw_data_mapped_content, 
    extra_info, create_time, update_time, message_id
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawDataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from labeling_task_raw_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from labeling_task_raw_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from labeling_task_raw_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawDataExample">
    delete from labeling_task_raw_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData">
    insert into labeling_task_raw_data (id, task_id, session_id, 
      session_time, raw_data_content, raw_data_headers, 
      raw_data_mapped_content, extra_info, create_time, 
      update_time, message_id)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{sessionId,jdbcType=VARCHAR}, 
      #{sessionTime,jdbcType=TIMESTAMP}, #{rawDataContent,jdbcType=CHAR}, #{rawDataHeaders,jdbcType=CHAR}, 
      #{rawDataMappedContent,jdbcType=CHAR}, #{extraInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{messageId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData">
    insert into labeling_task_raw_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="sessionTime != null">
        session_time,
      </if>
      <if test="rawDataContent != null">
        raw_data_content,
      </if>
      <if test="rawDataHeaders != null">
        raw_data_headers,
      </if>
      <if test="rawDataMappedContent != null">
        raw_data_mapped_content,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionTime != null">
        #{sessionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawDataContent != null">
        #{rawDataContent,jdbcType=CHAR},
      </if>
      <if test="rawDataHeaders != null">
        #{rawDataHeaders,jdbcType=CHAR},
      </if>
      <if test="rawDataMappedContent != null">
        #{rawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawDataExample" resultType="java.lang.Long">
    select count(*) from labeling_task_raw_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update labeling_task_raw_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.sessionId != null">
        session_id = #{record.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="record.sessionTime != null">
        session_time = #{record.sessionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rawDataContent != null">
        raw_data_content = #{record.rawDataContent,jdbcType=CHAR},
      </if>
      <if test="record.rawDataHeaders != null">
        raw_data_headers = #{record.rawDataHeaders,jdbcType=CHAR},
      </if>
      <if test="record.rawDataMappedContent != null">
        raw_data_mapped_content = #{record.rawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.messageId != null">
        message_id = #{record.messageId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update labeling_task_raw_data
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      session_id = #{record.sessionId,jdbcType=VARCHAR},
      session_time = #{record.sessionTime,jdbcType=TIMESTAMP},
      raw_data_content = #{record.rawDataContent,jdbcType=CHAR},
      raw_data_headers = #{record.rawDataHeaders,jdbcType=CHAR},
      raw_data_mapped_content = #{record.rawDataMappedContent,jdbcType=CHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      message_id = #{record.messageId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData">
    update labeling_task_raw_data
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionTime != null">
        session_time = #{sessionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawDataContent != null">
        raw_data_content = #{rawDataContent,jdbcType=CHAR},
      </if>
      <if test="rawDataHeaders != null">
        raw_data_headers = #{rawDataHeaders,jdbcType=CHAR},
      </if>
      <if test="rawDataMappedContent != null">
        raw_data_mapped_content = #{rawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData">
    update labeling_task_raw_data
    set task_id = #{taskId,jdbcType=BIGINT},
      session_id = #{sessionId,jdbcType=VARCHAR},
      session_time = #{sessionTime,jdbcType=TIMESTAMP},
      raw_data_content = #{rawDataContent,jdbcType=CHAR},
      raw_data_headers = #{rawDataHeaders,jdbcType=CHAR},
      raw_data_mapped_content = #{rawDataMappedContent,jdbcType=CHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      message_id = #{messageId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into labeling_task_raw_data (id, task_id, session_id,message_id,
    session_time, raw_data_content, raw_data_headers, raw_data_mapped_content,
    extra_info, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT}, #{item.sessionId,jdbcType=VARCHAR}, #{item.messageId,jdbcType=VARCHAR},
      #{item.sessionTime,jdbcType=TIMESTAMP}, #{item.rawDataContent,jdbcType=CHAR},#{item.rawDataHeaders,jdbcType=CHAR}, #{item.rawDataMappedContent,jdbcType=CHAR},
      #{item.extraInfo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="listByIds" parameterType="java.util.List" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
    FROM labeling_task_raw_data
    WHERE id IN
      <foreach collection="rawDataIds" item="id" separator="," open="(" close=")">
        #{id}
      </foreach>
  </select>
  <select id="getCountByTaskIds" parameterType="java.util.List" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingTaskRawDataPO">
    select task_id as taskId, count(*) as totalNum, count(distinct session_id) as totalSessionNum
    from labeling_task_raw_data
    where task_id in
    <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
      #{taskId,jdbcType=BIGINT}
    </foreach>
    group by task_id
  </select>
  <select id="pageQueryByTaskIdWithOffset" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    labeling_task_raw_data
    WHERE
    task_id = #{taskId}
    ORDER BY
    id ASC
    LIMIT #{offset}, #{limit}
  </select>
  <select id="pageSessionByTaskIdWithOffset" resultType="java.lang.String">
      SELECT session_id
      FROM labeling_task_raw_data
      WHERE task_id = #{taskId}
      GROUP BY session_id
          LIMIT #{offset}, #{limit}
  </select>
  <select id="listCountByTaskId" resultType="com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingTaskCountDTO">
      select count(*) as totalCount, count(distinct session_id) as sessionCount
      from labeling_task_raw_data
      where task_id = #{taskId}
  </select>
  <select id="listCopyRelationByIds" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingRawDataCopyRelationPO">
    SELECT
     id AS copyRawDataId,
     extra_info->>'$.reLabelingTaskRawDataId' as rawDataId
    FROM labeling_task_raw_data
    WHERE
    JSON_EXTRACT(extra_info, '$.reLabelingTaskRawDataId') IN
    <foreach collection="rawDataIds" item="rawDataId" open="(" separator="," close=")">
      #{rawDataId}
    </foreach>
  </select>
</mapper>