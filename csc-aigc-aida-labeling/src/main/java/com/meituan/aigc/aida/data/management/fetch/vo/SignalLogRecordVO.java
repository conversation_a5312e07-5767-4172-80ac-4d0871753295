package com.meituan.aigc.aida.data.management.fetch.vo;

import lombok.Data;

/**
 * @Author: guo<PERSON><PERSON>
 * @Create: 2025/4/8 17:49
 * @Version: 1.0
 */
@Data
public class SignalLogRecordVO {

    /**
     * 主键ID
     */
    private String id;
    /**
     * 应用ID
     */
    private String appid;
    /**
     * AI搭会话ID
     */
    private String conversationid;
    /**
     * 应用入参
     */
    private String inputs;
    /**
     * 应用出参
     */
    private String outputs;
    /**
     * 弹屏ACID
     */
    private String popupacid;
    /**
     * 创建时间
     */
    private String createdat;
    /**
     * 更新时间
     */
    private String updatedat;
    /**
     * 智能侧SessionId
     */
    private String sessionid;
    /**
     * 标问ID列表
     */
    private String typicalquestionids;
    /**
     * '联系类型，online: 在线渠道, call: 电话渠道
     */
    private String contacttype;
    /**
     * 联络id，在线: 转人工chatId, 热线: 热线联络id
     */
    private String contactid;
    /**
     * 客服MIS
     */
    private String staffmis;
    /**
     * 消息来源: STAFF: 客服发送的消息, CUSTOMER: 用户发送的消息
     */
    private String chatmessagefromtype;
    /**
     * 客服发送的消息内容
     */
    private String staffmessagecontent;
    /**
     * 用户发送的消息内容
     */
    private String customermessagecontent;
    /**
     * 业务消息ID，用于标识一条消息的唯一业务消息ID，比如在线的海豚消息ID
     */
    private String bizmessageid;
    /**
     * 扩展字段
     */
    private String commoninfo;
    /**
     * 消息发送时间
     */
    private String messageoccurredtime;
    /**
     * 信号集合
     */
    private String signaldatacontent;


}
