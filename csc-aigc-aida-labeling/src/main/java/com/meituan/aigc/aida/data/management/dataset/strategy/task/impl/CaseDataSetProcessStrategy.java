package com.meituan.aigc.aida.data.management.dataset.strategy.task.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.meituan.aigc.aida.data.management.dataset.dto.DataSourceField;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateResponseParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetParseHeadParam;
import com.meituan.aigc.aida.labeling.common.constant.RedisConstant;
import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.inf.xmdlog.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * Case分析平台数据集任务处理策略
 */
@Slf4j
@Component
public class CaseDataSetProcessStrategy extends AbstractDataSetProcessStrategy {


    @Autowired(required = false)
    @Qualifier("redisClient")
    private RedisStoreClient redisStoreClient;

    @Resource
    private DataSetVersionRepository dataSetVersionRepository;

    @Resource
    private DataSetRepository dataSetRepository;

    @Override
    public Integer getStrategyType() {
        return DataSourceEnum.CASE.getCode();
    }

    @Override
    public DataSetCreateResponseParam createDataSet(DataSetCreateParam param) {
        log.info("开始创建数据集，参数：{}", JSON.toJSONString(param));

        // 1. 参数校验
        validateCreateDataSetParam(param);

        // 2. 处理数据集
        DataSetCreateResponseParam processResult = processDataSet(param);

        log.info("数据集创建完成，数据集ID：{}，锁ID：{}", processResult.getDataSetId(), processResult.getLockId());
        return processResult;
    }


    /**
     * 参数校验
     */
    private void validateCreateDataSetParam(DataSetCreateParam param) {
        // 1. 锁相关操作优先处理
        Long dataSetId = param.getDataSetId();
        String lockId = param.getLockId();

        if (dataSetId != null) {
            // 验证锁，如果不满足条件直接抛出错误
            validateLock(dataSetId, lockId);
        }

        // 2. 基础参数校验
        CheckUtil.paramCheck(StringUtils.isNotBlank(param.getDataSetName()), "数据集名称不能为空");
        CheckUtil.paramCheck(param.getUsageCategory() != null, "数据用途不能为空");
        CheckUtil.paramCheck(param.getDataSource() != null, "数据来源不能为空");
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(param.getHeadList()), "表头列表不能为空");
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(param.getDataList()), "数据列表不能为空");

        // 3. 校验数据记录
        validateDataRecords(param);
    }

    /**
     * 验证锁
     */
    private void validateLock(Long dataSetId, String lockId) {
        if (StringUtils.isBlank(lockId)) {
            throw new AidaRpcException("锁ID不能为空");
        }

        StoreKey storeKey = new StoreKey(RedisConstant.DATASET_LOCK, dataSetId);
        String existingLockId = redisStoreClient.get(storeKey);
        log.info("验证数据集锁，dataSetId：{}，lockId：{}，existingLockId：{}", dataSetId, lockId, existingLockId);

        if (existingLockId == null || !existingLockId.equals(lockId)) {
            throw new AidaRpcException("数据集锁验证失败，请重新获取锁");
        }
    }

    /**
     * 处理数据集
     */
    private DataSetCreateResponseParam processDataSet(DataSetCreateParam param) {
        Long dataSetId = param.getDataSetId();
        String lockId = param.getLockId();
        DataSetVersion dataSetVersion;
        DataSet dataSet;

        if (dataSetId == null) {
            // 首次创建数据集
            dataSet = createNewDataSet(param);
            dataSetId = dataSet.getId();
            lockId = generateLockId();

            // 设置锁
            setDataSetLock(dataSetId, lockId);
            dataSetVersion = createDataSetVersion(dataSetId, param);
        } else {
            dataSet = dataSetRepository.getDataSetById(dataSetId);
            dataSetVersion = getLatestVersion(dataSetId);
        }

        // 调用通用的保存方法
        commonSaveDataInBatches(param, dataSet, dataSetVersion);

        // 如果是最后一批，释放锁
        if (Boolean.TRUE.equals(param.getIsEnd())) {
            releaseLock(dataSetId, lockId);
        }

        return new DataSetCreateResponseParam(dataSetId, lockId);
    }

    /**
     * 释放锁
     */
    private void releaseLock(Long dataSetId, String lockId) {
        StoreKey storeKey = new StoreKey(RedisConstant.DATASET_LOCK, dataSetId);
        redisStoreClient.delete(storeKey);
        log.info("释放数据集锁成功，dataSetId：{}，lockId：{}", dataSetId, lockId);
    }

    /**
     * 获取最新版本ID
     */
    private DataSetVersion getLatestVersion(Long dataSetId) {
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        return dataSetVersionRepository.getById(dataSet.getLatestVersionId());
    }

    /**
     * 生成锁ID
     */
    private String generateLockId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 设置数据集锁
     */
    private void setDataSetLock(Long dataSetId, String lockId) {
        log.info("设置数据集锁，dataSetId：{}，lockId：{}", dataSetId, lockId);
        StoreKey storeKey = new StoreKey(RedisConstant.DATASET_LOCK, dataSetId);
        int expireSeconds = Lion.getInt(ConfigUtil.getAppkey(), LionConstant.DATASET_LOCK_EXPIRE, 60 * 60 * 24);
        Boolean success = redisStoreClient.setnx(storeKey, lockId, expireSeconds);
        if (!Boolean.TRUE.equals(success)) {
            throw new AidaRpcException("数据集正在被其他操作使用，请稍后重试");
        }
    }

    /**
     * 解析表头
     *
     * @param param 解析参数
     * @return 表头字段列表
     */
    @Override
    public List<DataFieldParam> parseHeader(DataSetParseHeadParam param) {
        throw new AidaTrainingCheckException("Case数据集不支持解析表头");
    }
} 