package com.meituan.aigc.aida.data.management.fetch.mq.consumer;

import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.fetch.service.SignalLoggingTriggerService;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * LLM聊天事件消息消费处理
 *
 * <AUTHOR>
 * @date 2025/04/03
 */
@Component("chatEventMessageListener")
@Slf4j
public class ChatEventMessageListener {
    private static final String LOG_PREFIX = "[ChatEventMessageListener]";

    @Resource
    private SignalLoggingTriggerService signalLoggingTriggerService;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    @MdpMafkaMsgReceive
    public ConsumeStatus consume(String msgBody) {
        if (BooleanUtils.isFalse(dataManagementLionConfig.getChatConsumerSwitch())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if (StringUtils.isBlank(msgBody)) {
            log.warn("{}, 消息体为空", LOG_PREFIX);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            signalLoggingTriggerService.processSignalLoggingTriggerByMessageString(msgBody);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("{}, 消费海豚消对话息异常, msg:{}", LOG_PREFIX, msgBody, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

}
