package com.meituan.aigc.aida.data.management.fetch.controller;

import com.meituan.aigc.aida.data.management.fetch.dto.SignalTrackingRuleDTO;
import com.meituan.aigc.aida.data.management.fetch.service.SignalTrackingService;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025-04-03 10:12
 * @description 信号埋点controller
 */
@Validated
@RestController
@RequestMapping("/api/v1/signal-tracking-rules")
@Slf4j
public class SignalTrackingController {

    @Autowired
    private SignalTrackingService signalTrackingService;

    /**
     * 埋点规则列表
     *
     * @param name     规则名称
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return
     */
    @GetMapping("/list")
    public Result<PageData<SignalTrackingRuleDTO>> list(@RequestParam(value = "name", required = false) String name,
                                                        @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                        @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        return Result.ok(signalTrackingService.pageSignalTrackingRuleList(name, pageNum, pageSize));
    }

    /**
     * 埋点规则详情
     *
     * @param ruleId 规则id
     * @return 埋点规则详情
     */
    @GetMapping("/detail")
    public Result<SignalTrackingRuleDTO> detail(@RequestParam("ruleId") Long ruleId) {
        return Result.ok(signalTrackingService.getSignalTrackingRuleDetail(ruleId));
    }

    /**
     * 创建埋点规则
     *
     * @param signalTrackingRuleDTO 埋点规则信息
     * @return 创建结果
     */
    @PostMapping("/create")
    public Result<?> createRule(@RequestBody SignalTrackingRuleDTO signalTrackingRuleDTO) {
        signalTrackingService.createSignalTrackingRule(signalTrackingRuleDTO);
        return Result.create();
    }

    /**
     * 更新埋点规则状态
     *
     * @param ruleId 规则id
     * @param status 状态
     * @return
     */
    @PutMapping("/status")
    public Result<?> updateStatus(@RequestParam("ruleId") Long ruleId, @RequestParam("status") Integer status) {
        signalTrackingService.updateStatus(ruleId, status);
        return Result.create();
    }

    /**
     * 删除埋点规则
     *
     * @param ruleId 规则id
     * @return
     */
    @PutMapping("/delete")
    public Result<?> deleteRule(@RequestParam("ruleId") Long ruleId) {
        signalTrackingService.deleteSignalTrackingRule(ruleId);
        return Result.create();
    }

    @GetMapping("/robot-config")
    public Result<String> getSignalTrackingRobotConfig(){
        return Result.ok(signalTrackingService.getSignalTrackingRobotConfig());
    }


}
