package com.meituan.aigc.aida.labeling.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabelingQualityCheckItemExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LabelingQualityCheckItemExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdIsNull() {
            addCriterion("labeling_data_id is null");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdIsNotNull() {
            addCriterion("labeling_data_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdEqualTo(Long value) {
            addCriterion("labeling_data_id =", value, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdNotEqualTo(Long value) {
            addCriterion("labeling_data_id <>", value, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdGreaterThan(Long value) {
            addCriterion("labeling_data_id >", value, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdGreaterThanOrEqualTo(Long value) {
            addCriterion("labeling_data_id >=", value, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdLessThan(Long value) {
            addCriterion("labeling_data_id <", value, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdLessThanOrEqualTo(Long value) {
            addCriterion("labeling_data_id <=", value, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdIn(List<Long> values) {
            addCriterion("labeling_data_id in", values, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdNotIn(List<Long> values) {
            addCriterion("labeling_data_id not in", values, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdBetween(Long value1, Long value2) {
            addCriterion("labeling_data_id between", value1, value2, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andLabelingDataIdNotBetween(Long value1, Long value2) {
            addCriterion("labeling_data_id not between", value1, value2, "labelingDataId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdIsNull() {
            addCriterion("quality_check_task_id is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdIsNotNull() {
            addCriterion("quality_check_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdEqualTo(Long value) {
            addCriterion("quality_check_task_id =", value, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdNotEqualTo(Long value) {
            addCriterion("quality_check_task_id <>", value, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdGreaterThan(Long value) {
            addCriterion("quality_check_task_id >", value, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("quality_check_task_id >=", value, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdLessThan(Long value) {
            addCriterion("quality_check_task_id <", value, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("quality_check_task_id <=", value, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdIn(List<Long> values) {
            addCriterion("quality_check_task_id in", values, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdNotIn(List<Long> values) {
            addCriterion("quality_check_task_id not in", values, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdBetween(Long value1, Long value2) {
            addCriterion("quality_check_task_id between", value1, value2, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("quality_check_task_id not between", value1, value2, "qualityCheckTaskId");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisIsNull() {
            addCriterion("quality_check_mis is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisIsNotNull() {
            addCriterion("quality_check_mis is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisEqualTo(String value) {
            addCriterion("quality_check_mis =", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisNotEqualTo(String value) {
            addCriterion("quality_check_mis <>", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisGreaterThan(String value) {
            addCriterion("quality_check_mis >", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisGreaterThanOrEqualTo(String value) {
            addCriterion("quality_check_mis >=", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisLessThan(String value) {
            addCriterion("quality_check_mis <", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisLessThanOrEqualTo(String value) {
            addCriterion("quality_check_mis <=", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisLike(String value) {
            addCriterion("quality_check_mis like", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisNotLike(String value) {
            addCriterion("quality_check_mis not like", value, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisIn(List<String> values) {
            addCriterion("quality_check_mis in", values, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisNotIn(List<String> values) {
            addCriterion("quality_check_mis not in", values, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisBetween(String value1, String value2) {
            addCriterion("quality_check_mis between", value1, value2, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckMisNotBetween(String value1, String value2) {
            addCriterion("quality_check_mis not between", value1, value2, "qualityCheckMis");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameIsNull() {
            addCriterion("quality_check_name is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameIsNotNull() {
            addCriterion("quality_check_name is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameEqualTo(String value) {
            addCriterion("quality_check_name =", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameNotEqualTo(String value) {
            addCriterion("quality_check_name <>", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameGreaterThan(String value) {
            addCriterion("quality_check_name >", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameGreaterThanOrEqualTo(String value) {
            addCriterion("quality_check_name >=", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameLessThan(String value) {
            addCriterion("quality_check_name <", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameLessThanOrEqualTo(String value) {
            addCriterion("quality_check_name <=", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameLike(String value) {
            addCriterion("quality_check_name like", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameNotLike(String value) {
            addCriterion("quality_check_name not like", value, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameIn(List<String> values) {
            addCriterion("quality_check_name in", values, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameNotIn(List<String> values) {
            addCriterion("quality_check_name not in", values, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameBetween(String value1, String value2) {
            addCriterion("quality_check_name between", value1, value2, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckNameNotBetween(String value1, String value2) {
            addCriterion("quality_check_name not between", value1, value2, "qualityCheckName");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultIsNull() {
            addCriterion("quality_check_result is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultIsNotNull() {
            addCriterion("quality_check_result is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultEqualTo(Boolean value) {
            addCriterion("quality_check_result =", value, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultNotEqualTo(Boolean value) {
            addCriterion("quality_check_result <>", value, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultGreaterThan(Boolean value) {
            addCriterion("quality_check_result >", value, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultGreaterThanOrEqualTo(Boolean value) {
            addCriterion("quality_check_result >=", value, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultLessThan(Boolean value) {
            addCriterion("quality_check_result <", value, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultLessThanOrEqualTo(Boolean value) {
            addCriterion("quality_check_result <=", value, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultIn(List<Boolean> values) {
            addCriterion("quality_check_result in", values, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultNotIn(List<Boolean> values) {
            addCriterion("quality_check_result not in", values, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultBetween(Boolean value1, Boolean value2) {
            addCriterion("quality_check_result between", value1, value2, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckResultNotBetween(Boolean value1, Boolean value2) {
            addCriterion("quality_check_result not between", value1, value2, "qualityCheckResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultIsNull() {
            addCriterion("quality_check_items_result is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultIsNotNull() {
            addCriterion("quality_check_items_result is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultEqualTo(String value) {
            addCriterion("quality_check_items_result =", value, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultNotEqualTo(String value) {
            addCriterion("quality_check_items_result <>", value, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultGreaterThan(String value) {
            addCriterion("quality_check_items_result >", value, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultGreaterThanOrEqualTo(String value) {
            addCriterion("quality_check_items_result >=", value, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultLessThan(String value) {
            addCriterion("quality_check_items_result <", value, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultLessThanOrEqualTo(String value) {
            addCriterion("quality_check_items_result <=", value, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultLike(String value) {
            addCriterion("quality_check_items_result like", value, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultNotLike(String value) {
            addCriterion("quality_check_items_result not like", value, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultIn(List<String> values) {
            addCriterion("quality_check_items_result in", values, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultNotIn(List<String> values) {
            addCriterion("quality_check_items_result not in", values, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultBetween(String value1, String value2) {
            addCriterion("quality_check_items_result between", value1, value2, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andQualityCheckItemsResultNotBetween(String value1, String value2) {
            addCriterion("quality_check_items_result not between", value1, value2, "qualityCheckItemsResult");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusIsNull() {
            addCriterion("checkable_status is null");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusIsNotNull() {
            addCriterion("checkable_status is not null");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusEqualTo(Byte value) {
            addCriterion("checkable_status =", value, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusNotEqualTo(Byte value) {
            addCriterion("checkable_status <>", value, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusGreaterThan(Byte value) {
            addCriterion("checkable_status >", value, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("checkable_status >=", value, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusLessThan(Byte value) {
            addCriterion("checkable_status <", value, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusLessThanOrEqualTo(Byte value) {
            addCriterion("checkable_status <=", value, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusIn(List<Byte> values) {
            addCriterion("checkable_status in", values, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusNotIn(List<Byte> values) {
            addCriterion("checkable_status not in", values, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusBetween(Byte value1, Byte value2) {
            addCriterion("checkable_status between", value1, value2, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andCheckableStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("checkable_status not between", value1, value2, "checkableStatus");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultIsNull() {
            addCriterion("modified_labeling_items_result is null");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultIsNotNull() {
            addCriterion("modified_labeling_items_result is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultEqualTo(String value) {
            addCriterion("modified_labeling_items_result =", value, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultNotEqualTo(String value) {
            addCriterion("modified_labeling_items_result <>", value, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultGreaterThan(String value) {
            addCriterion("modified_labeling_items_result >", value, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultGreaterThanOrEqualTo(String value) {
            addCriterion("modified_labeling_items_result >=", value, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultLessThan(String value) {
            addCriterion("modified_labeling_items_result <", value, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultLessThanOrEqualTo(String value) {
            addCriterion("modified_labeling_items_result <=", value, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultLike(String value) {
            addCriterion("modified_labeling_items_result like", value, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultNotLike(String value) {
            addCriterion("modified_labeling_items_result not like", value, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultIn(List<String> values) {
            addCriterion("modified_labeling_items_result in", values, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultNotIn(List<String> values) {
            addCriterion("modified_labeling_items_result not in", values, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultBetween(String value1, String value2) {
            addCriterion("modified_labeling_items_result between", value1, value2, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedLabelingItemsResultNotBetween(String value1, String value2) {
            addCriterion("modified_labeling_items_result not between", value1, value2, "modifiedLabelingItemsResult");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentIsNull() {
            addCriterion("modified_raw_data_mapped_content is null");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentIsNotNull() {
            addCriterion("modified_raw_data_mapped_content is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentEqualTo(String value) {
            addCriterion("modified_raw_data_mapped_content =", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentNotEqualTo(String value) {
            addCriterion("modified_raw_data_mapped_content <>", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentGreaterThan(String value) {
            addCriterion("modified_raw_data_mapped_content >", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentGreaterThanOrEqualTo(String value) {
            addCriterion("modified_raw_data_mapped_content >=", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentLessThan(String value) {
            addCriterion("modified_raw_data_mapped_content <", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentLessThanOrEqualTo(String value) {
            addCriterion("modified_raw_data_mapped_content <=", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentLike(String value) {
            addCriterion("modified_raw_data_mapped_content like", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentNotLike(String value) {
            addCriterion("modified_raw_data_mapped_content not like", value, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentIn(List<String> values) {
            addCriterion("modified_raw_data_mapped_content in", values, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentNotIn(List<String> values) {
            addCriterion("modified_raw_data_mapped_content not in", values, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentBetween(String value1, String value2) {
            addCriterion("modified_raw_data_mapped_content between", value1, value2, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andModifiedRawDataMappedContentNotBetween(String value1, String value2) {
            addCriterion("modified_raw_data_mapped_content not between", value1, value2, "modifiedRawDataMappedContent");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeIsNull() {
            addCriterion("first_check_time is null");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeIsNotNull() {
            addCriterion("first_check_time is not null");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeEqualTo(Date value) {
            addCriterion("first_check_time =", value, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeNotEqualTo(Date value) {
            addCriterion("first_check_time <>", value, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeGreaterThan(Date value) {
            addCriterion("first_check_time >", value, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("first_check_time >=", value, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeLessThan(Date value) {
            addCriterion("first_check_time <", value, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeLessThanOrEqualTo(Date value) {
            addCriterion("first_check_time <=", value, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeIn(List<Date> values) {
            addCriterion("first_check_time in", values, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeNotIn(List<Date> values) {
            addCriterion("first_check_time not in", values, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeBetween(Date value1, Date value2) {
            addCriterion("first_check_time between", value1, value2, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andFirstCheckTimeNotBetween(Date value1, Date value2) {
            addCriterion("first_check_time not between", value1, value2, "firstCheckTime");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(String value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(String value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(String value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(String value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(String value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(String value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLike(String value) {
            addCriterion("session_id like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLike(String value) {
            addCriterion("session_id not like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<String> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<String> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(String value1, String value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(String value1, String value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNull() {
            addCriterion("message_id is null");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNotNull() {
            addCriterion("message_id is not null");
            return (Criteria) this;
        }

        public Criteria andMessageIdEqualTo(String value) {
            addCriterion("message_id =", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotEqualTo(String value) {
            addCriterion("message_id <>", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThan(String value) {
            addCriterion("message_id >", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanOrEqualTo(String value) {
            addCriterion("message_id >=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThan(String value) {
            addCriterion("message_id <", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanOrEqualTo(String value) {
            addCriterion("message_id <=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLike(String value) {
            addCriterion("message_id like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotLike(String value) {
            addCriterion("message_id not like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIn(List<String> values) {
            addCriterion("message_id in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotIn(List<String> values) {
            addCriterion("message_id not in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdBetween(String value1, String value2) {
            addCriterion("message_id between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotBetween(String value1, String value2) {
            addCriterion("message_id not between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdIsNull() {
            addCriterion("raw_data_id is null");
            return (Criteria) this;
        }

        public Criteria andRawDataIdIsNotNull() {
            addCriterion("raw_data_id is not null");
            return (Criteria) this;
        }

        public Criteria andRawDataIdEqualTo(Long value) {
            addCriterion("raw_data_id =", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdNotEqualTo(Long value) {
            addCriterion("raw_data_id <>", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdGreaterThan(Long value) {
            addCriterion("raw_data_id >", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdGreaterThanOrEqualTo(Long value) {
            addCriterion("raw_data_id >=", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdLessThan(Long value) {
            addCriterion("raw_data_id <", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdLessThanOrEqualTo(Long value) {
            addCriterion("raw_data_id <=", value, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdIn(List<Long> values) {
            addCriterion("raw_data_id in", values, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdNotIn(List<Long> values) {
            addCriterion("raw_data_id not in", values, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdBetween(Long value1, Long value2) {
            addCriterion("raw_data_id between", value1, value2, "rawDataId");
            return (Criteria) this;
        }

        public Criteria andRawDataIdNotBetween(Long value1, Long value2) {
            addCriterion("raw_data_id not between", value1, value2, "rawDataId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}