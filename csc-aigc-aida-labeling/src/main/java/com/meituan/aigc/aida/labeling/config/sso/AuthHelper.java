package com.meituan.aigc.aida.labeling.config.sso;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingSsoAuthException;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 */
@Slf4j
public class AuthHelper {

    /**
     * sso鉴权失败
     */
    public static void ssoFailed(HttpServletResponse res, String msg) {
        PrintWriter out = null;
        try {
            res.setContentType("application/json;charset=UTF-8");
            out = res.getWriter();
            Result<?> result = Result.create();
            result.setCode(401);
            result.setMessage(msg);
            out.write(JSON.toJSONString(result));
            out.flush();
        } catch (IOException e) {
            Cat.logError(new AidaTrainingSsoAuthException("组装response错误", e));
            log.error("组装response错误", e);
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

}
