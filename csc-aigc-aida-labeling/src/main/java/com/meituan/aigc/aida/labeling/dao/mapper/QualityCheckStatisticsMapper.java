package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics;
import com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatisticsExample;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DailyStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DailyBizStatisticsVO;
import java.util.List;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingAndCheckCountVO;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonnelDetailQuery;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.BizQualityCheckQueryVO;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.BaseStatisticsDTO;
import org.apache.ibatis.annotations.Param;

public interface QualityCheckStatisticsMapper {
    long countByExample(QualityCheckStatisticsExample example);

    int deleteByExample(QualityCheckStatisticsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(QualityCheckStatistics record);

    int insertSelective(QualityCheckStatistics record);

    List<QualityCheckStatistics> selectByExample(QualityCheckStatisticsExample example);

    QualityCheckStatistics selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") QualityCheckStatistics record, @Param("example") QualityCheckStatisticsExample example);

    int updateByExample(@Param("record") QualityCheckStatistics record, @Param("example") QualityCheckStatisticsExample example);

    int updateByPrimaryKeySelective(QualityCheckStatistics record);

    int updateByPrimaryKey(QualityCheckStatistics record);

    /**
     * 查询指定业务的质检统计数据
     * @param param
     * @return
     */
    List<DailyBizStatisticsVO> queryDailyTrends(@Param("param") DailyStatisticsDTO param);

    LabelingAndCheckCountVO statisticalQualityCheckData(@Param("query") DataStatisticalQuery query);

    /**
     * 查询人员明细
     * @param query 查询条件
     * @return 结果
     */
    List<PersonnelDetailVO> queryPersonnelDetail(@Param("query") PersonnelDetailQuery query);

    /**
     * 查询单个业务质检率趋势
     * @param query 查询条件
     * @return 结果
     */
    List<BizQualityCheckQueryVO> querySingleBizQualityCheckRateTrends(@Param("query") BaseStatisticsDTO query);

    /**
     * 查询多个业务质检率
     * @param query 查询条件
     * @return 结果
     */
    List<BizQualityCheckQueryVO> queryBizQualityCheckRate(@Param("query") BaseStatisticsDTO query);

    void batchInsert(@Param("qualityCheckStatisticsList") List<QualityCheckStatistics> qualityCheckStatisticsList);
}