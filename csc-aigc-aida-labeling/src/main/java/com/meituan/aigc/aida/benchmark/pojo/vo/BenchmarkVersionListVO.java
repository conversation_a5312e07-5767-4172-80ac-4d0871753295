package com.meituan.aigc.aida.benchmark.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Benchmark版本列表VO
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BenchmarkVersionListVO {

    /**
     * 版本列表
     */
    private List<BenchmarkVersionItemVO> versions;
} 