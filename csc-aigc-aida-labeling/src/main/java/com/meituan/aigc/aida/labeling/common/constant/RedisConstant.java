package com.meituan.aigc.aida.labeling.common.constant;

/**
 * <AUTHOR>
 * @date 2025-03-18 17:31
 * @description redis常量
 */
public class RedisConstant {

    /**
     * 分配任务taskId
     */
    public static final String DISTRIBUTE_LABELING_TASK_ID = "distribute_labeling_task_id";

    /**
     * 分配任务质检项id
     */
    public static final String DISTRIBUTE_QUALITY_TASK_ID = "distribute_quality_task_id";

    /**
     * 校验ac弹屏Id存在缓存key  verify_exist_popup_ac_id  存在
     */
    public static final String VERIFY_EXIST_POPUP_AC_ID = "verify_exist_popup_ac_id";

    /**
     * 标注任务回写数据锁
     */
    public static final String LABELING_TASK_WRITE_BACK_LOCK = "labeling_task_write_back_lock";

    /**
     * 任务转交锁
     */
    public static final String TASK_TRANSFER_LOCK = "task_transfer_lock";

    /**
     * 数据集处理锁
     */
    public static final String DATASET_LOCK = "dataset_lock";

    /**
     * 数据集版本操作锁
     */
    public static final String DATASET_VERSION_OPERATE_LOCK = "dataset_version_operate_lock";

    /**
     * 打标任务ID
     */
    public static final String MARKING_TASK_ID = "marking_task_id";

    /**
     * 复标分布式锁
     */
    public static final String RE_LABELING_TASK_ID = "re_labeling_task_id";

    /**
     * 质检信息暂存
     */
    public static final String CHECK_TEMPORARY_STORAGE = "check_temporary_storage";

    /**
     * 标注信息暂存
     */
    public static final String LABEL_TEMPORARY_STORAGE = "label_temporary_storage";

    /**
     * 线上会话query维度标注扩展信息
     */
    public static final String LABEL_ONLINE_QUERY_EXTRA = "label_online_query_extra";



}