package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * @Author: guowenhui
 * @Create: 2025/3/3 09:55
 * @Version: 1.0
 */
@Data
public class LabelTaskDataExportVO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 会话时间
     */
    private Date sessionTime;

    /**
     * 大模型消息ID
     */
    private String messageId;

    /**
     * 原始数据内容
     */
    private String rawDataContent;

    /**
     * 原始数据表头
     */
    private String rawDataHeaders;

    /**
     * 原始数据内容映射内容
     */
    private String rawDataMappedContent;

    /**
     * 扩展信息(json格式)
     */
    private String extraInfo;
    /**
     * 被修改的原始数据内容映射内容, 单独存储字段, 查看详情、导出时覆盖原始数据表中的raw_data_mapped_content
     */
    private String modifiedRawDataMappedContent;
    /**
     * 自定义标注项标注结果
     */
    private String labelingItemsResult;
    /**
     * 质检人Mis
     */
    private String qualityCheckMis;

    /**
     * 质检结果: 1-标注正确 2-标注错误
     */
    private Integer qualityCheckResult;

    /**
     * 自定义质检项质检结果
     */
    private String qualityCheckItemsResult;

    /**
     * 标注人Mis
     */
    private String labelMis;

    /**
     * 标注人名称
     */
    private String labelName;

    /**
     * 标注时间
     */
    private String labelTime;

    /**
     * 质检人名称
     */
    private String qualityCheckName;

    /**
     * 质检时间
     */
    private String qualityCheckTime;
    /**
     * 质检员修改后的标注信息
     */
    private String qualityCheckModifiedLabelingItems;
    /**
     * 被质检员修改的原始数据内容映射内容, 单独存储字段, 查看质检详情、导出时单独展示
     */
    private String qualityCheckModifiedRawDataMappedContent;
    /**
     * 子任务Id
     */
    private Long subTaskId;
}
