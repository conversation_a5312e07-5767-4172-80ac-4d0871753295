package com.meituan.aigc.aida.data.management.fetch.common.convertor;

import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchRecordDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskDTO;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-08 11:57
 * @description 数据拉取任务转换类
 */
@Slf4j
public class DataFetchTaskConvertor {

    public static DataFetchTaskDTO buildDataFetchTaskDTO(DataFetchTask dataFetchTask) {
        if (Objects.isNull(dataFetchTask)) {
            return null;
        }
        DataFetchTaskDTO dataFetchTaskDTO = new DataFetchTaskDTO();
        try {
            dataFetchTaskDTO.setId(dataFetchTask.getId());
            dataFetchTaskDTO.setName(dataFetchTask.getName());
            dataFetchTaskDTO.setDescription(dataFetchTask.getDescription());
            dataFetchTaskDTO.setDataType(dataFetchTask.getDataType());
            dataFetchTaskDTO.setDataCount(dataFetchTask.getDataCount());
            dataFetchTaskDTO.setTaskStatus(dataFetchTask.getTaskStatus());
            dataFetchTaskDTO.setCreateMis(dataFetchTask.getCreateMis());
            dataFetchTaskDTO.setCreateName(dataFetchTask.getCreateName());
            dataFetchTaskDTO.setFilterConditions(dataFetchTask.getFilterConditions());
            dataFetchTaskDTO.setOutputFields(dataFetchTask.getOutputFields());
            dataFetchTaskDTO.setCreateTime(DateUtil.formatDate(dataFetchTask.getCreateTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD));
            dataFetchTaskDTO.setUpdateTime(DateUtil.formatDate(dataFetchTask.getUpdateTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD));
            return dataFetchTaskDTO;
        } catch (Exception e) {
            log.error("日期转换失败，任务id:{}", dataFetchTaskDTO.getId(), e);
        }
        return dataFetchTaskDTO;
    }

    public static DataFetchRecordDTO buildDataFetchRecordDTO(DataFetchRecordWithBLOBs dataFetchRecord) {
        if (Objects.isNull(dataFetchRecord)) {
            return null;
        }
        DataFetchRecordDTO dataFetchRecordDTO = new DataFetchRecordDTO();
        try {
            dataFetchRecordDTO.setId(dataFetchRecord.getId());
            dataFetchRecordDTO.setTaskId(dataFetchRecord.getTaskId());
            dataFetchRecordDTO.setSessionId(dataFetchRecord.getSessionId());
            dataFetchRecordDTO.setContactId(dataFetchRecord.getContactId());
            dataFetchRecordDTO.setStaffMis(dataFetchRecord.getStaffMis());
            dataFetchRecordDTO.setStaffMessageContent(dataFetchRecord.getStaffMessageContent());
            dataFetchRecordDTO.setMessageId(dataFetchRecord.getMessageId());
            dataFetchRecordDTO.setSignalDataContent(dataFetchRecord.getSignalDataContent());
            if (Objects.nonNull(dataFetchRecord.getMessageOccurredTime())){
                dataFetchRecordDTO.setCreateTime(DateUtil.formatDate(dataFetchRecord.getMessageOccurredTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD));
            }else {
                dataFetchRecordDTO.setCreateTime("-");
            }
            dataFetchRecordDTO.setCommonInfo(dataFetchRecord.getCommonInfo());
            dataFetchRecordDTO.setCustomerMessageContent(dataFetchRecord.getCustomerMessageContent());
            dataFetchRecordDTO.setChatMessageFromType(dataFetchRecord.getChatMessageFromType());
            dataFetchRecordDTO.setContactType(dataFetchRecord.getContactType());
            dataFetchRecordDTO.setTypicalQuestionIds(dataFetchRecord.getTypicalQuestionIds());
            dataFetchRecordDTO.setPopupAcId(dataFetchRecord.getPopupAcId());
            dataFetchRecordDTO.setInputs(dataFetchRecord.getInputs());
            return dataFetchRecordDTO;
        } catch (Exception e) {
            log.error("日期转换失败，记录id:{}", dataFetchRecordDTO.getId(), e);
        }
        return dataFetchRecordDTO;
    }
}
