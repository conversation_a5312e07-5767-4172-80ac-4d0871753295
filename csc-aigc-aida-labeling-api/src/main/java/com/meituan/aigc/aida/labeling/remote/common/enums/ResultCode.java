package com.meituan.aigc.aida.labeling.remote.common.enums;

/**
 * 统一响应状态码枚举
 * 
 * <AUTHOR>
 * @date 2025/05/07
 */
public enum ResultCode {
    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),
    
    /**
     * 失败
     */
    FAIL(500, "服务端错误"),
    
    /**
     * 参数校验失败
     */
    VALIDATE_FAILED(400, "参数检验失败");
    
    private int code;
    private String message;
    
    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
}
