package com.meituan.aigc.aida.labeling.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-03-06 10:14
 * @description 标注子任务转交参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelingSubTaskTransferParam implements Serializable {

    /**
     * 子任务id
     */
    @NotNull(message = "子任务id不能为空")
    private Long subTaskId;

    /**
     * 标注人mis
     */
    @NotNull(message = "标注人mis不能为空")

    private String transferLabelerMis;

    /**
     * 标注人姓名
     */
    @NotNull(message = "标注人姓名不能为空")
    private String transferLabelerName;
}
