package com.meituan.aigc.aida.labeling.service;

import com.meituan.aigc.aida.labeling.param.*;
import com.meituan.aigc.aida.labeling.param.quality.check.QualityCheckDistributeParam;
import com.meituan.aigc.aida.labeling.param.task.LabelingDistributionParam;
import com.meituan.aigc.aida.labeling.param.task.LabelingGroupConsistencyParam;
import com.meituan.aigc.aida.labeling.param.task.RecycleTaskParam;
import com.meituan.aigc.aida.labeling.param.task.TaskReLabelingParam;
import com.meituan.aigc.aida.labeling.pojo.dto.*;
import com.meituan.aigc.aida.labeling.pojo.dto.group.GroupConfigList;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingGroupDetailDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingTaskCountDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingTaskDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.task.TaskGroupDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.QualityCheckOverviewVO;

import java.util.List;

/**
 * Description 任务服务接口
 *
 * <AUTHOR>
 */
public interface TaskService {

    /**
     * 获取mis信息
     *
     * @param mis mis号
     * @return 用户信息
     */
    List<MisDataDTO> getMisList(String mis);

    /**
     * 导出标注数据
     *
     * @param param 请求参数
     */
    void recycleLabelingData(RecycleTaskParam param);

    /**
     * 质检分发
     *
     * @param param 质检分发参数
     */
    void distributeQualityCheck(QualityCheckDistributeParam param);

    /**
     * 导入文件
     *
     * @param param 创建标注任务参数
     */
    Long createLabelingTask(LabelingTaskParam param);

    /**
     * 查询标注任务列表
     *
     * @param taskName 任务名称
     * @param taskId   任务ID
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 任务列表
     */
    PageData<LabelingTaskDTO> taskList(String taskName, Long taskId, Integer pageNum, Integer pageSize);

    /**
     * 查询任务分组列表
     *
     * @param taskId 任务ID
     * @return 任务分组列表
     */
    TaskGroupDTO listGroupsByTaskId(Long taskId);

    /**
     * 查询标注项模版列表
     *
     * @return 模版列表
     */
    List<InspectionItemTemplateDTO> templateList();

    /**
     * 查询标注项列表
     *
     * @param templateId 模版ID
     * @return 标注项列表
     */
    List<InspectionItemDTO> itemList(Long templateId);

    /**
     * 查询质检详情
     *
     * @param taskId 任务ID
     * @return 质检详情列表
     */
    QualityCheckOverviewVO qualityCheckDetails(Long taskId);

    /**
     * 分配标注任务
     *
     * @param param 分配参数
     */
    void distributeLabelingTask(LabelingDistributionParam param);

    /**
     * 删除标注任务
     *
     * @param taskId 任务ID
     */
    void deleteLabelingTask(Long taskId);

    /**
     * 查询主任务原始数据详情
     *
     * @param taskId   任务ID
     * @param pageNum  起始索引
     * @param pageSize 每页数量
     * @return 原始数据详情
     */
    PageData<LabelingTaskRawDataDTO> pageRawDataByTaskId(Long taskId, Integer pageNum, Integer pageSize);

    /**
     * 保存标注项模板
     *
     * @param inspectionItemTemplateParam 标注项模板参数
     */
    void saveLabelingTemplates(InspectionItemTemplateParam inspectionItemTemplateParam);

    /**
     * 删除标注项模板
     *
     * @param templateId 模板ID
     */
    void deleteLabelingTemplate(Long templateId);

    /**
     * 查询分组一致性信息
     *
     * @param param 分组一致性查询参数
     * @return 分组一致性信息列表
     */
    LabelingGroupDetailDTO listGroupConsistency(LabelingGroupConsistencyParam param);

    /**
     * 查询分组列表
     *
     * @param taskId 任务ID
     * @return 分组列表
     */
    GroupConfigList listLabelingTemplateGroup(Long taskId);

    /**
     * 获取任务下标注数据量
     *
     * @param taskId 任务ID
     */
    LabelingTaskCountDTO getLabelingTaskCount(Long taskId);

    /**
     * 获取任务下质检数据量
     *
     * @param taskId       任务ID
     * @param transferType 质检类型 1-标注 2-质检
     * @return 质检数据量
     */
    TransferInfoDTO getTransferInfo(Long taskId, Integer transferType);

    /**
     * 转交任务
     *
     * @param param 转交参数
     */
    void transfer(TaskTransferParam param);

    /**
     * 标注任务复标
     * @param param 参数
     */
    void reLabeling(TaskReLabelingParam param);

}
