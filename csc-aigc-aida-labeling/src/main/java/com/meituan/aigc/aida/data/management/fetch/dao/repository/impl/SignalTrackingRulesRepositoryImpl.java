package com.meituan.aigc.aida.data.management.fetch.dao.repository.impl;

import com.dianping.zebra.util.StringUtils;
import com.meituan.aigc.aida.data.management.fetch.dao.mapper.LlmSignalTrackingRulesMapper;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRulesExample;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.SignalTrackingRulesRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-03 11:13
 * @description 信号埋点规则仓储实现
 */
@Repository
public class SignalTrackingRulesRepositoryImpl implements SignalTrackingRulesRepository {
    @Resource
    private LlmSignalTrackingRulesMapper llmSignalTrackingRulesMapper;

    @Override
    public List<LlmSignalTrackingRules> listSignalTrackingRule(String name) {
        LlmSignalTrackingRulesExample example = new LlmSignalTrackingRulesExample();
        LlmSignalTrackingRulesExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        criteria.andIsDeleteEqualTo(Boolean.FALSE);
        example.setOrderByClause("status DESC, update_time DESC");
        return llmSignalTrackingRulesMapper.selectByExample(example);
    }

    @Override
    public LlmSignalTrackingRules getSignalTrackingRuleById(Long ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }
        return llmSignalTrackingRulesMapper.selectByPrimaryKey(ruleId);
    }

    @Override
    public void insertSignalTrackingRule(LlmSignalTrackingRules rule) {
        if (Objects.isNull(rule)) {
            return;
        }
        llmSignalTrackingRulesMapper.insertSelective(rule);
    }

    @Override
    public void updateSignalTrackingRule(LlmSignalTrackingRules rule) {
        if (Objects.isNull(rule)) {
            return;
        }
        llmSignalTrackingRulesMapper.updateByPrimaryKeySelective(rule);
    }

    @Override
    public void updateStatus(Long ruleId, Integer status) {
        if (Objects.isNull(ruleId)) {
            return;
        }
        LlmSignalTrackingRules rule = new LlmSignalTrackingRules();
        rule.setId(ruleId);
        rule.setStatus(status);
        rule.setUpdateTime(new Date());
        llmSignalTrackingRulesMapper.updateByPrimaryKeySelective(rule);
    }

    @Override
    public List<LlmSignalTrackingRules> listEnableByMisIdsAndTypicalQuestionIds(String staffMis, String typicalQuestionId) {
        return llmSignalTrackingRulesMapper.listEnableByMisIdsAndTypicalQuestionIds(staffMis, typicalQuestionId);
    }

    @Override
    public List<LlmSignalTrackingRules> listEnableByMisIdsAndTypicalQuestionIdsIsBlank(String staffMis) {
        return llmSignalTrackingRulesMapper.listEnableByMisIdsAndTypicalQuestionIdsIsBlank(staffMis);
    }
}
