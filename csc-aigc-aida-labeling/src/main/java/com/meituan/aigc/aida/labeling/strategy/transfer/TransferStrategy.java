package com.meituan.aigc.aida.labeling.strategy.transfer;

import com.meituan.aigc.aida.labeling.param.TaskTransferParam;
import com.meituan.aigc.aida.labeling.pojo.dto.TransferInfoDTO;

public interface TransferStrategy {

    /**
     * 执行转交
     *
     * @param param 转交参数
     */
    void transfer(TaskTransferParam param);

    /**
     * 获取标注主任务id
     *
     * @param subTaskId 子任务id
     * @return 任务id
     */
    Long getLabelingTaskId(Long subTaskId);

    /**
     * 获取任务下质检数据量
     *
     * @param taskId       任务ID
     * @param transferType 质检类型 1-标注 2-质检
     * @return 质检数据量
     */
    TransferInfoDTO getTransferInfo(Long taskId, Integer transferType);

    /**
     * 获取策略code
     *
     * @return 策略code
     */
    Integer getStrategyCode();

    /**
     * 获取当前标注子任务/质检任务的状态
     * @param currentTaskId 标注子任务id/质检任务id
     * @return 标注子任务/质检任务状态
     */
    Integer getCurrentTaskStatus(Long currentTaskId);

    /**
     * 是否可以转交 可以 true  不可以 false
     */
    Boolean canTransfer(Long subTaskId);

}
