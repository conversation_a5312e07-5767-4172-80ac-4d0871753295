package com.meituan.aigc.aida.data.management.fetch.common.enums;

import lombok.Getter;

/**
 * @Author: guo<PERSON><PERSON>
 * @Create: 2025/4/8 14:23
 * @Version: 1.0
 */
@Getter
public enum DataFetchTaskStatus {

    PROCESSING(1, "进行中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "失败"),
    CANCELED(4, "已取消"),
    ;

    private final Integer code;
    private final String name;

    DataFetchTaskStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DataFetchTaskStatus findByCode(Integer code) {
        for (DataFetchTaskStatus status : DataFetchTaskStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
