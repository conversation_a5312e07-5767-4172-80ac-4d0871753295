package com.meituan.aigc.aida.labeling.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InspectionItemExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public InspectionItemExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Long value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Long value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Long value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Long value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Long> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Long> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Long value1, Long value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNull() {
            addCriterion("data_type is null");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNotNull() {
            addCriterion("data_type is not null");
            return (Criteria) this;
        }

        public Criteria andDataTypeEqualTo(Byte value) {
            addCriterion("data_type =", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotEqualTo(Byte value) {
            addCriterion("data_type <>", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThan(Byte value) {
            addCriterion("data_type >", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("data_type >=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThan(Byte value) {
            addCriterion("data_type <", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThanOrEqualTo(Byte value) {
            addCriterion("data_type <=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIn(List<Byte> values) {
            addCriterion("data_type in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotIn(List<Byte> values) {
            addCriterion("data_type not in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeBetween(Byte value1, Byte value2) {
            addCriterion("data_type between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("data_type not between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNull() {
            addCriterion("item_type is null");
            return (Criteria) this;
        }

        public Criteria andItemTypeIsNotNull() {
            addCriterion("item_type is not null");
            return (Criteria) this;
        }

        public Criteria andItemTypeEqualTo(Byte value) {
            addCriterion("item_type =", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotEqualTo(Byte value) {
            addCriterion("item_type <>", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThan(Byte value) {
            addCriterion("item_type >", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("item_type >=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThan(Byte value) {
            addCriterion("item_type <", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeLessThanOrEqualTo(Byte value) {
            addCriterion("item_type <=", value, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeIn(List<Byte> values) {
            addCriterion("item_type in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotIn(List<Byte> values) {
            addCriterion("item_type not in", values, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeBetween(Byte value1, Byte value2) {
            addCriterion("item_type between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andItemTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("item_type not between", value1, value2, "itemType");
            return (Criteria) this;
        }

        public Criteria andEnumListIsNull() {
            addCriterion("enum_list is null");
            return (Criteria) this;
        }

        public Criteria andEnumListIsNotNull() {
            addCriterion("enum_list is not null");
            return (Criteria) this;
        }

        public Criteria andEnumListEqualTo(String value) {
            addCriterion("enum_list =", value, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListNotEqualTo(String value) {
            addCriterion("enum_list <>", value, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListGreaterThan(String value) {
            addCriterion("enum_list >", value, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListGreaterThanOrEqualTo(String value) {
            addCriterion("enum_list >=", value, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListLessThan(String value) {
            addCriterion("enum_list <", value, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListLessThanOrEqualTo(String value) {
            addCriterion("enum_list <=", value, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListLike(String value) {
            addCriterion("enum_list like", value, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListNotLike(String value) {
            addCriterion("enum_list not like", value, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListIn(List<String> values) {
            addCriterion("enum_list in", values, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListNotIn(List<String> values) {
            addCriterion("enum_list not in", values, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListBetween(String value1, String value2) {
            addCriterion("enum_list between", value1, value2, "enumList");
            return (Criteria) this;
        }

        public Criteria andEnumListNotBetween(String value1, String value2) {
            addCriterion("enum_list not between", value1, value2, "enumList");
            return (Criteria) this;
        }

        public Criteria andIsCompareIsNull() {
            addCriterion("is_compare is null");
            return (Criteria) this;
        }

        public Criteria andIsCompareIsNotNull() {
            addCriterion("is_compare is not null");
            return (Criteria) this;
        }

        public Criteria andIsCompareEqualTo(Byte value) {
            addCriterion("is_compare =", value, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareNotEqualTo(Byte value) {
            addCriterion("is_compare <>", value, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareGreaterThan(Byte value) {
            addCriterion("is_compare >", value, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_compare >=", value, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareLessThan(Byte value) {
            addCriterion("is_compare <", value, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareLessThanOrEqualTo(Byte value) {
            addCriterion("is_compare <=", value, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareIn(List<Byte> values) {
            addCriterion("is_compare in", values, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareNotIn(List<Byte> values) {
            addCriterion("is_compare not in", values, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareBetween(Byte value1, Byte value2) {
            addCriterion("is_compare between", value1, value2, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsCompareNotBetween(Byte value1, Byte value2) {
            addCriterion("is_compare not between", value1, value2, "isCompare");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIsNull() {
            addCriterion("is_required is null");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIsNotNull() {
            addCriterion("is_required is not null");
            return (Criteria) this;
        }

        public Criteria andIsRequiredEqualTo(Byte value) {
            addCriterion("is_required =", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotEqualTo(Byte value) {
            addCriterion("is_required <>", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredGreaterThan(Byte value) {
            addCriterion("is_required >", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_required >=", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredLessThan(Byte value) {
            addCriterion("is_required <", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredLessThanOrEqualTo(Byte value) {
            addCriterion("is_required <=", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIn(List<Byte> values) {
            addCriterion("is_required in", values, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotIn(List<Byte> values) {
            addCriterion("is_required not in", values, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredBetween(Byte value1, Byte value2) {
            addCriterion("is_required between", value1, value2, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotBetween(Byte value1, Byte value2) {
            addCriterion("is_required not between", value1, value2, "isRequired");
            return (Criteria) this;
        }

        public Criteria andRankNumIsNull() {
            addCriterion("rank_num is null");
            return (Criteria) this;
        }

        public Criteria andRankNumIsNotNull() {
            addCriterion("rank_num is not null");
            return (Criteria) this;
        }

        public Criteria andRankNumEqualTo(Integer value) {
            addCriterion("rank_num =", value, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumNotEqualTo(Integer value) {
            addCriterion("rank_num <>", value, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumGreaterThan(Integer value) {
            addCriterion("rank_num >", value, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("rank_num >=", value, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumLessThan(Integer value) {
            addCriterion("rank_num <", value, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumLessThanOrEqualTo(Integer value) {
            addCriterion("rank_num <=", value, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumIn(List<Integer> values) {
            addCriterion("rank_num in", values, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumNotIn(List<Integer> values) {
            addCriterion("rank_num not in", values, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumBetween(Integer value1, Integer value2) {
            addCriterion("rank_num between", value1, value2, "rankNum");
            return (Criteria) this;
        }

        public Criteria andRankNumNotBetween(Integer value1, Integer value2) {
            addCriterion("rank_num not between", value1, value2, "rankNum");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Long value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Long value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Long value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Long value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Long value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Long> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Long> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Long value1, Long value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Long value1, Long value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentEnumIsNull() {
            addCriterion("parent_enum is null");
            return (Criteria) this;
        }

        public Criteria andParentEnumIsNotNull() {
            addCriterion("parent_enum is not null");
            return (Criteria) this;
        }

        public Criteria andParentEnumEqualTo(String value) {
            addCriterion("parent_enum =", value, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumNotEqualTo(String value) {
            addCriterion("parent_enum <>", value, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumGreaterThan(String value) {
            addCriterion("parent_enum >", value, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumGreaterThanOrEqualTo(String value) {
            addCriterion("parent_enum >=", value, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumLessThan(String value) {
            addCriterion("parent_enum <", value, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumLessThanOrEqualTo(String value) {
            addCriterion("parent_enum <=", value, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumLike(String value) {
            addCriterion("parent_enum like", value, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumNotLike(String value) {
            addCriterion("parent_enum not like", value, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumIn(List<String> values) {
            addCriterion("parent_enum in", values, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumNotIn(List<String> values) {
            addCriterion("parent_enum not in", values, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumBetween(String value1, String value2) {
            addCriterion("parent_enum between", value1, value2, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andParentEnumNotBetween(String value1, String value2) {
            addCriterion("parent_enum not between", value1, value2, "parentEnum");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCreateMisIsNull() {
            addCriterion("create_mis is null");
            return (Criteria) this;
        }

        public Criteria andCreateMisIsNotNull() {
            addCriterion("create_mis is not null");
            return (Criteria) this;
        }

        public Criteria andCreateMisEqualTo(String value) {
            addCriterion("create_mis =", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisNotEqualTo(String value) {
            addCriterion("create_mis <>", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisGreaterThan(String value) {
            addCriterion("create_mis >", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisGreaterThanOrEqualTo(String value) {
            addCriterion("create_mis >=", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisLessThan(String value) {
            addCriterion("create_mis <", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisLessThanOrEqualTo(String value) {
            addCriterion("create_mis <=", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisLike(String value) {
            addCriterion("create_mis like", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisNotLike(String value) {
            addCriterion("create_mis not like", value, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisIn(List<String> values) {
            addCriterion("create_mis in", values, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisNotIn(List<String> values) {
            addCriterion("create_mis not in", values, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisBetween(String value1, String value2) {
            addCriterion("create_mis between", value1, value2, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateMisNotBetween(String value1, String value2) {
            addCriterion("create_mis not between", value1, value2, "createMis");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateMisIsNull() {
            addCriterion("update_mis is null");
            return (Criteria) this;
        }

        public Criteria andUpdateMisIsNotNull() {
            addCriterion("update_mis is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateMisEqualTo(String value) {
            addCriterion("update_mis =", value, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisNotEqualTo(String value) {
            addCriterion("update_mis <>", value, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisGreaterThan(String value) {
            addCriterion("update_mis >", value, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisGreaterThanOrEqualTo(String value) {
            addCriterion("update_mis >=", value, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisLessThan(String value) {
            addCriterion("update_mis <", value, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisLessThanOrEqualTo(String value) {
            addCriterion("update_mis <=", value, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisLike(String value) {
            addCriterion("update_mis like", value, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisNotLike(String value) {
            addCriterion("update_mis not like", value, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisIn(List<String> values) {
            addCriterion("update_mis in", values, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisNotIn(List<String> values) {
            addCriterion("update_mis not in", values, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisBetween(String value1, String value2) {
            addCriterion("update_mis between", value1, value2, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateMisNotBetween(String value1, String value2) {
            addCriterion("update_mis not between", value1, value2, "updateMis");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}