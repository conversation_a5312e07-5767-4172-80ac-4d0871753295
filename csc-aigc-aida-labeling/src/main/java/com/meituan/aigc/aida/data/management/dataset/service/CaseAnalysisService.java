package com.meituan.aigc.aida.data.management.dataset.service;

import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetCreateResponseDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.dto.DataSetTryLockDTO;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.*;


public interface CaseAnalysisService {

    /**
     * 创建数据集
     *
     * @param param CreateDataSetRequestParam
     * @return 数据集id
     */
    DataSetCreateResponseDTO createDataSet(CreateDataSetRequestParam param);

    /**
     * 更新数据集
     *
     * @param param UpdateDataSetRequestParam
     * @return void
     */
    void updateDataSet(UpdateDataSetRequestParam param);

    /**
     * 获取数据集锁id
     *
     * @param dataSetId 数据集id
     * @return DataSetTryLockDTO
     */
    DataSetTryLockDTO getLockIdByDataSetId(Long dataSetId);

    /**
     * 释放数据集锁
     *
     * @param dataSetId 数据集id
     * @param lockId 锁id
     * @return 释放结果
     */
    boolean releaseDataSetLock(Long dataSetId, String lockId);

    /**
     * 分页查询数据集
     *
     * @param param DataSetPageQueryParam
     * @return PageRemoteData
     */
    DatasetQueryResultDTO pageQueryDataSet(DataSetPageQueryParam param);
}
