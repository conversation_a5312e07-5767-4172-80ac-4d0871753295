package com.meituan.aigc.aida.data.management.fetch.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.csc.aircraft.api.dto.InterfacePolymericResponse;
import com.dianping.csc.aircraft.api.dto.inner.InputParamDTO;
import com.dianping.csc.aircraft.api.service.inner.IFaceRemoteService;
import com.dianping.csc.haitun.kefu.dto.ResponseDTO;
import com.dianping.csc.haitun.kefu.dto.StaffInfoFacadeDTO;
import com.dianping.csc.haitun.kefu.service.StaffFacadeService;
import com.dianping.csc.pacific.domain.common.RespDTO;
import com.dianping.haitun.cases.dto.SessionBriefDTO;
import com.dianping.haitun.cases.service.ManualDialogInfoService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.meituan.aigc.aida.common.util.BizThreadUtils;
import com.meituan.aigc.aida.common.util.ObjectConverter;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.fetch.biz.pacific.butler.ContextUtils;
import com.meituan.aigc.aida.data.management.fetch.common.constans.DolphinChatConstant;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChatMessageFromEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.EventTypeEnum;
import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs;
import com.meituan.aigc.aida.data.management.fetch.dto.BaseSingalTrackingBusParamDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.PopupAcInterfaceDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.DelayTriggerRobotDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.DolphinChatMafkaMessageDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.SignalMockTriggerMessageDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.pacific.PacificButlerContextParamDTO;
import com.meituan.aigc.aida.data.management.fetch.helper.AcInputDistinctHelper;
import com.meituan.aigc.aida.data.management.fetch.helper.AidaSignalLoggingAppInvokeHelper;
import com.meituan.aigc.aida.data.management.fetch.helper.SignalTrackingRulesHelper;
import com.meituan.aigc.aida.data.management.fetch.helper.TypicalQuestionIdQueryHelper;
import com.meituan.aigc.aida.data.management.fetch.mq.producer.DelayTriggerRobotToMafkaProducer;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;
import com.meituan.aigc.aida.data.management.fetch.service.PacificAcInterfaceLogService;
import com.meituan.aigc.aida.data.management.fetch.service.SignalLoggingTriggerService;
import com.meituan.mtrace.Tracer;
import com.sankuai.csc.workbench.bff.api.common.enums.ContactTypeEnum;
import com.sankuai.csc.workbench.bff.api.workbench.dto.InitButlerResDTO;
import com.sankuai.mdp.csc.pacific.context.api.enums.ChannelTypeEnum;
import com.sankuai.pacific.cscpacific.contact.chat.api.dto.PopupDataRequestDTO;
import com.sankuai.pacific.cscpacific.contact.chat.api.remote.PopupRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.meituan.aigc.aida.data.management.fetch.common.constans.DolphinChatConstant.*;

/**
 * 信号模拟触发服务实现类
 * <p>
 * 该服务负责处理客服系统中的信号模拟触发，主要功能包括:
 * 1. 管理缓存(弹窗数据、接口参数、客服信息)
 * 2. 多种接口的参数校验和处理
 * 3. 执行AI搭埋点Workflow机器人
 *
 * <p>
 * 调用AI搭埋点Workflow流程:
 * 1. 根据客服mis/标问，寻找规则List，遍历规则构建映射: <acId, robotList(针对单个规则内去重)>, <acId, delayInvokeRobotList(针对单个规则内去重)>
 * 2. 根据contactId, contactType = online, staffMis查找星脉埋点表中所有的AC input: [acInputs]
 * 3. 对Inputs去重: [acInputObjs]
 * 4. 遍历[acInputObjs]: 找到1.中规则配置的机器人List: [robotList]
 * 5. 遍历机器人, 调用AI搭: invokeAida, delayInvokeAida
 *
 * <AUTHOR>
 * @date 2025/04/03
 */
@Service
@Slf4j
public class SignalLoggingTriggerServiceImpl implements SignalLoggingTriggerService {

    private static final String LOG_PREFIX = "[SignalLoggingTriggerService]";

    private final ExecutorService signalLoggingInvokeAidaExecuteThreadPool = BizThreadUtils.createRhinoThreadPool(
            "signal-logging-invoke-aida-execute", 10, 20, 1000, 60, TimeUnit.MINUTES,
            "signal-logging-invoke-aida-execute-id-%d");

    @Resource
    private ContextUtils contextUtils;

    @Autowired
    private StaffFacadeService staffFacadeService;

    @Autowired
    private PopupRemoteService popupRemoteService;

    @Autowired
    private IFaceRemoteService faceRemoteService;

    @Autowired
    private ManualDialogInfoService manualDialogInfoService;

    @Resource
    private SignalTrackingRulesHelper signalTrackingRulesHelper;

    @Resource
    private AidaSignalLoggingAppInvokeHelper aidaAppInvokeHelper;

    @Resource
    private PacificAcInterfaceLogService pacificAcInterfaceLogService;

    @Resource
    private AcInputDistinctHelper acInputDistinctHelper;

    @Resource
    private TypicalQuestionIdQueryHelper typicalQuestionIdQueryHelper;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    @Resource
    private DelayTriggerRobotToMafkaProducer delayTriggerRobotToMafkaProducer;

    /**
     * 接口参数缓存
     */
    private Cache<String, Map<String, Object>> popupDataCache;
    /**
     * 接口参数缓存
     */
    private LoadingCache<Long, Set<String>> validParamsCache;
    /**
     * 客服信息缓存
     */
    private LoadingCache<Long, StaffInfoFacadeDTO> staffInfoCache;

    /**
     * 初始化缓存组件
     * <p>
     * 初始化三个主要缓存:
     * 1. 弹窗数据缓存(popupDataCache): 缓存客户弹窗信息
     * 2. 接口参数缓存(validParamsCache): 缓存接口的有效参数集合
     * 3. 客服信息缓存(staffInfoCache): 缓存客服基本信息
     */
    @PostConstruct
    public void init() {
        // 初始化弹窗数据缓存
        popupDataCache = CacheBuilder.newBuilder()
                .expireAfterWrite(Optional.ofNullable(dataManagementLionConfig.getPopupCacheExpireMinutes()).orElse(10), TimeUnit.MINUTES)
                .maximumSize(1000) // 最多缓存1000个key
                .build();
        log.info("{}, init popupDataCache with expireMinutes = [{}]", LOG_PREFIX, dataManagementLionConfig.getPopupCacheExpireMinutes());

        // 初始化接口参数缓存
        validParamsCache = CacheBuilder.newBuilder()
                .expireAfterWrite(Optional.ofNullable(dataManagementLionConfig.getInterfaceParamCacheMinutes()).orElse(10), TimeUnit.MINUTES)
                .maximumSize(1000)
                .build(new CacheLoader<Long, Set<String>>() {
                    @Override
                    public Set<String> load(@NotNull Long interfaceId) {
                        InterfacePolymericResponse response = faceRemoteService.getPolymericResponse(interfaceId);
                        return response.getInputParamDTOList().stream()
                                .map(InputParamDTO::getCode)
                                .collect(Collectors.toSet());
                    }
                });
        log.info("{}, init validParamsCache with expireMinutes = [{}]", LOG_PREFIX, dataManagementLionConfig.getInterfaceParamCacheMinutes());

        staffInfoCache = CacheBuilder.newBuilder()
                .expireAfterWrite(Optional.ofNullable(dataManagementLionConfig.getStaffCacheMinutes()).orElse(30), TimeUnit.MINUTES)
                .maximumSize(1000)
                .build(new CacheLoader<Long, StaffInfoFacadeDTO>() {
                    @Override
                    public StaffInfoFacadeDTO load(@NotNull Long chatStaffId) {
                        log.info("{}, 加载客服信息缓存, chatStaffId = [{}]", LOG_PREFIX, chatStaffId);
                        ResponseDTO<StaffInfoFacadeDTO> chatStaffInfoResp = staffFacadeService
                                .getStaffInfoById(chatStaffId.intValue());
                        log.info("{}, 加载客服信息缓存响应, resp = [{}]", LOG_PREFIX,
                                ObjectConverter.convertToJson(chatStaffInfoResp));

                        if (null == chatStaffInfoResp || !chatStaffInfoResp.isSuccess()
                                || null == chatStaffInfoResp.getData()) {
                            log.warn("{}, 加载客服信息缓存失败, chatStaffId = [{}]", LOG_PREFIX, chatStaffId);
                            return null;
                        }
                        return chatStaffInfoResp.getData();
                    }
                });
        log.info("{}, init staffInfoCache with expireMinutes = [{}]", LOG_PREFIX, dataManagementLionConfig.getStaffCacheMinutes());
    }

    /**
     * 通过海豚消息处理AI搭埋点机器人应用触发
     * <p>
     * 将消息字符串解析为消息DTO并进行处理
     *
     * @param messageString 消息字符串，包含事件和会话信息
     */
    @Override
    public void processSignalLoggingTriggerByMessageString(String messageString) {
        if (StringUtils.isBlank(messageString)) {
            return;
        }
        // 1. 解析消息
        DolphinChatMafkaMessageDTO dolphinChatMafkaMessageDTO = buildFromMessageJsonString(messageString);
        if (isInvalidEvent(dolphinChatMafkaMessageDTO)) {
            log.info("{}, 事件需要过滤", LOG_PREFIX);
            return;
        }
        log.info("{}, 消费有效的海豚对话消息, MessageDTO = {}", LOG_PREFIX, JSONObject.toJSONString(dolphinChatMafkaMessageDTO));
        // 2. 初始化事件数据
        initEventDataStaffInfo(dolphinChatMafkaMessageDTO);
        SignalMockTriggerMessageDTO signalMockTriggerMessageDTO = buildSignalMockTriggerMessage(
                dolphinChatMafkaMessageDTO);
        // 3. 处理触发
        processSignalLoggingTrigger(signalMockTriggerMessageDTO);
    }

    /**
     * 处理信号Mock触发消息
     * <p>
     * 执行流程:
     * 1. 校验消息合法性
     * 2. 构建全局参数，包括会话信息和弹窗数据
     * 3. 获取并执行所有Mock脚本
     * 4. 并行处理每个脚本的前置接口记录
     *
     * @param messageDTO 触发消息DTO，包含会话信息和用户消息
     */
    public void processSignalLoggingTrigger(SignalMockTriggerMessageDTO messageDTO) {
        log.info("{}, 消费海豚消息, 触发AI搭埋点机器人, msg:{}", LOG_PREFIX, messageDTO);
        // 1. 解析消息
        if (Objects.isNull(messageDTO)) {
            String errorMsg = String.format("%s, 消息体解析为空", LOG_PREFIX);
            log.warn(errorMsg);
            Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "EmptyMessage", errorMsg);
            return;
        }

        // 2. 构建AI搭随路参数信息
        BaseSingalTrackingBusParamDTO busParamDTO = buildBaseSingalTrackingBusParamDTO(messageDTO);
        if (Objects.isNull(busParamDTO)) {
            return;
        }

        // 3. 根据标问列表、客服mis, 获取弹屏AC接口与机器人ID的映射关系
        Triple<Map<String, List<String>>, Map<String, List<String>>, Map<String, Integer>> acInterfaceToAidaAppIdsMap = signalTrackingRulesHelper
                .getAcInterfaceToAidaAppIdsMap(buildRobotListQuery(busParamDTO));
        // 所有机器人id
        Map<String, List<String>> acToRobotIdsMap = acInterfaceToAidaAppIdsMap.getLeft();
        if (MapUtils.isEmpty(acToRobotIdsMap)) {
            log.warn("{}, 没有可用的机器人列表", LOG_PREFIX);
            return;
        }
        // 延迟执行机器人id
        Map<String, List<String>> acInterfaceToDelayRobotIdsMap = acInterfaceToAidaAppIdsMap.getMiddle();

        // 4. 收集所有配置的弹屏AC ID
        Set<String> cfgPopupAcIds = acToRobotIdsMap.keySet();

        // 5. 根据 contactId, contactType = online, staffMis 查找星脉埋点表中所有的AC input
        List<PacificAcInterfaceLogWithBlobs> acLogs = pacificAcInterfaceLogService
                .listByContactIdContactTypeAndStaffMis(busParamDTO.getContactId(), busParamDTO.getContactType(),
                        busParamDTO.getStaffMis());
        // 获取一个包含acId和acInput的列表
        List<PopupAcInterfaceDTO> toTriggerAcInputList = getToTriggerAcInputList(acLogs, cfgPopupAcIds);

        // 没有找到需要触发的AC接口记录，直接返回
        if (CollectionUtils.isEmpty(toTriggerAcInputList)) {
            log.warn("{}, 没有找到需要触发的AC接口记录, Message:{}", LOG_PREFIX, JSONObject.toJSONString(messageDTO));
            return;
        }

        // 6. 根据input字段去重，使用辅助类处理去重逻辑
        List<PopupAcInterfaceDTO> distinctToTriggerAcInputList = acInputDistinctHelper
                .getDistinctAcInputs(toTriggerAcInputList, getValidParams(toTriggerAcInputList));

        // 7. 根据去重后的input, 依次调用从需要执行的AI搭机器人ID, 并根据AI搭应用返回进行埋点
        doTriggerAidaSignalLoggingApp(distinctToTriggerAcInputList, acToRobotIdsMap, busParamDTO, acInterfaceToDelayRobotIdsMap, acInterfaceToAidaAppIdsMap.getRight());
    }

    /**
     * 检查事件是否无效
     * <p>
     * 事件在以下情况下被认为无效:
     * 1. 事件DTO为空
     * 2. 事件数据为空
     * 3. 客服员ID无效
     * 4. 消息来源不是客服
     *
     * @param eventDTO 事件DTO
     * @return true如果事件无效，false如果事件有效
     */
    private boolean isInvalidEvent(DolphinChatMafkaMessageDTO eventDTO) {
        if (Objects.isNull(eventDTO)) {
            log.warn("{}, 事件DTO为空", LOG_PREFIX);
            return true;
        }
        DolphinChatMafkaMessageDTO.EventData eventObject = eventDTO.getEventObject();
        if (Objects.isNull(eventObject)) {
            log.warn("{}, 事件数据为空", LOG_PREFIX);
            return true;
        }
        if (BooleanUtils.isTrue(eventObject.getIsSystem())) {
            log.warn("{}, 事件数据为系统消息", LOG_PREFIX);
            return true;
        }
        DolphinChatMafkaMessageDTO.BusinessData businessData = eventDTO.getBusinessData();
        if (Objects.isNull(businessData)) {
            log.warn("{}, 业务数据为空", LOG_PREFIX);
            return true;
        }
        Long chatStaffId = businessData.getChatStaffId();
        if (null == chatStaffId || chatStaffId <= 0) {
            log.warn("{}, 客服ID无效: {}", LOG_PREFIX, chatStaffId);
            return true;
        }
        ChatMessageFromEnum chatMessageFromEnum = businessData.getChatMessageFromEnum();
        if (Objects.isNull(chatMessageFromEnum)) {
            log.warn("{}, 消息来源为空", LOG_PREFIX);
            return true;
        }
        /**
         * 只处理客服发送的消息
         */
        boolean isNotFromStaff = ChatMessageFromEnum.STAFF != chatMessageFromEnum;
        if (isNotFromStaff) {
            log.warn("{}, 消息不是来自客服, 来源: {}", LOG_PREFIX, chatMessageFromEnum);
        }
        return isNotFromStaff;
    }

    /**
     * 执行AI搭机器人调用逻辑
     *
     * @param distinctToTriggerAcInputList
     * @param acToRobotIdsMap
     * @param busParamDTO
     */
    private void doTriggerAidaSignalLoggingApp(List<PopupAcInterfaceDTO> distinctToTriggerAcInputList, Map<String, List<String>> acToRobotIdsMap,
                                               BaseSingalTrackingBusParamDTO busParamDTO, Map<String, List<String>> acInterfaceToDelayRobotIdsMap, Map<String, Integer> acInterfaceToDelayTimeMap) {
        if (CollectionUtils.isEmpty(distinctToTriggerAcInputList)) {
            log.warn("{}, 去重后没有可用的AC接口输入数据", LOG_PREFIX);
            return;
        }
        for (PopupAcInterfaceDTO acInterface : distinctToTriggerAcInputList) {
            String acIdStr = String.valueOf(acInterface.getInterfaceId());
            List<String> robotIds = acToRobotIdsMap.get(acIdStr);
            List<String> delayRobotIds = acInterfaceToDelayRobotIdsMap.get(acIdStr);

            if (CollectionUtils.isEmpty(robotIds)) {
                log.warn("{}, AC接口[{}]没有对应的AI搭机器人ID", LOG_PREFIX, acIdStr);
                continue;
            }

            log.info("{}, 开始处理AC接口[{}], 找到对应的AI搭机器人ID: {}", LOG_PREFIX, acIdStr, robotIds);

            // 对每个机器人ID进行调用
            for (String robotId : robotIds) {
                try {
                    // 准备调用数据
                    Map inputData = ObjectConverter.convert(acInterface.getInput(), Map.class);
                    if (MapUtils.isEmpty(inputData)) {
                        log.warn("{}, AC接口[{}]输入数据解析失败, 跳过机器人[{}]调用", LOG_PREFIX, acIdStr, robotId);
                        continue;
                    }
                    // 延迟执行机器人，不直接调用aidaAppInvokeHelper.invokeAidaAppAndLogging
                    if (CollectionUtils.isNotEmpty(delayRobotIds) && delayRobotIds.contains(robotId)) {
                        executeDelayRobot(busParamDTO, acInterfaceToDelayTimeMap, robotId, acIdStr, inputData);
                        continue;
                    }

                    // 准备机器人调用参数
                    // robotId inputData
                    log.info("{}, 调用AI搭机器人, robotId: {}, acId: {}", LOG_PREFIX, robotId, acIdStr);
                    signalLoggingInvokeAidaExecuteThreadPool
                            .submit(() -> aidaAppInvokeHelper.invokeAidaAppAndLogging(robotId, inputData, busParamDTO,
                                    acIdStr));
                } catch (Exception e) {
                    log.error("{}, 处理AC接口[{}]的AI搭机器人[{}]调用准备时发生异常", LOG_PREFIX, acIdStr, robotId, e);
                    Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "PrepareRobotCallException",
                            e.getMessage());
                }
            }
        }

    }

    private void executeDelayRobot(BaseSingalTrackingBusParamDTO busParamDTO, Map<String, Integer> acInterfaceToDelayTimeMap, String robotId, String acIdStr, Map inputData) {
        if (MapUtils.isNotEmpty(acInterfaceToDelayTimeMap)
                && Objects.nonNull(acInterfaceToDelayTimeMap.get(acIdStr))) {
            DelayTriggerRobotDTO delayTriggerRobot = DelayTriggerRobotDTO.builder()
                    .robotId(robotId)
                    .acIdStr(acIdStr)
                    .traceId(Tracer.id())
                    .inputData(inputData)
                    .busParamDTO(busParamDTO)
                    .build();
            String message = JSONObject.toJSONString(delayTriggerRobot);
            Long delayTime = (long) (acInterfaceToDelayTimeMap.get(acIdStr) * 1000);
            delayTriggerRobotToMafkaProducer.sendDelayMessage(message, delayTime);
        }
    }

    /**
     * 获取接口的有效参数列表，用于后续去重
     *
     * @param toTriggerAcInputList
     * @return
     */
    private Set<String> getValidParams(List<PopupAcInterfaceDTO> toTriggerAcInputList) {
        // 获取接口的有效参数列表，用于后续去重
        Set<String> validParams = Collections.emptySet();
        try {
            PopupAcInterfaceDTO firstRecord = toTriggerAcInputList.get(0);
            Long interfaceId = firstRecord.getInterfaceId();
            if (interfaceId != null) {
                log.info("{}, 获取接口有效参数, interfaceId:{}", LOG_PREFIX, interfaceId);
                validParams = validParamsCache.get(interfaceId);
            } else {
                log.warn("{}, 无法获取接口ID, 将使用空参数集合进行去重", LOG_PREFIX);
            }
        } catch (Exception e) {
            log.error("{}, 获取接口参数失败, 将使用空参数集合进行去重", LOG_PREFIX, e);
        }
        return validParams;
    }

    /**
     * 获取需要执行的AC接口与机器人ID的映射关系
     *
     * @param acLogs        前置接口日志列表
     * @param cfgPopupAcIds 配置的弹窗AC ID集合
     * @return 需要触发的AC接口列表
     */
    private static List<PopupAcInterfaceDTO> getToTriggerAcInputList(List<PacificAcInterfaceLogWithBlobs> acLogs,
                                                                     Set<String> cfgPopupAcIds) {
        List<PopupAcInterfaceDTO> acInterfaces = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(acLogs)) {
            for (PacificAcInterfaceLogWithBlobs acLog : acLogs) {
                Integer acId = acLog.getInterfaceId();
                String acInput = acLog.getInput();
                // 检查AC ID是否为空
                if (acId == null || StringUtils.isBlank(acInput)) {
                    String errorMsg = String.format("%s, AC ID OR Input为空", LOG_PREFIX);
                    log.error(errorMsg);
                    Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "EmptyAcIdOrInput", errorMsg);
                    continue;
                }

                String acIdStr = String.valueOf(acId);
                if (cfgPopupAcIds.contains(acIdStr)) {
                    // 创建PopupAcInterfaceDTO对象
                    PopupAcInterfaceDTO dto = new PopupAcInterfaceDTO();
                    dto.setId(acLog.getId());
                    dto.setInterfaceId(Long.valueOf(acId));
                    dto.setInput(acInput);
                    dto.setSendTime(acLog.getSendTime() != null ? Long.valueOf(acLog.getSendTime()) : null);
                    acInterfaces.add(dto);
                }
            }
        }
        return acInterfaces;
    }

    /**
     * 构建信号埋点业务参数DTO
     *
     * @param messageDTO
     * @return
     */
    private BaseSingalTrackingBusParamDTO buildBaseSingalTrackingBusParamDTO(SignalMockTriggerMessageDTO messageDTO) {
        BaseSingalTrackingBusParamDTO busParamDTO = new BaseSingalTrackingBusParamDTO();
        // 生成随机32位字符串作为uuid
        busParamDTO.setUuid(UUID.randomUUID().toString().replace("-", ""));
        busParamDTO.setContactType(messageDTO.getContactType());
        String contactId = getContactId(messageDTO);
        if (StringUtils.isBlank(contactId)) {
            return null;
        }
        if (Objects.equals(ChannelEnum.ONLINE.getCode(), messageDTO.getContactType())) {
            busParamDTO.setChatId(contactId);
        }
        busParamDTO.setContactId(contactId);
        Long sessionId = getSessionId(contactId);
        if (sessionId == null) {
            return null;
        }
        busParamDTO.setSessionId(String.valueOf(sessionId));
        List<String> typicalIds = typicalQuestionIdQueryHelper.getTypicalQuestionIds(String.valueOf(sessionId));
        if (CollectionUtils.isEmpty(typicalIds)) {
            log.info("{}, 未找到标问ID列表, sessionId:{}", LOG_PREFIX, sessionId);
        }
        busParamDTO.setTypicalQuestionIds(typicalIds);
        busParamDTO.setChatMessageFromType(messageDTO.getChatMessageFromType());
        busParamDTO.setBizMessageId(messageDTO.getMessageId());
        busParamDTO.setMessageOccurredTime(messageDTO.getMessageOccurredTime());
        busParamDTO.setStaffMis(messageDTO.getStaffMis());
        busParamDTO.setStaffMessageContent(messageDTO.getStaffMessageContent());
        busParamDTO.setPathParamsButler(messageDTO.getContextParam());
        busParamDTO.setPathParamsPopup(
                getPopupData(Objects.equals(ChannelEnum.ONLINE.getCode(), messageDTO.getContactType())
                        ? ContactTypeEnum.CHAT.getCode()
                        : ContactTypeEnum.CALL.getCode(), contactId));
        return busParamDTO;
    }

    /**
     * 获取会话ID
     *
     * @param contactId
     * @return
     */
    @Nullable
    private Long getSessionId(String contactId) {
        SessionBriefDTO sessionByManualDialogId = manualDialogInfoService
                .getSessionByManualDialogId(Long.valueOf(contactId));
        Long sessionId = sessionByManualDialogId.getSessionId();
        if (Objects.isNull(sessionId)) {
            log.error("{}, 查询太平洋会话ID失败, contactId:{}", LOG_PREFIX, contactId);
            Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "GetSessionByManualDialogIdFAIL", "");
            return null;
        }
        return sessionId;
    }

    /**
     * 获取ContactId
     *
     * @param messageDTO
     * @return
     */
    @Nullable
    private static String getContactId(SignalMockTriggerMessageDTO messageDTO) {
        String contactId = messageDTO.getContactId();
        if (!NumberUtils.isDigits(contactId)) {
            log.error("{}, contactId:{} 不是数字", LOG_PREFIX, contactId);
            Cat.logEvent("Singal.Aida.Workflow.Execute", "FAIL", "InvalidContactId", "");
            return null;
        }
        return contactId;
    }

    /**
     * 构建机器人列表查询参数
     *
     * @param busParamDTO
     * @return
     */
    private RobotListQuery buildRobotListQuery(BaseSingalTrackingBusParamDTO busParamDTO) {
        RobotListQuery robotListQuery = new RobotListQuery();
        // 逗号分隔的标问列表
        robotListQuery.setTypicalQuestionIds(String.join(CommonConstants.FieldName.ENGLISH_COMMA,
                Optional.ofNullable(busParamDTO.getTypicalQuestionIds())
                        .orElse(Collections.emptyList())));
        // 客服mis
        robotListQuery.setMisList(busParamDTO.getStaffMis());
        return robotListQuery;
    }

    /**
     * 获取弹窗数据
     * <p>
     * 先从缓存中获取，如果缓存不存在则远程调用服务获取
     * 获取到的数据会被缓存一定时间(默认10分钟)
     *
     * @param type      弹窗类型，1=电话，2=在线
     * @param contactId 会话标识
     * @return 弹窗数据映射，如果获取失败则返回空映射
     */
    private Map<String, Object> getPopupData(Integer type, String contactId) {
        String cacheKey = String.format("%s:%s", type, contactId);

        // 尝试从缓存获取
        Map<String, Object> cachedData = popupDataCache.getIfPresent(cacheKey);
        if (cachedData != null) {
            log.info("{}, getPopupData from cache, type = [{}], contactId = [{}]", LOG_PREFIX, type, contactId);
            return cachedData;
        }

        PopupDataRequestDTO requestDTO = new PopupDataRequestDTO();
        requestDTO.setType(type);
        requestDTO.setContactId(contactId);
        try {
            log.info("{}, getPopupData from remote, type = [{}], contactId = [{}]", LOG_PREFIX, type, contactId);
            RespDTO<Map<String, Object>> popupData = popupRemoteService.getPopupData(requestDTO);
            log.info("{}, getPopupData, result = [{}]", LOG_PREFIX, ObjectConverter.convertToJson(popupData));

            Map<String, Object> result = Collections.emptyMap();
            if (Objects.nonNull(popupData) && popupData.isSuccess()) {
                result = popupData.getData();
            }

            // 如果结果不为空，则缓存
            if (!result.isEmpty()) {
                popupDataCache.put(cacheKey, result);
            }
            return result;
        } catch (Exception e) {
            log.error("{}, getPopupData, type = [{}], contactId = [{}]", LOG_PREFIX, type, contactId, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 构建信号Mock触发消息
     * <p>
     * 将事件数据转换为触发消息，并初始化管家信息
     *
     * @param eventDTO 事件数据，包含会话和客服信息
     * @return 触发消息DTO，包含会话、客服和管家信息
     */
    private SignalMockTriggerMessageDTO buildSignalMockTriggerMessage(
            DolphinChatMafkaMessageDTO dolphinChatMafkaMessageDTO) {
        DolphinChatMafkaMessageDTO.EventData eventData = dolphinChatMafkaMessageDTO.getEventObject();
        DolphinChatMafkaMessageDTO.BusinessData businessData = dolphinChatMafkaMessageDTO.getBusinessData();
        if (Objects.isNull(eventData) || Objects.isNull(businessData)) {
            return null;
        }
        SignalMockTriggerMessageDTO messageDTO = new SignalMockTriggerMessageDTO();
        messageDTO.setContactId(String.valueOf(eventData.getServiceId()));
        messageDTO.setMessageOccurredTime(String.valueOf(eventData.getOccurredOn()));
        messageDTO.setContactType(ChannelEnum.ONLINE.getCode());
        messageDTO.setChatMessageFromType(
                Objects.nonNull(businessData.getChatMessageFromEnum()) ? businessData.getChatMessageFromEnum().getCode()
                        : CommonConstants.EMPTY_STRING);
        messageDTO.setMessageId(String.valueOf(eventData.getMessageId()));
        messageDTO.setStaffMis(businessData.getPacificStaffMis());
        messageDTO.setUserMsgContent(businessData.getCustomerMessageContent());
        messageDTO.setStaffMessageContent(businessData.getStaffMessageContent());

        // 初始化管家信息
        try {
            Pair<InitButlerResDTO, Map<String, String>> contextInfo = contextUtils
                    .getContextInfo(String.valueOf(eventData.getServiceId()), ChannelTypeEnum.ON_LINE);
            if (null != contextInfo) {
                PacificButlerContextParamDTO pacificButlerContextParamDTO = new PacificButlerContextParamDTO();
                pacificButlerContextParamDTO.setButlerRes(contextInfo.getLeft());
                pacificButlerContextParamDTO.setButlerParamMap(contextInfo.getRight());
                messageDTO.setContextParam(pacificButlerContextParamDTO);
            }
        } catch (Exception e) {
            log.error("{}, 初始化管家信息异常, serviceId:{}", LOG_PREFIX, eventData.getServiceId(), e);
        }
        return messageDTO;
    }

    /**
     * 从消息JSON字符串构建消息DTO
     * <p>
     * 解析消息字符串并设置事件类型和消息来源
     * 支持的事件类型:
     * - 分配结果通知: 会话开始
     * - 客服/用户消息发送: 会话进行中
     *
     * @param messageJsonString 消息JSON字符串
     * @return 消息DTO，如果解析失败或事件类型不匹配则返回null
     */
    public DolphinChatMafkaMessageDTO buildFromMessageJsonString(String messageJsonString) {
        if (StringUtils.isBlank(messageJsonString)) {
            return null;
        }
        final String eventTypeKye = "eventType";
        String eventType = JSONObject.parseObject(messageJsonString).getString(eventTypeKye);
        // 提前过滤非 客服发送的消息 事件
        if (!Objects.equals(eventType, DolphinChatConstant.STAFF_MESSAGE_SEND_EVENT)) {
            log.info("{}, 接收到非客服发送的消息事件类型 [{}], 跳过处理.", LOG_PREFIX, eventType);
            return null;
        }
        DolphinChatMafkaMessageDTO dolphinChatMafkaMessageDTO = ObjectConverter.convert(messageJsonString,
                DolphinChatMafkaMessageDTO.class);
        if (null == dolphinChatMafkaMessageDTO) {
            log.error("{}, 解析消息JSON字符串失败, messageJsonString:{}", LOG_PREFIX, messageJsonString);
            return null;
        }
        DolphinChatMafkaMessageDTO.EventData eventData = ObjectConverter.convert(dolphinChatMafkaMessageDTO.getEvent(),
                DolphinChatMafkaMessageDTO.EventData.class);
        if (null == eventData) {
            // 此处失败可能是因为 staffMessageSendEvent 的 event 格式也不对，需要记录错误
            log.error("{}, 解析 staffMessageSendEvent 事件数据失败, event:{}", LOG_PREFIX,
                    dolphinChatMafkaMessageDTO.getEvent());
            return null;
        }
        dolphinChatMafkaMessageDTO.setEventObject(eventData);

        // 创建业务数据对象
        DolphinChatMafkaMessageDTO.BusinessData businessData = buildBusinessData(dolphinChatMafkaMessageDTO);
        dolphinChatMafkaMessageDTO.setBusinessData(businessData);
        return dolphinChatMafkaMessageDTO;
    }

    /**
     * 构建业务数据
     *
     * @param messageDTO 海豚聊天消息DTO
     * @return 业务数据对象
     */
    private static DolphinChatMafkaMessageDTO.BusinessData buildBusinessData(
            DolphinChatMafkaMessageDTO messageDTO) {
        // 创建业务数据对象
        DolphinChatMafkaMessageDTO.BusinessData businessData = new DolphinChatMafkaMessageDTO.BusinessData();

        // 根据事件类型设置会话状态
        if (Objects.equals(messageDTO.getEventType(), ALLOCATE_RESULT_NOTICE_EVENT)) {
            // 分配结果通知事件,设置为会话开始状态
            businessData.setEventTypeEnum(EventTypeEnum.CHAT_SESSION_START);
        } else {
            // 其他事件设置为会话进行中状态
            businessData.setEventTypeEnum(EventTypeEnum.CHAT_SESSION_IN_PROCESS);
        }

        //  获取事件数据对象
        DolphinChatMafkaMessageDTO.EventData eventData = messageDTO.getEventObject();

        // 处理消息发送事件
        if (Objects.equals(messageDTO.getEventType(), STAFF_MESSAGE_SEND_EVENT)
                || Objects.equals(messageDTO.getEventType(), USER_MESSAGE_SEND_EVENT)) {
            // 获取客户和客服的消息内容
            String customerMessageContent = eventData.getContent();
            String staffMessageContent = eventData.getMsgContent();
            businessData.setCustomerMessageContent(customerMessageContent);
            businessData.setStaffMessageContent(staffMessageContent);
            // 设置消息来源(客服/客户)
            businessData.setChatMessageFromEnum(
                    Objects.equals(messageDTO.getEventType(), STAFF_MESSAGE_SEND_EVENT)
                            ? ChatMessageFromEnum.STAFF
                            : ChatMessageFromEnum.CUSTOMER);
        }
        // 设置客服ID
        businessData.setChatStaffId(eventData.getStaffId());
        return businessData;
    }

    /**
     * 初始化事件数据中的客服信息
     * <p>
     * 通过客服ID获取Pacific客服ID和MIS号
     *
     * @param messageDTO 事件数据
     */
    private void initEventDataStaffInfo(DolphinChatMafkaMessageDTO messageDTO) {
        if (!ObjectUtils.allNotNull(messageDTO, messageDTO.getEventObject(), messageDTO.getBusinessData())) {
            log.error("{}, 事件数据不完整: {}", LOG_PREFIX, messageDTO);
            return;
        }
        DolphinChatMafkaMessageDTO.BusinessData businessData = messageDTO.getBusinessData();
        StaffInfoFacadeDTO staffInfoFacadeDTO = getPacificStaffIdByChatStaffId(messageDTO.getEventObject().getStaffId());
        if (Objects.isNull(staffInfoFacadeDTO)) {
            log.info("{}, 初始化事件数据中的客服信息失败, 客服信息为空, chatStaffId = [{}]", LOG_PREFIX,
                    messageDTO.getEventObject().getStaffId());
            return;
        }
        businessData.setPacificStaffId(Long.parseLong(staffInfoFacadeDTO.getBizId()));
        businessData.setPacificStaffMis(staffInfoFacadeDTO.getMisNo());
    }

    /**
     * 通过客服员ID获取Pacific客服信息
     * <p>
     * 从缓存中获取客服信息，如果缓存不存在则远程调用服务获取
     *
     * @param chatStaffId 客服员ID
     * @return 客服信息DTO，如果获取失败则返回null
     */
    private StaffInfoFacadeDTO getPacificStaffIdByChatStaffId(Long chatStaffId) {
        if (null == chatStaffId) {
            return null;
        }
        try {
            return staffInfoCache.get(chatStaffId);
        } catch (Exception e) {
            log.error("{}, 获取客服信息异常, chatStaffId = [{}]", LOG_PREFIX, chatStaffId, e);
            return null;
        }
    }
}
