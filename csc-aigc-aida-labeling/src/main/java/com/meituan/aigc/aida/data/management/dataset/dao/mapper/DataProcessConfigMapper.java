package com.meituan.aigc.aida.data.management.dataset.dao.mapper;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessConfig;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessConfigExample;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessConfigWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DataProcessConfigMapper {
    long countByExample(DataProcessConfigExample example);

    int deleteByExample(DataProcessConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DataProcessConfigWithBLOBs record);

    int insertSelective(DataProcessConfigWithBLOBs record);

    List<DataProcessConfigWithBLOBs> selectByExampleWithBLOBs(DataProcessConfigExample example);

    List<DataProcessConfig> selectByExample(DataProcessConfigExample example);

    DataProcessConfigWithBLOBs selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DataProcessConfigWithBLOBs record, @Param("example") DataProcessConfigExample example);

    int updateByExampleWithBLOBs(@Param("record") DataProcessConfigWithBLOBs record, @Param("example") DataProcessConfigExample example);

    int updateByExample(@Param("record") DataProcessConfig record, @Param("example") DataProcessConfigExample example);

    int updateByPrimaryKeySelective(DataProcessConfigWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(DataProcessConfigWithBLOBs record);

    int updateByPrimaryKey(DataProcessConfig record);
}