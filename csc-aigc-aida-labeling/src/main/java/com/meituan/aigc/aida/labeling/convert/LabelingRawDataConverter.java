package com.meituan.aigc.aida.labeling.convert;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * @Author: guowenhui
 * @Create: 2025/6/11 11:01
 * @Version: 1.0
 */
@Mapper
public interface LabelingRawDataConverter {

    LabelingRawDataConverter INSTANCE = Mappers.getMapper(LabelingRawDataConverter.class);

    @Mappings(value = {
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "taskId", ignore = true)
    })
    LabelingTaskRawData converterToLabelingTaskRawData(LabelingTaskRawData labelingTaskRawData);

}
