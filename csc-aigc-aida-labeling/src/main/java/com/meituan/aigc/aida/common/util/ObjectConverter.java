package com.meituan.aigc.aida.common.util;

import java.util.Map;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 对象转换工具类
 * 1. 支持 Map、String、JSONObject 转换为指定类
 * 2. 支持 JSON 字符串转换为指定类型
 * 3. 支持对象转JSON字符串
 * 4. 支持字符串转换为 Long 类型
 *
 * <AUTHOR>
 * @date 2025/04/03
 */
@Slf4j
public class ObjectConverter {

    private static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = new ObjectMapper();
        // 配置ObjectMapper忽略未知字段，避免反序列化失败
        OBJECT_MAPPER.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                false);
    }

    /**
     * 对象转换, 支持 Map、String、JSONObject 转换为指定类
     * 
     * @param source
     * @param targetClass
     * @return
     * @param <T>
     */
    public static <T> T convert(Object source, Class<T> targetClass) {
        try {
            if (source == null) {
                return null;
            }
            if (targetClass.isInstance(source)) {
                return targetClass.cast(source);
            }

            if (source instanceof Map) {
                return OBJECT_MAPPER.convertValue(source, targetClass);
            }

            if (source instanceof String) {
                return OBJECT_MAPPER.readValue((String) source, targetClass);
            }

            return OBJECT_MAPPER.convertValue(source, targetClass);
        } catch (Exception e) {
            log.error("转换对象失败. 源对象: {}, 目标类: {}", source, targetClass.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @param object 要转换的对象
     * @return 转换后的JSON字符串，如果转换失败则返回null
     */
    public static String convertToJson(Object object) {
        if (object == null) {
            return null;
        }

        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            log.error("将对象转换为JSON字符串失败. 对象: {}", object, e);
            return null;
        }
    }

    /**
     * 将字符串转换为Long类型
     *
     * @param str 要转换的字符串
     * @return 转换后的Long值，如果转换失败则返回null
     */
    public static Long stringToLong(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }

        try {
            return Long.parseLong(str.trim());
        } catch (NumberFormatException e) {
            log.warn("无法将字符串转换为Long: {}", str, e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     *
     * @param json          JSON字符串
     * @param typeReference 类型引用
     * @param <T>           目标类型
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        try {
            if (json == null || json.isEmpty()) {
                return null;
            }
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (Exception e) {
            log.error("JSON转换失败. json: {}, typeReference: {}", json, typeReference.getType(), e);
            return null;
        }
    }
}