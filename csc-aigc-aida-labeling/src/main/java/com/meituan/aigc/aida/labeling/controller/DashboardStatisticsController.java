package com.meituan.aigc.aida.labeling.controller;

import com.meituan.aigc.aida.labeling.param.*;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.meituan.aigc.aida.labeling.pojo.vo.MetricVO;
import com.meituan.aigc.aida.labeling.pojo.vo.DailyTrendsVO;
import java.util.List;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.BizTypeAndTaskTypeQueryVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DashboardOverviewStatsVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DashboardTaskItemVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.BizQualityCheckRateVO;
import com.meituan.aigc.aida.labeling.service.DashboardStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 15:02
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/dashboard")
public class DashboardStatisticsController {

    @Resource
    private DashboardStatisticsService dashboardStatisticsService;

    /**
     * 质检人员排行榜统计图表
     * @param param 请求参数
     * @return 结果
     */
    @PostMapping("/statistical-charts/person")
    public Result<List<MetricVO>> getPersonStatisticalCharts(@RequestBody PersonStatisticsRequestParam param) {
        List<MetricVO> data = dashboardStatisticsService.getPersonStatisticalCharts(param);
        return Result.ok(data);
    }

    /**
     * 大盘任务快照列表查询
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/task-list")
    public Result<PageData<DashboardTaskItemVO>> pageTasks(@RequestBody DashboardTaskListQueryParam param){
        return Result.ok(dashboardStatisticsService.pageTasks(param));
    }

    /**
     * 查询业务场景和任务类型等数据
     * @return 结果
     */
    @GetMapping("/query-condition")
    public Result<BizTypeAndTaskTypeQueryVO> queryCondition(){
        return Result.ok(dashboardStatisticsService.queryCondition());
    }

    /**
     * 大盘总览数据统计
     * @param param 参数
     * @return 统计结果
     */
    @PostMapping("/overview-stats")
    public Result<DashboardOverviewStatsVO> overviewStats(@RequestBody BaseStatisticsRequestParam param){
        return Result.ok(dashboardStatisticsService.overviewStats(param));
    }

    /**
     * 每日标注/质检趋势
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/statistical-charts/daily-trends")
    public Result<DailyTrendsVO> getDailyTrends(@RequestBody DailyStatisticsRequestParam param) {
        DailyTrendsVO data = dashboardStatisticsService.getDailyTrends(param);
        return Result.ok(data);
    }

    /**
     * 各业务质检正确率/单个业务质检正确率趋势
     * @param param 参数
     * @return 返回结果
     */
    @PostMapping("/statistical-charts/biz-correct-rate")
    public Result<BizQualityCheckRateVO> getBizQualityCheckRate(@RequestBody BaseStatisticsRequestParam param){
        return Result.ok(dashboardStatisticsService.getBizQualityCheckRate(param));
    }

    /**
     * 人员明细详情
     * @param param 参数
     * @return 结果
     */
    @PostMapping("/personnel-statistics")
    public Result<PageData<PersonnelDetailVO>> getPersonnelDetail(@RequestBody PersonnelDetailRequestParam param){
        PageData<PersonnelDetailVO> data = dashboardStatisticsService.getPersonnelDetail(param);
        return Result.ok(data);
    }
}
