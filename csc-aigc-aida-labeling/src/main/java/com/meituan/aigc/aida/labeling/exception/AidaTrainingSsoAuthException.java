package com.meituan.aigc.aida.labeling.exception;

import com.sankuai.it.sso.sdk.enums.AuthFailedCodeEnum;

/**
 * SSO鉴权失败异常
 */
public class AidaTrainingSsoAuthException extends AidaTrainingException {

    /**
     * SSO鉴权失败的错误码
     */
    private AuthFailedCodeEnum failedCode;

    public AidaTrainingSsoAuthException() {
        super();
    }

    public AidaTrainingSsoAuthException(String message) {
        super(message);
    }

    public AidaTrainingSsoAuthException(String message, AuthFailedCodeEnum failedCode) {
        super(message);
        this.failedCode = failedCode;
    }

    public AidaTrainingSsoAuthException(Throwable t) {
        super(t);
    }

    /**
     * 带有错误消息和原因的构造函数
     * 
     * @param message 错误消息
     * @param cause   原因异常
     */
    public AidaTrainingSsoAuthException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 获取SSO鉴权失败的错误码
     * 
     * @return 鉴权失败的错误码枚举
     */
    public AuthFailedCodeEnum getFailedCode() {
        return failedCode;
    }
}