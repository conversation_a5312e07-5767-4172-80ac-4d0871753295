package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.dianping.cat.util.StringUtils;
import com.meituan.aigc.aida.labeling.common.enums.LabelingTaskGroupStatus;
import com.meituan.aigc.aida.labeling.convert.LabelingTaskGroupConvert;
import com.meituan.aigc.aida.labeling.dao.mapper.LabelingTaskGroupMapper;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroupExample;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskGroupRepository;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelTaskGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:09
 * @Version: 1.0
 */
@Repository
@Slf4j
public class LabelingTaskGroupRepositoryImpl implements LabelingTaskGroupRepository {

    @Resource
    private LabelingTaskGroupMapper labelingTaskGroupMapper;

    @Override
    public List<LabelTaskGroupDTO> listLabelGroupByTaskId(Long taskId) {
        int[] statusCodes = {
                LabelingTaskGroupStatus.FAILED.getCode()
        };
        List<Byte> statusList = IntStream.of(statusCodes)
                .mapToObj(code -> (byte) code)
                .collect(Collectors.toList());
        LabelingTaskGroupExample example = new LabelingTaskGroupExample();
        example.createCriteria().andTaskIdEqualTo(taskId).andRecycleStatusNotIn(statusList).andIsDeletedEqualTo(false);
        List<LabelingTaskGroup> labelingTaskGroups = labelingTaskGroupMapper.selectByExample(example);
        return labelingTaskGroups.stream().map(LabelingTaskGroupConvert::convertLabelTaskGroupDTO).collect(Collectors.toList());
    }

    @Override
    public LabelingTaskGroup getLabelGroupById(Long groupId) {
        if (Objects.isNull(groupId)) {
            return null;
        }
        return labelingTaskGroupMapper.selectByPrimaryKey(groupId);
    }

    @Override
    public void insertLabelingTaskGroup(LabelingTaskGroup labelingTaskGroup) {
        if (Objects.isNull(labelingTaskGroup)) {
            return;
        }
        labelingTaskGroupMapper.insertGroup(labelingTaskGroup);
    }

    @Override
    public void updateGroup(LabelingTaskGroup group) {
        if (Objects.isNull(group)) {
            return;
        }
        labelingTaskGroupMapper.updateByPrimaryKeySelective(group);
    }

    @Override
    public List<LabelingTaskGroup> listAllLabelGroupByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return Collections.emptyList();
        }
        LabelingTaskGroupExample example = new LabelingTaskGroupExample();
        example.createCriteria().andTaskIdEqualTo(taskId).andIsDeletedEqualTo(false);
        return labelingTaskGroupMapper.selectByExample(example);
    }

    @Override
    public void updateGroupNameById(Long groupId, String groupName) {
        if (Objects.isNull(groupId) || StringUtils.isBlank(groupName)) {
            return;
        }
        LabelingTaskGroup group = new LabelingTaskGroup();
        group.setId(groupId);
        group.setGroupName(groupName);
        labelingTaskGroupMapper.updateByPrimaryKeySelective(group);
    }

    @Override
    public void updateGroupStatusByTaskId(Long taskId, Integer status) {
        if (Objects.isNull(taskId) || Objects.isNull(status)) {
            return;
        }
        LabelingTaskGroup group = new LabelingTaskGroup();
        group.setRecycleStatus(status);
        LabelingTaskGroupExample example = new LabelingTaskGroupExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        labelingTaskGroupMapper.updateByExampleSelective(group, example);
    }

    @Override
    public void insert(LabelingTaskGroup group) {
        labelingTaskGroupMapper.insert(group);
    }

    @Override
    public void updateById(LabelingTaskGroup labelingTaskGroup) {
        if (Objects.isNull(labelingTaskGroup)){
            return;
        }
        labelingTaskGroupMapper.updateByPrimaryKey(labelingTaskGroup);
    }
}
