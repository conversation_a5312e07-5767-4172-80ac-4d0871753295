package com.meituan.aigc.aida.data.management.fetch.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DataFetchRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DataFetchRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(String value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(String value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(String value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(String value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(String value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(String value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLike(String value) {
            addCriterion("session_id like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLike(String value) {
            addCriterion("session_id not like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<String> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<String> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(String value1, String value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(String value1, String value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andContactIdIsNull() {
            addCriterion("contact_id is null");
            return (Criteria) this;
        }

        public Criteria andContactIdIsNotNull() {
            addCriterion("contact_id is not null");
            return (Criteria) this;
        }

        public Criteria andContactIdEqualTo(String value) {
            addCriterion("contact_id =", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdNotEqualTo(String value) {
            addCriterion("contact_id <>", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdGreaterThan(String value) {
            addCriterion("contact_id >", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdGreaterThanOrEqualTo(String value) {
            addCriterion("contact_id >=", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdLessThan(String value) {
            addCriterion("contact_id <", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdLessThanOrEqualTo(String value) {
            addCriterion("contact_id <=", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdLike(String value) {
            addCriterion("contact_id like", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdNotLike(String value) {
            addCriterion("contact_id not like", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdIn(List<String> values) {
            addCriterion("contact_id in", values, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdNotIn(List<String> values) {
            addCriterion("contact_id not in", values, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdBetween(String value1, String value2) {
            addCriterion("contact_id between", value1, value2, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdNotBetween(String value1, String value2) {
            addCriterion("contact_id not between", value1, value2, "contactId");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentIsNull() {
            addCriterion("signal_data_content is null");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentIsNotNull() {
            addCriterion("signal_data_content is not null");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentEqualTo(String value) {
            addCriterion("signal_data_content =", value, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentNotEqualTo(String value) {
            addCriterion("signal_data_content <>", value, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentGreaterThan(String value) {
            addCriterion("signal_data_content >", value, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentGreaterThanOrEqualTo(String value) {
            addCriterion("signal_data_content >=", value, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentLessThan(String value) {
            addCriterion("signal_data_content <", value, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentLessThanOrEqualTo(String value) {
            addCriterion("signal_data_content <=", value, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentLike(String value) {
            addCriterion("signal_data_content like", value, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentNotLike(String value) {
            addCriterion("signal_data_content not like", value, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentIn(List<String> values) {
            addCriterion("signal_data_content in", values, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentNotIn(List<String> values) {
            addCriterion("signal_data_content not in", values, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentBetween(String value1, String value2) {
            addCriterion("signal_data_content between", value1, value2, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andSignalDataContentNotBetween(String value1, String value2) {
            addCriterion("signal_data_content not between", value1, value2, "signalDataContent");
            return (Criteria) this;
        }

        public Criteria andStaffMisIsNull() {
            addCriterion("staff_mis is null");
            return (Criteria) this;
        }

        public Criteria andStaffMisIsNotNull() {
            addCriterion("staff_mis is not null");
            return (Criteria) this;
        }

        public Criteria andStaffMisEqualTo(String value) {
            addCriterion("staff_mis =", value, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisNotEqualTo(String value) {
            addCriterion("staff_mis <>", value, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisGreaterThan(String value) {
            addCriterion("staff_mis >", value, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisGreaterThanOrEqualTo(String value) {
            addCriterion("staff_mis >=", value, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisLessThan(String value) {
            addCriterion("staff_mis <", value, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisLessThanOrEqualTo(String value) {
            addCriterion("staff_mis <=", value, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisLike(String value) {
            addCriterion("staff_mis like", value, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisNotLike(String value) {
            addCriterion("staff_mis not like", value, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisIn(List<String> values) {
            addCriterion("staff_mis in", values, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisNotIn(List<String> values) {
            addCriterion("staff_mis not in", values, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisBetween(String value1, String value2) {
            addCriterion("staff_mis between", value1, value2, "staffMis");
            return (Criteria) this;
        }

        public Criteria andStaffMisNotBetween(String value1, String value2) {
            addCriterion("staff_mis not between", value1, value2, "staffMis");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNull() {
            addCriterion("message_id is null");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNotNull() {
            addCriterion("message_id is not null");
            return (Criteria) this;
        }

        public Criteria andMessageIdEqualTo(String value) {
            addCriterion("message_id =", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotEqualTo(String value) {
            addCriterion("message_id <>", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThan(String value) {
            addCriterion("message_id >", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanOrEqualTo(String value) {
            addCriterion("message_id >=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThan(String value) {
            addCriterion("message_id <", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanOrEqualTo(String value) {
            addCriterion("message_id <=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLike(String value) {
            addCriterion("message_id like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotLike(String value) {
            addCriterion("message_id not like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIn(List<String> values) {
            addCriterion("message_id in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotIn(List<String> values) {
            addCriterion("message_id not in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdBetween(String value1, String value2) {
            addCriterion("message_id between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotBetween(String value1, String value2) {
            addCriterion("message_id not between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeIsNull() {
            addCriterion("message_occurred_time is null");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeIsNotNull() {
            addCriterion("message_occurred_time is not null");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeEqualTo(Date value) {
            addCriterion("message_occurred_time =", value, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeNotEqualTo(Date value) {
            addCriterion("message_occurred_time <>", value, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeGreaterThan(Date value) {
            addCriterion("message_occurred_time >", value, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("message_occurred_time >=", value, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeLessThan(Date value) {
            addCriterion("message_occurred_time <", value, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeLessThanOrEqualTo(Date value) {
            addCriterion("message_occurred_time <=", value, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeIn(List<Date> values) {
            addCriterion("message_occurred_time in", values, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeNotIn(List<Date> values) {
            addCriterion("message_occurred_time not in", values, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeBetween(Date value1, Date value2) {
            addCriterion("message_occurred_time between", value1, value2, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andMessageOccurredTimeNotBetween(Date value1, Date value2) {
            addCriterion("message_occurred_time not between", value1, value2, "messageOccurredTime");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeIsNull() {
            addCriterion("chat_message_from_type is null");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeIsNotNull() {
            addCriterion("chat_message_from_type is not null");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeEqualTo(String value) {
            addCriterion("chat_message_from_type =", value, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeNotEqualTo(String value) {
            addCriterion("chat_message_from_type <>", value, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeGreaterThan(String value) {
            addCriterion("chat_message_from_type >", value, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeGreaterThanOrEqualTo(String value) {
            addCriterion("chat_message_from_type >=", value, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeLessThan(String value) {
            addCriterion("chat_message_from_type <", value, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeLessThanOrEqualTo(String value) {
            addCriterion("chat_message_from_type <=", value, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeLike(String value) {
            addCriterion("chat_message_from_type like", value, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeNotLike(String value) {
            addCriterion("chat_message_from_type not like", value, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeIn(List<String> values) {
            addCriterion("chat_message_from_type in", values, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeNotIn(List<String> values) {
            addCriterion("chat_message_from_type not in", values, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeBetween(String value1, String value2) {
            addCriterion("chat_message_from_type between", value1, value2, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andChatMessageFromTypeNotBetween(String value1, String value2) {
            addCriterion("chat_message_from_type not between", value1, value2, "chatMessageFromType");
            return (Criteria) this;
        }

        public Criteria andContactTypeIsNull() {
            addCriterion("contact_type is null");
            return (Criteria) this;
        }

        public Criteria andContactTypeIsNotNull() {
            addCriterion("contact_type is not null");
            return (Criteria) this;
        }

        public Criteria andContactTypeEqualTo(String value) {
            addCriterion("contact_type =", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotEqualTo(String value) {
            addCriterion("contact_type <>", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeGreaterThan(String value) {
            addCriterion("contact_type >", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeGreaterThanOrEqualTo(String value) {
            addCriterion("contact_type >=", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeLessThan(String value) {
            addCriterion("contact_type <", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeLessThanOrEqualTo(String value) {
            addCriterion("contact_type <=", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeLike(String value) {
            addCriterion("contact_type like", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotLike(String value) {
            addCriterion("contact_type not like", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeIn(List<String> values) {
            addCriterion("contact_type in", values, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotIn(List<String> values) {
            addCriterion("contact_type not in", values, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeBetween(String value1, String value2) {
            addCriterion("contact_type between", value1, value2, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotBetween(String value1, String value2) {
            addCriterion("contact_type not between", value1, value2, "contactType");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsIsNull() {
            addCriterion("typical_question_ids is null");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsIsNotNull() {
            addCriterion("typical_question_ids is not null");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsEqualTo(String value) {
            addCriterion("typical_question_ids =", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsNotEqualTo(String value) {
            addCriterion("typical_question_ids <>", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsGreaterThan(String value) {
            addCriterion("typical_question_ids >", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsGreaterThanOrEqualTo(String value) {
            addCriterion("typical_question_ids >=", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsLessThan(String value) {
            addCriterion("typical_question_ids <", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsLessThanOrEqualTo(String value) {
            addCriterion("typical_question_ids <=", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsLike(String value) {
            addCriterion("typical_question_ids like", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsNotLike(String value) {
            addCriterion("typical_question_ids not like", value, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsIn(List<String> values) {
            addCriterion("typical_question_ids in", values, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsNotIn(List<String> values) {
            addCriterion("typical_question_ids not in", values, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsBetween(String value1, String value2) {
            addCriterion("typical_question_ids between", value1, value2, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andTypicalQuestionIdsNotBetween(String value1, String value2) {
            addCriterion("typical_question_ids not between", value1, value2, "typicalQuestionIds");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdIsNull() {
            addCriterion("popup_ac_id is null");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdIsNotNull() {
            addCriterion("popup_ac_id is not null");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdEqualTo(String value) {
            addCriterion("popup_ac_id =", value, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdNotEqualTo(String value) {
            addCriterion("popup_ac_id <>", value, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdGreaterThan(String value) {
            addCriterion("popup_ac_id >", value, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdGreaterThanOrEqualTo(String value) {
            addCriterion("popup_ac_id >=", value, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdLessThan(String value) {
            addCriterion("popup_ac_id <", value, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdLessThanOrEqualTo(String value) {
            addCriterion("popup_ac_id <=", value, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdLike(String value) {
            addCriterion("popup_ac_id like", value, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdNotLike(String value) {
            addCriterion("popup_ac_id not like", value, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdIn(List<String> values) {
            addCriterion("popup_ac_id in", values, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdNotIn(List<String> values) {
            addCriterion("popup_ac_id not in", values, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdBetween(String value1, String value2) {
            addCriterion("popup_ac_id between", value1, value2, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andPopupAcIdNotBetween(String value1, String value2) {
            addCriterion("popup_ac_id not between", value1, value2, "popupAcId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Boolean value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Boolean value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Boolean value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Boolean value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Boolean value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Boolean> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Boolean> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Boolean value1, Boolean value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}