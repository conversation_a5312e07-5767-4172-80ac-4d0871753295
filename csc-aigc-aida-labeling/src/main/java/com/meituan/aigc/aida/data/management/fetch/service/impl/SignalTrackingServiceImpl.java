package com.meituan.aigc.aida.data.management.fetch.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.zebra.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.common.squirrel.SquirrelClient;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.fetch.common.convertor.SignalTrackingConvertor;
import com.meituan.aigc.aida.data.management.fetch.common.enums.LlmSignalTrackingRulesStatus;
import com.meituan.aigc.aida.data.management.fetch.dao.model.LlmSignalTrackingRules;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.SignalTrackingRulesRepository;
import com.meituan.aigc.aida.data.management.fetch.dto.AidaAppCfgDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.SignalTrackingRuleDTO;
import com.meituan.aigc.aida.data.management.fetch.helper.SignalTrackingRulesHelper;
import com.meituan.aigc.aida.data.management.fetch.service.SignalTrackingService;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.common.constant.RedisConstant;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.csc.aigc.runtime.api.AidaConfigService;
import com.meituan.csc.aigc.runtime.dto.aida.AppConfigDTO;
import com.meituan.csc.aigc.runtime.dto.aida.param.AppConfigRequestParam;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-03 11:05
 * @description 信号埋点规则服务
 */
@Service
@Slf4j
public class SignalTrackingServiceImpl implements SignalTrackingService {

    /**
     * MIS字段分隔符
     */
    private static final String MIS_SEPARATOR = ",";
    
    /**
     * 延迟执行最小时间（秒）
     */
    private static final Integer MIN_DELAY_TIME = 5;
    
    /**
     * 延迟执行最大时间（秒）
     */
    private static final Integer MAX_DELAY_TIME = 3600;

    @Resource
    private SignalTrackingRulesRepository signalTrackingRulesRepository;

    @Autowired
    private AidaConfigService aidaConfigService;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    @Resource
    private SquirrelClient squirrelClient;

    @Override
    public PageData<SignalTrackingRuleDTO> pageSignalTrackingRuleList(String name, String creatorName, Integer pageNum, Integer pageSize) {
        log.info("开始查询埋点规则列表,taskName:{},creatorName:{},pageNum:{},pageSize:{}", name, creatorName, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<LlmSignalTrackingRules> llmSignalTrackingRules = signalTrackingRulesRepository
                .listSignalTrackingRule(name, creatorName);
        if (CollectionUtils.isEmpty(llmSignalTrackingRules)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<LlmSignalTrackingRules> pageInfo = new PageInfo<>(llmSignalTrackingRules);
        PageHelper.clearPage();
        List<SignalTrackingRuleDTO> signalTrackingRuleList = llmSignalTrackingRules.stream()
                .map(SignalTrackingConvertor::convertSignalTrackingRuleDTO)
                .collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), signalTrackingRuleList);
    }

    @Override
    public SignalTrackingRuleDTO getSignalTrackingRuleDetail(Long ruleId) {
        log.info("查询埋点规则详情,规则Id:{}", ruleId);
        LlmSignalTrackingRules signalTrackingRuleById = signalTrackingRulesRepository.getSignalTrackingRuleById(ruleId);
        if (Objects.isNull(signalTrackingRuleById)) {
            return null;
        }
        return SignalTrackingConvertor.convertSignalTrackingRuleDTO(signalTrackingRuleById);
    }

    @Override
    public void createSignalTrackingRule(SignalTrackingRuleDTO rule) {
        checkParam(rule);
        checkAppId(rule);
        
        // 检查MIS数量限制
        // 业务规则：无论新增还是修改规则，都需要确保生效后的MIS总数不超过配置上限
        if (Objects.isNull(rule.getId())) {
            // 新增规则场景：检查新规则的MIS加入后是否超限
            checkMisCountLimit(rule.getMisIds(), null);
        } else {
            // 修改规则场景：排除当前规则的MIS，检查修改后的MIS是否超限
            checkMisCountLimit(rule.getMisIds(), rule.getId());
        }
        
        LlmSignalTrackingRules llmSignalTrackingRules = SignalTrackingConvertor.convertLlmSignalTrackingRules(rule);
        if (Objects.isNull(llmSignalTrackingRules)) {
            return;
        }
        // 新增埋点规则
        if (Objects.isNull(rule.getId())) {
            // 仅新增时候设置创建人mis
            llmSignalTrackingRules.setCreatorMis(Optional.ofNullable(UserUtils.getUser()).map(User::getLogin)
                    .orElse(CommonConstants.FieldName.UNKNOWN));
            llmSignalTrackingRules.setCreatorName(Optional.ofNullable(UserUtils.getUser()).map(User::getName)
                    .orElse(llmSignalTrackingRules.getCreatorMis()));
            signalTrackingRulesRepository.insertSignalTrackingRule(llmSignalTrackingRules);
            log.info("新增埋点规则成功,ruleId:{}", llmSignalTrackingRules.getId());
        } else {
            // 更新埋点规则
            llmSignalTrackingRules.setUpdateTime(new Date());
            signalTrackingRulesRepository.updateSignalTrackingRule(llmSignalTrackingRules);
            log.info("更新埋点规则成功,ruleId:{}", llmSignalTrackingRules.getId());
        }
        // 清除弹窗AC ID缓存
        clearVerifyExistPopupAcIdCache();
    }

    @Override
    public void deleteSignalTrackingRule(Long ruleId) {
        log.info("开始删除埋点规则,规则Id:{}", ruleId);
        CheckUtil.paramCheck(Objects.nonNull(ruleId), "埋点规则Id不能为空");
        LlmSignalTrackingRules signalTrackingRule = signalTrackingRulesRepository.getSignalTrackingRuleById(ruleId);
        CheckUtil.paramCheck(Objects.nonNull(signalTrackingRule), "埋点规则不存在");
        signalTrackingRule.setIsDelete(Boolean.TRUE);
        signalTrackingRule.setUpdateTime(new Date());
        signalTrackingRulesRepository.updateSignalTrackingRule(signalTrackingRule);
        log.info("埋点规则删除成功,规则Id:{}", ruleId);
        // 清除弹窗AC ID缓存
        clearVerifyExistPopupAcIdCache();
    }

    @Override
    public void updateStatus(Long ruleId, Integer status) {
        log.info("开始更新埋点规则状态,规则Id:{},状态:{}", ruleId, status);
        CheckUtil.paramCheck(Objects.nonNull(ruleId), "埋点规则Id不能为空");
        CheckUtil.paramCheck(Objects.nonNull(status), "埋点规则状态不能为空");
        
        // 启用规则时需要检查MIS数量限制
        // 业务规则：只有在启用规则时才需要检查，禁用规则不会增加MIS数量负担
        if (Objects.equals(status, LlmSignalTrackingRulesStatus.ENABLE.getCode())) {
            // 获取当前规则信息
            LlmSignalTrackingRules currentRule = signalTrackingRulesRepository.getSignalTrackingRuleById(ruleId);
            CheckUtil.paramCheck(Objects.nonNull(currentRule), "埋点规则不存在");
            
            // 检查启用该规则后MIS数量是否超限
            // 注意：这里需要排除当前规则ID，因为当前规则状态还是禁用的，不在生效规则列表中
            checkMisCountLimit(currentRule.getMisIds(), ruleId);
        }
        
        signalTrackingRulesRepository.updateStatus(ruleId, status);
        // 清除弹窗AC ID缓存
        clearVerifyExistPopupAcIdCache();
    }

    @Override
    public void clearVerifyExistPopupAcIdCache() {
        try {
            log.info("清除弹窗AC ID缓存");
            squirrelClient.delete(RedisConstant.VERIFY_EXIST_POPUP_AC_ID, "");
        } catch (Exception e) {
            log.error("清除弹窗AC ID缓存失败", e);
        }
    }

    @Override
    public String getSignalTrackingRobotConfig() {
        return dataManagementLionConfig.getSignalTrackingRobotConfig();
    }

    /**
     * 参数校验
     *
     * @param rule
     */
    private void checkParam(SignalTrackingRuleDTO rule) {
        if (Objects.isNull(rule)) {
            throw new AidaTrainingCheckException("入参不能为空");
        }
        if (StringUtils.isBlank(rule.getName())) {
            throw new AidaTrainingCheckException("埋点规则名称不能为空");
        }
        if (StringUtils.isBlank(rule.getMisIds())) {
            throw new AidaTrainingCheckException("客服mis号不能为空");
        }
        if (StringUtils.isBlank(rule.getAcInterface())) {
            throw new AidaTrainingCheckException("太平洋弹屏AC接口ID不能为空");
        }
        if (StringUtils.isBlank(rule.getAidaAppCfg())) {
            throw new AidaTrainingCheckException("请至少添加一个机器人");
        }
        AidaAppCfgDTO aidaAppCfgDTO = JSON.parseObject(rule.getAidaAppCfg(), new TypeReference<AidaAppCfgDTO>() {
        });
        if (CollectionUtils.isNotEmpty(aidaAppCfgDTO.getDelayTriggerRobot()) && Objects.isNull(aidaAppCfgDTO.getDelayTime())){
            throw new AidaTrainingCheckException("请填写延迟执行时间");
        }
        if (Objects.nonNull(aidaAppCfgDTO.getDelayTime()) && (aidaAppCfgDTO.getDelayTime() < MIN_DELAY_TIME || aidaAppCfgDTO.getDelayTime() > MAX_DELAY_TIME)){
            throw new AidaTrainingCheckException(String.format("延迟执行时间范围%d-%d秒", MIN_DELAY_TIME, MAX_DELAY_TIME));
        }
    }

    /**
     * 检查MIS数量是否超过限制
     * <p>
     * 业务背景：
     * 为了控制信号埋点规则中配置的MIS总数，避免系统负载过高，需要对所有生效规则中的MIS进行数量限制。
     * 当用户新增规则、修改规则或启用规则时，系统会检查所有生效规则中去重后的MIS总数是否超过配置的上限。
     *
     * @param newMisIds 新增或修改的MIS IDs（逗号分隔的字符串）
     * @param excludeRuleId 排除的规则ID（修改规则时使用，避免重复计算当前规则的MIS；新增时传null）
     */
    private void checkMisCountLimit(String newMisIds, Long excludeRuleId) {
        try {
            // 获取配置并检查是否需要限制
            DataManagementLionConfig.SignalRuleUpperLimitConfig config = dataManagementLionConfig.getSignalRuleUpperLimitConfig();
            if (config.getLimitNumber() == null || config.getLimitNumber() <= 0) {
                return; // 支持动态关闭限制功能
            }

            // 统计所有MIS数量
            Set<String> allMisSet = collectAllMisFromRules(excludeRuleId);
            addNewMisToSet(allMisSet, newMisIds);
            
            // 检查是否超过限制
            validateMisCountLimit(allMisSet, config);
            
        } catch (AidaTrainingCheckException e) {
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            log.error("检查MIS数量限制时发生异常", e);
            // 设计决策：异常情况下不阻止业务流程，只记录日志
        }
    }

    /**
     * 从所有生效规则中收集MIS，去重统计
     *
     * @param excludeRuleId 需要排除的规则ID
     * @return 去重后的MIS集合
     */
    private Set<String> collectAllMisFromRules(Long excludeRuleId) {
        List<LlmSignalTrackingRules> enabledRules = signalTrackingRulesRepository.listAllEnabledRules();
        Set<String> allMisSet = new HashSet<>();
        
        for (LlmSignalTrackingRules rule : enabledRules) {
            // 修改规则场景：排除当前正在修改的规则，避免重复计算
            if (excludeRuleId != null && Objects.equals(rule.getId(), excludeRuleId)) {
                continue;
            }
            
            addMisFromStringToSet(allMisSet, rule.getMisIds());
        }
        
        return allMisSet;
    }

    /**
     * 将新的MIS添加到统计集合中
     *
     * @param allMisSet 现有MIS集合
     * @param newMisIds 新的MIS字符串
     */
    private void addNewMisToSet(Set<String> allMisSet, String newMisIds) {
        addMisFromStringToSet(allMisSet, newMisIds);
    }

    /**
     * 解析MIS字符串并添加到集合（逗号分隔格式处理）
     *
     * @param misSet MIS集合
     * @param misIds MIS字符串（格式："user1,user2,user3"）
     */
    private void addMisFromStringToSet(Set<String> misSet, String misIds) {
        if (StringUtils.isBlank(misIds)) {
            return;
        }
        
        String[] misArray = misIds.split(MIS_SEPARATOR);
        for (String mis : misArray) {
            String trimmedMis = mis.trim();
            if (StringUtils.isNotBlank(trimmedMis)) {
                misSet.add(trimmedMis); // Set集合自动去重
            }
        }
    }

    /**
     * 验证MIS数量是否超过限制
     *
     * @param allMisSet 所有MIS集合
     * @param config 限制配置
     */
    private void validateMisCountLimit(Set<String> allMisSet, DataManagementLionConfig.SignalRuleUpperLimitConfig config) {
        if (allMisSet.size() > config.getLimitNumber()) {
            log.warn("MIS数量超过限制，当前数量：{}，限制数量：{}", allMisSet.size(), config.getLimitNumber());
            throw new AidaTrainingCheckException(config.getLimitMsg());
        }
        
        log.info("MIS数量检查通过，当前数量：{}，限制数量：{}", allMisSet.size(), config.getLimitNumber());
    }

    /**
     * 校验appId
     *
     * @param rule 埋点规则
     */
    private void checkAppId(SignalTrackingRuleDTO rule) {
        String aidaAppCfg = rule.getAidaAppCfg();
        Set<String> robotIdSet = SignalTrackingRulesHelper.getRobotId(aidaAppCfg);
        if (CollectionUtils.isEmpty(robotIdSet)) {
            throw new AidaTrainingCheckException("埋点机器人不能为空");
        }
        for (String robotId : robotIdSet) {
            try {
                AppConfigRequestParam param = new AppConfigRequestParam();
                param.setAppId(robotId);
                param.setAuthorization(String.format("Bearer %s", aidaConfigService.getAppToken(robotId)));
                param.setUser(Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse(""));
                AppConfigDTO appConfigNoToken = aidaConfigService.getAppConfigNoToken(param);
                if (Objects.isNull(appConfigNoToken)) {
                    throw new AidaTrainingCheckException("埋点机器人不存在: " + robotId);
                }
            } catch (Exception e) {
                log.error("获取机器人信息失败，robotId={}，错误信息：{}", robotId, e.getMessage(), e);
                throw new AidaTrainingCheckException(e.getMessage() + "，appId=" + robotId);
            }
        }
    }
}
