package com.meituan.aigc.aida.data.management.dataset.strategy.field.impl;

import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 处理后信号字段策略
 */
@Slf4j
@Component
public class ProcessedSignalFieldStrategy extends AbstractDataFieldStrategy {

    // 匹配{key:value}格式的正则表达式
    private static final String SIGNAL_PATTERN = "\\{\\s*([^:{}]+)\\s*:\\s*([^:{}]+)\\s*\\}";

    @Override
    public Integer getStrategyType() {
        return FieldTypeEnum.PROCESSED_SIGNAL.getCode();
    }

    @Override
    public void validateValue(String value) throws Exception {
        if (org.apache.commons.lang3.StringUtils.isBlank(value)) {
            return;
        }

        // 处理后信号：必须满足{key:value}格式，可以是多个，多个之间可以用空格或者换行分隔，或者不分隔
        String normalizedValue = value.trim();

        // 使用正则表达式匹配所有{key:value}格式
        Pattern pattern = Pattern.compile(SIGNAL_PATTERN);
        Matcher matcher = pattern.matcher(normalizedValue);

        if (!matcher.find()) {
            throw new AidaRpcException("处理后信号格式不正确，必须满足{key:value}格式");
        }

        // 验证整个字符串是否都符合格式（去掉匹配的部分后，剩余部分应该只有空格或换行）
        String remaining = normalizedValue;
        matcher.reset();
        while (matcher.find()) {
            remaining = remaining.replace(matcher.group(), "");
        }

        // 检查剩余部分是否只包含空白字符
        if (!remaining.trim().isEmpty()) {
            throw new AidaRpcException("处理后信号格式不正确，包含非法字符：" + remaining.trim());
        }
    }

    @Override
    public void processFieldData(String columnName, String value,
                                Map<String, Object> textData,
                                Map<String, Object> flattenedData,
                                Map<String, Date> dateData,
                                Map<String, Object> nestedData) {
        // 处理后信号：按照校验的格式存为json
        try {
            Map<String, String> signalMap = new HashMap<>();
            String normalizedValue = value.trim();

            Pattern pattern = Pattern.compile(SIGNAL_PATTERN);
            Matcher matcher = pattern.matcher(normalizedValue);

            while (matcher.find()) {
                String key = matcher.group(1).trim();
                String val = matcher.group(2).trim();
                signalMap.put(key, val);
            }

            if (!signalMap.isEmpty()) {
                flattenedData.put(columnName, signalMap);
            } else {
                textData.put(columnName, value);
            }
        } catch (Exception e) {
            // 处理失败时作为文本存储
            log.warn("处理后信号字段解析失败，作为文本处理：{}", e.getMessage());
            textData.put(columnName, value);
        }
    }
} 