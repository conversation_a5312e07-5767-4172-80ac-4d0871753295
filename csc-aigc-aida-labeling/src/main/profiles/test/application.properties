management.server.port=8081
management.endpoints.web.base-path=/monitor
management.endpoints.web.path-mapping.health=/alive

server.port=8081

sso.auth-url=http://api.upm-in.sankuai.com
sso.sso-url=https://sso.sankuai.com
sso.sso-api-url=http://api.sso-in.sankuai.com

mybatis.mapper-locations=classpath:mappers/*.xml,classpath:mappers/data.management/*.xml
mybatis.type-aliases-package=com.meituan.aigc.aida.labeling.dao.model,com.meituan.aigc.aida.data.management.fetch.dao.model

sso.clientId=0ba083f39f
sso.secret=a4e73a3bd6664a28a20682b4101db082

spring.servlet.multipart.max-file-size=300MB
spring.servlet.multipart.max-request-size=300MB