package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON>
 * @Create: 2025/2/28 16:46
 * @Version: 1.0
 */
@Getter
public enum QualityCheckDataStatus {
    WAITING_QUALITY_CHECK(1, "待质检"),
    QUALITY_CHECKED(2, "已质检");

    private final int code;
    private final String value;

    QualityCheckDataStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static QualityCheckDataStatus getByCode(int code) {
        for (QualityCheckDataStatus status : QualityCheckDataStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
