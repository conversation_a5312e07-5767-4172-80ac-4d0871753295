package com.meituan.aigc.aida.data.management.fetch.dao.repository.impl;

import com.meituan.aigc.aida.data.management.fetch.dao.mapper.PacificAcInterfaceLogMapper;
import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.PacificAcInterfaceLogRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/4/3 16:12
 * @Version: 1.0
 */
@Repository
public class PacificAcInterfaceLogRepositoryImpl implements PacificAcInterfaceLogRepository {
    
    @Resource
    private PacificAcInterfaceLogMapper pacificAcInterfaceLogMapper;
    
    @Override
    public void addSelective(PacificAcInterfaceLogWithBlobs pacificAcInterfaceLogWithBlobs) {
        if (Objects.isNull(pacificAcInterfaceLogWithBlobs)){
            return;
        }
        pacificAcInterfaceLogMapper.insertSelective(pacificAcInterfaceLogWithBlobs);
    }

    @Override
    public List<PacificAcInterfaceLogWithBlobs> listByContactIdContactTypeAndStaffMis(String contactId, String contactType, String staffMis) {
        return pacificAcInterfaceLogMapper.listByContactIdContactTypeAndStaffMis(contactId, contactType, staffMis);
    }
}
