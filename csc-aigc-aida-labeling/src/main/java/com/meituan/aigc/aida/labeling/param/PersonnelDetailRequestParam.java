package com.meituan.aigc.aida.labeling.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper = true)
public class PersonnelDetailRequestParam extends BaseStatisticsRequestParam{
    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 排序方向
     */
    private String sortOrder;

    /**
     * 用户mis号
     */
    private String userMis;

    /**
     * 查询类型 1.标注 2.质检
     * @see com.meituan.aigc.aida.labeling.common.enums.InspectedTypeEnum
     */
    private Integer queryType;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页条数
     */
    private Integer pageSize;
}
