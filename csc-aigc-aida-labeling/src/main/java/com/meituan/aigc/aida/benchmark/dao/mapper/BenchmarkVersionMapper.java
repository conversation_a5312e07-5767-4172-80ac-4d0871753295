package com.meituan.aigc.aida.benchmark.dao.mapper;

import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion;
import com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersionExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * Benchmark版本表
 *
 * <AUTHOR>
 */
public interface BenchmarkVersionMapper {
    long countByExample(BenchmarkVersionExample example);

    int deleteByExample(BenchmarkVersionExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BenchmarkVersion record);

    int insertSelective(BenchmarkVersion record);

    List<BenchmarkVersion> selectByExample(BenchmarkVersionExample example);

    BenchmarkVersion selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BenchmarkVersion record, @Param("example") BenchmarkVersionExample example);

    int updateByExample(@Param("record") BenchmarkVersion record, @Param("example") BenchmarkVersionExample example);

    int updateByPrimaryKeySelective(BenchmarkVersion record);

    int updateByPrimaryKey(BenchmarkVersion record);
}