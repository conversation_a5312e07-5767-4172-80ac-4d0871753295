package com.meituan.aigc.aida.labeling.pojo.dto;

import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import lombok.Data;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/3/28 09:58
 * @Version: 1.0
 */
@Data
public class QualityCheckResultSaveDTO {

    /**
     * id
     */
    private Long id;
    /**
     * 原始数据内容映射内容JSONString
     */
    private String rawDataMappedContent;
    /**
     * 质检结果
     */
    private Integer qualityCheckResult;
    /**
     * 质检反馈JSONString
     */
    private String qualityCheckRemark;
    /**
     * 质检员修改后的标注结果
     */
    private List<List<LabelResult>> modifiedLabelingItems;

}
