package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.convert.LabelingSubTaskConvert;
import com.meituan.aigc.aida.labeling.dao.mapper.LabelingSubTaskMapper;
import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTaskExample;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskPO;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubtaskCountPo;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingSubTaskRepository;
import com.meituan.aigc.aida.labeling.param.LabelingListParam;
import com.meituan.aigc.aida.labeling.pojo.dto.LabelSubTaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:08
 * @Version: 1.0
 */
@Repository
@Slf4j
public class LabelingSubTaskRepositoryImpl implements LabelingSubTaskRepository {
    @Resource
    private LabelingSubTaskMapper labelingSubTaskMapper;

    @Override
    public List<Long> listSubTaskIdByGroupId(Long groupId, Long subTaskId) {
        LabelingSubTaskExample example = new LabelingSubTaskExample();
        example.createCriteria()
                .andGroupIdEqualTo(groupId)
                .andIdNotEqualTo(subTaskId)
                .andIsDeletedEqualTo(Boolean.FALSE);
        List<LabelingSubTask> labelingSubTasks = labelingSubTaskMapper.selectByExample(example);
        return labelingSubTasks.stream()
                .map(LabelingSubTask::getId)
                .collect(Collectors.toList());
    }

    @Override
    public int transferSubTask(Long subTaskId, String mis, String name) {
        LabelingSubTask labelingSubTask = new LabelingSubTask();
        labelingSubTask.setId(subTaskId);
        labelingSubTask.setLabelerMis(mis);
        labelingSubTask.setLabelerName(name);
        return labelingSubTaskMapper.updateByPrimaryKeySelective(labelingSubTask);
    }

    @Override
    public List<LabelSubTaskDTO> listSubTaskByGroupId(Long taskId, Long groupId) {
        LabelingSubTaskExample example = new LabelingSubTaskExample();
        example.createCriteria()
                .andTaskIdEqualTo(taskId)
                .andGroupIdEqualTo(groupId)
                .andIsDeletedEqualTo(Boolean.FALSE);
        List<LabelingSubTask> labelingSubTasks = labelingSubTaskMapper.selectByExample(example);
        return labelingSubTasks.stream().map(LabelingSubTaskConvert::convertLabelSubTaskDTO).collect(Collectors.toList());
    }

    @Override
    public LabelingSubTask getById(Long subTaskId) {
        if (Objects.isNull(subTaskId)) {
            return null;
        }
        return labelingSubTaskMapper.selectByPrimaryKey(subTaskId);
    }

    @Override
    public void updateById(LabelingSubTask labelingSubTask) {
        if (Objects.isNull(labelingSubTask)) {
            return;
        }
        labelingSubTaskMapper.updateByPrimaryKeySelective(labelingSubTask);
    }

    @Override
    public List<LabelingSubTask> listByGroupId(Long groupId) {
        if (Objects.isNull(groupId)) {
            return Collections.emptyList();
        }
        LabelingSubTaskExample example = new LabelingSubTaskExample();
        example.createCriteria()
                .andGroupIdEqualTo(groupId)
                .andIsDeletedEqualTo(Boolean.FALSE);
        return labelingSubTaskMapper.selectByExample(example);
    }

    @Override
    public List<LabelingSubTaskPO> listByTaskIdAndNameAndLabelerMis(LabelingListParam param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        return labelingSubTaskMapper.listByTaskIdAndNameAndLabelerMis(param);
    }

    @Override
    public void batchInsertSubTask(List<LabelingSubTask> subTaskList) {
        if (CollectionUtils.isEmpty(subTaskList)) {
            return;
        }
        labelingSubTaskMapper.batchInsertSubTasks(subTaskList);
    }

    @Override
    public List<LabelingSubTask> listAllByTaskId(Long taskId) {
        LabelingSubTaskExample example = new LabelingSubTaskExample();
        example.createCriteria().andTaskIdEqualTo(taskId).andIsDeletedEqualTo(Boolean.FALSE);
        return labelingSubTaskMapper.selectByExample(example);
    }

    @Override
    public List<LabelingSubTask> listAllByTaskIdList(List<Long> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return Collections.emptyList();
        }
        LabelingSubTaskExample example = new LabelingSubTaskExample();
        example.createCriteria().andTaskIdIn(taskIdList).andIsDeletedEqualTo(Boolean.FALSE);
        return labelingSubTaskMapper.selectByExample(example);
    }

    @Override
    public void updateStatusByTaskId(Long taskId, Integer status) {
        if (Objects.isNull(taskId) || Objects.isNull(status)) {
            return;
        }
        LabelingSubTaskExample example = new LabelingSubTaskExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        LabelingSubTask labelingSubTask = new LabelingSubTask();
        labelingSubTask.setStatus(status);
        labelingSubTaskMapper.updateByExampleSelective(labelingSubTask, example);
    }

    @Override
    public List<LabelingSubtaskCountPo> countSubTask(Long taskId) {
        if (Objects.isNull(taskId)) {
            return Collections.emptyList();
        }
        return labelingSubTaskMapper.countSubTask(taskId);
    }

    @Override
    public Integer countGroupNumByQualityCheckItemIds(List<Long> checkItemIds) {
        if (Objects.isNull(checkItemIds)) {
            return 0;
        }
        return labelingSubTaskMapper.countGroupNumByQualityCheckItemIds(checkItemIds);
    }

    @Override
    public List<LabelingSubTask> listByGroupIdList(List<Long> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return Collections.emptyList();
        }
        LabelingSubTaskExample example = new LabelingSubTaskExample();
        example.createCriteria()
                .andGroupIdIn(groupIdList)
                .andIsDeletedEqualTo(Boolean.FALSE);
        return labelingSubTaskMapper.selectByExample(example);
    }

    @Override
    public void insert(LabelingSubTask labelingSubTask) {
        labelingSubTaskMapper.insert(labelingSubTask);
    }

    @Override
    public List<LabelingSubTask> listByTaskIdAndGroupId(Long taskId, Long groupId) {
        LabelingSubTaskExample example = new LabelingSubTaskExample();
        example.createCriteria()
                .andTaskIdEqualTo(taskId)
                .andGroupIdEqualTo(groupId)
                .andIsDeletedEqualTo(Boolean.FALSE);
        return labelingSubTaskMapper.selectByExample(example);
    }

    @Override
    public List<LabelingSubTask> listByIds(List<Long> subTaskIds) {
        if (CollectionUtils.isEmpty(subTaskIds)) {
            return Collections.emptyList();
        }
        return labelingSubTaskMapper.listByIds(subTaskIds);
    }
}
