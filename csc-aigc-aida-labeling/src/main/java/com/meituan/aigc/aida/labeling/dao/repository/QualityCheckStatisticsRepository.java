package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DailyBizStatisticsVO;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DailyStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonnelDetailQuery;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingAndCheckCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.BizQualityCheckQueryVO;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.BaseStatisticsDTO;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 14:55
 * @Version: 1.0
 */
public interface QualityCheckStatisticsRepository {
    /**
     * 查询质检的每日统计数据
     * @param param 查询条件
     * @return 结果
     */
    List<DailyBizStatisticsVO> queryDailyTrends(DailyStatisticsDTO param);

    /**
     * 统计质检数量
     * @param query 查询条件
     * @return 结果
     */
    LabelingAndCheckCountVO statisticalQualityCheckData(DataStatisticalQuery query);

    /**
     * 查询质检的人员明细
     * @param query 查询条件
     * @return 结果
     */
    List<PersonnelDetailVO> queryPersonnelDetail(PersonnelDetailQuery query);

    /**
     * 查询单个业务质检率趋势
     * @param query 查询条件
     * @return 结果
     */
    List<BizQualityCheckQueryVO> querySingleBizQualityCheckRateTrends(BaseStatisticsDTO query);

    /**
     * 查询多个业务质检率
     * @param query 查询条件
     * @return 结果
     */
    List<BizQualityCheckQueryVO> queryBizQualityCheckRate(BaseStatisticsDTO query);

    /**
     * 批量插入
     * @param qualityCheckStatisticsList 数据
     */
    void batchInsert(List<QualityCheckStatistics> qualityCheckStatisticsList);

}
