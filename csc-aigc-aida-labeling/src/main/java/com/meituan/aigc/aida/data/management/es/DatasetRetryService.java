package com.meituan.aigc.aida.data.management.es;

import com.meituan.aigc.aida.common.enums.EsOperationEnum;
import com.meituan.aigc.aida.config.EsLionConfig;
import com.meituan.aigc.aida.data.management.es.query.BatchResult;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 类描述.
 * @date 2025/5/27
 */

@Service
@Slf4j
public class DatasetRetryService {

    @Resource
    private EsLionConfig esLionConfig;

    @Resource
    private DatasetEsBaseIndexService datasetEsBaseIndexService;

    /**
     * ES批量操作重试执行器
     * 支持失败重试机制，只对失败的数据进行重试
     *
     * @param datasetList 数据集列表
     * @param indexName   索引名称
     * @param operation   操作类型
     * @return 批量插入结果
     */
    public BatchResult executeWithRetry(List<DatasetEsIndex> datasetList, String indexName, EsOperationEnum operation) {
        long startTime = System.currentTimeMillis();

        log.info("Starting batch insert with retry mechanism (maxRetries: {}) for {} datasets to index: {}",
                esLionConfig.getMaxRetries(), datasetList.size(), indexName);

        List<DatasetEsIndex> remainingDatasets = new ArrayList<>(datasetList);
        List<String> allErrorMessages = new ArrayList<>();
        List<String> failedDocIds = new ArrayList<>();

        int attempt = 0;

        while (attempt <= esLionConfig.getMaxRetries() && !remainingDatasets.isEmpty()) {
            attempt++;

            try {
                log.info("Attempt {} to insert {} datasets", attempt, remainingDatasets.size());

                BulkResponse response = null;
                if (EsOperationEnum.INSERT.equals(operation)) {
                    response = datasetEsBaseIndexService.batchInsert(remainingDatasets, indexName);
                } else if (EsOperationEnum.UPDATE.equals(operation)) {
                    response = datasetEsBaseIndexService.batchUpdate(remainingDatasets, indexName);
                } else if (EsOperationEnum.DELETE.equals(operation)) {
                    response = datasetEsBaseIndexService.batchDelete(remainingDatasets, indexName);
                }

                if (response != null && response.hasFailures()) {
                    List<DatasetEsIndex> failedDatasets = new ArrayList<>();

                    for (int i = 0; i < response.getItems().length; i++) {
                        if (response.getItems()[i].isFailed() && i < remainingDatasets.size() && remainingDatasets.get(i) != null) {
                            failedDatasets.add(remainingDatasets.get(i));
                        }
                    }

                    if (failedDatasets.isEmpty()) {
                        break;
                    }

                    if (attempt <= esLionConfig.getMaxRetries()) {
                        remainingDatasets = failedDatasets;
                    } else {
                        // 最大重试次数用完，记录剩余失败的数据集
                        failedDatasets.forEach(dataset -> failedDocIds.add(dataset.getDocumentId()));
                        log.error("Max retries ({}) exceeded. {} datasets failed to insert",
                                esLionConfig.getMaxRetries(), failedDatasets.size());
                    }
                } else {
                    log.info("Successfully batch inserted {} datasets to index: {} on attempt {}",
                            remainingDatasets.size(), indexName, attempt);
                    break;
                }

            } catch (Exception e) {
                String errorMsg = String.format("Attempt %d failed with exception: %s", attempt, e.getMessage());
                allErrorMessages.add(errorMsg);
                log.error("Exception on attempt {} to batch insert datasets to index: {}",
                        attempt, indexName, e);
            }
        }

        long endTime = System.currentTimeMillis();
        int successCount = datasetList.size() - failedDocIds.size();
        boolean allSuccess = failedDocIds.isEmpty();

        BatchResult result = BatchResult.builder()
                .allSuccess(allSuccess)
                .totalCount(datasetList.size())
                .successCount(successCount)
                .failedCount(failedDocIds.size())
                .totalTimeMs(endTime - startTime)
                .failedDocIds(failedDocIds)
                .errorMessages(allErrorMessages)
                .index(indexName)
                .operation(operation)
                .build();

        log.info("Batch insert completed: {}", result.getSummary());
        return result;
    }
}
