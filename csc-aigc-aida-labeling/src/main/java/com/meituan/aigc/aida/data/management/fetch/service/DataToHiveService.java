package com.meituan.aigc.aida.data.management.fetch.service;

import com.meituan.aigc.aida.data.management.fetch.dto.hive.signal.SignalLoggingToHiveDTO;

/**
 * 数据同步到Hive的服务接口
 *
 * <AUTHOR>
 * @date 2025/04/03
 */
public interface DataToHiveService {

    /**
     * 推送信号埋点数据到Hive
     *
     * @param signalLogging2HiveDTO 信号日志数据传输对象
     */
    void pushSignalLoggingData(SignalLoggingToHiveDTO signalLogging2HiveDTO);
}
