package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 16:43
 * @Version: 1.0
 */
@Getter
public enum LabelingTaskStatus {
    CREATE(1, "创建中"),
    WAITING_ASSIGNED(2, "待分配"),
    ASSIGNING(3, "分配中"),
    IN_LABELING(4, "标注中"),
    COMPLETE(5, "标注完成"),
    FAILED(7, "失败"),
    RE_LABELING(8, "复标中"),
    RE_LABELED(9, "复标完成"),
    ARCHIVED(10, "已失效"),
    ;

    private final int code;
    private final String value;

    LabelingTaskStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LabelingTaskStatus getByCode(int code) {
        for (LabelingTaskStatus status : LabelingTaskStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 获取支持复标的任务状态
     * @return 结果
     */
    public static List<Integer> getCanReLabelingStatus(){
        List<Integer> cantReLabelingStatus = new ArrayList<>();
        cantReLabelingStatus.add(LabelingTaskStatus.IN_LABELING.getCode());
        cantReLabelingStatus.add(LabelingTaskStatus.COMPLETE.getCode());
        cantReLabelingStatus.add(LabelingTaskStatus.RE_LABELING.getCode());
        cantReLabelingStatus.add(LabelingTaskStatus.RE_LABELED.getCode());
        return cantReLabelingStatus;
    }

    /**
     * 获取复标任务状态
     * @return 结果
     */
    public static List<Integer> getReLabelingStatus(){
        List<Integer> reLabelingStatus = new ArrayList<>();
        reLabelingStatus.add(LabelingTaskStatus.RE_LABELING.getCode());
        reLabelingStatus.add(LabelingTaskStatus.RE_LABELED.getCode());
        return reLabelingStatus;
    }
    
}
