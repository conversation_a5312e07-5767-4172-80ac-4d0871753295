package com.meituan.aigc.aida.data.management.fetch.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.fetch.common.enums.CallTypeEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChatMessageFromEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.TrackingRulesChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.dto.BaseSingalTrackingBusParamDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.SignalMockTriggerMessageDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.pacific.PacificButlerContextParamDTO;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;
import com.sankuai.csc.workbench.bff.api.workbench.dto.InitButlerResDTO;
import com.sankuai.mdp.csc.pacific.context.api.enums.ChannelTypeEnum;
import com.sankuai.vcfaqr.voice.well.dto.FullSentenceDto;
import com.sankuai.vcfaqr.voice.well.dto.PushDto;
import com.sankuai.vcfaqr.voice.well.dto.enums.PushTypeEnum;
import com.sankuai.vcfaqr.voice.well.dto.enums.SpeakerTypEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 热线信号模拟触发服务实现类
 * <p>
 * 该服务负责处理热线客服系统中的信号模拟触发，主要功能包括:
 * 1. 处理木星语音消息
 * 2. 解析热线会话事件
 * 3. 构建热线渠道的业务参数
 * 4. 触发热线渠道的AI搭埋点机器人
 * 5. 处理木星MQ的partyUuid与AC MQ的contactId关联
 *
 * <AUTHOR>
 * @date 2025/06/06
 */
@Service("hotlineSignalLoggingTriggerService")
@Slf4j
public class HotlineSignalLoggingTriggerServiceImpl extends AbstractSignalLoggingTriggerService {

    private static final String LOG_PREFIX = "[HotlineSignalLoggingTriggerService]";

    /**
     * 通过木星语音消息处理AI搭埋点机器人应用触发
     * <p>
     * 将消息字符串解析为语音消息DTO并进行处理
     *
     * @param messageString 消息字符串，包含语音事件和会话信息
     */
    @Override
    public void processSignalLoggingTriggerByMessageString(String messageString) {
        if (StringUtils.isBlank(messageString)) {
            return;
        }
        // 1. 解析木星语音消息
        FullSentenceDto fullSentenceDto = buildFromVoiceMessageJsonString(messageString);
        if (isInvalidVoiceEvent(fullSentenceDto)) {
            return;
        }
        // 2. 构建触发消息
        SignalMockTriggerMessageDTO signalMockTriggerMessageDTO = buildSignalMockTriggerMessageFromVoice(fullSentenceDto);
        // 3. 处理触发
        processSignalLoggingTrigger(signalMockTriggerMessageDTO);
    }

    /**
     * 检查语音事件是否无效
     * <p>
     * 语音事件在以下情况下被认为无效:
     * 1. 语音事件DTO为空
     * 2. partyUuid为空
     * 3. 非呼入呼叫类型
     * 4. 语音内容为空
     * 5. 不是客服发送的语音消息
     *
     * @param fullSentenceDto 语音事件DTO
     * @return true如果事件无效，false如果事件有效
     */
    private boolean isInvalidVoiceEvent(FullSentenceDto fullSentenceDto) {
        if (Objects.isNull(fullSentenceDto)) {
            return true;
        }
        if (StringUtils.isBlank(fullSentenceDto.getPartyUuid())) {
            return true;
        }
        // 过滤非呼入的呼叫类型
        if (!Objects.equals(fullSentenceDto.getCallType(), CallTypeEnum.INBOUND.getCode())) {
            return true;
        }
        if (StringUtils.isBlank(fullSentenceDto.getText())) {
            return true;
        }
        // 检查是否为客服发送的语音
        if (nonStaffSentence(fullSentenceDto)) {
            return true;
        }
        if (StringUtils.isBlank(fullSentenceDto.getMis())) {
            log.warn("{}, 客服mis为空", LOG_PREFIX);
            return true;
        }
        return false;
    }

    /**
     *  判断语音消息是否非客服
     *
     * @param fullSentenceDto 语音事件DTO
     * @return
     */
    private boolean nonStaffSentence(FullSentenceDto fullSentenceDto) {
        return !Objects.equals(fullSentenceDto.getSpeaker_type(), SpeakerTypEnum.AGENT.getCode());
    }

    /**
     * 从语音消息构建信号Mock触发消息
     * <p>
     * 将语音事件数据转换为触发消息
     *
     * @param fullSentenceDto 语音事件数据
     * @return 触发消息DTO，包含语音会话信息
     */
    private SignalMockTriggerMessageDTO buildSignalMockTriggerMessageFromVoice(FullSentenceDto fullSentenceDto) {
        if (Objects.isNull(fullSentenceDto)) {
            return null;
        }
        SignalMockTriggerMessageDTO messageDTO = new SignalMockTriggerMessageDTO();
        messageDTO.setContactType(ChannelEnum.CALL.getCode());
        // 使用partyUuid作为contactId
        messageDTO.setContactId(fullSentenceDto.getPartyUuid());
        // 设置原始联系ID
        messageDTO.setOriContactId(fullSentenceDto.getOriContactID());
        messageDTO.setMessageOccurredTime(getMessageOccurredTime(fullSentenceDto));
        messageDTO.setChatMessageFromType(ChatMessageFromEnum.STAFF.getCode());
        messageDTO.setMessageId(generateVoiceMessageId());
        messageDTO.setStaffMis(fullSentenceDto.getMis());
        messageDTO.setUserMsgContent(CommonConstants.EMPTY_STRING);
        messageDTO.setStaffMessageContent(fullSentenceDto.getText());
        // 初始化管家信息
        try {
            Pair<InitButlerResDTO, Map<String, String>> contextInfo = contextUtils
                    .getContextInfo(String.valueOf(messageDTO.getContactId()), ChannelTypeEnum.HOT_LINE);
            if (null != contextInfo) {
                PacificButlerContextParamDTO pacificButlerContextParamDTO = new PacificButlerContextParamDTO();
                pacificButlerContextParamDTO.setButlerRes(contextInfo.getLeft());
                pacificButlerContextParamDTO.setButlerParamMap(contextInfo.getRight());
                messageDTO.setContextParam(pacificButlerContextParamDTO);
            }
        } catch (Exception e) {
            log.error("{}, 初始化管家信息异常, serviceId:{}", LOG_PREFIX, messageDTO.getContactId(), e);
        }
        return messageDTO;
    }

    /**
     * 设置消息发生时间
     *
     * @param fullSentenceDto
     * @return
     */
    @NotNull
    private static String getMessageOccurredTime(FullSentenceDto fullSentenceDto) {
        // 优先使用establishTS字段，为空时才使用当前时间
        String messageOccurredTime;
        if (Objects.nonNull(fullSentenceDto.getEstablishTS()) && fullSentenceDto.getEstablishTS() > 0) {
            // establishTS是微秒级时间戳，需要转换为毫秒级
            long establishTsMillis = fullSentenceDto.getEstablishTS() / 1000;
            messageOccurredTime = String.valueOf(establishTsMillis);
        } else {
            messageOccurredTime = String.valueOf(System.currentTimeMillis());
        }
        return messageOccurredTime;
    }

    /**
     * 生成语音消息ID
     * <p>
     * 生成唯一的消息ID
     *
     * @return 消息ID
     */
    private String generateVoiceMessageId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 从语音消息JSON字符串构建语音消息DTO
     * <p>
     * 解析语音消息字符串并验证事件类型
     *
     * @param messageJsonString 语音消息JSON字符串
     * @return 语音消息DTO，如果解析失败或事件类型不匹配则返回null
     */
    public FullSentenceDto buildFromVoiceMessageJsonString(String messageJsonString) {
        if (StringUtils.isBlank(messageJsonString)) {
            return null;
        }
        try {
            // 先解析为PushDto<FullSentenceDto>
            PushDto<FullSentenceDto> pushDto = JSONObject.parseObject(messageJsonString,
                    new TypeReference<PushDto<FullSentenceDto>>() {
                    });
            if (Objects.isNull(pushDto)) {
                log.error("{}, 解析PushDto失败, messageJsonString:{}", LOG_PREFIX, messageJsonString);
                return null;
            }

            // 检查是否为完整句子事件
            if (!Objects.equals(pushDto.getType(), PushTypeEnum.FULL_SENTENCE.getCode())) {
                return null;
            }

            // 从PushDto的data字段获取FullSentenceDto
            FullSentenceDto fullSentenceDto = pushDto.getData();
            if (Objects.isNull(fullSentenceDto)) {
                return null;
            }
            return fullSentenceDto;
        } catch (Exception e) {
            log.error("{}, 解析语音消息JSON字符串异常, messageJsonString:{}", LOG_PREFIX, messageJsonString, e);
            return null;
        }
    }

    /**
     * 构建机器人列表查询参数
     * <p>
     * 为热线渠道构建特定的查询参数
     *
     * @param busParamDTO 业务参数DTO
     * @return 机器人列表查询参数
     */
    @Override
    protected RobotListQuery buildRobotListQuery(BaseSingalTrackingBusParamDTO busParamDTO) {
        RobotListQuery robotListQuery = new RobotListQuery();
        // 客服mis
        robotListQuery.setMisList(busParamDTO.getStaffMis());

        // 设置渠道为热线
        robotListQuery.setChannel(TrackingRulesChannelEnum.CALL.getCode());
        return robotListQuery;
    }

    /**
     * 获取渠道类型
     *
     * @return 热线渠道类型
     */
    @Override
    protected TrackingRulesChannelEnum getChannelType() {
        return TrackingRulesChannelEnum.CALL;
    }
} 