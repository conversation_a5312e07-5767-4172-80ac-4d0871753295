package com.meituan.aigc.aida.data.management.dataset.strategy.field;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;

import java.util.Date;
import java.util.Map;

/**
 * 数据字段处理策略接口
 */
public interface DataFieldStrategy {
    
    /**
     * 获取策略类型
     * 
     * @return 字段类型枚举
     */
    Integer getStrategyType();
    
    /**
     * 验证字段值
     * 
     * @param value 字段值
     * @throws Exception 验证失败时抛出异常
     */
    void validateValue(String value) throws Exception;
    
    /**
     * 处理字段数据
     * 
     * @param columnName    字段名
     * @param value         字段值
     * @param textData      文本类型数据映射
     * @param flattenedData 扁平化数据映射
     * @param dateData      日期数据映射
     * @param nestedData    嵌套数据映射
     */
    void processFieldData(String columnName, String value,
                          Map<String, Object> textData,
                          Map<String, Object> flattenedData,
                          Map<String, Date> dateData,
                          Map<String, Object> nestedData);
}
