package com.meituan.aigc.aida.data.management.dataset.dao.repository.impl;

import com.meituan.aigc.aida.data.management.dataset.dao.mapper.DataSetVersionMapper;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersionExample;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-27 10:40
 * @description
 */
@Repository
@Slf4j
public class DataSetVersionRepositoryImpl implements DataSetVersionRepository {
    @Resource
    private DataSetVersionMapper dataSetVersionMapper;

    @Override
    public DataSetVersion getVersionByDataSetIdAndVersionId(Long dataSetId, Long versionId) {
        if (Objects.isNull(dataSetId) || Objects.isNull(versionId)) {
            return null;
        }
        DataSetVersionExample example = new DataSetVersionExample();
        DataSetVersionExample.Criteria criteria = example.createCriteria();
        criteria.andDataSetIdEqualTo(dataSetId);
        criteria.andIsDeletedEqualTo(Boolean.FALSE);
        criteria.andIdEqualTo(versionId);
        List<DataSetVersion> versions = dataSetVersionMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(versions)) {
            return null;
        }
        return versions.stream().findFirst().orElse(null);
    }

    @Override
    public List<DataSetVersion> listVersionByDataSetId(Long dataSetId) {
        if(Objects.isNull(dataSetId)){
            return Collections.emptyList();
        }
        DataSetVersionExample example = new DataSetVersionExample();
        example.createCriteria().andDataSetIdEqualTo(dataSetId).andIsDeletedEqualTo(Boolean.FALSE);
        return dataSetVersionMapper.selectByExample(example);
    }

    @Override
    public DataSetVersion getById(Long versionId) {
        return dataSetVersionMapper.selectByPrimaryKey(versionId);
    }

    @Override
    public Long createDataSetVersion(DataSetVersion version) {
        dataSetVersionMapper.insertSelective(version);
        return version.getId();
    }

    @Override
    public void updateSelectiveById(DataSetVersion version) {
        if (Objects.isNull(version)) {
            return;
        }
        dataSetVersionMapper.updateByPrimaryKeySelective(version);
    }
}
