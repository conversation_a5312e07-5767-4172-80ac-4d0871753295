package com.meituan.aigc.aida.data.management.fetch.dto.aida;

import com.meituan.aigc.aida.data.management.fetch.dto.BaseSingalTrackingBusParamDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Aida工作流业务参数DTO
 *
 * <AUTHOR>
 * @date 2025/04/03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AidaWorkflowBusParamDTO extends BaseSingalTrackingBusParamDTO {

    /**
     * 渠道
     */
    private String channel;

}
