package com.meituan.aigc.aida.labeling.pojo.dto;

import com.meituan.aigc.aida.labeling.param.PageData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-03-03 16:18
 * @description 标注子任务详情
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelingDetailDataDTO {

    /**
     * 分页数据
     */
    private PageData<DetailDataItemDTO> pageData;
    /**
     * 标注数量
     */
    private Integer labeledCount;
    /**
     * 未标注数量
     */
    private Integer unLabelCount;

    /**
     * 总数据量
     */
    private Integer totalCount;

}

