package com.meituan.aigc.aida.data.management.dataset.service;

import com.meituan.aigc.aida.data.management.dataset.dto.DatasetDistributeResponseDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFieldListResponseDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFilterPageResponseDTO;
import com.meituan.aigc.aida.data.management.dataset.param.DatasetDistributeQueryParam;
import com.meituan.aigc.aida.data.management.dataset.param.DatasetFilterPageQueryParam;
import com.meituan.aigc.aida.data.management.dataset.param.DatasetSaveParam;
import org.springframework.validation.annotation.Validated;

/**
 * 数据集洞察-分析服务
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集分析相关业务逻辑
 */
public interface DatasetAnalysisService {

    /**
     * 查询数据集分布情况
     *
     * @param param 查询参数
     * @return 数据集分布结果
     */
    DatasetDistributeResponseDTO queryDatasetDistribute(DatasetDistributeQueryParam param);

    /**
     * 查询一级字段下的二级字段列表
     *
     * @param param 查询参数
     * @return 字段列表结果
     */
    DatasetFieldListResponseDTO querySubFields(DatasetDistributeQueryParam param);

    /**
     * 数据集筛选分页查询
     *
     * @param param 查询参数
     * @return 分页查询结果
     */
    DatasetFilterPageResponseDTO queryDatasetPage(@Validated DatasetFilterPageQueryParam param);

    /**
     * 保存数据集
     *
     * @param param 保存参数
     */
    void saveDataset(@Validated DatasetSaveParam param);
} 