<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.InspectionItemMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.InspectionItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="item_type" jdbcType="TINYINT" property="itemType" />
    <result column="enum_list" jdbcType="VARCHAR" property="enumList" />
    <result column="is_compare" jdbcType="TINYINT" property="isCompare" />
    <result column="is_required" jdbcType="TINYINT" property="isRequired" />
    <result column="rank_num" jdbcType="INTEGER" property="rankNum" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="parent_enum" jdbcType="VARCHAR" property="parentEnum" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_mis" jdbcType="VARCHAR" property="createMis" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_mis" jdbcType="VARCHAR" property="updateMis" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_id, name, data_type, item_type, enum_list, is_compare, is_required, 
    rank_num, parent_id, parent_enum, is_deleted, create_mis, create_time, update_mis, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from inspection_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from inspection_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from inspection_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemExample">
    delete from inspection_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItem">
    insert into inspection_item (id, template_id, name, 
      data_type, item_type, enum_list, 
      is_compare, is_required, rank_num, 
      parent_id, parent_enum, is_deleted, 
      create_mis, create_time, update_mis, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{templateId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{dataType,jdbcType=TINYINT}, #{itemType,jdbcType=TINYINT}, #{enumList,jdbcType=VARCHAR}, 
      #{isCompare,jdbcType=TINYINT}, #{isRequired,jdbcType=TINYINT}, #{rankNum,jdbcType=INTEGER}, 
      #{parentId,jdbcType=BIGINT}, #{parentEnum,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIT}, 
      #{createMis,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateMis,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItem">
    insert into inspection_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="enumList != null">
        enum_list,
      </if>
      <if test="isCompare != null">
        is_compare,
      </if>
      <if test="isRequired != null">
        is_required,
      </if>
      <if test="rankNum != null">
        rank_num,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="parentEnum != null">
        parent_enum,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createMis != null">
        create_mis,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateMis != null">
        update_mis,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=TINYINT},
      </if>
      <if test="enumList != null">
        #{enumList,jdbcType=VARCHAR},
      </if>
      <if test="isCompare != null">
        #{isCompare,jdbcType=TINYINT},
      </if>
      <if test="isRequired != null">
        #{isRequired,jdbcType=TINYINT},
      </if>
      <if test="rankNum != null">
        #{rankNum,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="parentEnum != null">
        #{parentEnum,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createMis != null">
        #{createMis,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateMis != null">
        #{updateMis,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItemExample" resultType="java.lang.Long">
    select count(*) from inspection_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update inspection_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=TINYINT},
      </if>
      <if test="record.enumList != null">
        enum_list = #{record.enumList,jdbcType=VARCHAR},
      </if>
      <if test="record.isCompare != null">
        is_compare = #{record.isCompare,jdbcType=TINYINT},
      </if>
      <if test="record.isRequired != null">
        is_required = #{record.isRequired,jdbcType=TINYINT},
      </if>
      <if test="record.rankNum != null">
        rank_num = #{record.rankNum,jdbcType=INTEGER},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.parentEnum != null">
        parent_enum = #{record.parentEnum,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.createMis != null">
        create_mis = #{record.createMis,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateMis != null">
        update_mis = #{record.updateMis,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update inspection_item
    set id = #{record.id,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=TINYINT},
      item_type = #{record.itemType,jdbcType=TINYINT},
      enum_list = #{record.enumList,jdbcType=VARCHAR},
      is_compare = #{record.isCompare,jdbcType=TINYINT},
      is_required = #{record.isRequired,jdbcType=TINYINT},
      rank_num = #{record.rankNum,jdbcType=INTEGER},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      parent_enum = #{record.parentEnum,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      create_mis = #{record.createMis,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_mis = #{record.updateMis,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItem">
    update inspection_item
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=TINYINT},
      </if>
      <if test="enumList != null">
        enum_list = #{enumList,jdbcType=VARCHAR},
      </if>
      <if test="isCompare != null">
        is_compare = #{isCompare,jdbcType=TINYINT},
      </if>
      <if test="isRequired != null">
        is_required = #{isRequired,jdbcType=TINYINT},
      </if>
      <if test="rankNum != null">
        rank_num = #{rankNum,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="parentEnum != null">
        parent_enum = #{parentEnum,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createMis != null">
        create_mis = #{createMis,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateMis != null">
        update_mis = #{updateMis,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.InspectionItem">
    update inspection_item
    set template_id = #{templateId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=TINYINT},
      item_type = #{itemType,jdbcType=TINYINT},
      enum_list = #{enumList,jdbcType=VARCHAR},
      is_compare = #{isCompare,jdbcType=TINYINT},
      is_required = #{isRequired,jdbcType=TINYINT},
      rank_num = #{rankNum,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=BIGINT},
      parent_enum = #{parentEnum,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      create_mis = #{createMis,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_mis = #{updateMis,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listByTemplateId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from inspection_item
    where template_id = #{templateId,jdbcType=BIGINT} and is_deleted = 0
  </select>

  <update id="batchUpdateItems" parameterType="java.util.List">
    <foreach collection="items" item="item" separator=";">
      UPDATE inspection_item
      <set>
        <if test="item.templateId != null">
          template_id = #{item.templateId},
        </if>
        <if test="item.name != null">
          name = #{item.name},
        </if>
        <if test="item.dataType != null">
          data_type = #{item.dataType},
        </if>
        <if test="item.itemType != null">
          item_type = #{item.itemType},
        </if>
        <if test="item.enumList != null">
          enum_list = #{item.enumList},
        </if>
        <if test="item.isCompare != null">
          is_compare = #{item.isCompare},
        </if>
        <if test="item.isRequired != null">
          is_required = #{item.isRequired},
        </if>
        <if test="item.rankNum != null" >
          rank_num = #{item.rankNum},
        </if>
        <if test="item.parentId != null">
          parent_id = #{item.parentId},
        </if>
        <if test="item.parentEnum != null">
          parent_enum = #{item.parentEnum},
        </if>
        <if test="item.isDeleted != null">
          is_deleted = #{item.isDeleted},
        </if>
        <if test="item.updateMis != null">
          update_mis = #{item.updateMis},
        </if>
        update_time = NOW()
      </set>
      WHERE id = #{item.id}
    </foreach>
  </update>

</mapper>