package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate;
import com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplateExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InspectionItemTemplateMapper {
    long countByExample(InspectionItemTemplateExample example);

    int deleteByExample(InspectionItemTemplateExample example);

    int deleteByPrimaryKey(Long id);

    int insert(InspectionItemTemplate record);

    int insertSelective(InspectionItemTemplate record);

    List<InspectionItemTemplate> selectByExample(InspectionItemTemplateExample example);

    InspectionItemTemplate selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") InspectionItemTemplate record, @Param("example") InspectionItemTemplateExample example);

    int updateByExample(@Param("record") InspectionItemTemplate record, @Param("example") InspectionItemTemplateExample example);

    int updateByPrimaryKeySelective(InspectionItemTemplate record);

    int updateByPrimaryKey(InspectionItemTemplate record);

    List<InspectionItemTemplate> listByTemplateType();

    Long insertInspectionItem(InspectionItemTemplate template);
}