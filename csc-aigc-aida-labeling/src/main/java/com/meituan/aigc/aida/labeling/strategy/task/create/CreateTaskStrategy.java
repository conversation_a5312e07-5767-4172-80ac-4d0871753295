package com.meituan.aigc.aida.labeling.strategy.task.create;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import org.apache.poi.ss.usermodel.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 17:12
 * @Version: 1.0
 */
public interface CreateTaskStrategy {

    /**
     * 获取策略支持的任务类型
     *
     * @return 任务类型
     */
    Integer getTaskType();

    /**
     * 校验表头
     *
     * @param file 上传文件
     */
    void checkHeader(MultipartFile file);

    /**
     * 处理行数据
     *
     * @param rowData 行数据
     * @param headers 表头
     * @return 处理结果
     */
    List<LabelingTaskRawData> processData(List<List<String>> rowData, List<String> headers);

    /**
     * 执行导出操作
     *
     * @param subTaskIds 子任务ID集合
     */
    void export(String taskName, List<Long> subTaskIds, Integer labelDetailStatus, String mis);

    /**
     * 创建表头样式
     */
    public static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        // 设置居中
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        return style;
    }


    /**
     * 根据值类型设置单元格值
     */
    public static void setCellValueByType(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

}
