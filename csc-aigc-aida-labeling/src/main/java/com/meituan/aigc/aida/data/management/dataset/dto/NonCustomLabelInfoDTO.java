package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.Data;

import java.util.List;

/**
 * 非自定义标签信息DTO
 */
@Data
public class NonCustomLabelInfoDTO {
    /**
     * 场景列表
     */
    private List<SceneInfo> sceneList;
    
    /**
     * 用途列表
     */
    private List<PurposeInfo> pruposeList;
    
    /**
     * 标签列表
     */
    private List<LabelInfo> labelList;
    
    /**
     * 场景信息
     */
    @Data
    public static class SceneInfo {
        /**
         * 场景ID
         */
        private String sceneId;
        
        /**
         * 场景名称
         */
        private String sceneName;
    }
    
    /**
     * 用途信息
     */
    @Data
    public static class PurposeInfo {
        /**
         * 用途ID
         */
        private String purposeId;
        
        /**
         * 用途名称
         */
        private String purposeName;
    }
    
    /**
     * 标签信息
     */
    @Data
    public static class LabelInfo {
        /**
         * 标签ID
         */
        private String labelId;
        
        /**
         * 标签名称
         */
        private String labelName;
    }
} 