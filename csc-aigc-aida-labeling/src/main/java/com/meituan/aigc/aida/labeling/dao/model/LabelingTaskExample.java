package com.meituan.aigc.aida.labeling.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LabelingTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public LabelingTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNull() {
            addCriterion("task_name is null");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNotNull() {
            addCriterion("task_name is not null");
            return (Criteria) this;
        }

        public Criteria andTaskNameEqualTo(String value) {
            addCriterion("task_name =", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotEqualTo(String value) {
            addCriterion("task_name <>", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameGreaterThan(String value) {
            addCriterion("task_name >", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameGreaterThanOrEqualTo(String value) {
            addCriterion("task_name >=", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLessThan(String value) {
            addCriterion("task_name <", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLessThanOrEqualTo(String value) {
            addCriterion("task_name <=", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLike(String value) {
            addCriterion("task_name like", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotLike(String value) {
            addCriterion("task_name not like", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameIn(List<String> values) {
            addCriterion("task_name in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotIn(List<String> values) {
            addCriterion("task_name not in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameBetween(String value1, String value2) {
            addCriterion("task_name between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotBetween(String value1, String value2) {
            addCriterion("task_name not between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andDataSetIdIsNull() {
            addCriterion("data_set_id is null");
            return (Criteria) this;
        }

        public Criteria andDataSetIdIsNotNull() {
            addCriterion("data_set_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataSetIdEqualTo(Long value) {
            addCriterion("data_set_id =", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdNotEqualTo(Long value) {
            addCriterion("data_set_id <>", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdGreaterThan(Long value) {
            addCriterion("data_set_id >", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_set_id >=", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdLessThan(Long value) {
            addCriterion("data_set_id <", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdLessThanOrEqualTo(Long value) {
            addCriterion("data_set_id <=", value, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdIn(List<Long> values) {
            addCriterion("data_set_id in", values, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdNotIn(List<Long> values) {
            addCriterion("data_set_id not in", values, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdBetween(Long value1, Long value2) {
            addCriterion("data_set_id between", value1, value2, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetIdNotBetween(Long value1, Long value2) {
            addCriterion("data_set_id not between", value1, value2, "dataSetId");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionIsNull() {
            addCriterion("data_set_version is null");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionIsNotNull() {
            addCriterion("data_set_version is not null");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionEqualTo(String value) {
            addCriterion("data_set_version =", value, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionNotEqualTo(String value) {
            addCriterion("data_set_version <>", value, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionGreaterThan(String value) {
            addCriterion("data_set_version >", value, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionGreaterThanOrEqualTo(String value) {
            addCriterion("data_set_version >=", value, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionLessThan(String value) {
            addCriterion("data_set_version <", value, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionLessThanOrEqualTo(String value) {
            addCriterion("data_set_version <=", value, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionLike(String value) {
            addCriterion("data_set_version like", value, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionNotLike(String value) {
            addCriterion("data_set_version not like", value, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionIn(List<String> values) {
            addCriterion("data_set_version in", values, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionNotIn(List<String> values) {
            addCriterion("data_set_version not in", values, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionBetween(String value1, String value2) {
            addCriterion("data_set_version between", value1, value2, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andDataSetVersionNotBetween(String value1, String value2) {
            addCriterion("data_set_version not between", value1, value2, "dataSetVersion");
            return (Criteria) this;
        }

        public Criteria andTaskDescIsNull() {
            addCriterion("task_desc is null");
            return (Criteria) this;
        }

        public Criteria andTaskDescIsNotNull() {
            addCriterion("task_desc is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDescEqualTo(String value) {
            addCriterion("task_desc =", value, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescNotEqualTo(String value) {
            addCriterion("task_desc <>", value, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescGreaterThan(String value) {
            addCriterion("task_desc >", value, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescGreaterThanOrEqualTo(String value) {
            addCriterion("task_desc >=", value, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescLessThan(String value) {
            addCriterion("task_desc <", value, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescLessThanOrEqualTo(String value) {
            addCriterion("task_desc <=", value, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescLike(String value) {
            addCriterion("task_desc like", value, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescNotLike(String value) {
            addCriterion("task_desc not like", value, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescIn(List<String> values) {
            addCriterion("task_desc in", values, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescNotIn(List<String> values) {
            addCriterion("task_desc not in", values, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescBetween(String value1, String value2) {
            addCriterion("task_desc between", value1, value2, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andTaskDescNotBetween(String value1, String value2) {
            addCriterion("task_desc not between", value1, value2, "taskDesc");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigIsNull() {
            addCriterion("labeling_config is null");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigIsNotNull() {
            addCriterion("labeling_config is not null");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigEqualTo(String value) {
            addCriterion("labeling_config =", value, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigNotEqualTo(String value) {
            addCriterion("labeling_config <>", value, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigGreaterThan(String value) {
            addCriterion("labeling_config >", value, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigGreaterThanOrEqualTo(String value) {
            addCriterion("labeling_config >=", value, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigLessThan(String value) {
            addCriterion("labeling_config <", value, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigLessThanOrEqualTo(String value) {
            addCriterion("labeling_config <=", value, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigLike(String value) {
            addCriterion("labeling_config like", value, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigNotLike(String value) {
            addCriterion("labeling_config not like", value, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigIn(List<String> values) {
            addCriterion("labeling_config in", values, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigNotIn(List<String> values) {
            addCriterion("labeling_config not in", values, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigBetween(String value1, String value2) {
            addCriterion("labeling_config between", value1, value2, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingConfigNotBetween(String value1, String value2) {
            addCriterion("labeling_config not between", value1, value2, "labelingConfig");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusIsNull() {
            addCriterion("labeling_status is null");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusIsNotNull() {
            addCriterion("labeling_status is not null");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusEqualTo(Byte value) {
            addCriterion("labeling_status =", value, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusNotEqualTo(Byte value) {
            addCriterion("labeling_status <>", value, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusGreaterThan(Byte value) {
            addCriterion("labeling_status >", value, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("labeling_status >=", value, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusLessThan(Byte value) {
            addCriterion("labeling_status <", value, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusLessThanOrEqualTo(Byte value) {
            addCriterion("labeling_status <=", value, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusIn(List<Byte> values) {
            addCriterion("labeling_status in", values, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusNotIn(List<Byte> values) {
            addCriterion("labeling_status not in", values, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusBetween(Byte value1, Byte value2) {
            addCriterion("labeling_status between", value1, value2, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("labeling_status not between", value1, value2, "labelingStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusIsNull() {
            addCriterion("quality_check_status is null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusIsNotNull() {
            addCriterion("quality_check_status is not null");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusEqualTo(Byte value) {
            addCriterion("quality_check_status =", value, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusNotEqualTo(Byte value) {
            addCriterion("quality_check_status <>", value, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusGreaterThan(Byte value) {
            addCriterion("quality_check_status >", value, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("quality_check_status >=", value, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusLessThan(Byte value) {
            addCriterion("quality_check_status <", value, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusLessThanOrEqualTo(Byte value) {
            addCriterion("quality_check_status <=", value, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusIn(List<Byte> values) {
            addCriterion("quality_check_status in", values, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusNotIn(List<Byte> values) {
            addCriterion("quality_check_status not in", values, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusBetween(Byte value1, Byte value2) {
            addCriterion("quality_check_status between", value1, value2, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andQualityCheckStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("quality_check_status not between", value1, value2, "qualityCheckStatus");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerIsNull() {
            addCriterion("labeling_manager is null");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerIsNotNull() {
            addCriterion("labeling_manager is not null");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerEqualTo(String value) {
            addCriterion("labeling_manager =", value, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNotEqualTo(String value) {
            addCriterion("labeling_manager <>", value, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerGreaterThan(String value) {
            addCriterion("labeling_manager >", value, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerGreaterThanOrEqualTo(String value) {
            addCriterion("labeling_manager >=", value, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerLessThan(String value) {
            addCriterion("labeling_manager <", value, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerLessThanOrEqualTo(String value) {
            addCriterion("labeling_manager <=", value, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerLike(String value) {
            addCriterion("labeling_manager like", value, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNotLike(String value) {
            addCriterion("labeling_manager not like", value, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerIn(List<String> values) {
            addCriterion("labeling_manager in", values, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNotIn(List<String> values) {
            addCriterion("labeling_manager not in", values, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerBetween(String value1, String value2) {
            addCriterion("labeling_manager between", value1, value2, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNotBetween(String value1, String value2) {
            addCriterion("labeling_manager not between", value1, value2, "labelingManager");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameIsNull() {
            addCriterion("labeling_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameIsNotNull() {
            addCriterion("labeling_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameEqualTo(String value) {
            addCriterion("labeling_manager_name =", value, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameNotEqualTo(String value) {
            addCriterion("labeling_manager_name <>", value, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameGreaterThan(String value) {
            addCriterion("labeling_manager_name >", value, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("labeling_manager_name >=", value, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameLessThan(String value) {
            addCriterion("labeling_manager_name <", value, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameLessThanOrEqualTo(String value) {
            addCriterion("labeling_manager_name <=", value, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameLike(String value) {
            addCriterion("labeling_manager_name like", value, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameNotLike(String value) {
            addCriterion("labeling_manager_name not like", value, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameIn(List<String> values) {
            addCriterion("labeling_manager_name in", values, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameNotIn(List<String> values) {
            addCriterion("labeling_manager_name not in", values, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameBetween(String value1, String value2) {
            addCriterion("labeling_manager_name between", value1, value2, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andLabelingManagerNameNotBetween(String value1, String value2) {
            addCriterion("labeling_manager_name not between", value1, value2, "labelingManagerName");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNull() {
            addCriterion("task_type is null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNotNull() {
            addCriterion("task_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeEqualTo(Byte value) {
            addCriterion("task_type =", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotEqualTo(Byte value) {
            addCriterion("task_type <>", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThan(Byte value) {
            addCriterion("task_type >", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("task_type >=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThan(Byte value) {
            addCriterion("task_type <", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThanOrEqualTo(Byte value) {
            addCriterion("task_type <=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIn(List<Byte> values) {
            addCriterion("task_type in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotIn(List<Byte> values) {
            addCriterion("task_type not in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeBetween(Byte value1, Byte value2) {
            addCriterion("task_type between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("task_type not between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNull() {
            addCriterion("data_type is null");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNotNull() {
            addCriterion("data_type is not null");
            return (Criteria) this;
        }

        public Criteria andDataTypeEqualTo(Byte value) {
            addCriterion("data_type =", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotEqualTo(Byte value) {
            addCriterion("data_type <>", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThan(Byte value) {
            addCriterion("data_type >", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("data_type >=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThan(Byte value) {
            addCriterion("data_type <", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThanOrEqualTo(Byte value) {
            addCriterion("data_type <=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIn(List<Byte> values) {
            addCriterion("data_type in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotIn(List<Byte> values) {
            addCriterion("data_type not in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeBetween(Byte value1, Byte value2) {
            addCriterion("data_type between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("data_type not between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andTaskSourceIsNull() {
            addCriterion("task_source is null");
            return (Criteria) this;
        }

        public Criteria andTaskSourceIsNotNull() {
            addCriterion("task_source is not null");
            return (Criteria) this;
        }

        public Criteria andTaskSourceEqualTo(Byte value) {
            addCriterion("task_source =", value, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceNotEqualTo(Byte value) {
            addCriterion("task_source <>", value, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceGreaterThan(Byte value) {
            addCriterion("task_source >", value, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceGreaterThanOrEqualTo(Byte value) {
            addCriterion("task_source >=", value, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceLessThan(Byte value) {
            addCriterion("task_source <", value, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceLessThanOrEqualTo(Byte value) {
            addCriterion("task_source <=", value, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceIn(List<Byte> values) {
            addCriterion("task_source in", values, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceNotIn(List<Byte> values) {
            addCriterion("task_source not in", values, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceBetween(Byte value1, Byte value2) {
            addCriterion("task_source between", value1, value2, "taskSource");
            return (Criteria) this;
        }

        public Criteria andTaskSourceNotBetween(Byte value1, Byte value2) {
            addCriterion("task_source not between", value1, value2, "taskSource");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathIsNull() {
            addCriterion("upload_file_path is null");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathIsNotNull() {
            addCriterion("upload_file_path is not null");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathEqualTo(String value) {
            addCriterion("upload_file_path =", value, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathNotEqualTo(String value) {
            addCriterion("upload_file_path <>", value, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathGreaterThan(String value) {
            addCriterion("upload_file_path >", value, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathGreaterThanOrEqualTo(String value) {
            addCriterion("upload_file_path >=", value, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathLessThan(String value) {
            addCriterion("upload_file_path <", value, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathLessThanOrEqualTo(String value) {
            addCriterion("upload_file_path <=", value, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathLike(String value) {
            addCriterion("upload_file_path like", value, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathNotLike(String value) {
            addCriterion("upload_file_path not like", value, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathIn(List<String> values) {
            addCriterion("upload_file_path in", values, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathNotIn(List<String> values) {
            addCriterion("upload_file_path not in", values, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathBetween(String value1, String value2) {
            addCriterion("upload_file_path between", value1, value2, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andUploadFilePathNotBetween(String value1, String value2) {
            addCriterion("upload_file_path not between", value1, value2, "uploadFilePath");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andBizTypeIsNull() {
            addCriterion("biz_type is null");
            return (Criteria) this;
        }

        public Criteria andBizTypeIsNotNull() {
            addCriterion("biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andBizTypeEqualTo(Long value) {
            addCriterion("biz_type =", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotEqualTo(Long value) {
            addCriterion("biz_type <>", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeGreaterThan(Long value) {
            addCriterion("biz_type >", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_type >=", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeLessThan(Long value) {
            addCriterion("biz_type <", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeLessThanOrEqualTo(Long value) {
            addCriterion("biz_type <=", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeIn(List<Long> values) {
            addCriterion("biz_type in", values, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotIn(List<Long> values) {
            addCriterion("biz_type not in", values, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeBetween(Long value1, Long value2) {
            addCriterion("biz_type between", value1, value2, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotBetween(Long value1, Long value2) {
            addCriterion("biz_type not between", value1, value2, "bizType");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNull() {
            addCriterion("creator_mis is null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNotNull() {
            addCriterion("creator_mis is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisEqualTo(String value) {
            addCriterion("creator_mis =", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotEqualTo(String value) {
            addCriterion("creator_mis <>", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThan(String value) {
            addCriterion("creator_mis >", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThanOrEqualTo(String value) {
            addCriterion("creator_mis >=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThan(String value) {
            addCriterion("creator_mis <", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThanOrEqualTo(String value) {
            addCriterion("creator_mis <=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLike(String value) {
            addCriterion("creator_mis like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotLike(String value) {
            addCriterion("creator_mis not like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIn(List<String> values) {
            addCriterion("creator_mis in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotIn(List<String> values) {
            addCriterion("creator_mis not in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisBetween(String value1, String value2) {
            addCriterion("creator_mis between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotBetween(String value1, String value2) {
            addCriterion("creator_mis not between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeIsNull() {
            addCriterion("check_finish_time is null");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeIsNotNull() {
            addCriterion("check_finish_time is not null");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeEqualTo(Date value) {
            addCriterion("check_finish_time =", value, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeNotEqualTo(Date value) {
            addCriterion("check_finish_time <>", value, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeGreaterThan(Date value) {
            addCriterion("check_finish_time >", value, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("check_finish_time >=", value, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeLessThan(Date value) {
            addCriterion("check_finish_time <", value, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeLessThanOrEqualTo(Date value) {
            addCriterion("check_finish_time <=", value, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeIn(List<Date> values) {
            addCriterion("check_finish_time in", values, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeNotIn(List<Date> values) {
            addCriterion("check_finish_time not in", values, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeBetween(Date value1, Date value2) {
            addCriterion("check_finish_time between", value1, value2, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andCheckFinishTimeNotBetween(Date value1, Date value2) {
            addCriterion("check_finish_time not between", value1, value2, "checkFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeIsNull() {
            addCriterion("labeling_finish_time is null");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeIsNotNull() {
            addCriterion("labeling_finish_time is not null");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeEqualTo(Date value) {
            addCriterion("labeling_finish_time =", value, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeNotEqualTo(Date value) {
            addCriterion("labeling_finish_time <>", value, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeGreaterThan(Date value) {
            addCriterion("labeling_finish_time >", value, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("labeling_finish_time >=", value, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeLessThan(Date value) {
            addCriterion("labeling_finish_time <", value, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeLessThanOrEqualTo(Date value) {
            addCriterion("labeling_finish_time <=", value, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeIn(List<Date> values) {
            addCriterion("labeling_finish_time in", values, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeNotIn(List<Date> values) {
            addCriterion("labeling_finish_time not in", values, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeBetween(Date value1, Date value2) {
            addCriterion("labeling_finish_time between", value1, value2, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andLabelingFinishTimeNotBetween(Date value1, Date value2) {
            addCriterion("labeling_finish_time not between", value1, value2, "labelingFinishTime");
            return (Criteria) this;
        }

        public Criteria andAssignTypeIsNull() {
            addCriterion("assign_type is null");
            return (Criteria) this;
        }

        public Criteria andAssignTypeIsNotNull() {
            addCriterion("assign_type is not null");
            return (Criteria) this;
        }

        public Criteria andAssignTypeEqualTo(Integer value) {
            addCriterion("assign_type =", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeNotEqualTo(Integer value) {
            addCriterion("assign_type <>", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeGreaterThan(Integer value) {
            addCriterion("assign_type >", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("assign_type >=", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeLessThan(Integer value) {
            addCriterion("assign_type <", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeLessThanOrEqualTo(Integer value) {
            addCriterion("assign_type <=", value, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeIn(List<Integer> values) {
            addCriterion("assign_type in", values, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeNotIn(List<Integer> values) {
            addCriterion("assign_type not in", values, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeBetween(Integer value1, Integer value2) {
            addCriterion("assign_type between", value1, value2, "assignType");
            return (Criteria) this;
        }

        public Criteria andAssignTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("assign_type not between", value1, value2, "assignType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}