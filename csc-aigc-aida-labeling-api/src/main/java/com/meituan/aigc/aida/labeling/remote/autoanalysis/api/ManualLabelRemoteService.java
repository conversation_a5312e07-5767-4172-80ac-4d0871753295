package com.meituan.aigc.aida.labeling.remote.autoanalysis.api;

import com.meituan.aigc.aida.labeling.remote.autoanalysis.dto.ManualLabelCreateRequestDTO;
import com.meituan.aigc.aida.labeling.remote.common.dto.ServiceResponseDTO;

/**
 * 自动分析工具人工标注服务接口
 * 
 * <AUTHOR>
 * @date 2025/05/07
 */
public interface ManualLabelRemoteService {

    /**
     * 创建人工标注任务
     *
     * @param request
     * @return
     */
    ServiceResponseDTO<Long> createManualLabelTask(ManualLabelCreateRequestDTO request);
}
