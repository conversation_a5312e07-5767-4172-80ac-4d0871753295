package com.meituan.aigc.aida.labeling.dao.po;

import lombok.Data;

import java.util.Date;

/**
 * 质检详情表信息PO
 *
 * <AUTHOR>
 */
@Data
public class LabelingQualityCheckItemPO {
    /**
     * 质检详情ID
     */
    private Long id;
    /**
     * 标注数据ID
     */
    private Long labelingDataId;
    /**
     * 原数据ID
     */
    private Long rawDataId;
    /**
     * 原始数据JSON
     */
    private String rawDataContent;

    /**
     * 原始数据表头
     */
    private String rawDataHeaders;
    /**
     * 原始数据内容映射内容JSON
     */
    private String rawDataMappedContent;
    /**
     * 标注项信息JSON
     */
    private String labelingItems;
    /**
     * 质检结果
     */
    private Integer qualityCheckResult;
    /**
     * 质检反馈
     */
    private String qualityCheckRemark;
    /**
     * 质检人
     */
    private String qualityChecker;
    /**
     * 质检时间
     */
    private Date qualityCheckTime;

    /**
     * 质检状态
     * @see com.meituan.aigc.aida.labeling.common.enums.QualityCheckDataStatus
     */
    private Integer qualityCheckStatus;

    /**
     * 标注人mis
     */
    private String labelerMis;

    /**
     * 标注人姓名
     */
    private String labelerName;

    /**
     * 标注时间
     */
    private Date labelTime;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 会话时间
     */
    private Date sessionTime;

    /**
     * 大模型消息ID
     */
    private String messageId;
    /**
     * extraInfo
     */
    private String extraInfo;
    /**
     * 质检员修改后的标注信息
     */
    private String modifiedLabelingItems;
    /**
     * 查看状态
     */
    private Integer viewStatus;
}
