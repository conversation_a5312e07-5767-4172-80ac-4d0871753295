package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.meituan.aigc.aida.labeling.dao.mapper.InspectionItemTemplateMapper;
import com.meituan.aigc.aida.labeling.dao.model.InspectionItemTemplate;
import com.meituan.aigc.aida.labeling.dao.repository.InspectionItemTemplateRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:04
 * @Version: 1.0
 */
@Repository
public class InspectionItemTemplateRepositoryImpl implements InspectionItemTemplateRepository {

    @Resource
    private InspectionItemTemplateMapper inspectionItemTemplateMapper;

    @Override
    public List<InspectionItemTemplate> listByTemplateType() {
        return inspectionItemTemplateMapper.listByTemplateType();
    }

    @Override
    public void insertTemplate(InspectionItemTemplate template) {
        if(Objects.isNull(template)){
            return;
        }
        inspectionItemTemplateMapper.insertInspectionItem(template);
    }

    @Override
    public void updateTemplate(InspectionItemTemplate template) {
        if(Objects.isNull(template)){
            return;
        }
        inspectionItemTemplateMapper.updateByPrimaryKeySelective(template);
    }

    @Override
    public InspectionItemTemplate getTemplateById(Long id) {
        if(Objects.isNull(id)){
            return null;
        }
        return inspectionItemTemplateMapper.selectByPrimaryKey(id);
    }
}
