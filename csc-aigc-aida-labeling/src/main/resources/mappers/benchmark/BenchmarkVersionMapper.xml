<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.benchmark.dao.mapper.BenchmarkVersionMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="version_name" jdbcType="VARCHAR" property="versionName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="eval_object" jdbcType="TINYINT" property="evalObject" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="created_mis" jdbcType="VARCHAR" property="createdMis" />
    <result column="updated_mis" jdbcType="VARCHAR" property="updatedMis" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, version_name, description, eval_object, status, created_mis, updated_mis, created_time, 
    updated_time
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from benchmark_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from benchmark_version
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from benchmark_version
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersionExample">
    delete from benchmark_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion">
    insert into benchmark_version (id, version_name, description, 
      eval_object, status, created_mis, 
      updated_mis, created_time, updated_time
      )
    values (#{id,jdbcType=BIGINT}, #{versionName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{evalObject,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{createdMis,jdbcType=VARCHAR}, 
      #{updatedMis,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion">
    insert into benchmark_version
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="versionName != null">
        version_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="evalObject != null">
        eval_object,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createdMis != null">
        created_mis,
      </if>
      <if test="updatedMis != null">
        updated_mis,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="versionName != null">
        #{versionName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="evalObject != null">
        #{evalObject,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createdMis != null">
        #{createdMis,jdbcType=VARCHAR},
      </if>
      <if test="updatedMis != null">
        #{updatedMis,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersionExample" resultType="java.lang.Long">
    select count(*) from benchmark_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update benchmark_version
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.versionName != null">
        version_name = #{record.versionName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.evalObject != null">
        eval_object = #{record.evalObject,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createdMis != null">
        created_mis = #{record.createdMis,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedMis != null">
        updated_mis = #{record.updatedMis,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null">
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update benchmark_version
    set id = #{record.id,jdbcType=BIGINT},
      version_name = #{record.versionName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      eval_object = #{record.evalObject,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      created_mis = #{record.createdMis,jdbcType=VARCHAR},
      updated_mis = #{record.updatedMis,jdbcType=VARCHAR},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{record.updatedTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion">
    update benchmark_version
    <set>
      <if test="versionName != null">
        version_name = #{versionName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="evalObject != null">
        eval_object = #{evalObject,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createdMis != null">
        created_mis = #{createdMis,jdbcType=VARCHAR},
      </if>
      <if test="updatedMis != null">
        updated_mis = #{updatedMis,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.benchmark.dao.model.BenchmarkVersion">
    update benchmark_version
    set version_name = #{versionName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      eval_object = #{evalObject,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      created_mis = #{createdMis,jdbcType=VARCHAR},
      updated_mis = #{updatedMis,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>