package com.meituan.aigc.aida.data.management.dataset.dao.repository.impl;

import com.dianping.zebra.util.StringUtils;
import com.meituan.aigc.aida.data.management.dataset.dao.mapper.DataProcessTaskMapper;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTaskExample;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataProcessTaskRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.meituan.aigc.aida.data.management.dataset.common.constants.DatasetCommonConstants.COMPLETE_STATUS;

/**
 * <AUTHOR>
 * @date 2025-05-26
 * @description 数据处理任务仓储层实现类
 */
@Repository
public class DataProcessTaskRepositoryImpl implements DataProcessTaskRepository {

    @Resource
    private DataProcessTaskMapper dataProcessTaskMapper;

    @Override
    public DataProcessTask getDataProcessTaskById(Long taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }
        return dataProcessTaskMapper.selectByPrimaryKey(taskId);
    }

    @Override
    public List<DataProcessTask> listDataProcessTaskByDatasetId(Long datasetId) {
        if (Objects.isNull(datasetId)) {
            return null;
        }
        DataProcessTaskExample example = new DataProcessTaskExample();
        example.createCriteria().andDatasetIdEqualTo(datasetId);
        example.setOrderByClause("create_time desc");
        return dataProcessTaskMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<DataProcessTask> listDataProcessTaskByName(String taskName) {
        DataProcessTaskExample example = new DataProcessTaskExample();
        DataProcessTaskExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(taskName)) {
            criteria.andTaskNameLike("%" + taskName + "%");
        }
        example.setOrderByClause("create_time desc");
        return dataProcessTaskMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<DataProcessTask> listDataProcessTaskByNameAndDatasetId(String taskName, Long datasetId) {
        DataProcessTaskExample example = new DataProcessTaskExample();
        DataProcessTaskExample.Criteria criteria = example.createCriteria();

        // 添加数据集ID条件
        if (Objects.nonNull(datasetId)) {
            criteria.andDatasetIdEqualTo(datasetId);
        }

        // 添加任务名称条件
        if (StringUtils.isNotBlank(taskName)) {
            criteria.andTaskNameLike("%" + taskName + "%");
        }

        criteria.andIsDeletedEqualTo(false);

        // 按创建时间倒序排序
        example.setOrderByClause("create_time desc");
        return dataProcessTaskMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<DataProcessTask> listDataProcessTaskByNameAndDatasetIdAndCreator(String taskName, Long datasetId, String creatorMis) {
        DataProcessTaskExample example = new DataProcessTaskExample();
        DataProcessTaskExample.Criteria criteria = example.createCriteria();

        // 添加数据集ID条件
        if (Objects.nonNull(datasetId)) {
            criteria.andDatasetIdEqualTo(datasetId);
        }

        // 添加任务名称条件
        if (StringUtils.isNotBlank(taskName)) {
            criteria.andTaskNameLike("%" + taskName + "%");
        }

        // 添加创建者条件
        if (StringUtils.isNotBlank(creatorMis)) {
            criteria.andCreatorMisEqualTo(creatorMis);
        }

        criteria.andIsDeletedEqualTo(false);

        // 按创建时间倒序排序
        example.setOrderByClause("create_time desc");
        return dataProcessTaskMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public Long createDataProcessTask(DataProcessTask dataProcessTask) {
        dataProcessTaskMapper.insertSelective(dataProcessTask);
        return dataProcessTask.getId();
    }

    @Override
    public void updateDataProcessTask(DataProcessTask dataProcessTask) {
        dataProcessTaskMapper.updateByPrimaryKeySelective(dataProcessTask);
    }

    @Override
    public List<DataProcessTask> listDataProcessTaskByDataSetIdAndVersionId(Long dataSetId, Long versionId) {
        if (Objects.isNull(dataSetId)) {
            return Collections.emptyList();
        }
        DataProcessTaskExample example = new DataProcessTaskExample();
        DataProcessTaskExample.Criteria criteria = example.createCriteria();
        criteria.andDatasetIdEqualTo(dataSetId);
        if (Objects.nonNull(versionId)) {
            criteria.andProcessDatasetVersionIdEqualTo(versionId);
        }
        // 查询状态为已完成的任务
        criteria.andStatusIn(COMPLETE_STATUS);
        criteria.andIsDeletedEqualTo(false);
        return dataProcessTaskMapper.selectByExampleWithBLOBs(example);
    }
} 