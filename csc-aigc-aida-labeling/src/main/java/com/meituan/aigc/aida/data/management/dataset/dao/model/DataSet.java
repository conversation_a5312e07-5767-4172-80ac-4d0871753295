package com.meituan.aigc.aida.data.management.dataset.dao.model;

import com.meituan.aigc.aida.data.management.dataset.enums.TrainingTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.TrainingUsageTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.UsageCategoryEnum;
import lombok.Data;

import java.util.Date;
@Data
public class DataSet {
    /**
     * 数据集ID
     */
    private Long id;
    /**
     * 数据集名称
     */
    private String name;
    /**
     * 数据集描述
     */
    private String description;
    /**
     * 创建人mis
     */
    private String creatorMis;
    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 数据获取任务Id
     */
    private Long fetchId;
    /**
     * 用途分类(1:模型训练/2:会话分析/3:自定义)
     * @see UsageCategoryEnum
     */
    private Integer usageCategory;
    /**
     * 模型训练用途类型(1:文本生成/2:图像生成/3:图像理解)
     * @see TrainingUsageTypeEnum
     */
    private Integer usageType;
    /**
     * 训练类型(1:sft/2:dpo/3:session维度)
     * @see TrainingTypeEnum
     */
    private Integer trainingType;
    /**
     * 自定义用途描述
     */
    private String taskDescription;
    /**
     * 数据格式(prompt_output/prompt_chosen_rejected)
     */
    private String dataFormat;
    /**
     * 数据条数
     */
    private Integer dataCount;
    /**
     * 最新数据集版本Id
     */
    private Long latestVersionId;
    /**
     * 数据集状态
     */
    private Byte dataSetStatus;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除
     */
    private Boolean isDeleted;
    /**
     * 数据来源
     */
    private Integer dataSource;
    /**
     * S3文件存储路径
     */
    private String filePath;
    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 扩展信息
     */
    private String extraInfo;
}