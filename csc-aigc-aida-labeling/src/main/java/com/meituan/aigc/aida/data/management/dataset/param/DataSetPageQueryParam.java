package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 数据集分页查询参数
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集管理 - 分页查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetPageQueryParam implements Serializable {
    /**
     * 数据集ID
     */
    private Long dataSetId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 数据来源
     */
    private Integer dataSource;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 页数
     */
    private Integer pageSize;
    
    /**
     * 排序字段类型 1-通用数据 2-自定义上传数据
     */
    private Integer sortType = 2;
    
    /**
     * 滚动ID
     */
    private String scrollId;
    
    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方式 ASC/DESC
     */
    private String sortOrder;
    
    /**
     * 查询条件
     */
    private List<DataSetQueryCondition> conditionList;
} 