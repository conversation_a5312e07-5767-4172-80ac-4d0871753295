package com.meituan.aigc.aida.data.management.dataset.param;

import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetQueryCondition;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DataSetCopyVersionParam implements Serializable {
    /**
     * 数据集id
     */
    private Long dataSetId;
    /**
     * 版本id
     */
    private Long versionId;
    /**
     * 版本名称
     */
    private String versionName;
    /**
     * 版本类型 1:使用已有版本继承/2:使用已有版本合并/3:从错题集导入/4:创建任务/5:标注结果回收/6:数据处理/7:洞察与分析导出
     *
     * @see com.meituan.aigc.aida.data.management.dataset.enums.DataSetVersionTypeEnum
     */
    private Integer versionType;
    /**
     * 版本描述
     */
    private String versionDescription;
    /**
     * 查询条件
     */
    private List<DataSetQueryCondition> conditionList;
    /**
     * 保存的列名
     */
    private List<String> savedColumnList;
}
