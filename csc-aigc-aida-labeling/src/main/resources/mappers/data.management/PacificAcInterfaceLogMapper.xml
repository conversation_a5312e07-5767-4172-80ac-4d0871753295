<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.data.management.fetch.dao.mapper.PacificAcInterfaceLogMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="contact_type" jdbcType="VARCHAR" property="contactType" />
    <result column="contact_id" jdbcType="VARCHAR" property="contactId" />
    <result column="staff_mis" jdbcType="VARCHAR" property="staffMis" />
    <result column="interface_id" jdbcType="INTEGER" property="interfaceId" />
    <result column="send_time" jdbcType="VARCHAR" property="sendTime" />
    <result column="parse_type" jdbcType="VARCHAR" property="parseType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs">
    <result column="input" jdbcType="LONGVARCHAR" property="input" />
    <result column="output" jdbcType="LONGVARCHAR" property="output" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, contact_type, contact_id, staff_mis, interface_id, send_time, parse_type, create_time, 
    update_time, is_delete
  </sql>
  <sql id="Blob_Column_List">
    input, output
  </sql>
  <select id="selectByExampleWithBlobs" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pacific_ac_interface_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from pacific_ac_interface_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pacific_ac_interface_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pacific_ac_interface_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogExample">
    delete from pacific_ac_interface_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs">
    insert into pacific_ac_interface_log (id, contact_type, contact_id, 
      staff_mis, interface_id, send_time, 
      parse_type, create_time, update_time, 
      is_delete, input, output
      )
    values (#{id,jdbcType=BIGINT}, #{contactType,jdbcType=VARCHAR}, #{contactId,jdbcType=VARCHAR}, 
      #{staffMis,jdbcType=VARCHAR}, #{interfaceId,jdbcType=INTEGER}, #{sendTime,jdbcType=VARCHAR}, 
      #{parseType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=BIT}, #{input,jdbcType=LONGVARCHAR}, #{output,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs">
    insert into pacific_ac_interface_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="contactType != null">
        contact_type,
      </if>
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="staffMis != null">
        staff_mis,
      </if>
      <if test="interfaceId != null">
        interface_id,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="parseType != null">
        parse_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="input != null">
        input,
      </if>
      <if test="output != null">
        output,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="contactType != null">
        #{contactType,jdbcType=VARCHAR},
      </if>
      <if test="contactId != null">
        #{contactId,jdbcType=VARCHAR},
      </if>
      <if test="staffMis != null">
        #{staffMis,jdbcType=VARCHAR},
      </if>
      <if test="interfaceId != null">
        #{interfaceId,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=VARCHAR},
      </if>
      <if test="parseType != null">
        #{parseType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="input != null">
        #{input,jdbcType=LONGVARCHAR},
      </if>
      <if test="output != null">
        #{output,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogExample" resultType="java.lang.Long">
    select count(*) from pacific_ac_interface_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pacific_ac_interface_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.contactType != null">
        contact_type = #{record.contactType,jdbcType=VARCHAR},
      </if>
      <if test="record.contactId != null">
        contact_id = #{record.contactId,jdbcType=VARCHAR},
      </if>
      <if test="record.staffMis != null">
        staff_mis = #{record.staffMis,jdbcType=VARCHAR},
      </if>
      <if test="record.interfaceId != null">
        interface_id = #{record.interfaceId,jdbcType=INTEGER},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=VARCHAR},
      </if>
      <if test="record.parseType != null">
        parse_type = #{record.parseType,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BIT},
      </if>
      <if test="record.input != null">
        input = #{record.input,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.output != null">
        output = #{record.output,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBlobs" parameterType="map">
    update pacific_ac_interface_log
    set id = #{record.id,jdbcType=BIGINT},
      contact_type = #{record.contactType,jdbcType=VARCHAR},
      contact_id = #{record.contactId,jdbcType=VARCHAR},
      staff_mis = #{record.staffMis,jdbcType=VARCHAR},
      interface_id = #{record.interfaceId,jdbcType=INTEGER},
      send_time = #{record.sendTime,jdbcType=VARCHAR},
      parse_type = #{record.parseType,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=BIT},
      input = #{record.input,jdbcType=LONGVARCHAR},
      output = #{record.output,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pacific_ac_interface_log
    set id = #{record.id,jdbcType=BIGINT},
      contact_type = #{record.contactType,jdbcType=VARCHAR},
      contact_id = #{record.contactId,jdbcType=VARCHAR},
      staff_mis = #{record.staffMis,jdbcType=VARCHAR},
      interface_id = #{record.interfaceId,jdbcType=INTEGER},
      send_time = #{record.sendTime,jdbcType=VARCHAR},
      parse_type = #{record.parseType,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs">
    update pacific_ac_interface_log
    <set>
      <if test="contactType != null">
        contact_type = #{contactType,jdbcType=VARCHAR},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=VARCHAR},
      </if>
      <if test="staffMis != null">
        staff_mis = #{staffMis,jdbcType=VARCHAR},
      </if>
      <if test="interfaceId != null">
        interface_id = #{interfaceId,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=VARCHAR},
      </if>
      <if test="parseType != null">
        parse_type = #{parseType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="input != null">
        input = #{input,jdbcType=LONGVARCHAR},
      </if>
      <if test="output != null">
        output = #{output,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBlobs" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs">
    update pacific_ac_interface_log
    set contact_type = #{contactType,jdbcType=VARCHAR},
      contact_id = #{contactId,jdbcType=VARCHAR},
      staff_mis = #{staffMis,jdbcType=VARCHAR},
      interface_id = #{interfaceId,jdbcType=INTEGER},
      send_time = #{sendTime,jdbcType=VARCHAR},
      parse_type = #{parseType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      input = #{input,jdbcType=LONGVARCHAR},
      output = #{output,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLog">
    update pacific_ac_interface_log
    set contact_type = #{contactType,jdbcType=VARCHAR},
      contact_id = #{contactId,jdbcType=VARCHAR},
      staff_mis = #{staffMis,jdbcType=VARCHAR},
      interface_id = #{interfaceId,jdbcType=INTEGER},
      send_time = #{sendTime,jdbcType=VARCHAR},
      parse_type = #{parseType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listByContactIdContactTypeAndStaffMis" resultMap="ResultMapWithBLOBs">
    SELECT
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    FROM pacific_ac_interface_log
    <where>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=VARCHAR}
      </if>
      <if test="contactType != null">
        AND contact_type = #{contactType,jdbcType=VARCHAR}
      </if>
      <if test="staffMis != null">
        AND staff_mis = #{staffMis,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>