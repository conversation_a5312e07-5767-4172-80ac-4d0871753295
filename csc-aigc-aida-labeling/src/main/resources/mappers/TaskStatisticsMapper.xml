<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.TaskStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.TaskStatistics">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="stat_date" jdbcType="DATE" property="statDate" />
    <result column="biz_type" jdbcType="BIGINT" property="bizType" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="labeling_status" jdbcType="TINYINT" property="labelingStatus" />
    <result column="quality_check_status" jdbcType="TINYINT" property="qualityCheckStatus" />
    <result column="total_sample_count" jdbcType="INTEGER" property="totalSampleCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, stat_date, biz_type, task_type, data_type, task_id, task_name, labeling_status, 
    quality_check_status, total_sample_count, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.TaskStatisticsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from task_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from task_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from task_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.TaskStatisticsExample">
    delete from task_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.TaskStatistics">
    insert into task_statistics (id, stat_date, biz_type, 
      task_type, data_type, task_id, 
      task_name, labeling_status, quality_check_status, 
      total_sample_count, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{statDate,jdbcType=DATE}, #{bizType,jdbcType=BIGINT},
      #{taskType,jdbcType=TINYINT}, #{dataType,jdbcType=TINYINT}, #{taskId,jdbcType=BIGINT}, 
      #{taskName,jdbcType=VARCHAR}, #{labelingStatus,jdbcType=TINYINT}, #{qualityCheckStatus,jdbcType=TINYINT}, 
      #{totalSampleCount,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.TaskStatistics">
    insert into task_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="statDate != null">
        stat_date,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="labelingStatus != null">
        labeling_status,
      </if>
      <if test="qualityCheckStatus != null">
        quality_check_status,
      </if>
      <if test="totalSampleCount != null">
        total_sample_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="statDate != null">
        #{statDate,jdbcType=DATE},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="labelingStatus != null">
        #{labelingStatus,jdbcType=TINYINT},
      </if>
      <if test="qualityCheckStatus != null">
        #{qualityCheckStatus,jdbcType=TINYINT},
      </if>
      <if test="totalSampleCount != null">
        #{totalSampleCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.TaskStatisticsExample" resultType="java.lang.Long">
    select count(*) from task_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update task_statistics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.statDate != null">
        stat_date = #{record.statDate,jdbcType=DATE},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=BIGINT},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=TINYINT},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.labelingStatus != null">
        labeling_status = #{record.labelingStatus,jdbcType=TINYINT},
      </if>
      <if test="record.qualityCheckStatus != null">
        quality_check_status = #{record.qualityCheckStatus,jdbcType=TINYINT},
      </if>
      <if test="record.totalSampleCount != null">
        total_sample_count = #{record.totalSampleCount,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update task_statistics
    set id = #{record.id,jdbcType=BIGINT},
      stat_date = #{record.statDate,jdbcType=DATE},
      biz_type = #{record.bizType,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=TINYINT},
      data_type = #{record.dataType,jdbcType=TINYINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      labeling_status = #{record.labelingStatus,jdbcType=TINYINT},
      quality_check_status = #{record.qualityCheckStatus,jdbcType=TINYINT},
      total_sample_count = #{record.totalSampleCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.TaskStatistics">
    update task_statistics
    <set>
      <if test="statDate != null">
        stat_date = #{statDate,jdbcType=DATE},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="labelingStatus != null">
        labeling_status = #{labelingStatus,jdbcType=TINYINT},
      </if>
      <if test="qualityCheckStatus != null">
        quality_check_status = #{qualityCheckStatus,jdbcType=TINYINT},
      </if>
      <if test="totalSampleCount != null">
        total_sample_count = #{totalSampleCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.TaskStatistics">
    update task_statistics
    set stat_date = #{statDate,jdbcType=DATE},
      biz_type = #{bizType,jdbcType=BIGINT},
      task_type = #{taskType,jdbcType=TINYINT},
      data_type = #{dataType,jdbcType=TINYINT},
      task_id = #{taskId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      labeling_status = #{labelingStatus,jdbcType=TINYINT},
      quality_check_status = #{qualityCheckStatus,jdbcType=TINYINT},
      total_sample_count = #{totalSampleCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listTasks" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DashboardTaskItemVO">
    SELECT
      statistics.task_id AS taskId,
      statistics.labeling_status AS labelingStatus,
      statistics.quality_check_status AS qualityCheckStatus,
      statistics.biz_type AS bizType,
      statistics.total_sample_count AS totalSampleCount,
      labeling.task_name AS taskName,
      labeling.labeling_manager AS labelingManager,
      labeling.labeling_manager_name AS labelingManagerName,
      DATE_FORMAT(labeling.create_time, '%Y-%m-%d %H:%i:%s') AS createTime
    FROM task_statistics AS statistics
    INNER JOIN (
      SELECT task_id, MAX(stat_date) AS stat_date, MAX(update_time) AS update_time
      FROM task_statistics
      <where>
        <if test="query.bizTypes != null">
          biz_type IN
          <foreach collection="query.bizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
          </foreach>
        </if>
        <if test="query.taskType != null">
          AND task_type = #{query.taskType}
        </if>
        <if test="query.dataType != null">
          AND data_type = #{query.dataType}
        </if>
        <if test="query.startDate != null">
          AND stat_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null">
          AND stat_date &lt;= #{query.endDate}
        </if>
        <if test="query.queryType != null and query.queryType == 1">
          AND labeling_status IN (5, 9)
        </if>
        <if test="query.queryType != null and query.queryType == 2">
          AND quality_check_status = 3
        </if>
      </where>
      GROUP BY task_id
    ) AS latest ON statistics.task_id = latest.task_id AND statistics.stat_date = latest.stat_date AND statistics.update_time = latest.update_time
    LEFT JOIN labeling_task AS labeling ON statistics.task_id = labeling.id
    GROUP BY statistics.task_id, statistics.labeling_status, statistics.biz_type, labeling.task_name, labeling.labeling_manager, labeling.labeling_manager_name, labeling.create_time
    ORDER BY statistics.stat_date DESC, statistics.update_time DESC
  </select>
  <select id="statisticalTaskData" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.TaskStatisticalVO">
    SELECT
      COUNT(statistics.task_id) AS totalTaskCount,
      COALESCE(SUM(statistics.labeling_status IN (5, 9) ), 0) AS labelingCompletedTaskCount,
      COALESCE(SUM(statistics.quality_check_status = 3), 0) AS checkCompletedTaskCount,
      COALESCE(SUM(statistics.total_sample_count), 0) AS totalSampleCount
    FROM task_statistics AS statistics
    INNER JOIN (
      SELECT task_id, MAX(stat_date) AS stat_date, MAX(update_time) AS update_time
      FROM task_statistics
      <where>
        <if test="query.bizTypes != null">
          biz_type IN
          <foreach collection="query.bizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
          </foreach>
        </if>
        <if test="query.taskType != null">
          AND task_type = #{query.taskType}
        </if>
        <if test="query.dataType != null">
          AND data_type = #{query.dataType}
        </if>
        <if test="query.startDate != null">
          AND stat_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null">
          AND stat_date &lt;= #{query.endDate}
        </if>
      </where>
      GROUP BY task_id
    ) AS latest ON statistics.task_id = latest.task_id AND statistics.stat_date = latest.stat_date AND statistics.update_time = latest.update_time
  </select>
  <insert id="batchInsert">
    INSERT INTO task_statistics
    (
    stat_date,
    biz_type,
    task_type,
    data_type,
    task_id,
    task_name,
    labeling_status,
    quality_check_status,
    total_sample_count,
    create_time,
    update_time
    )
    VALUES
    <foreach collection="taskStatisticsList" item="item" separator=",">
      (
      #{item.statDate},
      #{item.bizType},
      #{item.taskType},
      #{item.dataType},
      #{item.taskId},
      #{item.taskName},
      #{item.labelingStatus},
      #{item.qualityCheckStatus},
      #{item.totalSampleCount},
      #{item.createTime},
      #{item.updateTime}
      )
    </foreach>
  </insert>

</mapper>