package com.meituan.aigc.aida.data.management.es.exception;

import com.meituan.aigc.aida.labeling.exception.AidaTrainingException;

/**
 * ES操作基础异常类
 * 所有ES相关操作异常的父类
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
public class TrainingEsException extends AidaTrainingException {

    public TrainingEsException() {
        super();
    }

    public TrainingEsException(String message) {
        super(message);
    }

    public TrainingEsException(Throwable cause) {
        super(cause);
    }

    public TrainingEsException(String message, Throwable cause) {
        super(message, cause);
    }
} 