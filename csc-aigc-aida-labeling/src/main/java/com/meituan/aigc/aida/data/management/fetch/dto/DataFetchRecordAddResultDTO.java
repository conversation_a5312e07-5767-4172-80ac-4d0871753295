package com.meituan.aigc.aida.data.management.fetch.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: guowenhui
 * @Create: 2025/4/15 17:19
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataFetchRecordAddResultDTO {

    /**
     * 最后一条数据的ID
     */
    private Long lastId;

    /**
     * 本次插入数据条数
     */
    private Integer addDataCount;

    /**
     * 最后一个合并的key
     */
    private String lastKey;

    public static DataFetchRecordAddResultDTO noDataResult(){
        DataFetchRecordAddResultDTO recordAddResultDTO = new DataFetchRecordAddResultDTO();
        recordAddResultDTO.setAddDataCount(0);
        recordAddResultDTO.setLastId(-1L);
        recordAddResultDTO.setLastKey("");
        return recordAddResultDTO;
    }

}
