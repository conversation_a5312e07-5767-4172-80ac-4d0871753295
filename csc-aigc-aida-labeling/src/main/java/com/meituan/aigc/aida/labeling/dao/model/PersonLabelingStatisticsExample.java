package com.meituan.aigc.aida.labeling.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class PersonLabelingStatisticsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PersonLabelingStatisticsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNull() {
            addCriterion("stat_date is null");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNotNull() {
            addCriterion("stat_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatDateEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date =", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date <>", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThan(Date value) {
            addCriterionForJDBCDate("stat_date >", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date >=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThan(Date value) {
            addCriterionForJDBCDate("stat_date <", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date <=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateIn(List<Date> values) {
            addCriterionForJDBCDate("stat_date in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("stat_date not in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("stat_date between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("stat_date not between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andBizTypeIsNull() {
            addCriterion("biz_type is null");
            return (Criteria) this;
        }

        public Criteria andBizTypeIsNotNull() {
            addCriterion("biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andBizTypeEqualTo(Long value) {
            addCriterion("biz_type =", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotEqualTo(Long value) {
            addCriterion("biz_type <>", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeGreaterThan(Long value) {
            addCriterion("biz_type >", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_type >=", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeLessThan(Long value) {
            addCriterion("biz_type <", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeLessThanOrEqualTo(Long value) {
            addCriterion("biz_type <=", value, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeIn(List<Long> values) {
            addCriterion("biz_type in", values, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotIn(List<Long> values) {
            addCriterion("biz_type not in", values, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeBetween(Long value1, Long value2) {
            addCriterion("biz_type between", value1, value2, "bizType");
            return (Criteria) this;
        }

        public Criteria andBizTypeNotBetween(Long value1, Long value2) {
            addCriterion("biz_type not between", value1, value2, "bizType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNull() {
            addCriterion("task_type is null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNotNull() {
            addCriterion("task_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeEqualTo(Integer value) {
            addCriterion("task_type =", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotEqualTo(Integer value) {
            addCriterion("task_type <>", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThan(Integer value) {
            addCriterion("task_type >", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_type >=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThan(Integer value) {
            addCriterion("task_type <", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThanOrEqualTo(Integer value) {
            addCriterion("task_type <=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIn(List<Integer> values) {
            addCriterion("task_type in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotIn(List<Integer> values) {
            addCriterion("task_type not in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeBetween(Integer value1, Integer value2) {
            addCriterion("task_type between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("task_type not between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNull() {
            addCriterion("data_type is null");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNotNull() {
            addCriterion("data_type is not null");
            return (Criteria) this;
        }

        public Criteria andDataTypeEqualTo(Integer value) {
            addCriterion("data_type =", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotEqualTo(Integer value) {
            addCriterion("data_type <>", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThan(Integer value) {
            addCriterion("data_type >", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_type >=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThan(Integer value) {
            addCriterion("data_type <", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThanOrEqualTo(Integer value) {
            addCriterion("data_type <=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIn(List<Integer> values) {
            addCriterion("data_type in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotIn(List<Integer> values) {
            addCriterion("data_type not in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeBetween(Integer value1, Integer value2) {
            addCriterion("data_type between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("data_type not between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andLabelerNameIsNull() {
            addCriterion("labeler_name is null");
            return (Criteria) this;
        }

        public Criteria andLabelerNameIsNotNull() {
            addCriterion("labeler_name is not null");
            return (Criteria) this;
        }

        public Criteria andLabelerNameEqualTo(String value) {
            addCriterion("labeler_name =", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameNotEqualTo(String value) {
            addCriterion("labeler_name <>", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameGreaterThan(String value) {
            addCriterion("labeler_name >", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameGreaterThanOrEqualTo(String value) {
            addCriterion("labeler_name >=", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameLessThan(String value) {
            addCriterion("labeler_name <", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameLessThanOrEqualTo(String value) {
            addCriterion("labeler_name <=", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameLike(String value) {
            addCriterion("labeler_name like", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameNotLike(String value) {
            addCriterion("labeler_name not like", value, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameIn(List<String> values) {
            addCriterion("labeler_name in", values, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameNotIn(List<String> values) {
            addCriterion("labeler_name not in", values, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameBetween(String value1, String value2) {
            addCriterion("labeler_name between", value1, value2, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerNameNotBetween(String value1, String value2) {
            addCriterion("labeler_name not between", value1, value2, "labelerName");
            return (Criteria) this;
        }

        public Criteria andLabelerMisIsNull() {
            addCriterion("labeler_mis is null");
            return (Criteria) this;
        }

        public Criteria andLabelerMisIsNotNull() {
            addCriterion("labeler_mis is not null");
            return (Criteria) this;
        }

        public Criteria andLabelerMisEqualTo(String value) {
            addCriterion("labeler_mis =", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisNotEqualTo(String value) {
            addCriterion("labeler_mis <>", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisGreaterThan(String value) {
            addCriterion("labeler_mis >", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisGreaterThanOrEqualTo(String value) {
            addCriterion("labeler_mis >=", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisLessThan(String value) {
            addCriterion("labeler_mis <", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisLessThanOrEqualTo(String value) {
            addCriterion("labeler_mis <=", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisLike(String value) {
            addCriterion("labeler_mis like", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisNotLike(String value) {
            addCriterion("labeler_mis not like", value, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisIn(List<String> values) {
            addCriterion("labeler_mis in", values, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisNotIn(List<String> values) {
            addCriterion("labeler_mis not in", values, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisBetween(String value1, String value2) {
            addCriterion("labeler_mis between", value1, value2, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelerMisNotBetween(String value1, String value2) {
            addCriterion("labeler_mis not between", value1, value2, "labelerMis");
            return (Criteria) this;
        }

        public Criteria andLabelingCountIsNull() {
            addCriterion("labeling_count is null");
            return (Criteria) this;
        }

        public Criteria andLabelingCountIsNotNull() {
            addCriterion("labeling_count is not null");
            return (Criteria) this;
        }

        public Criteria andLabelingCountEqualTo(Integer value) {
            addCriterion("labeling_count =", value, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountNotEqualTo(Integer value) {
            addCriterion("labeling_count <>", value, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountGreaterThan(Integer value) {
            addCriterion("labeling_count >", value, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("labeling_count >=", value, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountLessThan(Integer value) {
            addCriterion("labeling_count <", value, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountLessThanOrEqualTo(Integer value) {
            addCriterion("labeling_count <=", value, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountIn(List<Integer> values) {
            addCriterion("labeling_count in", values, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountNotIn(List<Integer> values) {
            addCriterion("labeling_count not in", values, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountBetween(Integer value1, Integer value2) {
            addCriterion("labeling_count between", value1, value2, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andLabelingCountNotBetween(Integer value1, Integer value2) {
            addCriterion("labeling_count not between", value1, value2, "labelingCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountIsNull() {
            addCriterion("qc_inspected_count is null");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountIsNotNull() {
            addCriterion("qc_inspected_count is not null");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountEqualTo(Integer value) {
            addCriterion("qc_inspected_count =", value, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountNotEqualTo(Integer value) {
            addCriterion("qc_inspected_count <>", value, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountGreaterThan(Integer value) {
            addCriterion("qc_inspected_count >", value, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("qc_inspected_count >=", value, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountLessThan(Integer value) {
            addCriterion("qc_inspected_count <", value, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountLessThanOrEqualTo(Integer value) {
            addCriterion("qc_inspected_count <=", value, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountIn(List<Integer> values) {
            addCriterion("qc_inspected_count in", values, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountNotIn(List<Integer> values) {
            addCriterion("qc_inspected_count not in", values, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountBetween(Integer value1, Integer value2) {
            addCriterion("qc_inspected_count between", value1, value2, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCountNotBetween(Integer value1, Integer value2) {
            addCriterion("qc_inspected_count not between", value1, value2, "qcInspectedCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountIsNull() {
            addCriterion("qc_inspected_correct_count is null");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountIsNotNull() {
            addCriterion("qc_inspected_correct_count is not null");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountEqualTo(Integer value) {
            addCriterion("qc_inspected_correct_count =", value, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountNotEqualTo(Integer value) {
            addCriterion("qc_inspected_correct_count <>", value, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountGreaterThan(Integer value) {
            addCriterion("qc_inspected_correct_count >", value, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("qc_inspected_correct_count >=", value, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountLessThan(Integer value) {
            addCriterion("qc_inspected_correct_count <", value, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountLessThanOrEqualTo(Integer value) {
            addCriterion("qc_inspected_correct_count <=", value, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountIn(List<Integer> values) {
            addCriterion("qc_inspected_correct_count in", values, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountNotIn(List<Integer> values) {
            addCriterion("qc_inspected_correct_count not in", values, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountBetween(Integer value1, Integer value2) {
            addCriterion("qc_inspected_correct_count between", value1, value2, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andQcInspectedCorrectCountNotBetween(Integer value1, Integer value2) {
            addCriterion("qc_inspected_correct_count not between", value1, value2, "qcInspectedCorrectCount");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}