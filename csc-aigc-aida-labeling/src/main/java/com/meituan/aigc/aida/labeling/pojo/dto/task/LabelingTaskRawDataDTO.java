package com.meituan.aigc.aida.labeling.pojo.dto.task;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: guowen<PERSON>
 * @Create: 2025/6/10 16:36
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelingTaskRawDataDTO extends LabelingTaskRawData {

    /**
     * 复标前原数据ID
     */
    private Long reLabelingRawDataId;

}
