package com.meituan.aigc.aida.labeling.remote.common.dto;

import lombok.Builder;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

/**
 * 人工标注结果DTO
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ManualLabelResultDTO implements Serializable {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;
    /**
     * 标注项ID
     */
    private Long id;
    /**
     * 标注项名称
     */
    private String name;
    /**
     * 数据类型 1-枚举 2-文本
     */
    private Integer dataType;
    /**
     * 指标类型 1-标注 2-质检
     */
    private Integer itemType;
    /**
     * 是否背靠背一致率标注项 1-是 2-否
     */
    private Integer isCompare;
    /**
     * 是否必填 1-是 2-否
     */
    private Integer isRequired;
    /**
     * 父指标ID，如果是父指标为空
     */
    private Long parentId;
    /**
     * 父指标枚举，当父指标选择该枚举后需要填子指标
     */
    private String parentEnumValue;
    /**
     * 标注值
     */
    private String value;
    /**
     * 枚举列表
     */
    private List<String> enumList;
}
