package com.meituan.aigc.aida.data.management.fetch.dto.pacific;

import com.sankuai.csc.workbench.bff.api.workbench.dto.InitButlerResDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 太平洋管家上下文参数DTO
 *
 * <AUTHOR>
 * @date 2025/04/03
 */
@Data
public class PacificButlerContextParamDTO implements Serializable {

    /**
     * 管家响应DTO
     */
    private InitButlerResDTO butlerRes;

    /**
     * 管家参数Map
     */
    private Map<String, String> butlerParamMap;
}
