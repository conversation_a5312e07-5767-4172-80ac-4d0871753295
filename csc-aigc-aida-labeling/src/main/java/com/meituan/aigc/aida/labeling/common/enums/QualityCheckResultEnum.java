package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON>
 * @Create: 2025/3/26 11:29
 * @Version: 1.0
 * 质检结果枚举
 */
@Getter
public enum QualityCheckResultEnum {

    CORRECT(1, "正确"),
    ERROR(2, "错误");

    private final int code;
    private final String value;

    QualityCheckResultEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static QualityCheckResultEnum getByCode(int code) {
        for (QualityCheckResultEnum resultEnum : QualityCheckResultEnum.values()) {
            if (resultEnum.getCode() == code) {
                return resultEnum;
            }
        }
        return null;
    }

}
