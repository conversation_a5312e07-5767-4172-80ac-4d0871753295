package com.meituan.aigc.aida.labeling.pojo.vo.dashboard;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: guowenhui
 * @Create: 2025/5/20 14:27
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DashboardOverviewStatsVO implements Serializable {

    /**
     * 任务统计
     */
    private DashboardTaskCountVO task;

    /**
     * 标注统计
     */
    private DashboardLabelingCountVO labeling;

    /**
     * 质检统计
     */
    private DashboardQualityCheckCountVO qualityCheck;

}
