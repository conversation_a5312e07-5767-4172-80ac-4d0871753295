package com.meituan.aigc.aida.data.management.fetch.dao.mapper;

import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTaskExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataFetchTaskMapper {
    long countByExample(DataFetchTaskExample example);

    int deleteByExample(DataFetchTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DataFetchTask record);

    int insertSelective(DataFetchTask record);

    List<DataFetchTask> selectByExample(DataFetchTaskExample example);

    DataFetchTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DataFetchTask record, @Param("example") DataFetchTaskExample example);

    int updateByExample(@Param("record") DataFetchTask record, @Param("example") DataFetchTaskExample example);

    int updateByPrimaryKeySelective(DataFetchTask record);

    int updateByPrimaryKey(DataFetchTask record);
}