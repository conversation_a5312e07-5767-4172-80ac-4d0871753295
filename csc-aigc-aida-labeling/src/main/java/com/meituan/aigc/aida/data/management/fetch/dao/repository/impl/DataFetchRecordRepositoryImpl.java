package com.meituan.aigc.aida.data.management.fetch.dao.repository.impl;

import com.meituan.aigc.aida.data.management.fetch.dao.mapper.DataFetchRecordMapper;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordExample;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchRecordRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/4/7 15:52
 * @Version: 1.0
 */
@Repository
public class DataFetchRecordRepositoryImpl implements DataFetchRecordRepository {

    @Resource
    private DataFetchRecordMapper dataFetchRecordMapper;


    @Override
    public List<DataFetchRecordWithBLOBs> listRecordByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return Collections.emptyList();
        }
        DataFetchRecordExample example = new DataFetchRecordExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        return dataFetchRecordMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public void batchInsert(List<DataFetchRecordWithBLOBs> dataFetchRecordList) {
        if (CollectionUtils.isEmpty(dataFetchRecordList)) {
            return;
        }
        dataFetchRecordMapper.batchInsertDataFetchRecord(dataFetchRecordList);
    }

    @Override
    public DataFetchRecordWithBLOBs getById(Long lastId) {
        if (Objects.isNull(lastId)) {
            return null;
        }
        return dataFetchRecordMapper.selectByPrimaryKey(lastId);
    }

    @Override
    public void updateByIdSelective(DataFetchRecordWithBLOBs dataFetchRecord) {
        if (Objects.isNull(dataFetchRecord)) {
            return;
        }
        dataFetchRecordMapper.updateByPrimaryKeySelective(dataFetchRecord);
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return;
        }
        DataFetchRecordExample example = new DataFetchRecordExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        DataFetchRecordWithBLOBs record = new DataFetchRecordWithBLOBs();
        record.setIsDelete(Boolean.TRUE);
        record.setUpdateTime(new Date());
        dataFetchRecordMapper.updateByExampleSelective(record, example);
    }

    @Override
    public Integer countByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return 0;
        }
        return dataFetchRecordMapper.countByTaskId(taskId);
    }

    @Override
    public DataFetchRecordWithBLOBs getOneByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }
        List<DataFetchRecordWithBLOBs> pageList = dataFetchRecordMapper.pageByTaskId(taskId, 0, 1);
        if (CollectionUtils.isEmpty(pageList)) {
            return null;
        }
        return pageList.get(0);
    }

    @Override
    public List<DataFetchRecordWithBLOBs> pageByTaskId(Long taskId, Integer offset, Integer pageSize) {
        if (Objects.isNull(taskId) || Objects.isNull(offset) || Objects.isNull(pageSize)) {
            return Collections.emptyList();
        }
        return dataFetchRecordMapper.pageByTaskId(taskId, offset, pageSize);
    }

    @Override
    public List<DataFetchRecordWithBLOBs> listByTaskIdAndLikeByInputs(Long taskId, String inputs) {
        if (Objects.isNull(taskId)) {
            return Collections.emptyList();
        }
        return dataFetchRecordMapper.listByTaskIdAndLikeByInputs(taskId, inputs);
    }


}
