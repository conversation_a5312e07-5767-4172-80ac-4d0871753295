package com.meituan.aigc.aida.labeling.remote.caseanalysis.param;

import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.SortTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-26 11:12
 * @description 数据集查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetPageQueryParam implements Serializable {
    /**
     * 数据集ID
     */
    private Long dataSetId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 数据来源
     * {@link DataSourceEnum#code}
     */
    private Integer dataSource;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 页数
     */
    private Integer pageSize;
    /**
     * 排序字段类型 1-通用数据 2-自定义上传数据
     *
     * @see com.meituan.aigc.aida.labeling.remote.common.enums.SortTypeEnum
     */
    private Integer sortType = SortTypeEnum.CUSTOM.getCode();
    /**
     * 滚动ID
     */
    private String scrollId;
    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方式 ASC/DESC
     *
     * @see com.meituan.aigc.aida.labeling.remote.common.enums.SortOrderEnum
     */
    private String sortOrder;
    /**
     * 查询条件
     */
    private List<DataSetQueryCondition> conditionList;
}
