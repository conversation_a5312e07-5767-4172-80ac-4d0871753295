package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.dao.mapper.LabelingQualityCheckItemMapper;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItem;
import com.meituan.aigc.aida.labeling.dao.model.LabelingQualityCheckItemExample;
import com.meituan.aigc.aida.labeling.dao.po.LabelingQualityCheckItemPO;
import com.meituan.aigc.aida.labeling.dao.po.QualityCheckTaskPO;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingQualityCheckItemRepository;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonQualityCheckStatisticsPO;
import com.meituan.aigc.aida.labeling.param.quality.check.LabelingQualityCheckItemParam;
import com.meituan.aigc.aida.labeling.pojo.dto.PageQueryDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingDataCheckResultVO;
import com.meituan.aigc.aida.labeling.pojo.vo.QualityCheckModifiedDataCountVO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:06
 * @Version: 1.0
 */
@Repository
public class LabelingQualityCheckItemRepositoryImpl implements LabelingQualityCheckItemRepository {
    @Resource
    private LabelingQualityCheckItemMapper labelingQualityCheckItemMapper;


    @Override
    public int batchInsert(List<LabelingQualityCheckItem> qualityCheckItems) {
        return labelingQualityCheckItemMapper.batchInsert(qualityCheckItems);
    }

    @Override
    public List<LabelingQualityCheckItemPO> listByQualityCheckTaskIdAndQualityCheckStatus(LabelingQualityCheckItemParam param) {
        return labelingQualityCheckItemMapper.listByQualityCheckTaskIdAndQualityCheckStatus(param);
    }

    @Override
    public void deleteByQualityCheckTaskIds(List<Long> qualityCheckTaskIds) {
        labelingQualityCheckItemMapper.deleteByQualityCheckTaskIds(qualityCheckTaskIds);
    }

    @Override
    public List<LabelingQualityCheckItemPO> listQualityCheckItemByTaskIdAndRawDataIds(Long qualityCheckTaskId, List<Long> rawDataIds) {
        if (Objects.isNull(qualityCheckTaskId)) {
            return Collections.emptyList();
        }
        return labelingQualityCheckItemMapper.listQualityCheckItemByTaskIdAndRawDataIds(qualityCheckTaskId, rawDataIds);
    }

    @Override
    public List<QualityCheckTaskPO> listByQualityCheckTaskId(List<Long> qualityCheckTaskIds) {
        return labelingQualityCheckItemMapper.listByQualityCheckTaskId(qualityCheckTaskIds);
    }

    @Override
    public LabelingQualityCheckItem getById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return labelingQualityCheckItemMapper.selectByPrimaryKey(id);
    }

    /**
     * 更新质检结果
     *
     * @param checkItem 质检详情
     */
    @Override
    public void updateByIdSelective(LabelingQualityCheckItem checkItem) {
        if (Objects.isNull(checkItem)) {
            return;
        }
        labelingQualityCheckItemMapper.updateByPrimaryKeySelective(checkItem);
    }

    @Override
    public Integer countByCheckTaskIdAndStatus(Long qualityCheckTaskId, Integer qualityCheckDataStatus) {
        return labelingQualityCheckItemMapper.countByCheckTaskIdAndStatus(qualityCheckTaskId, qualityCheckDataStatus);
    }

    @Override
    public PageQueryDTO<?> countByTaskIdAndLabelerMis(Long checkTaskId, String labelerMis) {
        return labelingQualityCheckItemMapper.countByTaskIdAndLabelerMis(checkTaskId, labelerMis);
    }

    @Override
    public List<LabelingDataCheckResultVO> countCheckDetailByCheckTaskIds(List<Long> qualityCheckTaskIds) {
        if (CollectionUtils.isEmpty(qualityCheckTaskIds)) {
            return Collections.emptyList();
        }
        return labelingQualityCheckItemMapper.countCheckDetailByCheckTaskIds(qualityCheckTaskIds);
    }

    @Override
    public Integer countRawDataNumByCheckTaskIdsAndStatus(List<Long> qualityCheckTaskIds, Integer checkStatus) {
        return labelingQualityCheckItemMapper.countRawDataNumByCheckTaskIdsAndStatus(qualityCheckTaskIds, checkStatus);
    }

    @Override
    public Integer countCheckTaskNumByQualityCheckItemIds(List<Long> checkItemIds) {
        if (CollectionUtils.isEmpty(checkItemIds)) {
            return 0;
        }
        return labelingQualityCheckItemMapper.countCheckTaskNumByQualityCheckItemIds(checkItemIds);
    }

    @Override
    public List<Long> listRawDataIdsByTaskAndRawDataIdAndStatus(Long qualityCheckTaskId, Long rawDataId, Integer checkAbleStatus, Integer qualityStatus) {
        return labelingQualityCheckItemMapper.listRawDataIdsByTaskAndRawDataIdAndStatus(qualityCheckTaskId, rawDataId, checkAbleStatus, qualityStatus);
    }

    @Override
    public Integer countRawDataIdsByTaskAndStatus(Long qualityCheckTaskId, Integer checkAbleStatus, Integer qualityStatus) {
        return labelingQualityCheckItemMapper.countRawDataIdsByTaskAndStatus(qualityCheckTaskId, checkAbleStatus, qualityStatus);
    }

    @Override
    public List<LabelingQualityCheckItem> listByDetailIdList(List<Long> labelingDetailIdList) {
        if (CollectionUtils.isEmpty(labelingDetailIdList)) {
            return Collections.emptyList();
        }
        LabelingQualityCheckItemExample example = new LabelingQualityCheckItemExample();
        example.createCriteria().andLabelingDataIdIn(labelingDetailIdList);
        return labelingQualityCheckItemMapper.selectByExample(example);
    }

    @Override
    public void batchUpdateById(List<LabelingQualityCheckItem> labelingQualityCheckItemList) {
        if (CollectionUtils.isEmpty(labelingQualityCheckItemList)) {
            return;
        }
        labelingQualityCheckItemMapper.batchUpdateByPrimaryKeySelective(labelingQualityCheckItemList);
    }

    @Override
    public List<String> listSessionIdByTaskAndSessionIdAndStatus(Long qualityCheckTaskId, String sessionId, Integer checkAbleStatus, Integer qualityStatus) {
        return labelingQualityCheckItemMapper.listSessionIdByTaskAndSessionIdAndStatus(qualityCheckTaskId, sessionId, checkAbleStatus, qualityStatus);
    }

    @Override
    public List<LabelingQualityCheckItemPO> listQualityCheckItemByTaskIdAndSessionIdList(Long qualityCheckTaskId, List<String> sessionIdList) {
        if (Objects.isNull(qualityCheckTaskId)) {
            return Collections.emptyList();
        }
        return labelingQualityCheckItemMapper.listQualityCheckItemByTaskIdAndSessionIdList(qualityCheckTaskId, sessionIdList);
    }

    @Override
    public QualityCheckModifiedDataCountVO countQualityCheckModifiedDataByTaskId(Long subTaskId) {
        if (Objects.isNull(subTaskId)) {
            return new QualityCheckModifiedDataCountVO(0, 0);
        }
        return labelingQualityCheckItemMapper.countQualityCheckModifiedDataByTaskId(subTaskId);
    }

    @Override
    public void updateById(LabelingQualityCheckItem labelingQualityCheckItem) {
        if (Objects.isNull(labelingQualityCheckItem)) {
            return;
        }
        labelingQualityCheckItemMapper.updateByPrimaryKey(labelingQualityCheckItem);
    }

    @Override
    public List<LabelingQualityCheckItem> listDetailIdByTaskIdAndSessionId(Long qualityCheckTaskId, String sessionId) {
        LabelingQualityCheckItemExample example = new LabelingQualityCheckItemExample();
        example.createCriteria().andQualityCheckTaskIdEqualTo(qualityCheckTaskId)
                .andSessionIdEqualTo(sessionId);
        return labelingQualityCheckItemMapper.selectByExample(example);
    }

    @Override
    public List<LabelingQualityCheckItem> listDetailIdByTaskIdAndRawDataId(Long qualityCheckTaskId, Long rawDataId) {
        LabelingQualityCheckItemExample example = new LabelingQualityCheckItemExample();
        example.createCriteria().andQualityCheckTaskIdEqualTo(qualityCheckTaskId)
                .andRawDataIdEqualTo(rawDataId);
        return labelingQualityCheckItemMapper.selectByExample(example);
    }

    @Override
    public int countSessionBySubTaskIdAndStatus(Long subTaskId, Integer status) {
        if (Objects.isNull(subTaskId)) {
            return 0;
        }
        return labelingQualityCheckItemMapper.countSessionBySubTaskIdAndStatus(subTaskId, status);
    }

    @Override
    public int countQueryBySubTaskIdAndStatus(Long subTaskId, Integer status) {
        if (Objects.isNull(subTaskId)) {
            return 0;
        }
        return labelingQualityCheckItemMapper.countQueryBySubTaskIdAndStatus(subTaskId, status);
    }

    @Override
    public List<LabelingQualityCheckItem> listDataByQualityCheckTaskIdAndQualityCheckStatus(Long qualityCheckTaskId, Integer qualityCheckStatus) {
        if (Objects.isNull(qualityCheckTaskId) && Objects.isNull(qualityCheckStatus)) {
            return new ArrayList<>();
        }
        LabelingQualityCheckItemExample example = new LabelingQualityCheckItemExample();
        example.createCriteria().andQualityCheckTaskIdEqualTo(qualityCheckTaskId).andStatusEqualTo(qualityCheckStatus);
        return labelingQualityCheckItemMapper.selectByExample(example);
    }

    @Override
    public List<PersonQualityCheckStatisticsPO> statisticsPersonQualityCheckData(Date startTime, Date endTime) {
        return labelingQualityCheckItemMapper.statisticsPersonQualityCheckData(startTime, endTime);
    }
}
