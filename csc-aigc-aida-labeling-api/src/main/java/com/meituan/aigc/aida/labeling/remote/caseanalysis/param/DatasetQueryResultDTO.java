package com.meituan.aigc.aida.labeling.remote.caseanalysis.param;

import com.meituan.aigc.aida.labeling.remote.common.dto.PageDataWithHead;
import lombok.Data;

import java.io.Serializable;

@Data
public class DatasetQueryResultDTO implements Serializable {
    /**
     * 滚动ID
     */
    private String scrollId;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 分页数据
     */
    private PageDataWithHead<DataFieldParam, DataSetRecordParam> pageData;
}
