package com.meituan.aigc.aida.labeling.pojo.vo;

import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistributionDimensionRequest implements Serializable {
    /**
     * 上一次分组序号
     */
    private int lastGroupIndex;
    /**
     * 任务信息
     */
    private LabelingTask labelingTask;
    /**
     * 分组id
     */
    private Long groupId;
    /**
     * 子任务列表
     */
    private List<LabelingSubTask> labelingSubTasks;
    /**
     * 任务数据总量
     */
    private Integer totalDataCount;
}
