package com.meituan.aigc.aida.data.management.fetch.helper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.meituan.aigc.aida.common.util.ObjectConverter;
import com.meituan.aigc.aida.data.management.fetch.dto.PopupAcInterfaceDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AC接口输入参数去重助手类
 * <p>
 * 该类负责处理AC接口输入参数的去重逻辑，主要功能包括：
 * 1. 将多条AC接口记录按照参数内容进行去重
 * 2. 过滤无效参数和空值参数
 * 3. 规范化参数格式以便比较
 * 4. 记录合并记录的信息
 * <p>
 * 去重逻辑示例:
 *
 * <pre>
 * 【示例1: 完全相同的参数会被去重】
 * 记录1: {"name": "张三", "age": 25, "city": "北京"}
 * 记录2: {"name": "张三", "age": 25, "city": "北京"}
 * 去重结果: 合并为一条记录，{"age": "25", "city": "北京", "name": "张三"}
 *
 * 【示例2: 参数顺序不同但内容相同的会被去重】
 * 记录1: {"name": "张三", "age": 25, "city": "北京"}
 * 记录2: {"city": "北京", "name": "张三", "age": 25}
 * 去重结果: 合并为一条记录，{"age": "25", "city": "北京", "name": "张三"}
 *
 * 【示例3: 参数值类型不同但内容相同的会被去重】
 * 记录1: {"name": "张三", "age": 25, "city": "北京"}
 * 记录2: {"name": "张三", "age": "25", "city": "北京"}
 * 去重结果: 合并为一条记录，{"age": "25", "city": "北京", "name": "张三"}
 *
 * 【示例4: 参数值不同的不会被去重】
 * 记录1: {"name": "张三", "age": 25, "city": "北京"}
 * 记录2: {"name": "李四", "age": 25, "city": "北京"}
 * 去重结果: 保留为两条记录
 *
 * 【示例5: 空值参数会被过滤，过滤后相同的会被去重】
 * 记录1: {"name": "张三", "age": 25, "city": ""}
 * 记录2: {"name": "张三", "age": 25, "city": null}
 * 记录3: {"name": "张三", "age": 25}
 * 去重结果: 合并为一条记录，{"age": "25", "name": "张三"}
 *
 * 【示例6: 无效参数会被过滤，过滤后相同的会被去重】
 * 接口定义的有效参数: ["name", "age", "city"]
 * 记录1: {"name": "张三", "age": 25, "phone": "13812345678"}
 * 记录2: {"name": "张三", "age": 25}
 * 去重结果: 合并为一条记录，{"age": "25", "name": "张三"}
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/04/08
 */
@Component
public class AcInputDistinctHelper {

    private static final Logger log = LoggerFactory.getLogger(AcInputDistinctHelper.class);
    private static final String LOG_PREFIX = "[AcInputDistinctHelper]";

    /**
     * 获取去重后的AC接口DTO列表
     * <p>
     * 主要流程：
     * 1. 首先对记录按照发送时间排序，确保最新记录优先处理
     * 2. 解析每条记录的输入参数，并过滤无效参数
     * 3. 基于参数内容进行去重，相同内容的记录合并为一条
     * 4. 选择每组重复记录的第一条作为代表，并记录被合并记录的信息
     *
     * @param records     前置接口记录列表
     * @param validParams 有效参数集合
     * @return 去重后的AC接口DTO列表
     */
    public List<PopupAcInterfaceDTO> getDistinctAcInputs(List<PopupAcInterfaceDTO> records, Set<String> validParams) {
        if (CollectionUtils.isEmpty(records)) {
            log.info("{} 无需去重，输入记录为空", LOG_PREFIX);
            return Collections.emptyList();
        }

        // 记录原始记录数量
        log.info("{} 开始进行AC Input参数去重, 原始记录数:{}", LOG_PREFIX, records.size());

        // 首先对记录按照sendTime排序，确保最新的记录最后处理
        sortRecordsBySendTime(records);

        // 用于存储去重后的入参，key为规范化后的JSON字符串，value为记录列表和对应的入参
        Map<String, Pair<List<PopupAcInterfaceDTO>, Map<String, String>>> distinctInputsMap = Maps.newHashMap();

        // 执行去重操作
        processDistinct(records, validParams, distinctInputsMap);

        // 转换为最终的结果列表
        List<PopupAcInterfaceDTO> result = buildResultList(distinctInputsMap);

        // 记录去重后的记录数量
        log.info("{} AC Input参数去重完成, 去重后记录数:{}", LOG_PREFIX, result.size());

        return result;
    }

    /**
     * 对记录按照发送时间排序
     * <p>
     * 按照sendTime升序排序，null值排在前面
     *
     * @param records 需要排序的记录列表
     */
    private void sortRecordsBySendTime(List<PopupAcInterfaceDTO> records) {
        records.sort(Comparator.comparing(PopupAcInterfaceDTO::getSendTime,
                Comparator.nullsFirst(Comparator.naturalOrder())));
    }

    /**
     * 执行去重操作
     * <p>
     * 遍历所有记录，解析其输入参数，并进行规范化处理和去重
     *
     * @param records           AC接口记录列表
     * @param validParams       有效参数集合
     * @param distinctInputsMap 存储去重结果的映射
     */
    private void processDistinct(List<PopupAcInterfaceDTO> records, Set<String> validParams,
                                 Map<String, Pair<List<PopupAcInterfaceDTO>, Map<String, String>>> distinctInputsMap) {
        // 遍历所有记录，进行去重
        for (PopupAcInterfaceDTO record : records) {
            String input = record.getInput();
            if (StringUtils.isBlank(input)) {
                continue;
            }

            try {
                // 解析输入参数并过滤无效参数
                Map<String, String> filteredParams = parseAndFilterParams(input, validParams);

                // 如果过滤后的参数为空，则跳过该记录
                if (filteredParams.isEmpty()) {
                    continue;
                }

                // 规范化参数格式并生成JSON字符串作为去重的键
                String normalizedJson = normalizeParams(filteredParams);

                // 根据规范化的JSON字符串进行去重
                mergeOrAddRecord(record, normalizedJson, filteredParams, distinctInputsMap);
            } catch (Exception e) {
                log.warn("{} 解析input字段失败, input:{}", LOG_PREFIX, input, e);
            }
        }
    }

    /**
     * 解析输入参数并过滤无效参数
     * <p>
     * 将输入参数解析为Map，并过滤出有效的非空参数
     *
     * @param input       输入参数JSON字符串
     * @param validParams 有效参数集合
     * @return 过滤后的参数映射
     */
    private Map<String, String> parseAndFilterParams(String input, Set<String> validParams) {
        // 解析input字段
        Map<String, Object> inputMap = ObjectConverter.fromJson(input,
                new TypeReference<Map<String, Object>>() {
                });
        if (inputMap == null) {
            return Collections.emptyMap();
        }

        // 过滤并验证参数
        Map<String, String> filteredMap = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : inputMap.entrySet()) {
            String paramName = entry.getKey();
            Object value = entry.getValue();

            // 跳过空值参数
            boolean isValueStringAndBlank = value instanceof String && StringUtils.isBlank((String) value);
            if (value == null || isValueStringAndBlank) {
                continue;
            }

            // 验证参数是否在接口定义中
            if (validParams.contains(paramName)) {
                // 将所有值转换为字符串
                filteredMap.put(paramName, String.valueOf(value));
            }
        }
        return filteredMap;
    }

    /**
     * 规范化参数格式
     * <p>
     * 使用TreeMap对参数进行排序，确保相同内容的参数生成相同的JSON字符串
     *
     * @param params 参数映射
     * @return 规范化后的JSON字符串
     */
    private String normalizeParams(Map<String, String> params) {
        // 使用TreeMap对参数进行排序，确保去重的一致性
        Map<String, String> sortedMap = new TreeMap<>(params);
        return ObjectConverter.convertToJson(sortedMap);
    }

    /**
     * 根据规范化的JSON字符串合并或添加记录
     * <p>
     * 如果已经存在相同参数的记录，则合并；否则添加新记录
     *
     * @param record            当前处理的记录
     * @param normalizedJson    规范化后的JSON字符串
     * @param filteredParams    过滤后的参数
     * @param distinctInputsMap 存储去重结果的映射
     */
    private void mergeOrAddRecord(PopupAcInterfaceDTO record, String normalizedJson, Map<String, String> filteredParams,
                                  Map<String, Pair<List<PopupAcInterfaceDTO>, Map<String, String>>> distinctInputsMap) {
        // 如果已经存在相同输入参数的记录，则合并记录
        if (distinctInputsMap.containsKey(normalizedJson)) {
            Pair<List<PopupAcInterfaceDTO>, Map<String, String>> existingPair = distinctInputsMap.get(normalizedJson);
            existingPair.getLeft().add(record);

            // 记录被合并的记录信息
            log.info("{} 记录被合并, 保留第一条记录, 被合并记录id:{}, input:{}",
                    LOG_PREFIX, record.getId(), record.getInput());
        } else {
            // 如果不存在相同输入参数的记录，则创建新的记录
            List<PopupAcInterfaceDTO> recordList = new ArrayList<>();
            recordList.add(record);
            distinctInputsMap.put(normalizedJson, Pair.of(recordList, filteredParams));
        }
    }

    /**
     * 构建结果列表
     * <p>
     * 将去重后的记录转换为最终的列表格式，并记录合并信息
     *
     * @param distinctInputsMap 去重后的记录映射
     * @return 最终的结果列表
     */
    private List<PopupAcInterfaceDTO> buildResultList(
            Map<String, Pair<List<PopupAcInterfaceDTO>, Map<String, String>>> distinctInputsMap) {
        List<PopupAcInterfaceDTO> result = new ArrayList<>();

        for (Pair<List<PopupAcInterfaceDTO>, Map<String, String>> pair : distinctInputsMap.values()) {
            // 获取该组记录的第一条记录作为代表
            PopupAcInterfaceDTO representative = pair.getLeft().get(0);

            // 如果有多条记录被合并，记录合并信息
            if (pair.getLeft().size() > 1) {
                String mergedIds = pair.getLeft().stream()
                        .skip(1) // 跳过第一条记录
                        .map(dto -> String.valueOf(dto.getId()))
                        .collect(Collectors.joining(","));
                log.info("{} 多条记录已合并为一条, 保留记录id:{}, 合并记录ids:{}",
                        LOG_PREFIX, representative.getId(), mergedIds);
            }

            // 使用经过规范化的input参数
            representative.setInput(ObjectConverter.convertToJson(pair.getRight()));
            result.add(representative);
        }

        return result;
    }
}