package com.meituan.aigc.aida.labeling.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 分页查询DTO
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PageQueryDTO<T> implements Serializable {

    /**
     * 分页查询总数量
     */
    private Integer totalCount;

    /**
     * 全部数据数量
     */
    private Integer allDataTotalCount;

    /**
     *  质检通过数量
     */
    private Integer qualityCheckedCount;

    /**
     * 质检未通过数量
     */
    private Integer noQualityCheckedCount;

    /**
     * 分页数据
     */
    private List<T> data;
}
