package com.meituan.aigc.aida.labeling.util.labeling.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import com.meituan.aigc.aida.labeling.param.task.LabelingTaskExtraInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class LabelingTaskUtil {

    /**
     * 追加错误信息
     *
     * @param labelingTask 标注任务
     * @param errorMessage 错误信息
     */
    public static void appendExtraInfo(LabelingTask labelingTask, String errorMessage) {
        if (labelingTask == null || StringUtils.isBlank(errorMessage)) {
            return;
        }
        String extraInfo = labelingTask.getExtraInfo();
        LabelingTaskExtraInfo labelingTaskExtraInfo;
        if (StringUtils.isBlank(extraInfo)) {
            labelingTaskExtraInfo = new LabelingTaskExtraInfo();
        } else {
            labelingTaskExtraInfo = JSONObject.parseObject(extraInfo, LabelingTaskExtraInfo.class);
        }
        labelingTaskExtraInfo.setErrorMessage(errorMessage);
        labelingTask.setExtraInfo(JSONObject.toJSONString(labelingTaskExtraInfo));
    }

    public static String filterNullHeader(String rawDataHeaders) {
        if (StringUtils.isNotBlank(rawDataHeaders)) {
            List<String> headers = JSON.parseArray(rawDataHeaders, String.class);
            // 过滤空表头
            if (CollectionUtils.isNotEmpty(headers)) {
                headers = headers.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                rawDataHeaders = JSONObject.toJSONString(headers);
            }
        }
        return rawDataHeaders;
    }

    public static String filterNullHeadValue(String content) {
        if (StringUtils.isNotBlank(content)) {
            JSONObject contentMap = JSON.parseObject(content);
            // 过滤空表头
            if (contentMap != null) {
                Map<String, Object> filterContentMap = contentMap.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                content = JSONObject.toJSONString(filterContentMap);
            }
        }
        return content;
    }
}
