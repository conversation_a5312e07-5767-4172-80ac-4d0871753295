<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.data.management.fetch.dao.mapper.DataFetchRecordMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="contact_id" jdbcType="VARCHAR" property="contactId" />
    <result column="signal_data_content" jdbcType="CHAR" property="signalDataContent" />
    <result column="staff_mis" jdbcType="VARCHAR" property="staffMis" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="message_occurred_time" jdbcType="TIMESTAMP" property="messageOccurredTime" />
    <result column="chat_message_from_type" jdbcType="VARCHAR" property="chatMessageFromType" />
    <result column="contact_type" jdbcType="VARCHAR" property="contactType" />
    <result column="typical_question_ids" jdbcType="VARCHAR" property="typicalQuestionIds" />
    <result column="popup_ac_id" jdbcType="VARCHAR" property="popupAcId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs">
    <result column="staff_message_content" jdbcType="LONGVARCHAR" property="staffMessageContent" />
    <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
    <result column="common_info" jdbcType="LONGVARCHAR" property="commonInfo" />
    <result column="customer_message_content" jdbcType="LONGVARCHAR" property="customerMessageContent" />
    <result column="inputs" jdbcType="LONGVARCHAR" property="inputs" />
    <result column="robot_invoke_info" jdbcType="LONGVARCHAR" property="robotInvokeInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, session_id, contact_id, signal_data_content, staff_mis, message_id,
    message_occurred_time, chat_message_from_type, contact_type, typical_question_ids,
    popup_ac_id, create_time, update_time, is_delete
  </sql>
  <sql id="Blob_Column_List">
    staff_message_content, extra_info, common_info, customer_message_content, inputs,
    robot_invoke_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from data_fetch_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from data_fetch_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from data_fetch_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_fetch_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordExample">
    delete from data_fetch_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs">
    insert into data_fetch_record (id, task_id, session_id,
                                   contact_id, signal_data_content, staff_mis,
                                   message_id, message_occurred_time, chat_message_from_type,
                                   contact_type, typical_question_ids, popup_ac_id,
                                   create_time, update_time, is_delete,
                                   staff_message_content, extra_info,
                                   common_info, customer_message_content,
                                   inputs, robot_invoke_info)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{sessionId,jdbcType=VARCHAR},
            #{contactId,jdbcType=VARCHAR}, #{signalDataContent,jdbcType=CHAR}, #{staffMis,jdbcType=VARCHAR},
            #{messageId,jdbcType=VARCHAR}, #{messageOccurredTime,jdbcType=TIMESTAMP}, #{chatMessageFromType,jdbcType=VARCHAR},
            #{contactType,jdbcType=VARCHAR}, #{typicalQuestionIds,jdbcType=VARCHAR}, #{popupAcId,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BIT},
            #{staffMessageContent,jdbcType=LONGVARCHAR}, #{extraInfo,jdbcType=LONGVARCHAR},
            #{commonInfo,jdbcType=LONGVARCHAR}, #{customerMessageContent,jdbcType=LONGVARCHAR},
            #{inputs,jdbcType=LONGVARCHAR}, #{robotInvokeInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs">
    insert into data_fetch_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="signalDataContent != null">
        signal_data_content,
      </if>
      <if test="staffMis != null">
        staff_mis,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="messageOccurredTime != null">
        message_occurred_time,
      </if>
      <if test="chatMessageFromType != null">
        chat_message_from_type,
      </if>
      <if test="contactType != null">
        contact_type,
      </if>
      <if test="typicalQuestionIds != null">
        typical_question_ids,
      </if>
      <if test="popupAcId != null">
        popup_ac_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="staffMessageContent != null">
        staff_message_content,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="commonInfo != null">
        common_info,
      </if>
      <if test="customerMessageContent != null">
        customer_message_content,
      </if>
      <if test="inputs != null">
        inputs,
      </if>
      <if test="robotInvokeInfo != null">
        robot_invoke_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="contactId != null">
        #{contactId,jdbcType=VARCHAR},
      </if>
      <if test="signalDataContent != null">
        #{signalDataContent,jdbcType=CHAR},
      </if>
      <if test="staffMis != null">
        #{staffMis,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="messageOccurredTime != null">
        #{messageOccurredTime,jdbcType=TIMESTAMP},
      </if>
      <if test="chatMessageFromType != null">
        #{chatMessageFromType,jdbcType=VARCHAR},
      </if>
      <if test="contactType != null">
        #{contactType,jdbcType=VARCHAR},
      </if>
      <if test="typicalQuestionIds != null">
        #{typicalQuestionIds,jdbcType=VARCHAR},
      </if>
      <if test="popupAcId != null">
        #{popupAcId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="staffMessageContent != null">
        #{staffMessageContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="commonInfo != null">
        #{commonInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="customerMessageContent != null">
        #{customerMessageContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="inputs != null">
        #{inputs,jdbcType=LONGVARCHAR},
      </if>
      <if test="robotInvokeInfo != null">
        #{robotInvokeInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordExample" resultType="java.lang.Long">
    select count(*) from data_fetch_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update data_fetch_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.sessionId != null">
        session_id = #{record.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="record.contactId != null">
        contact_id = #{record.contactId,jdbcType=VARCHAR},
      </if>
      <if test="record.signalDataContent != null">
        signal_data_content = #{record.signalDataContent,jdbcType=CHAR},
      </if>
      <if test="record.staffMis != null">
        staff_mis = #{record.staffMis,jdbcType=VARCHAR},
      </if>
      <if test="record.messageId != null">
        message_id = #{record.messageId,jdbcType=VARCHAR},
      </if>
      <if test="record.messageOccurredTime != null">
        message_occurred_time = #{record.messageOccurredTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.chatMessageFromType != null">
        chat_message_from_type = #{record.chatMessageFromType,jdbcType=VARCHAR},
      </if>
      <if test="record.contactType != null">
        contact_type = #{record.contactType,jdbcType=VARCHAR},
      </if>
      <if test="record.typicalQuestionIds != null">
        typical_question_ids = #{record.typicalQuestionIds,jdbcType=VARCHAR},
      </if>
      <if test="record.popupAcId != null">
        popup_ac_id = #{record.popupAcId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BIT},
      </if>
      <if test="record.staffMessageContent != null">
        staff_message_content = #{record.staffMessageContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.commonInfo != null">
        common_info = #{record.commonInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.customerMessageContent != null">
        customer_message_content = #{record.customerMessageContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.inputs != null">
        inputs = #{record.inputs,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.robotInvokeInfo != null">
        robot_invoke_info = #{record.robotInvokeInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update data_fetch_record
    set id = #{record.id,jdbcType=BIGINT},
    task_id = #{record.taskId,jdbcType=BIGINT},
    session_id = #{record.sessionId,jdbcType=VARCHAR},
    contact_id = #{record.contactId,jdbcType=VARCHAR},
    signal_data_content = #{record.signalDataContent,jdbcType=CHAR},
    staff_mis = #{record.staffMis,jdbcType=VARCHAR},
    message_id = #{record.messageId,jdbcType=VARCHAR},
    message_occurred_time = #{record.messageOccurredTime,jdbcType=TIMESTAMP},
    chat_message_from_type = #{record.chatMessageFromType,jdbcType=VARCHAR},
    contact_type = #{record.contactType,jdbcType=VARCHAR},
    typical_question_ids = #{record.typicalQuestionIds,jdbcType=VARCHAR},
    popup_ac_id = #{record.popupAcId,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    is_delete = #{record.isDelete,jdbcType=BIT},
    staff_message_content = #{record.staffMessageContent,jdbcType=LONGVARCHAR},
    extra_info = #{record.extraInfo,jdbcType=LONGVARCHAR},
    common_info = #{record.commonInfo,jdbcType=LONGVARCHAR},
    customer_message_content = #{record.customerMessageContent,jdbcType=LONGVARCHAR},
    inputs = #{record.inputs,jdbcType=LONGVARCHAR},
    robot_invoke_info = #{record.robotInvokeInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update data_fetch_record
    set id = #{record.id,jdbcType=BIGINT},
    task_id = #{record.taskId,jdbcType=BIGINT},
    session_id = #{record.sessionId,jdbcType=VARCHAR},
    contact_id = #{record.contactId,jdbcType=VARCHAR},
    signal_data_content = #{record.signalDataContent,jdbcType=CHAR},
    staff_mis = #{record.staffMis,jdbcType=VARCHAR},
    message_id = #{record.messageId,jdbcType=VARCHAR},
    message_occurred_time = #{record.messageOccurredTime,jdbcType=TIMESTAMP},
    chat_message_from_type = #{record.chatMessageFromType,jdbcType=VARCHAR},
    contact_type = #{record.contactType,jdbcType=VARCHAR},
    typical_question_ids = #{record.typicalQuestionIds,jdbcType=VARCHAR},
    popup_ac_id = #{record.popupAcId,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    is_delete = #{record.isDelete,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs">
    update data_fetch_record
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=VARCHAR},
      </if>
      <if test="signalDataContent != null">
        signal_data_content = #{signalDataContent,jdbcType=CHAR},
      </if>
      <if test="staffMis != null">
        staff_mis = #{staffMis,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="messageOccurredTime != null">
        message_occurred_time = #{messageOccurredTime,jdbcType=TIMESTAMP},
      </if>
      <if test="chatMessageFromType != null">
        chat_message_from_type = #{chatMessageFromType,jdbcType=VARCHAR},
      </if>
      <if test="contactType != null">
        contact_type = #{contactType,jdbcType=VARCHAR},
      </if>
      <if test="typicalQuestionIds != null">
        typical_question_ids = #{typicalQuestionIds,jdbcType=VARCHAR},
      </if>
      <if test="popupAcId != null">
        popup_ac_id = #{popupAcId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="staffMessageContent != null">
        staff_message_content = #{staffMessageContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="commonInfo != null">
        common_info = #{commonInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="customerMessageContent != null">
        customer_message_content = #{customerMessageContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="inputs != null">
        inputs = #{inputs,jdbcType=LONGVARCHAR},
      </if>
      <if test="robotInvokeInfo != null">
        robot_invoke_info = #{robotInvokeInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecordWithBLOBs">
    update data_fetch_record
    set task_id = #{taskId,jdbcType=BIGINT},
        session_id = #{sessionId,jdbcType=VARCHAR},
        contact_id = #{contactId,jdbcType=VARCHAR},
        signal_data_content = #{signalDataContent,jdbcType=CHAR},
        staff_mis = #{staffMis,jdbcType=VARCHAR},
        message_id = #{messageId,jdbcType=VARCHAR},
        message_occurred_time = #{messageOccurredTime,jdbcType=TIMESTAMP},
        chat_message_from_type = #{chatMessageFromType,jdbcType=VARCHAR},
        contact_type = #{contactType,jdbcType=VARCHAR},
        typical_question_ids = #{typicalQuestionIds,jdbcType=VARCHAR},
        popup_ac_id = #{popupAcId,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=BIT},
        staff_message_content = #{staffMessageContent,jdbcType=LONGVARCHAR},
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
        common_info = #{commonInfo,jdbcType=LONGVARCHAR},
        customer_message_content = #{customerMessageContent,jdbcType=LONGVARCHAR},
        inputs = #{inputs,jdbcType=LONGVARCHAR},
        robot_invoke_info = #{robotInvokeInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchRecord">
    update data_fetch_record
    set task_id = #{taskId,jdbcType=BIGINT},
        session_id = #{sessionId,jdbcType=VARCHAR},
        contact_id = #{contactId,jdbcType=VARCHAR},
        signal_data_content = #{signalDataContent,jdbcType=CHAR},
        staff_mis = #{staffMis,jdbcType=VARCHAR},
        message_id = #{messageId,jdbcType=VARCHAR},
        message_occurred_time = #{messageOccurredTime,jdbcType=TIMESTAMP},
        chat_message_from_type = #{chatMessageFromType,jdbcType=VARCHAR},
        contact_type = #{contactType,jdbcType=VARCHAR},
        typical_question_ids = #{typicalQuestionIds,jdbcType=VARCHAR},
        popup_ac_id = #{popupAcId,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsertDataFetchRecord" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO data_fetch_record (
    task_id,
    session_id,
    contact_id,
    signal_data_content,
    staff_mis,
    staff_message_content,
    message_id,
    extra_info,
    message_occurred_time,
    common_info,
    customer_message_content,
    chat_message_from_type,
    contact_type,
    typical_question_ids,
    popup_ac_id,
    inputs,
    create_time,
    update_time,
    robot_invoke_info
    ) VALUES
    <foreach collection="dataFetchRecordList" item="item" separator=",">
      (
      #{item.taskId},
      #{item.sessionId},
      #{item.contactId},
      #{item.signalDataContent},
      #{item.staffMis},
      #{item.staffMessageContent},
      #{item.messageId},
      #{item.extraInfo},
      #{item.messageOccurredTime},
      #{item.commonInfo},
      #{item.customerMessageContent},
      #{item.chatMessageFromType},
      #{item.contactType},
      #{item.typicalQuestionIds},
      #{item.popupAcId},
      #{item.inputs},
      #{item.createTime},
      #{item.updateTime},
      #{item.robotInvokeInfo}
      )
    </foreach>
  </insert>
  <select id="countByTaskId" resultType="java.lang.Integer">
    SELECT COUNT(id) FROM data_fetch_record WHERE task_id = #{taskId}
  </select>
  <select id="pageByTaskId" resultMap="ResultMapWithBLOBs">
    SELECT
    <include refid="Base_Column_List" />
    ,staff_message_content, extra_info, common_info, customer_message_content, inputs
    FROM data_fetch_record
    WHERE
      task_id = #{taskId}
    LIMIT #{offset},#{pageSize}
  </select>
  <select id="listByTaskIdAndLikeByInputs" resultMap="ResultMapWithBLOBs">
    SELECT
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    FROM data_fetch_record
    <where>
      <if test="taskId != null">
        task_id = #{taskId}
      </if>
      <if test="inputs != null">
        AND inputs LIKE CONCAT('%', #{inputs}, '%')
      </if>
    </where>
  </select>

</mapper>