package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.InspectionItem;
import com.meituan.aigc.aida.labeling.dao.model.InspectionItemExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InspectionItemMapper {
    long countByExample(InspectionItemExample example);

    int deleteByExample(InspectionItemExample example);

    int deleteByPrimaryKey(Long id);

    int insert(InspectionItem record);

    int insertSelective(InspectionItem record);

    List<InspectionItem> selectByExample(InspectionItemExample example);

    InspectionItem selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") InspectionItem record, @Param("example") InspectionItemExample example);

    int updateByExample(@Param("record") InspectionItem record, @Param("example") InspectionItemExample example);

    int updateByPrimaryKeySelective(InspectionItem record);

    int updateByPrimaryKey(InspectionItem record);

    List<InspectionItem> listByTemplateId(Long templateId);

    void batchUpdateItems(@Param("items") List<InspectionItem> items);
}