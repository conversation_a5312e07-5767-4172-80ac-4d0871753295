package com.meituan.aigc.aida.labeling.dao.repository.impl;

import com.meituan.aigc.aida.labeling.dao.mapper.PersonLabelingStatisticsMapper;
import com.meituan.aigc.aida.labeling.dao.model.PersonLabelingStatistics;
import com.meituan.aigc.aida.labeling.dao.repository.PersonLabelingStatisticsRepository;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DataStatisticalQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonnelDetailQuery;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonLabelingStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingAndCheckCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonLabelingQueryVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DailyStatisticsDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DailyBizStatisticsVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/5/19 14:57
 * @Version: 1.0
 */
@Repository
public class PersonLabelingStatisticsRepositoryImpl implements PersonLabelingStatisticsRepository {

    @Resource
    private PersonLabelingStatisticsMapper personLabelingStatisticsMapper;

    @Override
    public List<PersonLabelingQueryVO> queryPersonLabelingStats(PersonLabelingStatisticsDTO param) {
        return personLabelingStatisticsMapper.queryPersonLabelingStats(param);
    }

    @Override
    public List<DailyBizStatisticsVO> queryDailyTrends(DailyStatisticsDTO param) {
        return personLabelingStatisticsMapper.queryDailyTrends(param);
    }
    @Override
    public LabelingAndCheckCountVO statisticalLabelingData(DataStatisticalQuery query) {
        return personLabelingStatisticsMapper.statisticalLabelingData(query);
    }

    @Override
    public List<PersonnelDetailVO> queryPersonnelDetail(PersonnelDetailQuery query) {
        return personLabelingStatisticsMapper.queryPersonnelDetail(query);
    }
    @Override
    public void batchInsert(List<PersonLabelingStatistics> personLabelingStatisticsList) {
        if (CollectionUtils.isEmpty(personLabelingStatisticsList)) {
            return;
        }
        personLabelingStatisticsMapper.batchInsert(personLabelingStatisticsList);
    }

}
