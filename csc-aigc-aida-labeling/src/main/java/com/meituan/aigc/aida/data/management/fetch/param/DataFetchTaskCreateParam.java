package com.meituan.aigc.aida.data.management.fetch.param;

import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskCreateFilterConditionsDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskCreateOutputFieldDTO;
import lombok.Data;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/8 10:12
 * @Version: 1.0
 */
@Data
public class DataFetchTaskCreateParam {

    /**
     * 任务名称
     */
    private String name;
    /**
     * 任务描述
     */
    private String description;
    /**
     * 数据类型
     */
    private Integer dataType;
    /**
     * 筛选条件
     */
    private DataFetchTaskCreateFilterConditionsDTO filterConditions;
    /**
     * 返回结果配置
     */
    private List<DataFetchTaskCreateOutputFieldDTO> outputFields;

}
