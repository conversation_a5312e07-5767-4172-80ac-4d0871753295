package com.meituan.aigc.aida.labeling.param.task;

import com.meituan.aigc.aida.labeling.param.LabelingDistributionGroupParam;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LabelingDistributionParam implements Serializable {
    /**
     * 分配方式 1:session维度分配 2:query维度分配
     */
    private Integer assignType;

    /**
     * 分组配置
     */
    private List<LabelingDistributionGroupParam> groupConfigList;
}
