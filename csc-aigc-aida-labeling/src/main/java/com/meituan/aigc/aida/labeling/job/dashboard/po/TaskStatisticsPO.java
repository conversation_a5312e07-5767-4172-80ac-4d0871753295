package com.meituan.aigc.aida.labeling.job.dashboard.po;

import lombok.Data;

/**
 * @Author: guo<PERSON><PERSON>
 * @Create: 2025/5/21 17:11
 * @Version: 1.0
 */
@Data
public class TaskStatisticsPO {

    /**
     * 业务类型
     */
    private Long bizType;
    /**
     * 数据类型
     */
    private Integer dataType;
    /**
     * 任务类型
     */
    private Integer taskType;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 标注状态
     */
    private Integer labelingStatus;
    /**
     * 质检状态
     */
    private Integer qualityCheckStatus;
    /**
     * 总样本量
     */
    private Integer totalSampleCount;

}
