package com.meituan.aigc.aida.data.management.fetch.service.impl;

import com.meituan.aigc.aida.data.management.fetch.dao.model.PacificAcInterfaceLogWithBlobs;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.PacificAcInterfaceLogRepository;
import com.meituan.aigc.aida.data.management.fetch.service.PacificAcInterfaceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/4/9 20:48
 * @Version: 1.0
 */
@Slf4j
@Service
public class PacificAcInterfaceLogServiceImpl implements PacificAcInterfaceLogService {


    @Resource
    private PacificAcInterfaceLogRepository pacificAcInterfaceLogRepository;


    @Override
    public List<PacificAcInterfaceLogWithBlobs> listByContactIdContactTypeAndStaffMis(String contactId, String contactType, String staffMis) {
        return pacificAcInterfaceLogRepository.listByContactIdContactTypeAndStaffMis(contactId, contactType, staffMis);
    }
}
