package com.meituan.aigc.aida.data.management.fetch.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-08 10:52
 * @description 数据拉取任务
 */
@Data
public class DataFetchTaskDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 任务名称
     */
    private String name;
    /**
     * 任务描述
     */
    private String description;
    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 数据量
     */
    private Integer dataCount;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 创建人mis
     */
    private String createMis;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 筛选条件
     */
    private String filterConditions;
    /**
     * 返回结果配置
     */
    private String outputFields;
}
