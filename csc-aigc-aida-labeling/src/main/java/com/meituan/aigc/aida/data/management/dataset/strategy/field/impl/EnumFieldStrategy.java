package com.meituan.aigc.aida.data.management.dataset.strategy.field.impl;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 枚举字段处理策略
 */
@Component
public class EnumFieldStrategy extends AbstractDataFieldStrategy {

    @Override
    public Integer getStrategyType() {
        return FieldTypeEnum.ENUM.getCode();
    }

    @Override
    public void validateValue(String value) {
        // 枚举：不做特殊校验
    }

    @Override
    public void processFieldData(String columnName, String value,
                                Map<String, Object> textData,
                                Map<String, Object> flattenedData,
                                Map<String, Date> dateData,
                                Map<String, Object> nestedData) {
        // 枚举：不做加工，直接存储
        textData.put(columnName, value);
    }
} 