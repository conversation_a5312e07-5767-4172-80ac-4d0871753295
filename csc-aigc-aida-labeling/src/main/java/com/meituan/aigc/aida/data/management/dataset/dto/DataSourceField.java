package com.meituan.aigc.aida.data.management.dataset.dto;

import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-23 14:50
 * @description
 */
@Data
public class DataSourceField implements Serializable {

    /**
     * 字段名称
     */
    private String fieldName;
    /**
     * 字段类型code
     * * {@link FieldTypeEnum}
     */
    private Integer fieldType;

    /**
     * 字段类型名称
     */
    private String fieldTypeName;

    /**
     * 枚举值列表
     */
    private List<String> enumList;
}
