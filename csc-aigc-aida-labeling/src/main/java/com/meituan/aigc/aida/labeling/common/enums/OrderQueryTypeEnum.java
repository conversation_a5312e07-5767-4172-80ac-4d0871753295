package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * @Author: yangyi<PERSON>i
 * @Create: 2025/05/19 10:56
 * @Version: 1.0
 */

@Getter
public enum OrderQueryTypeEnum {
    TOP_TEN(1, "top10"),
    BOTTOM_TEN(2, "bottom10");

    private final Integer code;
    private final String value;
    
    OrderQueryTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public String toString() {
        return "OrderQueryTypeEnum{" +
                "code=" + code +
                ", value='" + value + '\'' +
                '}';
    }

    // 根据code获取value
    public static String getValueByCode(Integer code) {
        for (OrderQueryTypeEnum queryType : OrderQueryTypeEnum.values()) {
            if (queryType.getCode() == (code)) {
                return queryType.getValue();
            }
        }
        return null;
    }
    
}
