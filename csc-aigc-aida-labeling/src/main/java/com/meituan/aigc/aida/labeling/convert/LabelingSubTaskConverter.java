package com.meituan.aigc.aida.labeling.convert;

import com.meituan.aigc.aida.labeling.dao.model.LabelingSubTask;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * @Author: guowenhui
 * @Create: 2025/6/10 19:27
 * @Version: 1.0
 */
@Mapper
public interface LabelingSubTaskConverter {

    LabelingSubTaskConverter INSTANCE = Mappers.getMapper(LabelingSubTaskConverter.class);

    @Mappings(value = {
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "updateTime", expression = "java(com.meituan.aigc.aida.labeling.util.DateUtil.getCurrentTimePlus30SecondsAsDate())")
    })
    LabelingSubTask converterToLabelingSubTask(LabelingSubTask labelingSubTask);

}
