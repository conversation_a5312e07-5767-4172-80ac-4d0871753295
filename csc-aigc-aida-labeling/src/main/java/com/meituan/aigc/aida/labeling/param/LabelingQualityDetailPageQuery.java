package com.meituan.aigc.aida.labeling.param;

import com.meituan.aigc.aida.labeling.common.enums.QualityCheckDataStatus;
import lombok.Data;

/**
 * @Author: guowenhui
 * @Create: 2025/3/28 16:07
 * @Version: 1.0
 */
@Data
public class LabelingQualityDetailPageQuery {

    /**
     * 质检任务ID
     */
    private Long qualityCheckTaskId;
    /**
     * 可检状态 1 可检 2 不可检
     */
    private Integer checkableStatus;
    /**
     * 质检状态{@link QualityCheckDataStatus}
     */
    private Integer qualityStatus;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 数据ID
     */
    private Long dataId;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页记录数
     */
    private Integer pageSize;

}
