package com.meituan.aigc.aida.labeling.strategy.transfer.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.aigc.aida.labeling.common.enums.*;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.dao.model.*;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingDetailRepository;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingSubTaskRepository;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskGroupRepository;
import com.meituan.aigc.aida.labeling.dao.repository.LabelingTaskRepository;
import com.meituan.aigc.aida.labeling.helper.TaskTransferHelper;
import com.meituan.aigc.aida.labeling.param.TaskTransferParam;
import com.meituan.aigc.aida.labeling.pojo.dto.TransferInfoDTO;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.strategy.transfer.TransferStrategy;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 标注任务转交
 *
 * <AUTHOR>
 */
@Service
public class LabelingTransfer implements TransferStrategy {

    @Autowired
    private LabelingTaskRepository labelingTaskRepository;

    @Autowired
    private LabelingSubTaskRepository labelingSubTaskRepository;

    @Autowired
    private LabelingDetailRepository labelingDetailRepository;

    @Autowired
    private LabelingTaskGroupRepository labelingTaskGroupRepository;

    @Autowired
    private PushElephantService pushElephantService;

    @Autowired
    private LionConfig lionConfig;

    /**
     * 转交任务发送大象消息格式
     */
    private static final String TRANSFER_TASK_MESSAGE_FORMAT = "任务「%s」转交已完成，请及时前往[训练系统|%s]查看。";

    /**
     * 标注任务转交逻辑：
     * 1、找到没有标注的数据，session维度按会话统计，query维度按原始数据统计，随机分配给转让人
     * 2、给转让人创建新分组，和原分组相比，不一样的数据包括分组状态、创建时间、数据量
     * 3、给转让人创建新任务，和原任务相比，不一样的数据包括任务状态、创建时间、数据量
     * 4、找到原分组中所有背标人的数据，创建新的标注任务，关联在新分组上，不一样的数据包括任务状态、创建时间、数据量，其中任务状态需要根据拆分出的数据完成情况重新检测
     * 5、处理原任务，分三种情况，1）原任务数据全部被转让，删除原任务，删除原任务对应的所有背标任务、删除原分组 2）原任务可转让数据全部转让，还剩自己之前标注过的，更新任务状态为标注完成，更新剩余数据量，检测所有背标任务在转让后是否已完成，更新状态，检测是否更新分组状态 3）可转让数据还未完全转让，更新数据量，找到所有背标任务，更新数据量
     */
    @Override
    public void transfer(TaskTransferParam param) {
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(param.getSubTaskId());
        CheckUtil.paramCheck(labelingSubTask != null, "标注子任务不存在");
        int maxTransferNum = getMaxTransferNum(param.getSubTaskId(), labelingSubTask.getAssignType());
        int transferNum = (int) param.getTransferConfig().stream().mapToLong(TaskTransferParam.TransferConfig::getTransferNum).sum();
        CheckUtil.paramCheck(transferNum <= maxTransferNum, "可转交数量超过最大可转交数量");
        List<LabelingDetail> labelingDetailList = labelingDetailRepository.listDataBySubTaskIdAndStatus(param.getSubTaskId(), LabelingDetailStatus.WAITING_LABELING.getCode());
        CheckUtil.operationCheck(CollectionUtils.isNotEmpty(labelingDetailList), "标注任务数据发生变化，请刷新页面后重新分配");
        LabelingTask labelingTask = labelingTaskRepository.getById(labelingSubTask.getTaskId());
        // 将可转交任务按照分配类型做聚合
        List<String> canTransferIdList = getTransferIdList(labelingSubTask, labelingDetailList);
        // 随机转交任务
        Map<String, List<String>> transferMisMap = TaskTransferHelper.transferTask(canTransferIdList, param.getTransferConfig());
        Map<String, TaskTransferParam.TransferConfig> transferMisConfigMap = param.getTransferConfig().stream().collect(Collectors.toMap(TaskTransferParam.TransferConfig::getTransferMis, Function.identity()));
        Date now = new Date();
        Map<String, List<LabelingDetail>> labelingDetailMap = getLabelingDetailMap(labelingSubTask, labelingDetailList);
        // 找到原分组的所有背标任务
        LabelingTaskGroup labelingTaskGroup = labelingTaskGroupRepository.getLabelGroupById(labelingSubTask.getGroupId());
        List<LabelingSubTask> otherLabelingSubTaskList = labelingSubTaskRepository.listByTaskIdAndGroupId(labelingSubTask.getTaskId(), labelingSubTask.getGroupId());
        // 过滤当前子任务
        otherLabelingSubTaskList = otherLabelingSubTaskList.stream().filter(subTask -> !subTask.getId().equals(labelingSubTask.getId())).collect(Collectors.toList());
        Map<Long, List<LabelingDetail>> otherLabelingDetailMap = getotherLabelingDetailMap(otherLabelingSubTaskList);
        List<Long> rawDataIdList = labelingDetailList.stream().map(LabelingDetail::getRawDataId).collect(Collectors.toList());
        List<LabelingSubTask> allLabelingSubTaskList = labelingSubTaskRepository.listAllByTaskId(labelingSubTask.getTaskId());
        Map<Long, List<LabelingSubTask>> groupLabelingTaskMap = allLabelingSubTaskList.stream().collect(Collectors.groupingBy(LabelingSubTask::getGroupId));
        Map<String, List<LabelingSubTask>> misLabelingTaskMap = allLabelingSubTaskList.stream().collect(Collectors.groupingBy(LabelingSubTask::getLabelerMis));

        // 处理转交的数据
        handleTransferTask(labelingTask, labelingSubTask, labelingDetailList, transferMisMap, transferMisConfigMap, now, labelingDetailMap, labelingTaskGroup, otherLabelingSubTaskList, otherLabelingDetailMap, groupLabelingTaskMap, misLabelingTaskMap);

        // 处理转交后原任务的数据
        List<String> transferIdList = transferMisMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        handleOldTask(labelingSubTask, maxTransferNum, transferNum, now, labelingTaskGroup, otherLabelingSubTaskList, otherLabelingDetailMap, rawDataIdList, labelingDetailList, transferIdList);

        // 发送大象消息
        pushElephant(labelingSubTask, transferMisMap);
    }

    @Override
    public Long getLabelingTaskId(Long subTaskId) {
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(subTaskId);
        CheckUtil.paramCheck(labelingSubTask != null, "标注子任务不存在");
        return labelingSubTask.getTaskId();
    }

    @Override
    public TransferInfoDTO getTransferInfo(Long taskId, Integer transferType) {
        // 标注任务转交统计标注任务的背标人和最大转交数量
        TransferInfoDTO transferInfo = new TransferInfoDTO();
        // 找到当前子任务所在分组的其他所有任务
        LabelingSubTask labelingSubTask = labelingSubTaskRepository.getById(taskId);
        CheckUtil.operationCheck(labelingSubTask != null, "子任务不存在");
        List<LabelingSubTask> allGroupSubTask = labelingSubTaskRepository.listByGroupId(labelingSubTask.getGroupId());
        List<LabelingSubTask> otherGroupSubTaskId = allGroupSubTask.stream().filter(subTask -> !subTask.getId().equals(taskId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(otherGroupSubTaskId)) {
            List<String> misList = otherGroupSubTaskId.stream().map(LabelingSubTask::getLabelerMis).distinct().collect(Collectors.toList());
            transferInfo.setCompareMisList(misList);
        }
        int maxTransferNum = getMaxTransferNum(taskId, labelingSubTask.getAssignType());
        transferInfo.setMaxTransferNum(maxTransferNum);
        return transferInfo;
    }

    @Override
    public Integer getStrategyCode() {
        return TransferTypeEnum.LABELING.getCode();
    }

    /**
     * 发送大象消息
     *
     * @param labelingSubTask 标注子任务
     * @param transferMisMap  转交人mis
     */
    private void pushElephant(LabelingSubTask labelingSubTask, Map<String, List<String>> transferMisMap) {
        List<String> misList = new ArrayList<>(transferMisMap.keySet());
        User user = UserUtils.getUser();
        if (user != null) {
            misList.add(user.getLogin());
        }
        String messageId = String.format(TRANSFER_TASK_MESSAGE_FORMAT, labelingSubTask.getName(), lionConfig.getSubTaskUrl());
        pushElephantService.pushElephant(messageId, misList);
    }

    private int getMaxTransferNum(Long taskId, Integer assignType) {
        // session维度分配的标注任务需要按照会话数量统计
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), assignType)) {
            return labelingDetailRepository.countSessionBySubTaskIdAndStatus(taskId, LabelingDetailStatus.WAITING_LABELING.getCode());
        } else if (Objects.equals(AssignTypeEnum.QUERY.getCode(), assignType)) {
            // query维度分配的标注任务需要按照query数量统计
            return labelingDetailRepository.countQueryBySubTaskIdAndStatus(taskId, LabelingDetailStatus.WAITING_LABELING.getCode());
        }
        return 0;
    }

    /**
     * 找到可以合并转交任务的历史任务
     * 可以合并的条件为 1、不是背标任务 2、转交的mis有历史任务且也不是背标任务
     *
     * @param mis                  转交的mis
     * @param misLabelingTaskMap   mis和标注任务映射
     * @param groupLabelingTaskMap 分组和标注任务映射
     * @return 是否可以合并任务
     */
    private LabelingSubTask findMergeTask(String mis, Map<String, List<LabelingSubTask>> misLabelingTaskMap, Map<Long, List<LabelingSubTask>> groupLabelingTaskMap, List<LabelingSubTask> otherLabelingSubTaskList) {
        // 转交的mis没有历史任务，不能合并
        if (!misLabelingTaskMap.containsKey(mis)) {
            return null;
        }
        // 当前任务为背标任务，不能合并
        if (CollectionUtils.isNotEmpty(otherLabelingSubTaskList)) {
            return null;
        }
        // 找到转交人的所有任务
        List<LabelingSubTask> transferMisLabelingSubTaskList = misLabelingTaskMap.get(mis);
        // 寻找不是背标的任务，如果没有，不能合并
        return transferMisLabelingSubTaskList.stream().filter(subTask -> groupLabelingTaskMap.get(subTask.getGroupId()).size() == 1).findFirst().orElse(null);
    }

    /**
     * 获取标注详情映射
     *
     * @param labelingSubTask    标注子任务
     * @param labelingDetailList 标注详情列表
     * @return 标注详情映射
     */
    private Map<String, List<LabelingDetail>> getLabelingDetailMap(LabelingSubTask labelingSubTask, List<LabelingDetail> labelingDetailList) {
        Map<String, List<LabelingDetail>> labelingDetailMap;
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), labelingSubTask.getAssignType())) {
            labelingDetailMap = labelingDetailList.stream().collect(Collectors.groupingBy(LabelingDetail::getSessionId));
        } else {
            labelingDetailMap = labelingDetailList.stream().collect(Collectors.groupingBy(item -> String.valueOf(item.getRawDataId())));
        }
        return labelingDetailMap;
    }

    /**
     * 获取转移ID列表
     *
     * @param labelingSubTask    标注子任务
     * @param labelingDetailList 标注详情列表
     * @return 转移ID列表
     */
    private List<String> getTransferIdList(LabelingSubTask labelingSubTask, List<LabelingDetail> labelingDetailList) {
        // session维度要根据会话来计算数量
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), labelingSubTask.getAssignType())) {
            return labelingDetailList.stream().map(LabelingDetail::getSessionId).distinct().collect(Collectors.toList());
        }
        // query维度要根据原数据来计算数量
        return labelingDetailList.stream().map(LabelingDetail -> String.valueOf(LabelingDetail.getRawDataId())).distinct().collect(Collectors.toList());
    }

    /**
     * 处理转交任务
     *
     * @param labelingSubTask          当前任务
     * @param labelingDetailList       当前任务数据
     * @param transferMisMap           转交人信息
     * @param transferMisConfigMap     转交配置
     * @param now                      当前时间
     * @param labelingDetailMap        当前任务数据Map
     * @param labelingTaskGroup        当前任务分组
     * @param otherLabelingSubTaskList 当前任务其他分组任务
     * @param otherLabelingDetailMap   当前任务其他分组任务数据
     * @param groupLabelingTaskMap     分组任务Map
     * @param misLabelingTaskMap       mis任务Map
     */
    private void handleTransferTask(LabelingTask labelingTask, LabelingSubTask labelingSubTask, List<LabelingDetail> labelingDetailList, Map<String, List<String>> transferMisMap, Map<String, TaskTransferParam.TransferConfig> transferMisConfigMap, Date now, Map<String, List<LabelingDetail>> labelingDetailMap, LabelingTaskGroup labelingTaskGroup, List<LabelingSubTask> otherLabelingSubTaskList, Map<Long, List<LabelingDetail>> otherLabelingDetailMap, Map<Long, List<LabelingSubTask>> groupLabelingTaskMap, Map<String, List<LabelingSubTask>> misLabelingTaskMap) {
        for (Map.Entry<String, List<String>> entry : transferMisMap.entrySet()) {
            String mis = entry.getKey();
            List<String> curMisTransferIdList = entry.getValue();
            // 找到所有分配出去的记录的原始数据ID，目的是为了和背标任务做关联
            List<Long> curMisTransferRawDataIdList = getCurMisTransferRawDataIdList(labelingSubTask, labelingDetailList, curMisTransferIdList);

            // 找到可以合并转交任务的历史任务
            // 可以合并的条件为 1、不是背标任务 2、转交的mis有历史任务且也不是背标任务
            LabelingSubTask newLabelingSubTask = findMergeTask(mis, misLabelingTaskMap, groupLabelingTaskMap, otherLabelingSubTaskList);
            if (newLabelingSubTask != null) {
                // 如果有可以合并的历史任务，执行合并操作
                // 1、更新任务的状态、数据量、更新时间 2、更新分组的状态，因为合并未完成的记录后，一定是未完成状态
                mergeTask(newLabelingSubTask, now, curMisTransferIdList);
            } else {
                // 如果没有可以合并的任务，创建新的分组和任务
                newLabelingSubTask = createNewGroupAndSubTask(labelingTask, labelingSubTask, transferMisConfigMap, now, labelingTaskGroup, otherLabelingSubTaskList, otherLabelingDetailMap, mis, curMisTransferIdList, curMisTransferRawDataIdList);
            }

            // 分配数据，更新转交任务详情
            updateTransferLabelDetail(transferMisConfigMap, labelingDetailMap, mis, curMisTransferIdList, newLabelingSubTask);

            // 转让完成后更新标注任务的状态为标注中
            if (newLabelingSubTask.getStatus() == LabelingSubTaskStatus.CREATING.getCode()) {
                newLabelingSubTask.setStatus(LabelingSubTaskStatus.IN_LABELING.getCode());
                labelingSubTaskRepository.updateById(newLabelingSubTask);
            }
        }
    }

    /**
     * 处理原任务
     *
     * @param labelingSubTask          原任务对象
     * @param maxTransferNum           最大可转交数量
     * @param transferNum              实际转交数量
     * @param now                      当前时间
     * @param labelingTaskGroup        任务分组对象
     * @param otherLabelingSubTaskList 其他任务列表
     * @param otherLabelingDetailMap   其他任务详情映射表
     * @param rawDataIdList            原始数据ID列表
     */
    private void handleOldTask(LabelingSubTask labelingSubTask, int maxTransferNum, int transferNum, Date now, LabelingTaskGroup labelingTaskGroup, List<LabelingSubTask> otherLabelingSubTaskList, Map<Long, List<LabelingDetail>> otherLabelingDetailMap, List<Long> rawDataIdList, List<LabelingDetail> labelingDetailList, List<String> transferIdList) {
        // 找到已标注的数据
        int restDataNum = getRestDataNum(labelingSubTask);

        // 判断是否所有标注任务都已完成
        boolean isAllLabelingTaskCompleted = true;

        // 完成后还需要处理拆分后剩余的标注任务
        // 处理当前任务
        int status = transferNum == maxTransferNum ? LabelingSubTaskStatus.WAITING_RECYCLED.getCode() : LabelingSubTaskStatus.IN_LABELING.getCode();
        if (status != LabelingSubTaskStatus.WAITING_RECYCLED.getCode()) {
            isAllLabelingTaskCompleted = false;
        }
        updateRestLabelingTask(labelingSubTask, maxTransferNum, transferNum, now, restDataNum, status);

        // 处理背标任务
        List<Long> transferRawDataIdList = getCurMisTransferRawDataIdList(labelingSubTask, labelingDetailList, transferIdList);
        if (CollectionUtils.isNotEmpty(otherLabelingSubTaskList)) {
            for (LabelingSubTask otherLabelingSubTask : otherLabelingSubTaskList) {
                List<LabelingDetail> otherLabelingDetailList = otherLabelingDetailMap.get(otherLabelingSubTask.getId());
                int oldTaskStatus = computeOldLabelingSubTaskStatus(otherLabelingDetailList, transferRawDataIdList);
                if (oldTaskStatus != LabelingSubTaskStatus.WAITING_RECYCLED.getCode()) {
                    isAllLabelingTaskCompleted = false;
                }
                updateRestLabelingTask(otherLabelingSubTask, maxTransferNum, transferNum, now, restDataNum, oldTaskStatus);
            }
        }

        // 处理旧分组
        // 如果分组下已经没有数据，删除分组
        if (restDataNum == 0 && transferNum == maxTransferNum) {
            labelingTaskGroup.setIsDeleted(true);
            labelingTaskGroupRepository.updateGroup(labelingTaskGroup);
        } else if (isAllLabelingTaskCompleted) {
            // 如果历史分组的所有任务都更新为了标注完成，需要把分组状态也更新
            labelingTaskGroup.setTotalDataCount(maxTransferNum - transferNum + restDataNum);
            labelingTaskGroup.setRecycleStatus(LabelingTaskGroupStatus.PENDING_RECYCLE.getCode());
            labelingTaskGroup.setUpdateTime(now);
            labelingTaskGroupRepository.updateGroup(labelingTaskGroup);
        } else {
            // 只更新数据量
            labelingTaskGroup.setTotalDataCount(maxTransferNum - transferNum + restDataNum);
            labelingTaskGroupRepository.updateGroup(labelingTaskGroup);
        }
    }

    /**
     * 更新转交任务详情
     *
     * @param transferMisConfigMap 转交配置映射表
     * @param labelingDetailMap    标注详情映射表
     * @param mis                  转交人mis
     * @param curMisTransferIdList 当前转交人ID列表
     * @param newLabelingSubTask   新的标注任务
     */
    private void updateTransferLabelDetail(Map<String, TaskTransferParam.TransferConfig> transferMisConfigMap, Map<String, List<LabelingDetail>> labelingDetailMap, String mis, List<String> curMisTransferIdList, LabelingSubTask newLabelingSubTask) {
        List<LabelingDetail> curMisLabelingDetail = new ArrayList<>();
        for (String transferId : curMisTransferIdList) {
            curMisLabelingDetail.addAll(labelingDetailMap.get(transferId));
        }
        // 更新标注人和关联的标注任务
        Long newLabelingSubTaskId = newLabelingSubTask.getId();
        curMisLabelingDetail.forEach(item -> {
            item.setLabelerMis(mis);
            item.setLabelerName(transferMisConfigMap.get(mis).getTransferName());
            item.setSubTaskId(newLabelingSubTaskId);
        });

        // 插入数据库
        labelingDetailRepository.batchUpdateLabelingDetail(curMisLabelingDetail);
    }

    /**
     * 合并任务
     *
     * @param newLabelingSubTask   新的标注任务
     * @param now                  当前时间
     * @param curMisTransferIdList 当前转交人ID列表
     */
    private void mergeTask(LabelingSubTask newLabelingSubTask, Date now, List<String> curMisTransferIdList) {
        // 1、更新任务的状态、数据量、更新时间 2、更新分组的状态，因为合并未完成的记录后，一定是未完成状态
        newLabelingSubTask.setUpdateTime(now);
        newLabelingSubTask.setStatus(LabelingSubTaskStatus.CREATING.getCode());
        newLabelingSubTask.setTotalDataCount(newLabelingSubTask.getTotalDataCount() + curMisTransferIdList.size());
        labelingSubTaskRepository.updateById(newLabelingSubTask);
        // 2、更新分组的状态，因为合并未完成的记录后，一定是未完成状态
        LabelingTaskGroup labelingTaskGroup = labelingTaskGroupRepository.getLabelGroupById(newLabelingSubTask.getGroupId());
        labelingTaskGroup.setRecycleStatus(LabelingTaskGroupStatus.CREATING.getCode());
        labelingTaskGroup.setTotalDataCount(labelingTaskGroup.getTotalDataCount() + curMisTransferIdList.size());
        labelingTaskGroup.setRecycleDataCount(0);
        labelingTaskGroupRepository.updateGroup(labelingTaskGroup);
    }

    /**
     * 创建新的分组和任务
     *
     * @param labelingSubTask             当前任务
     * @param transferMisConfigMap        转交配置
     * @param now                         当前时间
     * @param labelingTaskGroup           当前分组
     * @param otherLabelingSubTaskList    其他任务
     * @param otherLabelingDetailMap      其他任务详情
     * @param mis                         当前转交人
     * @param curMisTransferIdList        当前转交人ID
     * @param curMisTransferRawDataIdList 当前转交人原始数据ID
     * @return 新的任务
     */
    private LabelingSubTask createNewGroupAndSubTask(LabelingTask labelingTask, LabelingSubTask labelingSubTask, Map<String, TaskTransferParam.TransferConfig> transferMisConfigMap, Date now, LabelingTaskGroup labelingTaskGroup, List<LabelingSubTask> otherLabelingSubTaskList, Map<Long, List<LabelingDetail>> otherLabelingDetailMap, String mis, List<String> curMisTransferIdList, List<Long> curMisTransferRawDataIdList) {
        LabelingSubTask newLabelingSubTask;
        // 创建新的分组
        LabelingTaskGroup newLabelingGroup = buildNewLabelingGroup(labelingTaskGroup, otherLabelingSubTaskList, now, transferMisConfigMap.get(mis).getTransferName(), curMisTransferIdList);
        labelingTaskGroupRepository.insert(newLabelingGroup);

        // 在新分组下创建新的标注任务
        newLabelingSubTask = buildNewLabelingSubTask(labelingTask, labelingSubTask, newLabelingGroup, transferMisConfigMap.get(mis).getTransferName(), now, mis, LabelingSubTaskStatus.CREATING.getCode(), curMisTransferIdList);
        labelingSubTaskRepository.insert(newLabelingSubTask);

        // 如果有其他背标任务，将其他背标任务拆出一个新的标注任务，新任务关联在新分组下
        if (CollectionUtils.isNotEmpty(otherLabelingSubTaskList)) {
            handleNewCompareSubTask(labelingTask, now, otherLabelingSubTaskList, otherLabelingDetailMap, curMisTransferIdList, curMisTransferRawDataIdList, newLabelingGroup);
        }
        return newLabelingSubTask;
    }

    /**
     * 获取其他标注任务详情列表
     *
     * @param otherLabelingSubTaskList 其他标注任务列表
     * @return 其他标注任务详情列表的映射关系
     */
    private Map<Long, List<LabelingDetail>> getotherLabelingDetailMap(List<LabelingSubTask> otherLabelingSubTaskList) {
        Map<Long, List<LabelingDetail>> otherLabelingDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(otherLabelingSubTaskList)) {
            List<Long> oterLabelingSubTaskIdList = otherLabelingSubTaskList.stream().map(LabelingSubTask::getId).collect(Collectors.toList());
            List<LabelingDetail> otherLabelingDetailList = labelingDetailRepository.listBySubTaskIdListAndRawDataIdList(oterLabelingSubTaskIdList, null);
            otherLabelingDetailMap = otherLabelingDetailList.stream().collect(Collectors.groupingBy(LabelingDetail::getSubTaskId));
        }
        return otherLabelingDetailMap;
    }

    /**
     * 获取当前MIS转移原始数据ID列表
     *
     * @param labelingSubTask      标注子任务
     * @param labelingDetailList   标注详情列表
     * @param curMisTransferIdList 当前MIS转移ID列表
     * @return 当前MIS转移原始数据ID列表
     */
    private List<Long> getCurMisTransferRawDataIdList(LabelingSubTask labelingSubTask, List<LabelingDetail> labelingDetailList, List<String> curMisTransferIdList) {
        List<Long> curMisTransferRawDataIdList;
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), labelingSubTask.getAssignType())) {
            curMisTransferRawDataIdList = labelingDetailList.stream().filter(item -> curMisTransferIdList.contains(item.getSessionId())).map(LabelingDetail::getRawDataId).collect(Collectors.toList());
        } else {
            curMisTransferRawDataIdList = labelingDetailList.stream().filter(item -> curMisTransferIdList.contains(String.valueOf(item.getRawDataId()))).map(LabelingDetail::getRawDataId).collect(Collectors.toList());
        }
        return curMisTransferRawDataIdList;
    }

    /**
     * 创建新的比较任务
     *
     * @param now                         当前时间
     * @param otherLabelingSubTaskList    其他标注任务列表
     * @param otherLabelingDetailMap      其他标注任务详情列表
     * @param curMisTransferIdList        当前MIS转移ID列表
     * @param curMisTransferRawDataIdList 当前MIS转移原始数据ID列表
     * @param newLabelingGroup            新的标注分组
     */
    private void handleNewCompareSubTask(LabelingTask labelingTask, Date now, List<LabelingSubTask> otherLabelingSubTaskList, Map<Long, List<LabelingDetail>> otherLabelingDetailMap, List<String> curMisTransferIdList, List<Long> curMisTransferRawDataIdList, LabelingTaskGroup newLabelingGroup) {
        for (LabelingSubTask otherLabelingSubTask : otherLabelingSubTaskList) {
            List<LabelingDetail> otherLabelingDetailList = otherLabelingDetailMap.get(otherLabelingSubTask.getId());
            // 检测拆分出的标注任务是否标注完成
            int newTaskStatus = computeNewLabelingSubTaskStatus(otherLabelingDetailList, curMisTransferRawDataIdList);
            LabelingSubTask newOtherLabelingSubTask = buildNewLabelingSubTask(labelingTask, otherLabelingSubTask, newLabelingGroup, otherLabelingSubTask.getLabelerName(), now, otherLabelingSubTask.getLabelerMis(), newTaskStatus, curMisTransferIdList);
            labelingSubTaskRepository.insert(newOtherLabelingSubTask);
            // 更新背标任务数据关联的任务ID
            List<LabelingDetail> otherTransferLabelingDetailList = otherLabelingDetailList.stream().filter(item -> curMisTransferRawDataIdList.contains(item.getRawDataId())).collect(Collectors.toList());
            otherTransferLabelingDetailList.forEach(item -> item.setSubTaskId(newOtherLabelingSubTask.getId()));
            labelingDetailRepository.batchUpdateLabelingDetail(otherLabelingDetailList);
        }
    }

    /**
     * 获取剩余标注数据量
     *
     * @param labelingSubTask 标注任务
     * @return 剩余标注数据量
     */
    private int getRestDataNum(LabelingSubTask labelingSubTask) {
        int restDataNum;
        if (Objects.equals(AssignTypeEnum.SESSION.getCode(), labelingSubTask.getAssignType())) {
            restDataNum = labelingDetailRepository.countSessionBySubTaskIdAndStatus(labelingSubTask.getId(), LabelingDetailStatus.LABELED.getCode());
        } else {
            restDataNum = labelingDetailRepository.countQueryBySubTaskIdAndStatus(labelingSubTask.getId(), LabelingDetailStatus.LABELED.getCode());
        }
        return restDataNum;
    }

    /**
     * 更新剩余标注任务的状态
     *
     * @param labelingSubTask 当前任务
     * @param maxTransferNum  最大转交数量
     * @param transferNum     已转交数量
     * @param now             当前时间
     * @param restDataNum     剩余标注数据量
     */
    private void updateRestLabelingTask(LabelingSubTask labelingSubTask, int maxTransferNum, int transferNum, Date now, int restDataNum, int status) {
        // 如果还有已标注数据，更新状态
        if (restDataNum > 0 || transferNum < maxTransferNum) {
            if (status != labelingSubTask.getStatus()) {
                labelingSubTask.setStatus(status);
            }
            labelingSubTask.setTotalDataCount(maxTransferNum - transferNum + restDataNum);
            // 如果是已完成，不需要更新时间
            if (status != LabelingSubTaskStatus.WAITING_RECYCLED.getCode()) {
                labelingSubTask.setUpdateTime(now);
            }
            labelingSubTaskRepository.updateById(labelingSubTask);
        } else {
            // 如果没有剩余数据，直接逻辑删除任务
            labelingSubTask.setIsDeleted(true);
            labelingSubTaskRepository.updateById(labelingSubTask);
        }
    }

    /**
     * 创建新的分组
     *
     * @param labelingTaskGroup        历史分组
     * @param otherLabelingSubTaskList 历史分组下的子任务
     * @param now                      当前时间
     * @param name                     转让人姓名
     * @param curMisTransferIdList     当前转让人需要分配的数据ID
     * @return 新的分组
     */
    private LabelingTaskGroup buildNewLabelingGroup(LabelingTaskGroup labelingTaskGroup, List<LabelingSubTask> otherLabelingSubTaskList, Date now, String name, List<String> curMisTransferIdList) {
        LabelingTaskGroup newLabelingTaskGroup = new LabelingTaskGroup();
        newLabelingTaskGroup.setTaskId(labelingTaskGroup.getTaskId());
        List<String> nameList = new ArrayList<>();
        nameList.add(name);
        if (CollectionUtils.isNotEmpty(otherLabelingSubTaskList)) {
            nameList.addAll(otherLabelingSubTaskList.stream().map(LabelingSubTask::getLabelerName).distinct().collect(Collectors.toList()));
        }
        newLabelingTaskGroup.setGroupName(String.join("vs", nameList));
        newLabelingTaskGroup.setRecycleStatus(LabelingTaskGroupStatus.CREATING.getCode());
        newLabelingTaskGroup.setTotalDataCount(curMisTransferIdList.size());
        newLabelingTaskGroup.setRecycleDataCount(0);
        newLabelingTaskGroup.setCreateTime(now);
        newLabelingTaskGroup.setUpdateTime(now);
        newLabelingTaskGroup.setIsDeleted(false);
        return newLabelingTaskGroup;
    }

    /**
     * 构建新的标注任务
     *
     * @param labelingSubTask      原标注任务
     * @param labelingTaskGroup    新分组
     * @param labelerName          转让人姓名
     * @param now                  当前时间
     * @param labelerMis           转让人mis
     * @param curMisTransferIdList 当前转让人需要分配的数据ID
     * @return 新标注任务
     */
    private LabelingSubTask buildNewLabelingSubTask(LabelingTask labelingTask, LabelingSubTask labelingSubTask, LabelingTaskGroup labelingTaskGroup, String labelerName, Date now, String labelerMis, int status, List<String> curMisTransferIdList) {
        LabelingSubTask newLabelingSubTask = new LabelingSubTask();
        newLabelingSubTask.setTaskId(labelingSubTask.getTaskId());
        newLabelingSubTask.setGroupId(labelingTaskGroup.getId());
        newLabelingSubTask.setName(labelingTask.getTaskName() + "-" + labelingTaskGroup.getGroupName());
        newLabelingSubTask.setLabelerMis(labelerMis);
        newLabelingSubTask.setStatus(status);
        newLabelingSubTask.setTotalDataCount(curMisTransferIdList.size());
        newLabelingSubTask.setIsDeleted(false);
        newLabelingSubTask.setTenantId(labelingSubTask.getTenantId());
        User user = UserUtils.getUser();
        newLabelingSubTask.setCreatorId(user != null ? user.getLogin() : "unknown");
        newLabelingSubTask.setCreateTime(now);
        newLabelingSubTask.setUpdateTime(now);
        newLabelingSubTask.setLabelerName(labelerName);
        newLabelingSubTask.setAssignType(labelingSubTask.getAssignType());
        return newLabelingSubTask;
    }

    /**
     * 计算旧分组标注任务状态
     *
     * @param allLabelingDetailList 所有标注任务详情
     * @param transferRawDataIdList 当前转让人分配的数据ID
     * @return 分组状态
     */
    private int computeOldLabelingSubTaskStatus(List<LabelingDetail> allLabelingDetailList, List<Long> transferRawDataIdList) {
        // 找到未分配出去且状态是未标注的记录，如果不存在，则所有剩余的已全部标注完成， 返回标注完成状态
        List<LabelingDetail> oldUnLabeledDetailList = allLabelingDetailList.stream().filter(item -> !transferRawDataIdList.contains(item.getRawDataId()) && LabelingDetailStatus.WAITING_LABELING.getCode() == item.getStatus()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oldUnLabeledDetailList)) {
            return LabelingSubTaskStatus.WAITING_RECYCLED.getCode();
        }
        return LabelingSubTaskStatus.IN_LABELING.getCode();
    }

    /**
     * 计算新分组标注任务状态
     *
     * @param allLabelingDetailList 所有标注任务详情
     * @param transferRawDataIdList 当前转让人分配的数据ID
     * @return 分组状态
     */
    private int computeNewLabelingSubTaskStatus(List<LabelingDetail> allLabelingDetailList, List<Long> transferRawDataIdList) {
        // 找到已分配出去且状态是未标注的记录，如果不存在，则分出去的已全部标注完成， 返回标注完成状态
        List<LabelingDetail> oldUnLabeledDetailList = allLabelingDetailList.stream().filter(item -> transferRawDataIdList.contains(item.getRawDataId()) && LabelingDetailStatus.WAITING_LABELING.getCode() == item.getStatus()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oldUnLabeledDetailList)) {
            return LabelingSubTaskStatus.WAITING_RECYCLED.getCode();
        }
        return LabelingSubTaskStatus.IN_LABELING.getCode();
    }
}
