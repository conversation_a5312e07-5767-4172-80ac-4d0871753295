package com.meituan.aigc.aida.labeling.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * 质检详情表实体类
 */
@Data
public class LabelingQualityCheckItem {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 标注数据ID
     */
    private Long labelingDataId;
    
    /**
     * 质检任务ID
     */
    private Long qualityCheckTaskId;
    
    /**
     * 质检人Mis
     */
    private String qualityCheckMis;

    /**
     * 质检人名称
     */
    private String qualityCheckName;
    
    /**
     * 质检结果: 1-标注正确 2-标注错误
     */
    private Integer qualityCheckResult;
    
    /**
     * 自定义质检项质检结果
     */
    private String qualityCheckItemsResult;
    
    /**
     * 状态(1:待质检 2:已质检)
     * {@link com.meituan.aigc.aida.labeling.common.enums.QualityCheckDataStatus}
     */
    private Integer status;

    /**
     * 是否可质检状态
     */
    private Integer checkableStatus;

    /**
     * 质检员修改后的标注结果，只有修改后才有值
     */
    private String modifiedLabelingItemsResult;

    /**
     * 被质检员修改的原始数据内容映射内容, 单独存储字段, 查看质检详情、导出时单独展示
     */
    private String modifiedRawDataMappedContent;

    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 首次质检时间
     */
    private Date firstCheckTime;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 大模型消息ID
     */
    private String messageId;

    /**
     * 原始数据ID
     */
    private Long rawDataId;
} 