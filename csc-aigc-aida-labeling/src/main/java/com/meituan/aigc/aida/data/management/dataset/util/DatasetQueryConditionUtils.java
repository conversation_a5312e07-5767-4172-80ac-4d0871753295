package com.meituan.aigc.aida.data.management.dataset.util;

import com.google.common.collect.Lists;
import com.meituan.aigc.aida.data.management.dataset.dto.DatasetFieldPathInfo;
import com.meituan.aigc.aida.data.management.dataset.dto.FieldMappingDTO;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetQueryCondition;
import com.meituan.aigc.aida.labeling.remote.common.enums.ConditionTypeEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 数据集查询条件构建工具类
 *
 * <AUTHOR>
 * @date 2025-06-03
 * @description 处理不同条件类型的ES查询条件构建逻辑
 */
@Slf4j
public class DatasetQueryConditionUtils {

    private static final String FLATTEN_PREFIX = "flattenedData";
    private static final String TEXT_PREFIX = "textData";
    private static final String ES_COMMON_DATA_PREFIX = "commonData";

    private static final List<Integer> FLATTEN_FIELD_TYPE = Lists.newArrayList(FieldTypeEnum.JSON.getCode(), FieldTypeEnum.PROCESSED_SIGNAL.getCode());


    /**
     * 添加查询条件到ES查询对象
     *
     * @param condition ES查询条件对象
     * @param pathInfo  字段路径信息
     */
    public static void addQueryCondition(DataSetQueryCondition condition, DatasetEsQueryCondition datasetEsQueryCondition,
                                         DatasetFieldPathInfo pathInfo) {
        if (Objects.isNull(condition)) {
            log.info("addQueryCondition-查询条件为空");
            return;
        }

        ConditionTypeEnum conditionType = ConditionTypeEnum.getByCode(condition.getConditionType());
        if (conditionType == null) {
            log.warn("addQueryCondition——查询的条件类型为空");
            return;
        }
        switch (conditionType) {
            case EQUAL:
                addEqualCondition(datasetEsQueryCondition, pathInfo, condition.getValue());
                break;
            case CONTAIN:
                addContainCondition(datasetEsQueryCondition, pathInfo, condition.getValue());
                break;
            case GREATER_THAN:
                addRangeCondition(datasetEsQueryCondition, pathInfo,
                        DatasetEsQueryCondition.RangeCondition.greaterThan(condition.getValue()));
                break;
            case LESS_THAN:
                addRangeCondition(datasetEsQueryCondition, pathInfo,
                        DatasetEsQueryCondition.RangeCondition.lessThan(condition.getValue()));
                break;
            case GREATER_THAN_OR_EQUAL:
                addRangeCondition(datasetEsQueryCondition, pathInfo,
                        DatasetEsQueryCondition.RangeCondition.greaterThanOrEqual(condition.getValue()));
                break;
            case LESS_THAN_OR_EQUAL:
                addRangeCondition(datasetEsQueryCondition, pathInfo,
                        DatasetEsQueryCondition.RangeCondition.lessThanOrEqual(condition.getValue()));
                break;
            case IS_NULL:
                addExistsCondition(datasetEsQueryCondition, pathInfo, false);
                break;
            default:
                log.warn("未处理的条件类型: {}, 使用默认的精确匹配查询", conditionType);
                addEqualCondition(datasetEsQueryCondition, pathInfo, condition.getValue());
                break;
        }
    }

    /**
     * 添加精确匹配条件
     */
    private static void addEqualCondition(DatasetEsQueryCondition condition, DatasetFieldPathInfo pathInfo, String searchValue) {
        if (pathInfo.getIsCommonField()) {
            condition.addTermConditionNested(pathInfo.getFieldName(), ES_COMMON_DATA_PREFIX, pathInfo.getCommonDataKeywordPath(), searchValue);
        } else if (isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
            condition.addTermCondition(pathInfo.getFieldName(), pathInfo.getFlattenPath(), searchValue);
        } else {
            condition.addTermConditionNested(pathInfo.getFieldName(), TEXT_PREFIX, pathInfo.getTextPath(), searchValue);
        }
    }

    /**
     * 添加包含条件
     */
    private static void addContainCondition(DatasetEsQueryCondition condition, DatasetFieldPathInfo pathInfo, String queryValue) {
        String wildcardValue = "*" + queryValue + "*";
        if (pathInfo.getIsCommonField()) {
            condition.addWildcardConditionNested(pathInfo.getFieldName(), ES_COMMON_DATA_PREFIX, pathInfo.getCommonDataKeywordPath(), wildcardValue);
        } else if (isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
            condition.addWildcardCondition(pathInfo.getFieldName(), pathInfo.getFlattenPath(), wildcardValue);
        } else {
            condition.addWildcardConditionNested(pathInfo.getFieldName(), TEXT_PREFIX, pathInfo.getTextPath(), wildcardValue);
        }
    }

    /**
     * 添加范围条件
     */
    private static void addRangeCondition(DatasetEsQueryCondition condition,
                                          DatasetFieldPathInfo pathInfo,
                                          DatasetEsQueryCondition.RangeCondition rangeCondition) {
        if (pathInfo.getIsCommonField()) {
            condition.addRangeConditionNested(pathInfo.getFieldName(), ES_COMMON_DATA_PREFIX, pathInfo.getCommonDataKeywordPath(), rangeCondition);
        } else if (isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
            condition.addRangeCondition(pathInfo.getFieldName(), pathInfo.getFlattenPath(), rangeCondition);
        } else {
            condition.addRangeConditionNested(pathInfo.getFieldName(), TEXT_PREFIX, pathInfo.getTextPath(), rangeCondition);
        }
    }

    /**
     * 添加存在性条件
     */
    private static void addExistsCondition(DatasetEsQueryCondition condition,
                                           DatasetFieldPathInfo pathInfo,
                                           boolean exists) {
        if (pathInfo.getIsCommonField()) {
            condition.addExistsConditionNested(pathInfo.getFieldName(), ES_COMMON_DATA_PREFIX, pathInfo.getCommonDataKeywordPath(), exists);
        } else if (isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
            condition.addExistsCondition(pathInfo.getFieldName(), pathInfo.getFlattenPath(), exists);
        } else {
            condition.addExistsConditionNested(pathInfo.getFieldName(), TEXT_PREFIX, pathInfo.getTextPath(), exists);
        }
    }

    /**
     * 判断是否使用扁平化路径存储
     */
    public static boolean isUseFlattenPath(FieldMappingDTO fieldMappingDTO) {
        return fieldMappingDTO != null &&
                fieldMappingDTO.getFieldType() != null &&
                FLATTEN_FIELD_TYPE.contains(fieldMappingDTO.getFieldType().getCode());
    }
}