package com.meituan.aigc.aida.data.management.dataset.param;

import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

@Data
public class DataSetParseHeadParam implements Serializable {
    /**
     * 数据来源 1:数据获取导入 2:文件上传 3: case分析
     */
    private Integer dataSource;
    /**
     * 数据获取任务id
     */
    private Long fetchId;
    /**
     * 文件
     */
    private MultipartFile file;
}
