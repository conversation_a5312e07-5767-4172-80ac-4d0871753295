package com.meituan.aigc.aida.data.management.dataset.dao.model;

public class DataProcessConfigWithBLOBs extends DataProcessConfig {
    private String ruleConfig;

    private String extraInfo;

    public String getRuleConfig() {
        return ruleConfig;
    }

    public void setRuleConfig(String ruleConfig) {
        this.ruleConfig = ruleConfig == null ? null : ruleConfig.trim();
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo == null ? null : extraInfo.trim();
    }
}