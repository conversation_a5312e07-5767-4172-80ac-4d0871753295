package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskUnLabelPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingNumCountPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingStatisticsPO;
import com.meituan.aigc.aida.labeling.param.LabelingDetailParam;
import com.meituan.aigc.aida.labeling.pojo.dto.DetailDataItemDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelDetailNumCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO;

import java.util.Date;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:05
 * @Version: 1.0
 */
public interface LabelingDetailRepository {

    /**
     * 分页查询子任务下的标注详情
     *
     * @param stuTaskIds 子任务ID
     * @param status     标注状态
     * @param pageNum    页码
     * @param pageSize   每页记录数
     * @return List<LabelingDetail>
     */
    List<LabelTaskDataExportVO> pageExportDataBySubTaskIdAndStatus(List<Long> stuTaskIds, Integer status, Integer pageNum, Integer pageSize);

    /**
     * 根据子任务id查询详情数据列表
     *
     * @param param 查询条件
     * @return
     */
    List<DetailDataItemDTO> pageDetailDataBySubTaskId(LabelingDetailParam param);

    /**
     * 根据主键集合查询标注详情
     *
     * @param detailDataIds 主键集合
     * @return 结果
     */
    List<LabelingDetail> listByIds(List<Long> detailDataIds);

    /**
     * 批量更新标注详情
     *
     * @param updateLabelingDetails 更新内容
     */
    void batchUpdateLabelingDetail(List<LabelingDetail> updateLabelingDetails);

    /**
     * 根据条件计数（条件均可为空）
     *
     * @param subTaskIds           子任务ID
     * @param rawDataId            原数据ID
     * @param labelingDetailStatus 标注状态
     * @return 数量
     */
    List<LabelingDetail> listBySubTaskIdRawDataIdAndStatus(List<Long> subTaskIds, Long rawDataId, Integer labelingDetailStatus);

    /**
     * 查询质检进度
     *
     * @param subTaskIds 子任务ID列表
     * @return 标注子任务列表
     */
    List<LabelingSubTaskUnLabelPO> listBySubTaskId(List<Long> subTaskIds);

    /**
     * 查询子任务下会话数量
     *
     * @param subTaskIds 子任务ID列表
     * @return 子任务ID和会话数量的映射
     */
    List<LabelingSubTaskUnLabelPO> listSessionCountBySubTaskId(List<Long> subTaskIds);

    /**
     * 根据主键更新
     *
     * @param labelingDetail 更新内容
     */
    void updateById(LabelingDetail labelingDetail);

    /**
     * 批量插入详情数据
     *
     * @param details
     */
    void batchInsertDetail(List<LabelingDetail> details);

    /**
     * 分页查询会话——会话下所有任务完成才算标注完成
     *
     * @param subTaskId   子任务ID
     * @param sessionId   会话ID
     * @param labelStatus 标注状态
     * @return 会话列表
     */
    List<String> pageSessionBySubtaskAndSessionIdAndStatus(Long subTaskId, String sessionId, Integer labelStatus);

    /**
     * 根据子任务ID和会话ID列表查询标注详情
     *
     * @param subTaskId 子任务ID
     * @param sessionId 会话ID列表
     * @return 标注详情列表
     */
    List<LabelingDetail> listBySubTaskIdAndSessionIdList(Long subTaskId, List<String> sessionId, Integer status);

    /**
     * 统计标注详情 数量
     *
     * @param subTaskId 子任务ID
     * @return session数
     */
    LabelDetailNumCountVO countNumBySubTask(Long subTaskId);

    /**
     * 统计一致率
     *
     * @param subTaskId 子任务ID
     * @return 一致率
     */
    Integer countConsistencyRateBySubTaskId(Long subTaskId);

    /**
     * 根据子任务ID和状态查询标注详情
     *
     * @param subTaskId 子任务ID
     * @param status    标注状态
     * @return 标注详情列表
     */
    List<LabelingDetail> listBySubTaskIdAndConsistency(Long subTaskId, Integer status);

    /**
     * 根据子任务ID和原始数据ID列表查询标注详情
     *
     * @param subTaskIds 子任务ID列表
     * @param rawDataIds 原始数据ID列表
     * @return 标注详情列表
     */
    List<LabelingDetail> listBySubTaskIdListAndRawDataIdList(List<Long> subTaskIds, List<Long> rawDataIds);

    /**
     * 查询指定子任务下可分配标注详情
     *
     * @param subTaskId 子任务ID
     * @return 标注详情列表
     */
    List<LabelingDetail> listAssignLabelingDetail(Long subTaskId);

    /**
     * 查询指定子任务下可分配会话列表
     *
     * @param subTaskId 子任务ID
     * @return 会话ID列表
     */
    List<String> listSessionAssignLabelingDetail(Long subTaskId);

    /**
     * 根据子任务ID和原始数据ID列表查询标注详情
     *
     * @param subTaskIds 子任务ID列表
     * @param rawDataIds 原始数据ID列表
     * @return 标注详情列表
     */
    List<LabelingDetail> listBysubTaskIdListAndRawDataIdList(List<Long> subTaskIds, List<Long> rawDataIds);

    /**
     * 根据子任务ID和标注状态批量更新标注人
     *
     * @param subTaskId           子任务ID
     * @param transferLabelerMis  标注人mis
     * @param transferLabelerName 标注人名称
     * @param labelingStatus      标注状态
     */
    void batchUpdateLabelerBySubTaskIdAndStatus(Long subTaskId, String labelerMis, String labelerName, Integer labelingStatus);

    /**
     * 根据主键查询标注详情
     *
     * @param labelingDataId 主键Id
     * @return 标注详情
     */
    LabelingDetail getById(Long labelingDataId);

    /**
     * 更新查看状态
     *
     * @param idList 更新条件
     */
    void updateViewStatusByIdList(List<Long> idList);

    /**
     * 根据子任务id查询详情数据列表
     *
     * @param subTaskId 子任务id
     * @return 详情数据列表
     */
    List<LabelingDetail> listBySubTaskId(Long subTaskId);

    /**
     * 根据子任务id和状态查询会话数量
     *
     * @param subTaskId 子任务id
     * @param status    状态
     * @return 会话数量
     */
    int countSessionBySubTaskIdAndStatus(Long subTaskId, Integer status);

    /**
     * 根据子任务id和状态查询query数量
     *
     * @param subTaskId 子任务id
     * @param status    状态
     * @return query数量
     */
    int countQueryBySubTaskIdAndStatus(Long subTaskId, Integer status);

    /**
     * 根据子任务ID和状态查询数据列表
     *
     * @param subTaskId 子任务ID
     * @param status    子任务状态
     * @return 数据列表
     */
    List<LabelingDetail> listDataBySubTaskIdAndStatus(Long subTaskId, Integer status);

    /**
     * 人员维度统计某一时间段内的标注数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 结果
     */
    List<PersonLabelingStatisticsPO> statisticsPersonLabelingData(Date startTime, Date endTime);

    /**
     * 根据子任务ID，标注状态，detailId查询详情列表，
     * 所有参数均可为空，需要自己在Service判断条件
     * @param subTaskId 子任务ID
     * @param labelStatus 标注状态
     * @return 结果
     */
    List<LabelingDetail> listByStatusAndDetailId(Long subTaskId, Integer labelStatus, Long detailId);

    /**
     * 分页查询标注详情数据
     * @param subTaskId  子任务ID
     * @param offset     偏移量
     * @param pageSize   每页记录数
     * @return 结果
     */
    List<LabelingDetail> pageDetailBySubTaskId(Long subTaskId, int offset, int pageSize);

    /**
     * 统计人员在某个任务中标注的数量
     * @param taskId  主任务ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 结果
     */
    List<PersonLabelingNumCountPO> listPersonLabelingCount(Long taskId, Date startTime, Date endTime);

    /**
     * 根据主键id和日期查询标注详情
     * @param ids 主键id
     * @param date 截止日期
     * @return
     */
    List<LabelingDetail> listLabelingDetailByIdsAndDate(List<Long> ids, Date date);
}
