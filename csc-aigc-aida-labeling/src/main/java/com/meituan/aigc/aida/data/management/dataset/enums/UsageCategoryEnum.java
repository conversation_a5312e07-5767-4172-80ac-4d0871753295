package com.meituan.aigc.aida.data.management.dataset.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-30 17:25
 * @description 数据用途分类
 */
@Getter
public enum UsageCategoryEnum {
    MODEL_TRAINING(1, "模型训练"),
    ONLINE_SESSION(2,"会话分析"),
    CUSTOM(3, "自定义");

    private Integer code;
    private String desc;

    UsageCategoryEnum(Integer code,  String desc) {
        this.code = code;
        this.desc = desc;
    }
}
