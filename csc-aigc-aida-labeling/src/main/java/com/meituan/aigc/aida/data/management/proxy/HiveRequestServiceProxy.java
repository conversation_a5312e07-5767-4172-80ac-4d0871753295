package com.meituan.aigc.aida.data.management.proxy;

import com.dianping.cat.Cat;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.QueryRequest;
import com.sankuai.data.talos.Talos;
import com.sankuai.data.talos.model.Engine;
import com.sankuai.data.talos.model.QueryInfo;
import com.sankuai.data.talos.model.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Hive请求服务代理，用于处理Hive查询相关操作
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class HiveRequestServiceProxy {

    /**
     * Talos客户端会话超时时间（秒）
     */
    private static final int SESSION_TIMEOUT_SECONDS = 60;

    /**
     * 查询执行成功状态码
     */
    private static final int QUERY_SUCCESS = 1;

    /**
     * 查询超时状态码
     */
    private static final int QUERY_TIMEOUT = 0;

    /**
     * 查询失败状态码
     */
    private static final int QUERY_FAILED = -1;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    /**
     * 获取Talos客户端实例
     *
     * @return Talos异步客户端
     * @throws TalosException 客户端创建异常
     */
    public AsyncTalosClient getClient() throws TalosException {
        AsyncTalosClient talosClient = new AsyncTalosClient(
                dataManagementLionConfig.getTalosUsername(),
                dataManagementLionConfig.getTalosPassword());
        talosClient.openSession(createRequestOption());
        return talosClient;
    }

    /**
     * 执行Hive查询
     *
     * @param client  Talos客户端
     * @param sql     待执行SQL
     * @param timeOut 超时时间（秒）
     * @return 查询结果
     * @throws TalosException 查询执行异常
     */
    public QueryResult invoke(AsyncTalosClient client, String sql, int timeOut) throws TalosException {
        QueryRequest request = new QueryRequest.Builder()
                .engine(Engine.Hive)
                .statement(sql)
                .build();
        String queryId = client.submit(request);
        int resultStatus = client.waitForFinished(queryId, timeOut, true);

        if (resultStatus == QUERY_SUCCESS) {
            return client.getQueryResult(queryId);
        }

        if (resultStatus == QUERY_FAILED) {
            //查询失败，获取查询失败原因
            QueryInfo queryInfo = client.getQueryInfo(queryId);
            log.error("异步查询hive库失败，失败原因：{}", queryInfo.getMessage());
        }

        if (resultStatus == QUERY_TIMEOUT) {
            // 超时后终止任务
            client.cancel(queryId);
        }

        CheckUtil.operationCheck(resultStatus != QUERY_TIMEOUT, String.format("查询hive超过%s秒，任务终止", timeOut));
        CheckUtil.operationCheck(resultStatus != QUERY_FAILED, "查询hive失败");
        return null;
    }

    /**
     * 关闭Talos客户端
     *
     * @param talosClient Talos客户端
     */
    public void closeClient(AsyncTalosClient talosClient) {
        if (Objects.isNull(talosClient)) {
            return;
        }

        try {
            boolean isSuccess = talosClient.closeSession(createRequestOption());
            CheckUtil.operationCheck(isSuccess, "关闭talos session失败");
        } catch (Exception e) {
            Cat.logError(e);
            log.error("关闭talos session失败,msg={}", e.getMessage(), e);
        }
    }

    /**
     * 创建Talos请求配置
     *
     * @return Talos请求配置
     */
    private Talos.RequestOption createRequestOption() {
        return new Talos.RequestOption(
                SESSION_TIMEOUT_SECONDS,
                SESSION_TIMEOUT_SECONDS,
                SESSION_TIMEOUT_SECONDS,
                TimeUnit.SECONDS);
    }

}
