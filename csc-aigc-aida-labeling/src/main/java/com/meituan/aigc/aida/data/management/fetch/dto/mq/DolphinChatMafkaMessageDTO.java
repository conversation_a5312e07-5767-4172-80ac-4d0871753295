package com.meituan.aigc.aida.data.management.fetch.dto.mq;

import com.meituan.aigc.aida.data.management.fetch.common.constans.DolphinChatConstant;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChatMessageFromEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.EventTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 海豚会话消息, <a href="https://km.sankuai.com/collabpage/2383226591">人工会话消息发送MQ</a>
 *
 * <AUTHOR>
 */
@Data
public class DolphinChatMafkaMessageDTO implements Serializable {

    /**
     * 事件类型 {@link DolphinChatConstant}
     */
    private String eventType;

    /**
     * 事件内容, jsonString
     */
    private String event;

    /**
     * 事件内容对象，严格对应JSON字符串中的数据
     */
    private EventData eventObject;

    /**
     * 业务数据对象，存储扩展的业务字段
     */
    private BusinessData businessData;

    private DolphinChatMafkaMessageDTO() {
    }

    /**
     * 事件数据，严格对应JSON字符串中的字段
     */
    @Data
    public static class EventData implements Serializable {
        /**
         * 服务ID，用于标识一个特定的服务或会话
         * 客服发送消息、用户发送消息均有此字段
         */
        private Long serviceId;

        /**
         * 客服ID，标识消息发送者的唯一ID
         * 客服发送消息、用户发送消息均有此字段
         */
        private Long staffId;

        /**
         * 客户ID，标识消息接收者的唯一ID
         * 客服发送消息、用户发送消息均有此字段
         */
        private Long customerId;

        /**
         * 对话ID，用于标识一次会话中的特定对话
         * 客服发送消息、用户发送消息均有此字段
         */
        private Long dialogId;

        /**
         * 技能ID，用于标识客服所具备的技能
         * 客服发送消息、用户发送消息均有此字段
         */
        private Long skillId;

        /**
         * 是否为系统消息，true表示是系统自动生成的消息，false表示是人工发送的消息
         * 客服发送消息特有字段
         */
        private Boolean isSystem;

        /**
         * 消息ID，用于标识一条消息的唯一ID
         * 用户发送消息特有字段（用户发送消息MQ中的海豚MessageID）
         */
        private Long manualMessageId;

        /**
         * 消息ID，用于标识一条消息的唯一ID
         * 客服发送消息特有字段（客服发送消息MQ中的海豚MessageID）
         */
        private Long messageId;

        /**
         * 事件ID，用于标识一个特定的事件
         * 客服发送消息特有字段（客服发送消息MQ中的门户MessageId）
         */
        private String cscMessageId;

        /**
         * 事件ID，用于标识一个特定的事件
         * 客服发送消息、用户发送消息均有此字段
         */
        private String eventId;

        /**
         * 发生时间，表示事件发生的时间戳
         * 客服发送消息、用户发送消息均有此字段
         */
        private Long occurredOn;

        /**
         * 消息内容
         * 用户发送消息特有字段，存储用户发送的内容
         */
        private String content;

        /**
         * 消息内容
         * 客服发送消息特有字段，存储客服发送的内容
         */
        private String msgContent;

        /**
         * 额外参数，JSON格式字符串
         * 客服发送消息特有字段
         */
        private String extParam;
    }

    /**
     * 业务数据，存储额外的业务字段
     */
    @Data
    public static class BusinessData implements Serializable {
        /**
         * 客服ID，标识消息发送者的唯一ID
         */
        private Long chatStaffId;

        /**
         * 开始事件类型
         */
        private EventTypeEnum eventTypeEnum;

        /**
         * 会话状态
         */
        private String sessionStatus;

        /**
         * 分配结果
         */
        private Boolean allocateResult;

        /**
         * 用户发送的消息内容
         */
        private String customerMessageContent;

        /**
         * 客服发送的消息内容
         */
        private String staffMessageContent;

        /**
         * 消息来源
         */
        private ChatMessageFromEnum chatMessageFromEnum;

        /**
         * 太平洋客服ID
         */
        private Long pacificStaffId;

        /**
         * 太平洋客服mis
         */
        private String pacificStaffMis;
    }
}