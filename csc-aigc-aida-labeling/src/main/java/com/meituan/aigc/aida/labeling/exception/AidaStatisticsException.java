package com.meituan.aigc.aida.labeling.exception;

public class AidaStatisticsException extends AidaTrainingException {
    public AidaStatisticsException() {
        super();
    }

    public AidaStatisticsException(String message) {
        super(message);
    }

    public AidaStatisticsException(Throwable cause) {
        super(cause);
    }

    /**
     * 带有错误消息和原因的构造函数
     * @param message 错误消息
     * @param cause 原因
     */
    public AidaStatisticsException(String message, Throwable cause) {
        super(message, cause);
    }
}
