package com.meituan.aigc.aida.labeling.strategy.upload.impl;

import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelingTaskRawDataVO;
import com.meituan.aigc.aida.labeling.strategy.upload.FileProcessingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * Excel文件处理策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExcelProcessingStrategy implements FileProcessingStrategy {

    /**
     * 支持的文件类型
     */
    List<String> fileTypes = Arrays.asList("xls", "xlsx");

    /**
     * 判断文件类型是否匹配
     */
    @Override
    public boolean isMe(String fileType) {
        return fileTypes.contains(fileType);
    }

    /**
     * 读取文件内容
     *
     * @param file 上传的文件
     * @return 文件内容
     */
    @Override
    public LabelingTaskRawDataVO readFile(MultipartFile file) {
        LabelingTaskRawDataVO result = new LabelingTaskRawDataVO();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            validateSheet(sheet);

            // 获取表头信息
            List<String> headers = extractHeaders(sheet);
            result.setHeaders(headers);

            // 获取数据内容
            List<List<String>> dataContent = extractDataContent(sheet, headers.size());
            result.setRawDataContent(dataContent);

            log.info("成功读取Excel文件，表头数量: {}, 数据行数: {}", headers.size(), dataContent.size());

        } catch (Exception e) {
            log.error("读取Excel文件失败", e);
            throw new AidaTrainingCheckException("读取Excel文件失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证Excel表格是否有效
     *
     * @param sheet 工作表
     */
    private void validateSheet(Sheet sheet) {
        Row headerRow = sheet.getRow(1);
        if (headerRow == null) {
            throw new AidaTrainingCheckException("表头不能为空!");
        }
    }

    /**
     * 从工作表中提取表头信息
     *
     * @param sheet 工作表
     * @return 表头列表
     */
    private List<String> extractHeaders(Sheet sheet) {
        List<String> headers = new ArrayList<>();
        Row headerRow = sheet.getRow(1);
        int columnCount = headerRow.getLastCellNum();

        for (int i = 0; i < columnCount; i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null && StringUtils.isNotBlank(cell.toString())) {
                headers.add(cell.toString().trim());
            }
        }

        return headers;
    }

    /**
     * 从工作表中提取数据内容
     *
     * @param sheet       工作表
     * @param columnCount 列数
     * @return 数据内容列表
     */
    private List<List<String>> extractDataContent(Sheet sheet, int columnCount) {
        List<List<String>> dataContent = new ArrayList<>();
        DataFormatter dataFormatter = new DataFormatter();

        // 获取数据内容（跳过两行表头）
        for (int rowIndex = 2; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row, columnCount)) {
                continue;
            }

            // 获取行数据
            List<String> rowData = extractRowData(row, columnCount, dataFormatter);
            dataContent.add(rowData);
        }

        return dataContent;
    }

    /**
     * 判断行是否为空行
     *
     * @param row         行
     * @param columnCount 列数
     * @return 是否为空行
     */
    private boolean isEmptyRow(Row row, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    /**
     * 提取行数据
     *
     * @param row           行
     * @param columnCount   列数
     * @param dataFormatter 数据格式化器
     * @return 行数据列表
     */
    private List<String> extractRowData(Row row, int columnCount, DataFormatter dataFormatter) {
        List<String> rowData = new ArrayList<>();

        for (int i = 0; i < columnCount; i++) {
            Cell cell = row.getCell(i);
            if (cell == null) {
                rowData.add("");
            } else {
                String cellValue = getCellValue(cell, dataFormatter);
                rowData.add(cellValue.trim());
            }
        }

        return rowData;
    }

    /**
     * 获取单元格值
     *
     * @param cell          单元格
     * @param dataFormatter 数据格式化器
     * @return 单元格值
     */
    private String getCellValue(Cell cell, DataFormatter dataFormatter) {
        // 处理带有公式的单元格
        if (cell.getCellType() == CellType.FORMULA) {
            try {
                // 尝试获取数字类型的值
                return String.valueOf((long) cell.getNumericCellValue());
            } catch (IllegalStateException e) {
                log.warn("获取数字类型的值失败, 尝试获取字符串类型的值");
                return cell.getStringCellValue();
            }
        } else if (cell.getCellType() == CellType.NUMERIC) {
            // 检查是否为日期格式
            if (DateUtil.isCellDateFormatted(cell)) {
                Date date = cell.getDateCellValue();
                return com.meituan.aigc.aida.labeling.util.DateUtil.formatDate(date, CommonConstants.DatePattern.DATE_TIME);
            } else {
                // 处理非日期的数值
                return dataFormatter.formatCellValue(cell);
            }
        } else {
            // 正常获取值
            return dataFormatter.formatCellValue(cell);
        }
    }

    /**
     * 计算Excel文件的内容行数
     *
     * @param file Excel文件
     * @return 内容行数
     */
    @Override
    public int countContentRows(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            int totalRows = 0;

            // 从第三行开始计数（跳过两行表头）
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    totalRows++;
                }
            }

            return totalRows;
        } catch (IOException e) {
            log.error("计算Excel文件行数失败", e);
            throw new AidaTrainingCheckException("计算Excel文件行数失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件表头
     *
     * @param file    上传的文件
     * @param columns 列数
     * @return 表头集合
     */
    @Override
    public Set<String> getFileHeader(MultipartFile file, Integer columns) throws IOException {
        Set<String> headerNameSet = new HashSet<>();
        InputStream inputStream = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(inputStream);
        // 获取第一个Sheet
        Sheet sheet = workbook.getSheetAt(0);
        // 解析第二行表头
        Row secondHeaderRow = sheet.getRow(1);
        for (int i = 0; i < columns; i++) {
            Cell cell = secondHeaderRow.getCell(i);
            if (cell != null) {
                headerNameSet.add(cell.getStringCellValue());
            }
        }
        return headerNameSet;
    }
}
