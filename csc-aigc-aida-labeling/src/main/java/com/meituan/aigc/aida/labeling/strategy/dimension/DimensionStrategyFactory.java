package com.meituan.aigc.aida.labeling.strategy.dimension;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class DimensionStrategyFactory {

    @Autowired
    private List<DimensionStrategy> dimensionStrategieList;

    private Map<Integer, DimensionStrategy> strategyMap;

    @PostConstruct
    public void init() {
        if (MapUtils.isEmpty(strategyMap)) {
            strategyMap = dimensionStrategieList.stream().collect(Collectors.toMap(DimensionStrategy::getDimensionType, Function.identity()));
        }
    }

    public DimensionStrategy getStrategy(Integer dimensionType) {
        return strategyMap.get(dimensionType);
    }
}
