package com.meituan.aigc.aida.benchmark.util;

import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkDatasetItemVO;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;

import java.text.SimpleDateFormat;
import java.util.Map;

/**
 * 评测数据转换工具类
 *
 * <AUTHOR>
 * @date 2025/6/26
 */

public class BenchmarkDataTransUtils {

    /**
     * 转换为BenchmarkDatasetItemVO
     *
     * @param labelingTask 标注任务
     * @param countMap     数据量map
     * @return VO对象
     */
    public static BenchmarkDatasetItemVO convertToBenchmarkDatasetItemVO(LabelingTask labelingTask, Map<Long, Integer> countMap) {
        return BenchmarkDatasetItemVO.builder()
                .id(labelingTask.getId())
                .name(labelingTask.getTaskName())
                .evalObject(BenchmarkNameUtils.getEvalObjectNameByTaskName(labelingTask.getTaskName()))
                .modelName(BenchmarkNameUtils.getModeNameByTaskName(labelingTask.getTaskName()))
                .version(BenchmarkNameUtils.getVersionByTaskName(labelingTask.getTaskName()))
                .dataCount(countMap.get(labelingTask.getId()))
                .createTime(labelingTask.getCreateTime())
                .build();
    }
}
