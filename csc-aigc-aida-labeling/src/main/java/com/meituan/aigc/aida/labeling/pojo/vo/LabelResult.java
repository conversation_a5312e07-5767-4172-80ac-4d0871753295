package com.meituan.aigc.aida.labeling.pojo.vo;

import com.meituan.aigc.aida.labeling.common.enums.LabelingItemRequired;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guowenhui
 * @Create: 2025/3/5 14:31
 * @Version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelResult {

    /**
     * 标注项ID
     */
    private Long id;
    /**
     * 标注项名称
     */
    private String name;
    /**
     * 数据类型 1-枚举 2-文本
     */
    private Integer dataType;
    /**
     * 指标类型 1-标注 2-质检
     */
    private Integer itemType;
    /**
     * 是否背靠背一致率标注项 1-是 2-否
     */
    private Integer isCompare;

    /**
     * 是否必填 1-是 2-否
     */
    private Integer isRequired;
    /**
     * 父指标ID，如果是父指标为空
     */
    private Long parentId;
    /**
     * 父指标枚举，当父指标选择该枚举后需要填子指标
     */
    private String parentEnumValue;
    /**
     * 标注值
     */
    private String value;
    /**
     * 枚举列表
     */
    private List<String> enumList;

    /**
     * 检查标注项集合中是否存在必填项的value为空的项
     *
     * @param labelingItems 标注项集合
     * @return 如果存在value为空的项返回true，否则返回false
     */
    public static boolean hasEmptyValue(List<List<LabelResult>> labelingItems) {
        if (CollectionUtils.isEmpty(labelingItems)) {
            return true;
        }

        // 使用Stream API扁平化处理嵌套集合并检查(只检查父节点是否必填)
        return labelingItems.stream()
                .flatMap(Collection::stream)
                .filter(item -> Objects.isNull(item.getParentId()))
                .filter(item -> Objects.isNull(item.getIsRequired()) || Objects.equals(LabelingItemRequired.YES.getCode(), item.getIsRequired()))
                .anyMatch(item -> StringUtils.isBlank(item.getValue()));
    }
}
