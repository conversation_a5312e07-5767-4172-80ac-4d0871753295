package com.meituan.aigc.aida.labeling.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 质检详情表信息DTO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LabelingQualityCheckItemDTO {
    /**
     * 质检详情ID
     */
    private Long id;
    /**
     * 质检数据ID
     */
    private Long dataId;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 会话时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sessionTime;
    /**
     * 大模型消息ID
     */
    private String messageId;
    /**
     * 三方消息ID
     */
    private String thirdMessageId;
    /**
     * 大模型消息
     */
    private String messageContent;
    /**
     * 原始数据JSON
     */
    private String rawDataContent;

    /**
     * 原始数据表头
     */
    private String rawDataHeaders;
    /**
     * 原始数据内容映射内容JSON
     */
    private String rawDataMappedContent;
    /**
     * 标注项信息JSON
     */
    private String labelingItems;
    /**
     * 质检结果
     */
    private Integer qualityCheckResult;
    /**
     * 质检反馈
     */
    private String qualityCheckRemark;
    /**
     * 质检人
     */
    private String qualityChecker;
    /**
     * 质检时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date qualityCheckTime;

    /**
     * 质检状态
     * @see com.meituan.aigc.aida.labeling.common.enums.QualityCheckDataStatus
     */
    private Integer qualityCheckStatus;

    /**
     * 标注人mis
     */
    private String labelerMis;

    /**
     * 标注人姓名
     */
    private String labelerName;

    /**
     * 标注时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date labelTime;
    /**
     * 质检员修改后的标注信息
     */
    private String modifiedLabelingItems;
}
