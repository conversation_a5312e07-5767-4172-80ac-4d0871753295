package com.meituan.aigc.aida.benchmark.common.constant;

/**
 * Benchmark相关常量
 *
 * <AUTHOR>
 * @date 2025/6/27
 */

public class BenchmarkConstant {
    /**
     * 可用率
     */
    public static final String AVAILABILITY_RATE = "可用率";

    /**
     * 满分率
     */
    public static final String FULL_SCORE_RATE = "满分率";

    /**
     * 综合得分
     */
    public static final String TOTAL_SCORES = "综合得分";

    /**
     * 评测试题长度标签
     */
    public static final String LENGTH_TAG = "长度标签";

    /**
     * 难易程度
     */
    public static final String DIFFICULTY_LEVEL = "难易程度";

    /**
     * 考点
     */
    public static final String EXAM_CENTER = "考点";

    /**
     * 主客观
     */
    public static final String SUBJECTIVE_OBJECTIVE = "主客观";

    /**
     * 是否强对抗场景
     */
    public static final String IS_STRONG_ANTI_SCENE = "是否强对抗场景";
}
