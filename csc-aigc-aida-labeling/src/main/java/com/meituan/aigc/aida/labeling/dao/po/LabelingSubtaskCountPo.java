package com.meituan.aigc.aida.labeling.dao.po;

import lombok.Data;

import java.io.Serializable;

/**
 * 子任务统计信息
 *
 * <AUTHOR>
 * @since 2025/03/27
 */
@Data
public class LabelingSubtaskCountPo implements Serializable {
    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 子任务id
     */
    private Long subtaskId;

    /**
     * 总数据量
     */
    private Integer totalCount;

    /**
     * 抽检数据量
     */
    private Integer inspectCount;

    /**
     * 总会话数据量
     */
    private Integer totalSessionCount;

    /**
     * 抽检会话数据量
     */
    private Integer inspectSessionCount;
}
