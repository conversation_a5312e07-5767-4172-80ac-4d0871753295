package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/3/7 14:59
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelingDetailBySessionVO {

    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 是否有大模型上下文
     */
    private Boolean hasContext;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * session维度标注详情
     */
    private LabelingDetailCommonVO sessionDimension;
    /**
     * query维度标注详情
     */
    private List<LabelingDetailInfoVO> conversations;
    /**
     * 标注项配置，这个字段表示的是任务配置，其他字段中表示的是已经标注的结果
     */
    private List<List<LabelResult>> labelingItems;

    /**
     * 是否可标注
     */
    private Boolean canLabel;
}
