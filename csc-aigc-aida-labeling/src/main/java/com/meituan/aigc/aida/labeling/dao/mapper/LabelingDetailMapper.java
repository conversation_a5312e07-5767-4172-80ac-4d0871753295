package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.LabelingDetail;
import com.meituan.aigc.aida.labeling.dao.model.LabelingDetailExample;
import com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskUnLabelPO;
import com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingStatisticsPO;
import com.meituan.aigc.aida.labeling.param.LabelingDetailParam;
import com.meituan.aigc.aida.labeling.pojo.dto.DetailDataItemDTO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelDetailNumCountVO;
import com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface LabelingDetailMapper {
    long countByExample(LabelingDetailExample example);

    int deleteByExample(LabelingDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LabelingDetail record);

    int insertSelective(LabelingDetail record);

    List<LabelingDetail> selectByExample(LabelingDetailExample example);

    LabelingDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LabelingDetail record, @Param("example") LabelingDetailExample example);

    int updateByExample(@Param("record") LabelingDetail record, @Param("example") LabelingDetailExample example);

    int updateByPrimaryKeySelective(LabelingDetail record);

    int updateByPrimaryKey(LabelingDetail record);

    List<LabelTaskDataExportVO> pageExportDataBySubTaskIdAndStatus(@Param("subTaskIds") List<Long> subTaskIds, @Param("status") Integer status, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    List<Long> listBySubTaskIds(@Param("subTaskIds") List<Long> subTaskIds);

    List<DetailDataItemDTO> pageQueryBySubTaskId(@Param("param") LabelingDetailParam param);

    List<LabelingDetail> listByIds(@Param("detailDataIds") List<Long> detailDataIds);

    int batchUpdateLabelingDetail(@Param("updateLabelingDetails") List<LabelingDetail> updateLabelingDetails);

    List<LabelingDetail> listBySubTaskIdRawDataIdAndStatus(@Param("subTaskIds") List<Long> subTaskIds, @Param("rawDataId") Long rawDataId, @Param("labelingDetailStatus") Integer labelingDetailStatus);

    List<LabelingSubTaskUnLabelPO> listBySubTaskId(@Param("subTaskIds") List<Long> subTaskIds);

    List<LabelingSubTaskUnLabelPO> listSessionCountBySubTaskId(@Param("subTaskIds") List<Long> subTaskIds);

    int batchInsertDetail(List<LabelingDetail> details);

    LabelDetailNumCountVO countNumBySubTask(@Param("subTaskId") Long subTaskId);

    List<String> pageSessionBySubtaskAndSessionIdAndStatus(@Param("subTaskId") Long subTaskId, @Param("sessionId") String sessionId, @Param("labelStatus") Integer labelStatus);

    Integer countConsistencyRateBySubTaskId(@Param("subTaskId") Long subTaskId);

    List<LabelingDetail> listDetailBySubTaskIds(@Param("subTaskIds") List<Long> subTaskIds);

    int batchUpdateLabelerBySubTaskIdAndStatus(@Param("subTaskId") Long subTaskId, @Param("labelerMis") String labelerMis, @Param("labelerName") String labelerName, @Param("labelingStatus") Integer labelingStatus);

    List<String> listSessionAssignLabelingDetail(@Param("subTaskId") Long subTaskId);

    /**
     * 更新查看状态
     *
     * @param idList 更新条件
     */
    void updateViewStatusByIdList(@Param("idList") List<Long> idList);

    /**
     * 根据子任务id和状态查询会话数量
     *
     * @param subTaskId 子任务id
     * @param status    状态
     * @return 会话数量
     */
    int countSessionBySubTaskIdAndStatus(@Param("subTaskId") Long subTaskId, @Param("status") Integer status);


    /**
     * 根据子任务id和状态查询query数量
     *
     * @param subTaskId 子任务id
     * @param status    状态
     * @return query数量
     */
    int countQueryBySubTaskIdAndStatus(@Param("subTaskId") Long subTaskId, @Param("status") Integer status);

    List<PersonLabelingStatisticsPO> statisticsPersonLabelingData(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}