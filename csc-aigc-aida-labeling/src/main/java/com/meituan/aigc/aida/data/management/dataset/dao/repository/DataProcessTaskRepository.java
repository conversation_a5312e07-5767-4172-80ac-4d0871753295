package com.meituan.aigc.aida.data.management.dataset.dao.repository;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-26
 * @description 数据处理任务仓储层
 */
public interface DataProcessTaskRepository {

    /**
     * 根据ID获取数据处理任务
     */
    DataProcessTask getDataProcessTaskById(Long taskId);

    /**
     * 根据数据集ID获取数据处理任务列表
     */
    List<DataProcessTask> listDataProcessTaskByDatasetId(Long datasetId);

    /**
     * 根据任务名称模糊查询数据处理任务
     */
    List<DataProcessTask> listDataProcessTaskByName(String taskName);

    /**
     * 根据任务名称和数据集ID查询数据处理任务
     */
    List<DataProcessTask> listDataProcessTaskByNameAndDatasetId(String taskName, Long datasetId);

    /**
     * 根据任务名称、数据集ID和创建者查询数据处理任务
     */
    List<DataProcessTask> listDataProcessTaskByNameAndDatasetIdAndCreator(String taskName, Long datasetId, String creatorMis);

    /**
     * 创建新数据处理任务
     */
    Long createDataProcessTask(DataProcessTask dataProcessTask);

    /**
     * 更新数据处理任务
     */
    void updateDataProcessTask(DataProcessTask dataProcessTask);
} 