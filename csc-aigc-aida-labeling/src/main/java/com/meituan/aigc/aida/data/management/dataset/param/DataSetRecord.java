package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 数据集记录参数
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集管理 - 数据记录参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetRecord implements Serializable {

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 数据详情内容
     */
    private Map<String, Object> content;
} 