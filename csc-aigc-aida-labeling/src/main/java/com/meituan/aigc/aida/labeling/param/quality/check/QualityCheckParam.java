package com.meituan.aigc.aida.labeling.param.quality.check;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 质检分配param
 * <AUTHOR>
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QualityCheckParam implements Serializable {
    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 抽样数量
     */
    private Integer dataCount;

    /**
     * 质检人
     */
    private List<QualityCheckMisParam> labelers;

    /**
     * 质检人mis
     */
    private String qualityCheckerMis;

    /**
     * 质检任务名称
     */
    private String taskName;

    /**
     * 分配类型，1-session 2-query
     */
    private Integer assignType;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;

    /**
     * 是否是管理员
     */
    private boolean isAdmin;
}
