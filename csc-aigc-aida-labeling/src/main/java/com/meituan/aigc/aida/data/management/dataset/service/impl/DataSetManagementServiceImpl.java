package com.meituan.aigc.aida.data.management.dataset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.meituan.aigc.aida.data.management.common.constant.LionConstant;
import com.meituan.aigc.aida.data.management.dataset.common.DataSetInvokeEs;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessTask;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataProcessTaskRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetRepository;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataSetVersionRepository;
import com.dianping.lion.client.util.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.aigc.aida.data.management.dataset.dto.*;
import com.meituan.aigc.aida.data.management.dataset.enums.DatasetPublishStatusEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.DatasetVersionStatusEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.EsStoreTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.enums.FileTypeEnum;
import com.meituan.aigc.aida.data.management.dataset.param.*;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetManagementService;
import com.meituan.aigc.aida.data.management.dataset.service.DatasetVersionLockService;
import com.meituan.aigc.aida.data.management.dataset.strategy.export.DataSetExportStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.export.DataSetExportStrategyFactory;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.DataFieldStrategyFactory;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.impl.JsonFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.field.impl.ProcessedSignalFieldStrategy;
import com.meituan.aigc.aida.data.management.dataset.strategy.task.DataSetProcessStrategyFactory;
import com.meituan.aigc.aida.data.management.dataset.strategy.task.impl.DataSetFieldConstants;
import com.meituan.aigc.aida.data.management.dataset.strategy.task.impl.OnlineDataSetProcessStrategy;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetFieldUtils;
import com.meituan.aigc.aida.data.management.dataset.util.DatasetQueryConditionUtils;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndex;
import com.meituan.aigc.aida.data.management.es.DatasetEsIndexService;
import com.meituan.aigc.aida.data.management.es.query.BatchResult;
import com.meituan.aigc.aida.data.management.es.query.DatasetEsQueryCondition;
import com.meituan.aigc.aida.data.management.es.query.DatasetPageResult;
import com.meituan.aigc.aida.data.management.fetch.common.convertor.DataFetchTaskConvertor;
import com.meituan.aigc.aida.data.management.fetch.common.enums.DataFetchTaskStatus;
import com.meituan.aigc.aida.data.management.fetch.dao.model.DataFetchTask;
import com.meituan.aigc.aida.data.management.fetch.dao.repository.DataFetchTaskRepository;
import com.meituan.aigc.aida.data.management.fetch.dto.DataFetchTaskDTO;
import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.exception.AidaRpcException;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.aigc.aida.labeling.service.UploadPlatformService;
import com.meituan.aigc.aida.labeling.service.common.PushElephantService;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.meituan.aigc.aida.labeling.util.DateUtil;
import com.meituan.aigc.aida.labeling.util.ThreadPoolUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.inf.xmdlog.ConfigUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.meituan.aigc.aida.data.management.dataset.common.constants.DatasetCommonConstants.*;
import static com.meituan.aigc.aida.labeling.util.DateUtil.DATE_PATTERN_YYYY_MM_DD;

/**
 * <AUTHOR>
 *
 * <p>
 * 数据集管理服务
 * @since 2025-05-23 16:23
 */
@Service
@Slf4j
public class DataSetManagementServiceImpl implements DataSetManagementService {
    @Resource
    private DataSetVersionRepository dataSetVersionRepository;

    @Resource
    private DataSetRepository dataSetRepository;

    @Resource
    private DatasetEsIndexService datasetEsIndexService;

    @Resource
    private DataSetInvokeEs dataSetInvokeEs;

    @Resource
    private com.meituan.aigc.aida.labeling.common.s3.S3Service s3Service;

    @Resource
    private PushElephantService pushElephantService;

    @Resource
    private DatasetVersionLockService versionLockService;

    @Resource
    private DataSetVersionServiceImpl dataSetVersionServiceImpl;

    private static final String LION_FIELD_TYPE_REGEX_MAPPING_KEY = "aida.data.field.type.regex.mapping";

    // 新增常量
    private static final int FIRST_BATCH = 1;
    private static final int SAMPLE_SIZE = 10;
    private static final int MIN_DATA_SIZE_FOR_MIDDLE_SAMPLE = 20;

    /**
     * 处理数据集创建任务的线程池
     */
    private static final ExecutorService DATASET_CREATE_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(100, 400, 200, "dataset-create-%d");

    /**
     * 系统可用的CPU核心数
     */
    private static final int CPU_CORE_COUNT = 4;

    /**
     * 处理标注任务导出的线程池
     */
    private static final ExecutorService EXPORT_DATA_SET_DATA_EXECUTOR = ThreadPoolUtil.createPoolWithTraceAndCat(
            CPU_CORE_COUNT * 2,
            CPU_CORE_COUNT * 4,
            200,
            "dataset-export-%d");

    @Autowired
    private DataSetProcessStrategyFactory dataSetProcessStrategyFactory;

    @Autowired
    private JsonFieldStrategy jsonFieldStrategy;

    @Autowired
    private DataFieldStrategyFactory dataFieldStrategyFactory;

    @Autowired
    private ProcessedSignalFieldStrategy processedSignalFieldStrategy;

    @Autowired
    private DataFetchTaskRepository dataFetchTaskRepository;

    @Autowired
    private DataProcessTaskRepository dataProcessTaskRepository;

    @Autowired
    private UploadPlatformService uploadPlatformService;

    @Autowired
    private DataSetExportStrategyFactory dataSetExportStrategyFactory;

    @Override
    public PageData<DataFetchTaskDTO> pageDataFetchTask(String taskName, Integer pageNum, Integer pageSize) {
        log.info("开始查询数据获取列表,taskName:{},pageNum:{},pageSize:{}", taskName, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<DataFetchTask> dataFetchTasks = dataFetchTaskRepository.listCompletedTask(taskName);
        if (CollectionUtils.isEmpty(dataFetchTasks)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<DataFetchTask> pageInfo = new PageInfo<>(dataFetchTasks);
        PageHelper.clearPage();
        List<DataFetchTaskDTO> dataFetchTaskList = dataFetchTasks.stream()
                .map(DataFetchTaskConvertor::buildDataFetchTaskDTO)
                .collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), dataFetchTaskList);
    }

    @Override
    public DataFetchContent getDataFetchByTaskId(Long taskId) {
        CheckUtil.paramCheck(Objects.nonNull(taskId), "任务id不能为空");
        DataFetchTask task = dataFetchTaskRepository.getTaskById(taskId);
        CheckUtil.paramCheck(Objects.nonNull(task), "任务不存在");
        DataFetchContent dataFetchContent = new DataFetchContent();
        dataFetchContent.setTaskName(task.getName());
        dataFetchContent.setStatus(task.getTaskStatus());
        dataFetchContent.setCount(task.getDataCount());
        // 数据获取解析字段
        OnlineDataSetProcessStrategy strategy = (OnlineDataSetProcessStrategy) dataSetProcessStrategyFactory.getStrategy(DataSourceEnum.ONLINE.getCode());
        DataSetParseHeadParam param = new DataSetParseHeadParam();
        param.setDataSource(DataSourceEnum.ONLINE.getCode());
        param.setFetchId(taskId);
        List<DataFieldParam> dataFieldParams = strategy.parseHeader(param);
        if (CollectionUtils.isNotEmpty(dataFieldParams)) {
            dataFetchContent.setHeadList(dataFieldParams);
            dataFetchContent.setFieldCount(dataFieldParams.size());
        }
        return dataFetchContent;
    }

    @Override
    public List<DataFieldParam> parseFileHeader(DataSetParseHeadParam param) {
        return dataSetProcessStrategyFactory.getStrategy(param.getDataSource()).parseHeader(param);
    }

    @Override
    public DataSetCreateResponseParam createDataSet(DataSetCreateParam param) {
        return dataSetProcessStrategyFactory.getStrategy(param.getDataSource()).createDataSet(param);
    }

    @Override
    public void createDataSetAsync(DataSetCreateParam param) {
        User user = UserUtils.getUser();
        param.setCreatorMis(user.getLogin());
        param.setCreatorName(user.getName());
        DATASET_CREATE_EXECUTOR.submit(() -> {
            try {
                createDataSet(param);
                String url = Lion.getString(ConfigUtil.getAppkey(), LionConstant.DATASET_MANAGE_URL);
                String messageContentFormat = "数据集【%s】创建成功，[请点击查看|%s]";
                String message = String.format(messageContentFormat, param.getDataSetName(), url);
                // 发送大象
                pushElephantService.pushElephant(message, user.getLogin());
            } catch (Exception e) {
                log.error("异步创建数据集失败, param:{}", JSONObject.toJSONString(param), e);
                pushElephantService.pushElephant(String.format("数据集【%s】创建失败，失败原因：%s", param.getDataSetName(), e.getMessage()), user.getLogin());
            }
        });
    }

    @Override
    public PageData<DataSetDTO> pageDataset(String name, Long datasetId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<DataSet> dataSets = dataSetRepository.listDataSetByNameAndId(name, datasetId);
        if (CollectionUtils.isEmpty(dataSets)) {
            return new PageData<>(0L, Collections.emptyList());
        }
        PageInfo<DataSet> pageInfo = new PageInfo<>(dataSets);
        PageHelper.clearPage();
        List<DataSetDTO> dataSetList = dataSets.stream().map(this::builderDataSetDTO).collect(Collectors.toList());
        return new PageData<>(pageInfo.getTotal(), dataSetList);
    }

    @Override
    public PageData<DataSetRecordDTO> pageDataSetDetail(Long dataSetId, Long versionId, Integer pageNum, Integer pageSize) {
        // 1. 获取数据集和版本信息
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");
        Long latestVersionId = dataSet.getLatestVersionId();

        DataSetVersion dataSetVersion;
        if (Objects.nonNull(versionId)) {
            dataSetVersion = dataSetVersionRepository.getById(versionId);
        } else {
            // 如果versionId为空，则获取最新版本
            CheckUtil.paramCheck(Objects.nonNull(latestVersionId), "数据集最新版本不存在");
            dataSetVersion = dataSetVersionRepository.getById(latestVersionId);
        }
        CheckUtil.paramCheck(Objects.nonNull(dataSetVersion), "数据集版本不存在");

        // 2. 构建ES查询条件
        DatasetEsQueryCondition condition = dataSetInvokeEs.getDatasetEsQueryConditionWithPage(dataSet, dataSetVersion, pageNum, pageSize);

        // 3. 调用ES查询服务
        DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);

        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageData<>(0L, Collections.emptyList());
        }

        // 4. 转换结果
        List<DataSetRecordDTO> recordDTOList = pageResult.getRecords().stream()
                .filter(Objects::nonNull)
                .map(esIndex -> convertToDataSetRecordDTO(esIndex, dataSetVersion.getHeadConfig()))
                .collect(Collectors.toList());

        return new PageData<>(pageResult.getTotal(), recordDTOList);
    }

    @Override
    public List<FieldEnumDTO> listFieldEnum() {
        return Arrays.stream(FieldTypeEnum.values())
                .map(fieldType -> {
                    FieldEnumDTO dto = new FieldEnumDTO();
                    dto.setCode(fieldType.getCode());
                    dto.setName(fieldType.getDescription());
                    dto.setDescription(fieldType.getExample());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<DataSourceField> listField(Long dataSetId, Long versionId) {
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(versionId), "数据集版本ID不能为空");
        DataSetVersion version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(dataSetId, versionId);
        CheckUtil.paramCheck(Objects.nonNull(version), "数据集版本不存在");
        DatasetHeadConfig datasetHeadConfig = JSON.parseObject(version.getHeadConfig(),
                new TypeReference<DatasetHeadConfig>() {
                });
        if (Objects.isNull(datasetHeadConfig) || CollectionUtils.isEmpty(datasetHeadConfig.getHeadList())) {
            return Collections.emptyList();
        }
        List<DataContentFieldDTO> headList = datasetHeadConfig.getHeadList();
        Map<String, FieldMappingDTO> fieldTypeMap = DatasetFieldUtils.convertHeadConfig2FieldMapping(version.getHeadConfig());
        return headList.stream().filter(Objects::nonNull).map(head -> convertToDataSourceField(head, fieldTypeMap)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateField(UpdateFieldParam updateFieldParam) {
        log.info("开始更新数据集字段配置, param:{}", JSONObject.toJSONString(updateFieldParam));
        // 1. 参数校验
        checkUpdateFieldParam(updateFieldParam);

        // 2. 获取数据集版本锁
        boolean success = versionLockService.tryLock(updateFieldParam.getDataSetId(), updateFieldParam.getVersionId(), "update_field");
        CheckUtil.operationCheck(success, "当前数据正在进行别的操作，请等待其他操作完成");

        try {
            // 3. 获取数据集版本信息
            DataSet dataSet = dataSetRepository.getDataSetById(updateFieldParam.getDataSetId());
            CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

            DataSetVersion dataSetVersion = dataSetVersionRepository.getById(updateFieldParam.getVersionId());
            CheckUtil.paramCheck(Objects.nonNull(dataSetVersion), "数据集版本不存在");

            // 4. 解析原始的headConfig
            DatasetHeadConfig originalHeadConfig = JSON.parseObject(dataSetVersion.getHeadConfig(),
                    new TypeReference<DatasetHeadConfig>() {
                    });

            List<DataContentFieldDTO> headList = originalHeadConfig.getHeadList();
            if (CollectionUtils.isEmpty(headList)) {
                log.warn("数据集版本的表头配置为空，dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
                return;
            }
            // 5. 原有的字段类型映射
            Map<String, Integer> originalFieldMap = headList.stream()
                    .collect(Collectors.toMap(DataContentFieldDTO::getColumnName, DataContentFieldDTO::getFieldType));

            // 6.发生变更的字段
            List<String> changedFields = getChangedField(updateFieldParam, originalFieldMap);

            // 7. 没有修改字段类型直接返回
            if (CollectionUtils.isEmpty(changedFields)) {
                log.info("未检测到字段类型变更，无需更新, dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
                return;
            }

            log.info("检测到{}个字段类型发生变更，开始更新处理, changedFields:{}", changedFields.size(), changedFields);

            // 8. 过滤变更字段
            List<DataSourceField> changedFieldList = updateFieldParam.getHeadList().stream()
                    .filter(Objects::nonNull)
                    .filter(field -> changedFields.contains(field.getFieldName()))
                    .collect(Collectors.toList());
            //  是否需要重新计算ES映射的标志位，初始值为false
            boolean needRecalculateEsMapping;
            // 9. 获取更新后的headConfig
            HeadConfigResult result = getHeadConfig(dataSet, dataSetVersion, changedFieldList);
            DatasetHeadConfig newHeadConfig = result.getHeadConfig();
            needRecalculateEsMapping = result.isNeedRecalculateEsMapping();

            dataSetVersion.setHeadConfig(JSON.toJSONString(newHeadConfig));
            dataSetVersion.setUpdateTime(new Date());
            dataSetVersionRepository.updateSelectiveById(dataSetVersion);

            //  10. 更新ES
            updateEsFieldAndData(dataSet, dataSetVersion, originalHeadConfig, JSON.toJSONString(newHeadConfig), needRecalculateEsMapping);
            log.info("成功更新数据集字段配置和ES字段映射, dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
        } finally {
            //  11. 释放锁
            versionLockService.releaseLock(updateFieldParam.getDataSetId(), updateFieldParam.getVersionId(), "update_field");
        }
    }

    /**
     * HeadConfig处理结果包装类
     */
    @Getter
    private static class HeadConfigResult {
        private final DatasetHeadConfig headConfig;
        private final boolean needRecalculateEsMapping;

        public HeadConfigResult(DatasetHeadConfig headConfig, boolean needRecalculateEsMapping) {
            this.headConfig = headConfig;
            this.needRecalculateEsMapping = needRecalculateEsMapping;
        }

    }

    /**
     * 获取变更的字段
     *
     * @param updateFieldParam 更新字段参数
     * @param originalFieldMap 原始字段映射
     * @return 变更的字段列表
     */
    private List<String> getChangedField(UpdateFieldParam updateFieldParam, Map<String, Integer> originalFieldMap) {
        List<String> changedFieldTypes = new ArrayList<>();
        for (DataSourceField updateField : updateFieldParam.getHeadList()) {
            String fieldName = updateField.getFieldName();
            Integer newFieldType = updateField.getFieldType();

            if (StringUtils.isBlank(fieldName) || Objects.isNull(newFieldType)) {
                continue;
            }
            // 原始字段类型
            Integer originalFieldType = originalFieldMap.get(fieldName);
            if (!Objects.equals(originalFieldType, newFieldType)) {
                changedFieldTypes.add(fieldName);
            }
        }
        return changedFieldTypes;
    }

    @Override
    public void exportDataSet(Long dataSetId, Long versionId, Integer fileType) {
        // 参数校验
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(versionId), "数据集版本ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(fileType), "导出文件类型不能为空");

        // 获取数据集和版本信息
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        DataSetVersion dataSetVersion = dataSetVersionRepository.getById(versionId);
        CheckUtil.paramCheck(Objects.nonNull(dataSetVersion), "数据集版本不存在");

        // 异步执行导出任务
        submitExportDataSet(fileType, dataSet, dataSetVersion);
    }

    /**
     * 异步提交数据集导出任务
     *
     * @param fileType       文件类型
     * @param dataSet        数据集对象
     * @param dataSetVersion 数据集版本对象
     */
    private void submitExportDataSet(Integer fileType, DataSet dataSet, DataSetVersion dataSetVersion) {
        User user = UserUtils.getUser();
        String userMis = Objects.nonNull(user) ? user.getLogin() : CommonConstants.FieldName.UNKNOWN;
        // 获取当前用户信息
        EXPORT_DATA_SET_DATA_EXECUTOR.submit(() -> {
            try {
                // 执行导出
                String fileName = String.format("%s_%s_%s", dataSet.getName(), dataSetVersion.getVersionName(),
                        DateUtil.formatDate(new Date(), "yyyyMMddHHmmss"));
                String fileUrl = exportDataSetToFile(dataSet, dataSetVersion, fileType, fileName, userMis);

                // 发送通知
                String fileTypeStr = FileTypeEnum.getDescByCode(fileType);
                String message = String.format("数据集【%s】(版本:%s)导出%s文件成功，[点击下载|%s]",
                        dataSet.getName(), dataSetVersion.getVersionName(), fileTypeStr, fileUrl);
                pushElephantService.pushElephant(message, userMis);

                log.info("数据集导出成功, dataSetId:{}, versionId:{}, fileType:{}, 用户:{}",
                        dataSet.getId(), dataSetVersion.getId(), fileType, userMis);
            } catch (Exception e) {
                log.error("数据集导出失败, dataSetId:{}, versionId:{}, fileType:{}, 用户:{}",
                        dataSet.getId(), dataSetVersion.getId(), fileType, userMis, e);
                String message = String.format("数据集【%s】(版本:%s)导出失败，原因：%s",
                        dataSet.getName(), dataSetVersion.getVersionName(), e.getMessage());
                pushElephantService.pushElephant(message, userMis);
            }
        });
    }

    /**
     * 将数据集导出为文件
     *
     * @param dataSet        数据集
     * @param dataSetVersion 数据集版本
     * @param fileType       文件类型 (1: Excel, 2: JSON, 3: JSONL)
     * @param fileName       文件名
     * @return 文件URL
     */
    private String exportDataSetToFile(DataSet dataSet, DataSetVersion dataSetVersion, Integer fileType, String fileName, String userMis) throws Exception {
        // 1. 构建查询条件
        DataSetInvokeEs.ConditionResult result = dataSetInvokeEs.buildScrollCondition(dataSet, dataSetVersion, null, null);
        DatasetEsQueryCondition condition = result.getCondition();

        Map<String, FieldMappingDTO> fieldTypeMap = DatasetFieldUtils.convertHeadConfig2FieldMapping(dataSetVersion.getHeadConfig());
        if (fieldTypeMap.containsKey(DataSetFieldConstants.FIELD_APPLICATION_INVOKE_INFO)) {
            Set<String> columnList = fieldTypeMap.keySet();
            columnList.remove(DataSetFieldConstants.FIELD_APPLICATION_INVOKE_INFO);
            configureReturnFields(condition, new ArrayList<>(columnList), fieldTypeMap);
        }

        // 2. 解析表头配置
        DatasetHeadConfig headConfig = JSON.parseObject(dataSetVersion.getHeadConfig(),
                new TypeReference<DatasetHeadConfig>() {
                });
        CheckUtil.paramCheck(Objects.nonNull(headConfig) && CollectionUtils.isNotEmpty(headConfig.getHeadList()),
                "数据集表头配置为空");

        // 3. 获取对应的导出策略
        DataSetExportStrategy exportStrategy = dataSetExportStrategyFactory.getStrategy(fileType);
        // 4. 使用策略执行导出
        File tempFile = exportStrategy.export(dataSet, dataSetVersion, condition, headConfig, fileName);

        // 5. 上传文件到S3
        String fileUrl = getFileUrl(fileName, tempFile, exportStrategy, userMis);

        // 6. 删除临时文件
        if (tempFile.exists()) {
            tempFile.delete();
        }
        return fileUrl;
    }

    private void configureReturnFields(DatasetEsQueryCondition condition, List<String> columnList, Map<String, FieldMappingDTO> fieldTypeMap) {
        List<String> fieldPath = new ArrayList<>();
        Set<String> commonFields = DatasetFieldUtils.getCommonFieldsFromLion();
        // 构建筛选条件
        for (String column : columnList) {
            DatasetFieldPathInfo pathInfo = DatasetFieldUtils.buildFieldPathInfo(Collections.singletonList(column), fieldTypeMap, commonFields);
            if (Objects.isNull(pathInfo)) {
                break;
            }
            if (pathInfo.getIsCommonField() != null && pathInfo.getIsCommonField()) {
                fieldPath.add(pathInfo.getCommonDataKeywordPath().replace(ES_KEYWORD_SUFFIX, ""));
            } else if (DatasetQueryConditionUtils.isUseFlattenPath(pathInfo.getFieldMappingDTO())) {
                fieldPath.add(pathInfo.getFlattenPath());
            } else {
                fieldPath.add(pathInfo.getTextPath().replace(ES_KEYWORD_SUFFIX, ""));
            }
        }
        condition.setFieldPath(fieldPath);
    }

    /**
     * 获取文件URL
     *
     * @param fileName       文件名
     * @param tempFile       临时文件
     * @param exportStrategy 导出策略
     * @return 文件URL
     * @throws IOException IO异常
     */
    private String getFileUrl(String fileName, File tempFile, DataSetExportStrategy exportStrategy, String mis) throws IOException {
        String fileUrl;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            try (FileInputStream fileInputStream = new FileInputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            // 构造完整的带扩展名的文件名
            String fullFileName = fileName + exportStrategy.getFileExtension();
            fileUrl = uploadPlatformService.uploadS3AndEncryption(new ByteArrayInputStream(outputStream.toByteArray()), fullFileName, fullFileName, exportStrategy.getFileType(), mis);
        }
        return fileUrl;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataSet(Long dataSetId) {
        // 参数校验
        CheckUtil.paramCheck(Objects.nonNull(dataSetId), "数据集ID不能为空");
        DataSet dataSet = dataSetRepository.getDataSetById(dataSetId);
        CheckUtil.paramCheck(Objects.nonNull(dataSet), "数据集不存在");

        // 查询所有版本
        List<DataSetVersion> versions = dataSetVersionRepository.listVersionByDataSetId(dataSetId);
        // 校验是否可以删除
        checkDataIsDelete(dataSetId, versions);
        // 删除数据集
        dataSet.setIsDeleted(Boolean.TRUE);
        dataSet.setUpdateTime(new Date());
        dataSetRepository.updateDataSet(dataSet);
        log.info("数据集已删除, dataSetId:{}", dataSetId);

        // 删除所有版本
        for (DataSetVersion version : versions) {
            version.setIsDeleted(Boolean.TRUE);
            version.setUpdateTime(new Date());
            dataSetVersionRepository.updateSelectiveById(version);

            // 删除该版本的ES数据
            dataSetInvokeEs.deleteVersionEsData(dataSet, version);
        }
        log.info("删除数据集完成, dataSetId:{}", dataSetId);
    }

    @Override
    @Transactional
    public void publishDataSet(Long dataSetId, Long versionId) {
        DataSetVersion version = getVersion(dataSetId, versionId);
        // 草稿状态可发布
        if (Objects.equals(version.getVersionStatus(), DatasetVersionStatusEnum.DRAFT.getCode())) {
            version.setVersionStatus(DatasetVersionStatusEnum.PUBLISHED.getCode());
            version.setUpdateTime(new Date());
            dataSetVersionRepository.updateSelectiveById(version);
        }
        // 将之前已发布过的版本置为草稿
        List<DataSetVersion> dataSetVersions = dataSetVersionRepository.listVersionByDataSetId(dataSetId);
        List<DataSetVersion> filteredVersions = dataSetVersions.stream()
                .filter(v -> !Objects.equals(v.getId(), versionId))
                .filter(v -> Objects.equals(v.getVersionStatus(), DatasetVersionStatusEnum.PUBLISHED.getCode()))
                .collect(Collectors.toList());
        for (DataSetVersion newDataSetVersion : filteredVersions) {
            newDataSetVersion.setVersionStatus(DatasetVersionStatusEnum.DRAFT.getCode());
            newDataSetVersion.setUpdateTime(new Date());
            dataSetVersionRepository.updateSelectiveById(newDataSetVersion);
        }
    }

    @Override
    public DatasetHeadConfig handleHeadList(DatasetHeadConfig historyHeadConfig, List<DataFieldParam> headList, List<DataSetRecordParam> dataList) {
        DatasetHeadConfig headConfig = new DatasetHeadConfig();
        // 处理默认的空类型和es字段映射
        List<DataContentFieldDTO> fieldList = new ArrayList<>();

        // 获取Lion配置的通用字段
        Set<String> commonFields = getCommonFieldsFromLion();

        // 为es字段映射准备计数器
        int textSegmentIndex = 1;
        int flattenSegmentIndex = 1;
        Map<String, DataContentFieldDTO> fieldMap = new HashMap<>();
        if (historyHeadConfig != null) {
            List<DatasetHeadConfig.HeadConfig> headConfigList = historyHeadConfig.getHeadConfig();
            Map<String, DatasetHeadConfig.HeadConfig> headConfigMap = headConfigList.stream().collect(Collectors.toMap(DatasetHeadConfig.HeadConfig::getStoreType, Function.identity()));
            if (Objects.nonNull(headConfigMap.get(EsStoreTypeEnum.TEXT.getName()))) {
                textSegmentIndex = headConfigMap.get(EsStoreTypeEnum.TEXT.getName()).getMaxIndex();
            }
            if (Objects.nonNull(headConfigMap.get(EsStoreTypeEnum.FLATTEN.getName()))) {
                flattenSegmentIndex = headConfigMap.get(EsStoreTypeEnum.FLATTEN.getName()).getMaxIndex();
            }
            // 构建字段映射，为了后续找到是否有历史已经映射过的字段
            if (CollectionUtils.isNotEmpty(historyHeadConfig.getHeadList())) {
                fieldMap = historyHeadConfig.getHeadList().stream().collect(Collectors.toMap(DataContentFieldDTO::getColumnName, Function.identity()));
            }
        }

        List<DatasetHeadConfig.HeadConfig> headConfigList = new ArrayList<>();
        for (DataFieldParam field : headList) {
            DataContentFieldDTO fieldDTO = new DataContentFieldDTO();
            fieldDTO.setColumnName(field.getColumnName());

            Integer fieldType = field.getFieldType();
            // 如果字段类型为空，则使用采样数据推断字段类型
            if (field.getFieldType() == null) {
                fieldType = inferFieldTypeFromSampleData(field.getColumnName(), dataList);
                field.setFieldType(fieldType);
                log.info("字段[{}]类型推断结果：{}", field.getColumnName(), fieldType);
            }
            fieldDTO.setFieldType(fieldType);

            // 已经有映射关系的使用旧的映射
            List<DataContentFieldDTO.KeyNode> oldTreeNodes = new ArrayList<>();
            if (fieldMap.containsKey(field.getColumnName())) {
                fieldDTO.setEsKey(fieldMap.get(field.getColumnName()).getEsKey());
                fieldDTO.setEsColumnName(fieldMap.get(field.getColumnName()).getEsColumnName());
                fieldDTO.setStoreType(fieldMap.get(field.getColumnName()).getStoreType());
                oldTreeNodes = fieldMap.get(field.getColumnName()).getTreeNodes();
            } else {
                // 设置ES字段映射（拆分为esKey和esColumnName）
                EsFieldMapping esMapping = generateEsFieldMapping(field, commonFields, textSegmentIndex, flattenSegmentIndex);
                fieldDTO.setEsKey(esMapping.getEsKey());
                fieldDTO.setEsColumnName(esMapping.getEsColumnName());
                // 如果不是通用字段，则递增segment索引
                if (!commonFields.contains(field.getColumnName())) {
                    if (DatasetQueryConditionUtils.isUseFlattenPath(field.getFieldType())) {
                        flattenSegmentIndex++;
                        fieldDTO.setStoreType(EsStoreTypeEnum.FLATTEN.getName());
                    } else {
                        textSegmentIndex++;
                        fieldDTO.setStoreType(EsStoreTypeEnum.TEXT.getName());
                    }
                }
            }
            if (Objects.equals(field.getFieldType(), FieldTypeEnum.JSON.getCode()) || Objects.equals(field.getFieldType(), FieldTypeEnum.PROCESSED_SIGNAL.getCode())) {
                fieldDTO.setTreeNodes(buildTreeNode(oldTreeNodes, field, dataList));
            }

            fieldList.add(fieldDTO);
        }
        Set<String> newFieldSet = fieldList.stream().map(DataContentFieldDTO::getColumnName).collect(Collectors.toSet());
        // 补充历史数据中没有出现的记录
        if (historyHeadConfig != null) {
            List<DataContentFieldDTO> historyFieldList = historyHeadConfig.getHeadList();
            for (DataContentFieldDTO historyField : historyFieldList) {
                if (!newFieldSet.contains(historyField.getColumnName())) {
                    fieldList.add(historyField);
                }
            }
        }
        headConfigList.add(new DatasetHeadConfig.HeadConfig(EsStoreTypeEnum.TEXT.getName(), textSegmentIndex));
        headConfigList.add(new DatasetHeadConfig.HeadConfig(EsStoreTypeEnum.FLATTEN.getName(), flattenSegmentIndex));
        headConfig.setHeadConfig(headConfigList);
        headConfig.setHeadList(fieldList);
        return headConfig;
    }

    @Override
    public List<DatasetEsIndex> buildDatasetEsIndices(Long dataSetId, Long versionId, Integer dataSource, List<DataSetRecordParam> dataList, DatasetHeadConfig headConfig, Date createTime, Date updateTime) {
        List<DatasetEsIndex> esIndexList = new ArrayList<>();
        for (DataSetRecordParam record : dataList) {
            DatasetEsIndex esIndex = new DatasetEsIndex();
            esIndex.setDatasetId(dataSetId.toString());
            esIndex.setVersionId(versionId.toString());
            esIndex.setDataSource(dataSource.toString());
            esIndex.setCreateTime(createTime);
            esIndex.setUpdateTime(updateTime);
            esIndex.setFieldMapping(JSONObject.toJSONString(headConfig));
            String dataId = record.getDataId();
            if (StringUtils.isBlank(dataId)) {
                dataId = UUID.randomUUID().toString();
            }
            esIndex.setDocumentId(dataId);

            // 处理数据内容
            Map<String, Object> textData = new HashMap<>();
            Map<String, Object> content = record.getContent();
            Map<String, Object> flattenedData = new HashMap<>();
            Map<String, Date> dateData = new HashMap<>();
            Map<String, Object> nestedData = new HashMap<>();
            // 新增：通用数据存储map
            Map<String, Object> commonData = new HashMap<>();

            // 遍历fieldList处理数据
            for (DataContentFieldDTO field : headConfig.getHeadList()) {
                String columnName = field.getColumnName();
                String value = content.get(columnName) == null ? "" : content.get(columnName).toString();
                Integer fieldType = field.getFieldType();
                String esKey = field.getEsKey();
                String esColumnName = field.getEsColumnName();

                if (StringUtils.isNotBlank(value)) {
                    // 使用策略模式处理不同类型的数据
                    DataFieldStrategy dataFieldStrategy = dataFieldStrategyFactory.getStrategy(fieldType);

                    // 根据esKey判断存储位置
                    if (ES_COMMON_DATA_PREFIX.equals(esKey)) {
                        // 通用字段：处理后存储到commonData中，使用esColumnName作为key
                        processFieldDataToCommonData(dataFieldStrategy, esColumnName, value, commonData, dateData);
                    } else {
                        // 普通字段：使用esColumnName作为字段名存储到对应的数据结构中
                        dataFieldStrategy.processFieldData(esColumnName, value, textData, flattenedData, dateData, nestedData);
                    }
                }
            }

            esIndex.setTextData(textData);
            esIndex.setFlattenedData(flattenedData);
            esIndex.setDateData(dateData);
            esIndex.setNestedData(nestedData);
            // 设置通用数据
            if (!commonData.isEmpty()) {
                esIndex.setCommonData(commonData);
            }

            esIndexList.add(esIndex);
        }

        return esIndexList;
    }

    /**
     * 获取数据集版本
     *
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @return 数据集版本信息
     */
    private DataSetVersion getVersion(Long dataSetId, Long versionId) {
        CheckUtil.paramCheck(Objects.nonNull(dataSetId) && Objects.nonNull(versionId), "数据集ID或版本ID不能为空");
        DataSetVersion version = dataSetVersionRepository.getVersionByDataSetIdAndVersionId(dataSetId, versionId);
        CheckUtil.paramCheck(Objects.nonNull(version), "数据集版本不存在");
        return version;
    }

    /**
     * 处理字段数据到通用数据存储
     *
     * @param dataFieldStrategy 数据字段处理器
     * @param esColumnName      ES字段名
     * @param value             字段值
     * @param commonData        通用数据存储map
     * @param dateData          日期数据存储map
     */
    private void processFieldDataToCommonData(DataFieldStrategy dataFieldStrategy, String esColumnName, String value,
                                              Map<String, Object> commonData, Map<String, Date> dateData) {
        // 创建临时存储maps用于处理
        Map<String, Object> tempTextData = new HashMap<>();
        Map<String, Object> tempFlattenedData = new HashMap<>();
        Map<String, Date> tempDateData = new HashMap<>();
        Map<String, Object> tempNestedData = new HashMap<>();

        // 使用处理器处理数据
        dataFieldStrategy.processFieldData(esColumnName, value, tempTextData, tempFlattenedData, tempDateData, tempNestedData);

        // 将处理结果存储到commonData中
        if (!tempTextData.isEmpty()) {
            commonData.putAll(tempTextData);
        }
        if (!tempFlattenedData.isEmpty()) {
            commonData.putAll(tempFlattenedData);
        }
        if (!tempNestedData.isEmpty()) {
            commonData.putAll(tempNestedData);
        }
        // 日期数据单独处理
        if (!tempDateData.isEmpty()) {
            dateData.putAll(tempDateData);
        }
    }

    /**
     * 生成ES字段映射
     *
     * @param field               表头配置
     * @param commonFields        通用字段集合
     * @param textSegmentIndex    文本类型数据段索引
     * @param flattenSegmentIndex 扁平化类型数据段索引
     * @return ES字段映射对象
     */
    private EsFieldMapping generateEsFieldMapping(DataFieldParam field, Set<String> commonFields, int textSegmentIndex, int flattenSegmentIndex) {

        if (commonFields.contains(field.getColumnName())) {
            return new EsFieldMapping(ES_COMMON_DATA_PREFIX, field.getColumnName());
        } else if (DatasetQueryConditionUtils.isUseFlattenPath(field.getFieldType())) {
            return new EsFieldMapping(ES_FLATTEN_PREFIX, "segment_" + flattenSegmentIndex);
        }
        return new EsFieldMapping(ES_TEXT_PREFIX, "segment_" + textSegmentIndex);
    }

    /**
     * ES字段映射内部类
     */
    private static class EsFieldMapping {
        private final String esKey;
        private final String esColumnName;

        public EsFieldMapping(String esKey, String esColumnName) {
            this.esKey = esKey;
            this.esColumnName = esColumnName;
        }

        public String getEsKey() {
            return esKey;
        }

        public String getEsColumnName() {
            return esColumnName;
        }
    }

    /**
     * 从采样数据推断字段类型
     *
     * @param columnName 字段名称
     * @param dataList   数据列表
     * @return 推断的字段类型
     */
    @Override
    public int inferFieldTypeFromSampleData(String columnName, List<DataSetRecordParam> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("数据列表为空，字段[{}]使用默认文本类型", columnName);
            return FieldTypeEnum.TEXT.getCode();
        }

        // 获取采样数据
        List<String> sampleValues = getSampleValues(columnName, dataList);
        if (CollectionUtils.isEmpty(sampleValues)) {
            log.warn("字段[{}]采样数据为空，使用默认文本类型", columnName);
            return FieldTypeEnum.TEXT.getCode();
        }

        // 推断字段类型
        Set<Integer> fieldTypes = new HashSet<>();
        for (String value : sampleValues) {
            if (StringUtils.isNotBlank(value)) {
                int fieldType = matchFieldTypeByValue(value);
                fieldTypes.add(fieldType);
            }
        }

        // 如果所有采样数据的字段类型都相同，则使用该类型，否则使用文本类型
        if (fieldTypes.size() == 1) {
            Integer inferredType = fieldTypes.iterator().next();
            log.info("字段[{}]类型推断成功，采样数量：{}，推断类型：{}", columnName, sampleValues.size(), inferredType);
            return inferredType;
        } else {
            log.info("字段[{}]类型推断结果不一致，采样数量：{}，类型集合：{}，使用文本类型",
                    columnName, sampleValues.size(), fieldTypes);
            return FieldTypeEnum.TEXT.getCode();
        }
    }

    @Override
    public void updateDataSet(DataSet dataSet, DataSetVersion updateVersion, List<DataFieldParam> headList, List<DataSetRecordParam> dataList) {
        DatasetHeadConfig historyHeadConfig = JSON.parseObject(updateVersion.getHeadConfig(),
                new TypeReference<DatasetHeadConfig>() {
                });
        DatasetHeadConfig headConfig = new DatasetHeadConfig();

        // 处理 headList
        if (CollectionUtils.isNotEmpty(headList)) {
            headConfig = handleHeadList(historyHeadConfig, headList, dataList);
            updateVersion.setHeadConfig(JSON.toJSONString(headConfig));
            updateVersion.setUpdateTime(new Date());
            dataSetVersionRepository.updateSelectiveById(updateVersion);
            log.info("数据集版本 headConfig 已更新，dataSetId: {}, versionId: {}", updateVersion.getDataSetId(), updateVersion.getId());
        }
        // 处理 dataList
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<DatasetEsIndex> datasetEsIndexList = buildDatasetEsIndices(updateVersion.getDataSetId(), updateVersion.getId(), dataSet.getDataSource(), dataList, headConfig, null, new Date());
            datasetEsIndexService.batchUpdateDatasets(datasetEsIndexList, DataSourceEnum.getValueByCode(dataSet.getDataSource()), updateVersion.getEsIndexName());
        }
    }

    /**
     * 根据数据值匹配数据类型
     * 优先级：Lion正则匹配、json格式、处理后信号，如果都不满足，则匹配为文本
     */
    private int matchFieldTypeByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return FieldTypeEnum.TEXT.getCode();
        }

        // 1. Lion正则匹配
        Integer lionMatchType = matchByLionRegex(value);
        if (lionMatchType != null) {
            return lionMatchType;
        }

        // 2. JSON格式检查
        if (isJsonFormat(value)) {
            return FieldTypeEnum.JSON.getCode();
        }

        // 3. 处理后信号格式检查
        if (isProcessedSignalFormat(value)) {
            return FieldTypeEnum.PROCESSED_SIGNAL.getCode();
        }

        // 4. 默认为文本
        return FieldTypeEnum.TEXT.getCode();
    }

    private List<DataContentFieldDTO.KeyNode> buildTreeNode(List<DataContentFieldDTO.KeyNode> oldTreeNodes, DataFieldParam field, List<DataSetRecordParam> dataList) {
        List<DataContentFieldDTO.KeyNode> treeNodeList = oldTreeNodes;
        Set<String> fieldNameSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(treeNodeList)) {
            fieldNameSet = treeNodeList.stream().map(DataContentFieldDTO.KeyNode::getKey).collect(Collectors.toSet());
        }

        // 2. 遍历dataList，构造KeyNode树结构
        for (DataSetRecordParam record : dataList) {
            if (record == null || record.getContent() == null) {
                continue;
            }

            Map<String, Object> content = record.getContent();
            if (!content.containsKey(field.getColumnName())) {
                continue;
            }

            Object fieldValue = content.get(field.getColumnName());
            if (fieldValue == null) {
                continue;
            }

            // 获取字段类型
            Integer fieldType = field.getFieldType();

            // 使用对应的DataFieldStrategy解析数据并构建KeyNode树
            List<DataContentFieldDTO.KeyNode> keyNodes = parseFieldValueToKeyNodes(fieldValue.toString(), fieldType);
            if (CollectionUtils.isNotEmpty(keyNodes)) {
                for (DataContentFieldDTO.KeyNode keyNode : keyNodes) {
                    if (!fieldNameSet.contains(keyNode.getKey())) {
                        treeNodeList.add(keyNode);
                        fieldNameSet.add(keyNode.getKey());
                    }
                }
            }
        }

        return treeNodeList;
    }

    /**
     * 根据字段类型解析字段值并构建KeyNode树结构
     *
     * @param valueStr  字段值字符串
     * @param fieldType 字段类型
     * @return KeyNode列表
     */
    private List<DataContentFieldDTO.KeyNode> parseFieldValueToKeyNodes(String valueStr, Integer fieldType) {
        List<DataContentFieldDTO.KeyNode> keyNodes = new ArrayList<>();

        try {
            // 获取对应的字段处理策略
            DataFieldStrategy strategy = dataFieldStrategyFactory.getStrategy(fieldType);
            if (strategy == null) {
                log.warn("未找到字段类型[{}]对应的处理策略", fieldType);
                return keyNodes;
            }

            // 使用策略解析字段值
            Object parsedValue = strategy.parseFieldValue(valueStr);

            // 将解析后的值转换为JSONObject用于构建KeyNode树
            JSONObject jsonObject = convertToJsonObject(parsedValue, fieldType);

            if (jsonObject != null) {
                keyNodes = buildKeyNodesFromJsonObject(jsonObject);
            }

        } catch (Exception e) {
            log.debug("解析字段值失败: {}, fieldType: {}", valueStr, fieldType, e);
        }

        return keyNodes;
    }

    /**
     * 将解析后的值转换为JSONObject
     *
     * @param parsedValue 解析后的值
     * @param fieldType   字段类型
     * @return JSONObject
     */
    private JSONObject convertToJsonObject(Object parsedValue, Integer fieldType) {
        if (parsedValue == null) {
            return null;
        }

        try {
            if (parsedValue instanceof JSONObject) {
                return (JSONObject) parsedValue;
            } else if (parsedValue instanceof Map) {
                // 将Map转换为JSONObject
                return new JSONObject((Map<String, Object>) parsedValue);
            } else if (Objects.equals(fieldType, FieldTypeEnum.PROCESSED_SIGNAL.getCode())) {
                // 处理后信号类型，如果解析结果是Map，包装在一个外层对象中
                if (parsedValue instanceof Map) {
                    JSONObject wrapper = new JSONObject();
                    wrapper.put("key", parsedValue);
                    return wrapper;
                }
            }

            // 尝试将字符串解析为JSON
            if (parsedValue instanceof String) {
                return JSON.parseObject((String) parsedValue);
            }

        } catch (Exception e) {
            log.debug("转换为JSONObject失败: {}", parsedValue, e);
        }

        return null;
    }


    /**
     * 从JSONObject构建KeyNode树结构
     *
     * @param jsonObject JSON对象
     * @return KeyNode列表
     */
    private List<DataContentFieldDTO.KeyNode> buildKeyNodesFromJsonObject(JSONObject jsonObject) {
        List<DataContentFieldDTO.KeyNode> keyNodes = new ArrayList<>();

        if (jsonObject == null) {
            return keyNodes;
        }

        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            DataContentFieldDTO.KeyNode keyNode = new DataContentFieldDTO.KeyNode();
            keyNode.setKey(key);

            // 递归处理子节点
            List<DataContentFieldDTO.KeyNode> children = parseValueToKeyNodes(value);
            keyNode.setChildren(children);

            keyNodes.add(keyNode);
        }

        return keyNodes;
    }

    /**
     * 递归解析值构建子KeyNode
     *
     * @param value JSON值
     * @return 子KeyNode列表
     */
    private List<DataContentFieldDTO.KeyNode> parseValueToKeyNodes(Object value) {
        List<DataContentFieldDTO.KeyNode> children = new ArrayList<>();

        if (value instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) value;
            for (String key : jsonObject.keySet()) {
                DataContentFieldDTO.KeyNode childNode = new DataContentFieldDTO.KeyNode();
                childNode.setKey(key);

                // 递归处理更深层的子节点
                List<DataContentFieldDTO.KeyNode> grandChildren = parseValueToKeyNodes(jsonObject.get(key));
                childNode.setChildren(grandChildren);

                children.add(childNode);
            }
        }
        // 对于数组类型，可以考虑是否需要特殊处理
        // 目前暂不处理数组内部的对象结构

        return children;
    }


    /**
     * 通过Lion配置的正则表达式匹配数据类型
     */
    private Integer matchByLionRegex(String value) {
        try {
            String configValue = Lion.getString(ConfigUtil.getAppkey(), LION_FIELD_TYPE_REGEX_MAPPING_KEY);
            if (StringUtils.isBlank(configValue)) {
                return null;
            }

            Map<String, String> regexMapping = JSON.parseObject(configValue, new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isEmpty(regexMapping)) {
                return null;
            }

            for (Map.Entry<String, String> entry : regexMapping.entrySet()) {
                String typeCode = entry.getKey();
                String regex = entry.getValue();

                if (StringUtils.isNotBlank(regex) && Pattern.matches(regex, value)) {
                    return Integer.parseInt(typeCode);
                }
            }
        } catch (Exception e) {
            log.warn("Lion配置正则匹配失败", e);
        }

        return null;
    }

    /**
     * 检查是否为JSON格式
     */
    private boolean isJsonFormat(String value) {
        try {
            jsonFieldStrategy.validateValue(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为处理后信号格式
     */
    private boolean isProcessedSignalFormat(String value) {
        try {
            processedSignalFieldStrategy.validateValue(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取字段的采样值
     *
     * @param columnName 字段名称
     * @param dataList   数据列表
     * @return 采样值列表
     */
    private List<String> getSampleValues(String columnName, List<DataSetRecordParam> dataList) {
        List<String> allValues = dataList.stream()
                .filter(Objects::nonNull)
                .map(DataSetRecordParam::getContent)
                .filter(Objects::nonNull)
                .map(content -> content.get(columnName))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allValues)) {
            return Collections.emptyList();
        }

        // 根据数据量决定采样策略
        if (allValues.size() <= MIN_DATA_SIZE_FOR_MIDDLE_SAMPLE) {
            // 数据量小于20条时，取前10条数据
            return allValues.stream()
                    .limit(SAMPLE_SIZE)
                    .collect(Collectors.toList());
        } else {
            // 数据量大于20条时，取中间10条数据
            int startIndex = (allValues.size() - SAMPLE_SIZE) / 2;
            int endIndex = startIndex + SAMPLE_SIZE;
            return allValues.subList(startIndex, Math.min(endIndex, allValues.size()));
        }
    }

    /**
     * 从Lion获取通用字段配置
     *
     * @return 通用字段集合
     */
    private Set<String> getCommonFieldsFromLion() {
        try {
            String commonFieldsConfig = Lion.getString(ConfigUtil.getAppkey(), LionConstant.LION_COMMON_FIELDS_KEY);
            if (StringUtils.isBlank(commonFieldsConfig)) {
                log.info("Lion配置[{}]为空，返回空的通用字段集合", LionConstant.LION_COMMON_FIELDS_KEY);
                return Collections.emptySet();
            }

            Set<String> commonFields = Arrays.stream(commonFieldsConfig.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            log.info("从Lion获取通用字段配置成功，字段数量：{}", commonFields.size());
            return commonFields;
        } catch (Exception e) {
            log.warn("从Lion获取通用字段配置失败", e);
            return Collections.emptySet();
        }
    }

    /**
     * 构建数据集DTO
     *
     * @param dataSet 数据集对象
     * @return 数据集DTO对象
     */
    private DataSetDTO builderDataSetDTO(DataSet dataSet) {
        if (Objects.isNull(dataSet)) {
            return null;
        }
        DataSetDTO dataSetDTO = new DataSetDTO();
        try {
            dataSetDTO.setId(dataSet.getId());
            dataSetDTO.setName(dataSet.getName());
            if (Objects.nonNull(dataSet.getLatestVersionId())) {
                dataSetDTO.setLatestVersionId(dataSet.getLatestVersionId());
                DataSetVersion dataSetVersion = dataSetVersionRepository.getById(dataSet.getLatestVersionId());
                dataSetDTO.setLatestVersionName(dataSetVersion.getVersionName());
            }
            // 判断数据集是否发布过
            boolean publishStatus = isPublishStatus(dataSet);
            // 数据集发布状态
            if (publishStatus) {
                dataSetDTO.setPublishStatus(DatasetPublishStatusEnum.PUBLISHED.getCode());
            } else {
                dataSetDTO.setPublishStatus(DatasetPublishStatusEnum.UN_PUBLISHED.getCode());
            }
            dataSetDTO.setStatus(dataSet.getDataSetStatus());
            dataSetDTO.setCreatorMis(dataSet.getCreatorMis());
            dataSetDTO.setCreatorName(dataSet.getCreatorName());
            dataSetDTO.setUsageCategory(dataSet.getUsageCategory());
            dataSetDTO.setDataSource(dataSet.getDataSource());
            dataSetDTO.setDataCount(dataSet.getDataCount());
            dataSetDTO.setCreateTime(DateUtil.formatDate(dataSet.getCreateTime(), DATE_PATTERN_YYYY_MM_DD));
            dataSetDTO.setUpdateTime(DateUtil.formatDate(dataSet.getUpdateTime(), DATE_PATTERN_YYYY_MM_DD));
        } catch (Exception e) {
            log.error("日期转换失败，数据集id:{}", dataSet.getId(), e);
        }
        return dataSetDTO;
    }

    /**
     * 判断数据集是否发布
     *
     * @param dataSet 数据集对象
     * @return true-已发布，false-未发布
     */
    private boolean isPublishStatus(DataSet dataSet) {
        List<DataSetVersion> versions = dataSetVersionRepository.listVersionByDataSetId(dataSet.getId());
        return versions.stream()
                .filter(Objects::nonNull)
                .anyMatch(version -> Objects.equals(version.getVersionStatus(), DatasetVersionStatusEnum.PUBLISHED.getCode()));
    }

    /**
     * 构建数据集ES索引
     *
     * @param esIndex ES索引记录
     */
    private DataSetRecordDTO convertToDataSetRecordDTO(DatasetEsIndex esIndex, String headConfig) {
        DataSetRecordDTO recordDTO = new DataSetRecordDTO();
        recordDTO.setId(esIndex.getDocumentId());
        recordDTO.setCreateTime(DateUtil.formatDate(esIndex.getCreateTime(), DATE_PATTERN_YYYY_MM_DD));
        recordDTO.setUpdateTime(DateUtil.formatDate(esIndex.getUpdateTime(), DATE_PATTERN_YYYY_MM_DD));
        try {
            DatasetHeadConfig datasetHeadConfig = JSON.parseObject(headConfig,
                    new TypeReference<DatasetHeadConfig>() {
                    });

            // 构建完整的数据视图（字段名 -> 值）
            Map<String, Object> allData = DatasetFieldUtils.convertEsIndexToRowData(esIndex, datasetHeadConfig);

            // 按照表头配置的顺序构建最终的内容Map
            Map<String, Object> content = new LinkedHashMap<>();
            List<DataContentFieldDTO> headList = datasetHeadConfig.getHeadList();

            for (DataContentFieldDTO field : headList) {
                String columnName = field.getColumnName();
                Object value = allData.get(columnName);
                DataFieldStrategy strategy = dataFieldStrategyFactory.getStrategy(field.getFieldType());
                if (value != null) {
                    String strValue = strategy.serializeFieldValue(value);
                    content.put(columnName, strValue);
                } else {
                    // 如果没有对应的值，放入空字符串以保持字段完整性
                    content.put(columnName, "-");
                }
            }
            recordDTO.setContent(JSONObject.toJSONString(content));
        } catch (Exception e) {
            log.debug("构建数据集ES索引失败, headConfig: {}", headConfig, e);
        }
        return recordDTO;
    }

    /**
     * 数据内容字段DTO转换为数据源字段
     *
     * @param dataContentFieldDTO 数据内容字段DTO对象
     * @return 返回转换后的数据源字段对象
     */
    private DataSourceField convertToDataSourceField(DataContentFieldDTO dataContentFieldDTO, Map<String, FieldMappingDTO> fieldTypeMap) {
        DataSourceField dataSourceField = new DataSourceField();
        dataSourceField.setFieldName(dataContentFieldDTO.getColumnName());
        dataSourceField.setFieldType(dataContentFieldDTO.getFieldType());
        dataSourceField.setFieldTypeName(FieldTypeEnum.getDescriptionByCode(dataContentFieldDTO.getFieldType()));
        if (fieldTypeMap.containsKey(dataContentFieldDTO.getColumnName())) {
            dataSourceField.setChildren(fieldTypeMap.get(dataContentFieldDTO.getColumnName()).getFirstLevelJsonKey());
        }
        return dataSourceField;
    }

    private static void checkUpdateFieldParam(UpdateFieldParam updateFieldParam) {
        CheckUtil.paramCheck(Objects.nonNull(updateFieldParam), "更新字段参数不能为空");
        CheckUtil.paramCheck(Objects.nonNull(updateFieldParam.getDataSetId()), "数据集ID不能为空");
        CheckUtil.paramCheck(Objects.nonNull(updateFieldParam.getVersionId()), "数据集版本ID不能为空");
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(updateFieldParam.getHeadList()), "字段列表不能为空");
    }

    /**
     * 更新ES字段和数据
     *
     * @param dataSet                  数据集对象
     * @param dataSetVersion           数据集版本对象
     * @param originalHeadConfig       原始字段配置
     * @param updatedHeadConfig        更新后的字段配置
     * @param needRecalculateEsMapping 是否需要重新计算ES映射
     */
    private void updateEsFieldAndData(DataSet dataSet, DataSetVersion dataSetVersion, DatasetHeadConfig originalHeadConfig, String updatedHeadConfig, boolean needRecalculateEsMapping) {
        String dataSource = DataSourceEnum.getValueByCode(dataSet.getDataSource());
        String indexName = dataSetVersion.getEsIndexName();

        if (StringUtils.isBlank(indexName)) {
            log.warn("数据集版本未关联ES索引，无需更新ES, dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
            return;
        }

        DatasetHeadConfig newHeadConfig = JSONObject.parseObject(updatedHeadConfig, DatasetHeadConfig.class);

        // 构建查询条件

        DataSetInvokeEs.ConditionResult result = dataSetInvokeEs.buildScrollCondition(dataSet, dataSetVersion, null, null);
        DatasetEsQueryCondition condition = result.getCondition();

        // 分批查询并更新
        boolean hasMore = true;
        int processedRecords = 0;

        while (hasMore) {
            DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);
            List<DatasetEsIndex> records = pageResult.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            // 批量重新组织数据
            for (DatasetEsIndex record : records) {
                //  判断是否需要重构记录数据
                if (needRecalculateEsMapping) {
                    reconstructRecordData(record, originalHeadConfig, newHeadConfig);
                }
                record.setFieldMapping(updatedHeadConfig);
                record.setUpdateTime(new Date());
            }

            // 批量更新ES(用insert接口直接替换原有的值)
            BatchResult batchResult = datasetEsIndexService.batchInsertDatasets(records, dataSource, indexName);
            if (!batchResult.isAllSuccess()) {
                log.error("更新ES字段映射失败，dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
            }

            processedRecords += records.size();
            log.debug("已处理记录数: {}", processedRecords);

            // 检查是否有更多数据
            hasMore = pageResult.getHasNext() != null && pageResult.getHasNext();
            if (hasMore) {
                condition.setScrollId(pageResult.getScrollId());
            }
        }

        log.info("ES字段映射和数据重构完成，总处理记录数: {}", processedRecords);
    }

    /**
     * 重构ES记录的数据存储结构
     *
     * @param record             ES记录
     * @param originalHeadConfig 原始表头配置JSON
     * @param newHeadConfig      新的表头配置
     */
    private void reconstructRecordData(DatasetEsIndex record, DatasetHeadConfig originalHeadConfig, DatasetHeadConfig newHeadConfig) {
        // 1. 构建原始数据的完整视图（字段名 -> 值）
        Map<String, Object> originalDataView = DatasetFieldUtils.convertEsIndexToRowData(record, originalHeadConfig);

        // 2. 根据新的配置重新组织数据
        Map<String, Object> newTextData = new HashMap<>();
        Map<String, Object> newCommonData = new HashMap<>();
        Map<String, Object> newFlattenedData = new HashMap<>();

        // 3. 按照新的字段配置重新分配数据
        for (DataContentFieldDTO field : newHeadConfig.getHeadList()) {
            String fieldName = field.getColumnName();
            Object value = originalDataView.get(fieldName);

            String esKey = field.getEsKey();
            String esColumnName = field.getEsColumnName();
            if (value != null) {
                // 根据esKey将数据放到对应的存储区域
                if (ES_COMMON_DATA_PREFIX.equals(esKey)) {
                    newCommonData.put(esColumnName, value);
                } else if (ES_FLATTEN_PREFIX.equals(esKey)) {
                    // 扁平化数据通常存储复杂对象，保持原有格式
                    value = convertJSON(value);
                    newFlattenedData.put(esColumnName, value);
                } else {
                    // 文本数据需要转换为字符串格式
                    String stringValue = convertValueToString(value);
                    newTextData.put(esColumnName, stringValue);
                }
            }
        }

        // 4. 更新记录的数据结构
        record.setTextData(newTextData);
        record.setCommonData(newCommonData);
        record.setFlattenedData(newFlattenedData);
    }

    private static Object convertJSON(Object value) {
        if (value instanceof String) {
            try {
                value = JSON.parse(value.toString());
            } catch (Exception e) {
                log.warn("解析JSON字符串失败，使用原始字符串: {}", value);
            }
        }
        return value;
    }

    /**
     * 获取表头配置
     *
     * @param dataSet          数据集
     * @param dataSetVersion   数据集版本
     * @param changedFieldList 变更字段列表
     * @return 表头配置结果
     */
    private HeadConfigResult getHeadConfig(DataSet dataSet, DataSetVersion dataSetVersion, List<DataSourceField> changedFieldList) {
        DatasetHeadConfig headConfig = JSONObject.parseObject(dataSetVersion.getHeadConfig(), DatasetHeadConfig.class);

        // 分批校验所有字段类型,并返回所有JSON树结构
        Map<String, List<DataContentFieldDTO.KeyNode>> jsonTreeNode = validateFieldTypesInBatches(dataSet, dataSetVersion, changedFieldList);

        Set<String> commonFields = getCommonFieldsFromLion();
        DatasetHeadConfig newHeadConfig = new DatasetHeadConfig();

        // 创建变更字段的快速查找Map
        Map<String, DataSourceField> changedFieldMap = changedFieldList.stream()
                .filter(field -> StringUtils.isNotBlank(field.getFieldName()) && Objects.nonNull(field.getFieldType()))
                .collect(Collectors.toMap(DataSourceField::getFieldName, Function.identity()));

        boolean needRecalculateEsMapping = false;

        // 直接操作原始的headList来保持字段顺序
        List<DataContentFieldDTO> headList = headConfig.getHeadList();
        for (DataContentFieldDTO existingField : headList) {
            String fieldName = existingField.getColumnName();

            // 检查当前字段是否需要更新
            DataSourceField changedField = changedFieldMap.get(fieldName);
            if (changedField != null) {
                Integer newFieldType = changedField.getFieldType();
                if (!commonFields.contains(fieldName)) {
                    // 旧的存储类型
                    String oldEsKey = existingField.getEsKey();
                    // 新的存储类型
                    String newEsKey = getEsKeyByFieldType(newFieldType);
                    if (!Objects.equals(oldEsKey, newEsKey)) {
                        log.info("检测到字段[{}]存储区域变更: {} -> {}, 需要重新计算ES映射",
                                fieldName, oldEsKey, newEsKey);
                        needRecalculateEsMapping = true;
                    }
                }

                Integer oldType = existingField.getFieldType();
                // 更新字段类型
                existingField.setFieldType(newFieldType);
                log.info("字段类型已更新, fieldName:{}, oldType:{}, newType:{}",
                        fieldName, oldType, newFieldType);
            }
        }

        // 直接使用原始的headList，保持字段顺序
        newHeadConfig.setHeadList(headList);

        // 如果有字段类型变更导致存储位置变更，重新计算所有字段的ES映射
        if (needRecalculateEsMapping) {
            recalculateEsFieldMapping(newHeadConfig, jsonTreeNode);
        }

        return new HeadConfigResult(newHeadConfig, needRecalculateEsMapping);
    }

    /**
     * 分批校验字段类型并获取JSON样本数据
     * 分批查询数据，每批数据都对所有变更字段进行校验
     * 只有所有批次都校验通过才返回，否则抛出异常
     *
     * @return 返回一个Map，键为字段名，值为JSON树结构
     */
    private Map<String, List<DataContentFieldDTO.KeyNode>> validateFieldTypesInBatches(DataSet dataSet, DataSetVersion dataSetVersion, List<DataSourceField> changedFieldList) {
        Map<String, List<DataContentFieldDTO.KeyNode>> jsonSamples = new HashMap<>();
        if (CollectionUtils.isEmpty(changedFieldList)) {
            return jsonSamples;
        }

        // 构建查询条件
        DataSetInvokeEs.ConditionResult result = dataSetInvokeEs.buildScrollCondition(dataSet, dataSetVersion, null, null);
        DatasetEsQueryCondition condition = result.getCondition();
        boolean hasMore = true;
        int batchCount = 0;
        int totalRecords = 0;

        log.info("开始分批校验字段类型, dataSetId:{}, versionId:{}, 变更字段数:{}",
                dataSet.getId(), dataSetVersion.getId(), changedFieldList.size());

        while (hasMore) {
            batchCount++;
            DatasetPageResult pageResult = datasetEsIndexService.pageQuery(condition);
            List<DatasetEsIndex> records = pageResult.getRecords();

            if (CollectionUtils.isEmpty(records)) {
                // 首次查询为空，说明没有数据，跳过校验
                if (batchCount == FIRST_BATCH) {
                    log.warn("无数据需要校验, dataSetId:{}, versionId:{}", dataSet.getId(), dataSetVersion.getId());
                    return jsonSamples;
                }
                break;
            }

            totalRecords += records.size();
            log.info("正在校验第{}批数据，当前批次记录数:{}, 累计记录数:{}", batchCount, records.size(), totalRecords);

            // 提取当前批次的字段样本进行校验
            Map<String, List<String>> batchFieldSamples = extractBatchFieldSamples(records, dataSetVersion, changedFieldList);

            // 对当前批次的每个变更字段进行校验
            for (DataSourceField field : changedFieldList) {
                String fieldName = field.getFieldName();
                Integer fieldType = field.getFieldType();

                if (StringUtils.isBlank(fieldName) || Objects.isNull(fieldType)) {
                    continue;
                }

                List<String> batchSamples = batchFieldSamples.get(fieldName);
                if (CollectionUtils.isNotEmpty(batchSamples)) {
                    boolean validationPassed = validateFieldType(fieldName, fieldType, batchSamples);
                    if (!validationPassed) {
                        throw new AidaRpcException(String.format("字段类型校验不通过, 字段名:%s,字段类型:%s", fieldName, FieldTypeEnum.getDescriptionByCode(fieldType)));
                    }
                    //  构建JSON树结构，用于后续处理
                    if (Objects.equals(fieldType, FieldTypeEnum.JSON.getCode())) {
                        // 遍历当前批次的样本值，使用parseFieldValueToKeyNodes解析每个值
                        List<DataContentFieldDTO.KeyNode> currentBatchKeys = new ArrayList<>();
                        for (String sampleValue : batchSamples) {
                            if (StringUtils.isNotBlank(sampleValue)) {
                                List<DataContentFieldDTO.KeyNode> keyNodes = parseFieldValueToKeyNodes(sampleValue, fieldType);
                                if (CollectionUtils.isNotEmpty(keyNodes)) {
                                    // 递归合并树结构，而不是简单添加
                                    mergeKeyNodeTrees(currentBatchKeys, keyNodes);
                                }
                            }
                        }

                        // 合并到全局样本中
                        mergeJsonKeys(jsonSamples, fieldName, currentBatchKeys);
                    }
                }
            }

            // 检查是否有更多数据
            hasMore = pageResult.getHasNext() != null && pageResult.getHasNext();
            if (hasMore) {
                condition.setScrollId(pageResult.getScrollId());
            }
        }

        log.info("字段类型分批校验完成，共处理{}批数据，总记录数:{}", batchCount, totalRecords);
        return jsonSamples;
    }

    /**
     * 从当前批次记录中提取字段样本
     */
    private Map<String, List<String>> extractBatchFieldSamples(List<DatasetEsIndex> records, DataSetVersion dataSetVersion, List<DataSourceField> changedFieldList) {
        Map<String, List<String>> batchSamples = new HashMap<>();

        // 只收集变更字段的样本数据
        Set<String> changedFieldNames = changedFieldList.stream()
                .map(DataSourceField::getFieldName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        for (DatasetEsIndex esIndex : records) {
            Map<String, Object> rowData = DatasetFieldUtils.convertEsIndexToRowData(esIndex,
                    JSON.parseObject(dataSetVersion.getHeadConfig(), new TypeReference<DatasetHeadConfig>() {
                    }));

            for (Map.Entry<String, Object> entry : rowData.entrySet()) {
                String fieldName = entry.getKey();
                // 只处理变更的字段
                if (changedFieldNames.contains(fieldName)) {
                    String value = convertValueToString(entry.getValue());
                    batchSamples.computeIfAbsent(fieldName, k -> new ArrayList<>()).add(value);
                }
            }
        }

        return batchSamples;
    }

    /**
     * 根据字段类型获取对应的esKey
     *
     * @param fieldType 字段类型
     * @return esKey
     */
    private String getEsKeyByFieldType(Integer fieldType) {
        if (DatasetQueryConditionUtils.isUseFlattenPath(fieldType)) {
            return ES_FLATTEN_PREFIX;
        }
        return ES_TEXT_PREFIX;
    }

    /**
     * 重新计算ES字段映射
     *
     * @param newHeadConfig     表头配置
     * @param jsonSampleKeyNode JSON样本数据
     */
    private void recalculateEsFieldMapping(DatasetHeadConfig newHeadConfig, Map<String, List<DataContentFieldDTO.KeyNode>> jsonSampleKeyNode) {
        Set<String> commonFields = getCommonFieldsFromLion();

        List<DatasetHeadConfig.HeadConfig> headConfigs = new ArrayList<>();
        // 重新分配segment索引
        int textSegmentIndex = 1;
        int flattenSegmentIndex = 1;

        // 直接操作headList来保持字段顺序
        List<DataContentFieldDTO> headList = newHeadConfig.getHeadList();
        for (DataContentFieldDTO field : headList) {
            String fieldName = field.getColumnName();
            Integer fieldType = field.getFieldType();

            // 通用字段的映射不变
            if (commonFields.contains(fieldName)) {
                field.setEsKey(ES_COMMON_DATA_PREFIX);
                field.setEsColumnName(fieldName);
            }
            // 扁平化类型字段
            else if (DatasetQueryConditionUtils.isUseFlattenPath(fieldType)) {
                field.setEsKey(ES_FLATTEN_PREFIX);
                field.setEsColumnName("segment_" + flattenSegmentIndex);
                field.setStoreType(EsStoreTypeEnum.FLATTEN.getName());

                // JSON类型需要构建简单的key结构
                if (FieldTypeEnum.JSON.getCode().equals(fieldType)) {
                    List<DataContentFieldDTO.KeyNode> keyNodes = jsonSampleKeyNode.get(fieldName);
                    field.setTreeNodes(keyNodes);
                }
                flattenSegmentIndex++;
            }
            // 文本类型字段
            else {
                field.setEsKey(ES_TEXT_PREFIX);
                field.setEsColumnName("segment_" + textSegmentIndex);
                field.setStoreType(EsStoreTypeEnum.TEXT.getName());
                textSegmentIndex++;
            }
        }
        headConfigs.add(new DatasetHeadConfig.HeadConfig(EsStoreTypeEnum.TEXT.getName(), textSegmentIndex));
        headConfigs.add(new DatasetHeadConfig.HeadConfig(EsStoreTypeEnum.FLATTEN.getName(), flattenSegmentIndex));
        newHeadConfig.setHeadConfig(headConfigs);
    }

    /**
     * 校验字段类型是否与样本数据匹配
     *
     * @param fieldName    字段名称
     * @param fieldType    目标字段类型
     * @param sampleValues 样本值列表
     * @return 校验是否通过
     */
    private boolean validateFieldType(String fieldName, Integer fieldType, List<String> sampleValues) {
        if (CollectionUtils.isEmpty(sampleValues)) {
            log.warn("字段[{}]样本数据为空，无法校验", fieldName);
            return false;
        }

        try {
            // 获取对应的字段处理策略
            DataFieldStrategy strategy = dataFieldStrategyFactory.getStrategy(fieldType);

            if (strategy == null) {
                log.warn("未找到字段类型[{}]对应的处理策略", fieldType);
                return false;
            }

            // 校验所有非空样本值
            for (String value : sampleValues) {
                if (StringUtils.isNotBlank(value)) {
                    try {
                        strategy.validateValue(value);
                    } catch (Exception e) {
                        log.warn("字段[{}]值[{}]不符合类型[{}]的校验规则: {}",
                                fieldName, value, fieldType, e.getMessage());
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("字段类型校验异常, fieldName:{}, fieldType:{}", fieldName, fieldType, e);
            return false;
        }
    }

    /**
     * 将值转换为字符串，保持JSON格式不被破坏
     *
     * @param value 原始值
     * @return 转换后的字符串
     */
    private String convertValueToString(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }

        // 如果已经是字符串，直接返回
        if (value instanceof String) {
            return (String) value;
        }

        // 如果是复杂对象（Map、List等），使用JSON序列化保持格式
        if (value instanceof Map || value instanceof List || value instanceof Object[]) {
            try {
                return JSON.toJSONString(value);
            } catch (Exception e) {
                log.warn("将复杂对象转换为JSON字符串失败，使用toString方法: {}", e.getMessage());
                return value.toString();
            }
        }

        // 其他类型直接转换为字符串
        return value.toString();
    }

    /**
     * 校验数据集是否可以删除
     *
     * @param dataSetId 数据集ID
     * @param versions  版本列表
     */
    private void checkDataIsDelete(Long dataSetId, List<DataSetVersion> versions) {
        if (CollectionUtils.isNotEmpty(versions)) {
            List<DataSetVersion> publishedVersions = versions.stream()
                    .filter(Objects::nonNull)
                    .filter(version -> Objects.equals(version.getVersionStatus(), DatasetVersionStatusEnum.PUBLISHED.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(publishedVersions)) {
                throw new AidaRpcException("当前数据集存在已发布的版本，不允许删除");
            }
        }

        //  查询数据集关联的数据处理任务列表
        List<DataProcessTask> dataProcessTasks = dataProcessTaskRepository.listDataProcessTaskByDataSetIdAndVersionId(dataSetId, null);
        if (CollectionUtils.isNotEmpty(dataProcessTasks)) {
            throw new AidaRpcException("当前数据集关联了数据处理任务，不允许删除");
        }
    }

    /**
     * 合并JSON字段的key，避免重复
     *
     * @param jsonSamples      全局JSON样本集合
     * @param fieldName        字段名
     * @param currentBatchKeys 当前批次的key列表
     */
    private void mergeJsonKeys(Map<String, List<DataContentFieldDTO.KeyNode>> jsonSamples, String fieldName, List<DataContentFieldDTO.KeyNode> currentBatchKeys) {
        if (jsonSamples.containsKey(fieldName)) {
            // 已存在该字段，需要合并key并去重
            List<DataContentFieldDTO.KeyNode> existingKeys = jsonSamples.get(fieldName);

            // 构建已存在key的Set，用于快速查找
            Set<String> existingKeyNames = existingKeys.stream()
                    .map(DataContentFieldDTO.KeyNode::getKey)
                    .collect(Collectors.toSet());

            // 只添加新的key，避免重复
            for (DataContentFieldDTO.KeyNode newKey : currentBatchKeys) {
                if (!existingKeyNames.contains(newKey.getKey())) {
                    existingKeys.add(newKey);
                    existingKeyNames.add(newKey.getKey()); // 同时更新Set以提高后续查找效率
                }
            }
        } else {
            // 首次添加该字段，直接存储
            jsonSamples.put(fieldName, new ArrayList<>(currentBatchKeys));
        }
    }

    /**
     * 递归合并树结构
     *
     * @param target 目标列表
     * @param source 源列表
     */
    private void mergeKeyNodeTrees(List<DataContentFieldDTO.KeyNode> target, List<DataContentFieldDTO.KeyNode> source) {
        for (DataContentFieldDTO.KeyNode sourceNode : source) {
            boolean added = false;
            for (DataContentFieldDTO.KeyNode targetNode : target) {
                if (sourceNode.getKey().equals(targetNode.getKey())) {
                    // 如果目标节点已经存在，则递归合并子节点
                    mergeKeyNodeTrees(targetNode.getChildren(), sourceNode.getChildren());
                    added = true;
                    break;
                }
            }
            if (!added) {
                target.add(sourceNode);
            }
        }
    }
}
