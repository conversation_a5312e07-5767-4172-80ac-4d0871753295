package com.meituan.aigc.aida.data.management.dataset.strategy.task.impl;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetVersion;
import com.meituan.aigc.aida.data.management.dataset.dto.DataSourceField;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateResponseParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetParseHeadParam;
import com.meituan.aigc.aida.data.management.dataset.param.DatasetHeadConfig;
import com.meituan.aigc.aida.data.management.dataset.util.FileParserUtil;
import com.meituan.aigc.aida.labeling.exception.AidaTrainingCheckException;
import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
import com.meituan.aigc.aida.labeling.remote.common.enums.FieldTypeEnum;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataSetRecordParam;
import com.meituan.aigc.aida.labeling.strategy.task.create.CreateTaskStrategyFactory;
import com.meituan.aigc.aida.labeling.util.CheckUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户上传数据集任务处理策略
 */
@Slf4j
@Component
public class UploadDataSetProcessStrategy extends AbstractDataSetProcessStrategy {

    @Autowired
    private CreateTaskStrategyFactory createTaskStrategyFactory;

    @Override
    public Integer getStrategyType() {
        return DataSourceEnum.UPLOAD.getCode();
    }

    @Override
    public DataSetCreateResponseParam createDataSet(DataSetCreateParam param) {
        log.info("开始处理上传文件数据集任务, param: {}", param);

        // 创建数据集
        DataSet dataSet = createNewDataSet(param);
        Long dataSetId = dataSet.getId();

        DataSetVersion dataSetVersion = createDataSetVersion(dataSetId, param);

        // 使用入参中的表头解析文件，只获取数据内容
        List<DataSetRecordParam> dataList = FileParserUtil.parseFileWithHeaders(param.getFile(), param.getHeadList());

        // 将解析的数据保存到参数中，以便分批处理
        param.setDataList(dataList);

        // 调用通用的保存方法
        commonSaveDataInBatches(param, dataSet, dataSetVersion);

        // 调用数据集管理服务创建数据集
        return new DataSetCreateResponseParam(dataSetId, null);
    }

    /**
     * 解析表头
     *
     * @param param 解析参数
     * @return 表头字段列表
     */
    @Override
    public List<DataFieldParam> parseHeader(DataSetParseHeadParam param) {
        MultipartFile file = param.getFile();
        CheckUtil.paramCheck(file != null && !file.isEmpty(), "上传文件不能为空");

        // 获取表头信息
        List<String> allHeadList = FileParserUtil.parseHeaders(file);
        CheckUtil.paramCheck(CollectionUtils.isNotEmpty(allHeadList), "上传文件表头不能为空");

        List<DataFieldParam> headList = new ArrayList<>();
        for (String head : allHeadList) {
            DataFieldParam dataFieldParam = new DataFieldParam();
            dataFieldParam.setColumnName(head);
            headList.add(dataFieldParam);
        }

        // 使用入参中的表头解析文件，只获取数据内容
        List<DataSetRecordParam> dataList = FileParserUtil.parseFileWithHeaders(param.getFile(), headList);

        // 推断每个表头的数据类型
        inferHeadTypeInBatches(dataList, headList, param.getFetchId());

        return headList;
    }
}