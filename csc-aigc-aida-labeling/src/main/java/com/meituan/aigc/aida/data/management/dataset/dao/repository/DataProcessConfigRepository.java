package com.meituan.aigc.aida.data.management.dataset.dao.repository;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessConfigWithBLOBs;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-26
 * @description 数据处理配置仓储层
 */
public interface DataProcessConfigRepository {

    /**
     * 根据ID获取数据处理配置
     */
    DataProcessConfigWithBLOBs getDataProcessConfigById(Long configId);

    /**
     * 根据任务ID获取数据处理配置列表
     */
    List<DataProcessConfigWithBLOBs> listDataProcessConfigByTaskId(Long taskId);

    /**
     * 创建新数据处理配置
     */
    Long createDataProcessConfig(DataProcessConfigWithBLOBs dataProcessConfig);

    /**
     * 更新数据处理配置
     */
    void updateDataProcessConfig(DataProcessConfigWithBLOBs dataProcessConfig);
} 