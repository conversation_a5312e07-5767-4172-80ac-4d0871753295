package com.meituan.aigc.aida.data.management.dataset.dao.repository.impl;

import com.meituan.aigc.aida.data.management.dataset.dao.mapper.DataProcessConfigMapper;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessConfigExample;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataProcessConfigWithBLOBs;
import com.meituan.aigc.aida.data.management.dataset.dao.repository.DataProcessConfigRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-26
 * @description 数据处理配置仓储层实现类
 */
@Repository
public class DataProcessConfigRepositoryImpl implements DataProcessConfigRepository {

    @Resource
    private DataProcessConfigMapper dataProcessConfigMapper;

    @Override
    public DataProcessConfigWithBLOBs getDataProcessConfigById(Long configId) {
        if (Objects.isNull(configId)) {
            return null;
        }
        return dataProcessConfigMapper.selectByPrimaryKey(configId);
    }

    @Override
    public List<DataProcessConfigWithBLOBs> listDataProcessConfigByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }
        DataProcessConfigExample example = new DataProcessConfigExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        example.setOrderByClause("create_time desc");
        return dataProcessConfigMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public Long createDataProcessConfig(DataProcessConfigWithBLOBs dataProcessConfig) {
        dataProcessConfigMapper.insertSelective(dataProcessConfig);
        return dataProcessConfig.getId();
    }

    @Override
    public void updateDataProcessConfig(DataProcessConfigWithBLOBs dataProcessConfig) {
        dataProcessConfigMapper.updateByPrimaryKeySelective(dataProcessConfig);
    }
} 