package com.meituan.aigc.aida.labeling.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * 检查项模版实体类
 */
@Data
public class InspectionItemTemplate {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 模版名称
     */
    private String templateName;
    
    /**
     * 模版描述
     */
    private String templateDesc;
    
    /**
     * 模版类型 1:标注模版 2:质检模版
     */
    private Integer templateType;
    
    /**
     * 是否删除: 0-否, 1-是
     */
    private Boolean isDeleted;
    
    /**
     * 租户id
     */
    private String tenantId;
    
    /**
     * 创建人MIS
     */
    private String createMis;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改人MIS
     */
    private String updateMis;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 