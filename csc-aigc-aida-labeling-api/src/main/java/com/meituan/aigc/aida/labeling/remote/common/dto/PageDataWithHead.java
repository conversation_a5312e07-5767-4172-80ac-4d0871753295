package com.meituan.aigc.aida.labeling.remote.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 分页查询结果——带表头
 *
 * <AUTHOR>
 * @param <V> 表头数据
 * @param <T> 内容数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageDataWithHead<V, T> extends PageData<T> implements Serializable {

    /**
     * 表头列表
     */
    private List<V> headList;
}
