package com.meituan.aigc.aida.data.management.fetch.dto.hive.signal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 信号埋点toHive-DTO类
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignalLoggingToHiveDTO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 弹屏AC ID
     */
    private String popupAcId;

    /**
     * 应用入参
     */
    private String inputs;

    /**
     * 应用出参
     */
    private String outputs;

    /**
     * 消息发送时间, 毫秒时间戳
     */
    private String messageOccurredTime;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 更新时间
     */
    private String updatedAt;

    /**
     * 智能侧SessionId
     */
    private String sessionId;

    /**
     * 标问ID列表
     */
    private String typicalQuestionIds;

    /**
     * 联系类型，online: 在线渠道, call: 电话渠道
     */
    private String contactType;

    /**
     * 联络id，在线: 转人工chatId, 热线: 热线联络id
     */
    private String contactId;

    /**
     * 客服MIS
     */
    private String staffMis;

    /**
     * 消息来源: STAFF: 客服发送的消息, CUSTOMER: 用户发送的消息
     */
    private String chatMessageFromType;

    /**
     * 客服发送的消息内容
     */
    private String staffMessageContent;

    /**
     * 用户发送的消息内容
     */
    private String customerMessageContent;

    /**
     * 业务消息ID，用于标识一条消息的唯一业务消息ID，比如在线的海豚消息ID
     */
    private String bizMessageId;

    /**
     * 扩展字段
     * @see SignalLoggingCommonInfoDTO
     */
    private String commonInfo;
}
