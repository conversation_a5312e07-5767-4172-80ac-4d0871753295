<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.QualityCheckStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="stat_date" jdbcType="DATE" property="statDate" />
    <result column="biz_type" jdbcType="BIGINT" property="bizType" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="quality_checker_name" jdbcType="VARCHAR" property="qualityCheckerName" />
    <result column="quality_checker_mis" jdbcType="VARCHAR" property="qualityCheckerMis" />
    <result column="quality_check_count" jdbcType="INTEGER" property="qualityCheckCount" />
    <result column="quality_check_correct_count" jdbcType="INTEGER" property="qualityCheckCorrectCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, stat_date, biz_type, task_type, data_type, quality_checker_name, quality_checker_mis, 
    quality_check_count, quality_check_correct_count, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatisticsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from quality_check_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from quality_check_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from quality_check_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatisticsExample">
    delete from quality_check_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics">
    insert into quality_check_statistics (id, stat_date, biz_type, 
      task_type, data_type, quality_checker_name, 
      quality_checker_mis, quality_check_count, quality_check_correct_count, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{statDate,jdbcType=DATE}, #{bizType,jdbcType=BIGINT},
      #{taskType,jdbcType=TINYINT}, #{dataType,jdbcType=TINYINT}, #{qualityCheckerName,jdbcType=VARCHAR}, 
      #{qualityCheckerMis,jdbcType=VARCHAR}, #{qualityCheckCount,jdbcType=INTEGER}, #{qualityCheckCorrectCount,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics">
    insert into quality_check_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="statDate != null">
        stat_date,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="qualityCheckerName != null">
        quality_checker_name,
      </if>
      <if test="qualityCheckerMis != null">
        quality_checker_mis,
      </if>
      <if test="qualityCheckCount != null">
        quality_check_count,
      </if>
      <if test="qualityCheckCorrectCount != null">
        quality_check_correct_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="statDate != null">
        #{statDate,jdbcType=DATE},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="qualityCheckerName != null">
        #{qualityCheckerName,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckerMis != null">
        #{qualityCheckerMis,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckCount != null">
        #{qualityCheckCount,jdbcType=INTEGER},
      </if>
      <if test="qualityCheckCorrectCount != null">
        #{qualityCheckCorrectCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatisticsExample" resultType="java.lang.Long">
    select count(*) from quality_check_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update quality_check_statistics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.statDate != null">
        stat_date = #{record.statDate,jdbcType=DATE},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=BIGINT},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=TINYINT},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.qualityCheckerName != null">
        quality_checker_name = #{record.qualityCheckerName,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityCheckerMis != null">
        quality_checker_mis = #{record.qualityCheckerMis,jdbcType=VARCHAR},
      </if>
      <if test="record.qualityCheckCount != null">
        quality_check_count = #{record.qualityCheckCount,jdbcType=INTEGER},
      </if>
      <if test="record.qualityCheckCorrectCount != null">
        quality_check_correct_count = #{record.qualityCheckCorrectCount,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update quality_check_statistics
    set id = #{record.id,jdbcType=BIGINT},
      stat_date = #{record.statDate,jdbcType=DATE},
      biz_type = #{record.bizType,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=TINYINT},
      data_type = #{record.dataType,jdbcType=TINYINT},
      quality_checker_name = #{record.qualityCheckerName,jdbcType=VARCHAR},
      quality_checker_mis = #{record.qualityCheckerMis,jdbcType=VARCHAR},
      quality_check_count = #{record.qualityCheckCount,jdbcType=INTEGER},
      quality_check_correct_count = #{record.qualityCheckCorrectCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics">
    update quality_check_statistics
    <set>
      <if test="statDate != null">
        stat_date = #{statDate,jdbcType=DATE},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="qualityCheckerName != null">
        quality_checker_name = #{qualityCheckerName,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckerMis != null">
        quality_checker_mis = #{qualityCheckerMis,jdbcType=VARCHAR},
      </if>
      <if test="qualityCheckCount != null">
        quality_check_count = #{qualityCheckCount,jdbcType=INTEGER},
      </if>
      <if test="qualityCheckCorrectCount != null">
        quality_check_correct_count = #{qualityCheckCorrectCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.QualityCheckStatistics">
    update quality_check_statistics
    set stat_date = #{statDate,jdbcType=DATE},
      biz_type = #{bizType,jdbcType=BIGINT},
      task_type = #{taskType,jdbcType=TINYINT},
      data_type = #{dataType,jdbcType=TINYINT},
      quality_checker_name = #{qualityCheckerName,jdbcType=VARCHAR},
      quality_checker_mis = #{qualityCheckerMis,jdbcType=VARCHAR},
      quality_check_count = #{qualityCheckCount,jdbcType=INTEGER},
      quality_check_correct_count = #{qualityCheckCorrectCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="queryDailyTrends" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.DailyBizStatisticsVO" parameterType="com.meituan.aigc.aida.labeling.pojo.dto.dashboard.DailyStatisticsDTO">
    SELECT
      biz_type as bizTypeId,
      DATE_FORMAT(stat_date, '%m/%d') as date,
      COALESCE(SUM(quality_check_count), 0) as dailyInspectedCount
    FROM
      quality_check_statistics
    <where>
      <if test="param.startDate != null">
        AND stat_date >= #{param.startDate,jdbcType=DATE}
      </if>
      <if test="param.endDate != null">
        AND stat_date &lt;= #{param.endDate,jdbcType=DATE}
      </if>
      <if test="param.taskType != null">
        AND task_type = #{param.taskType,jdbcType=TINYINT}
      </if>
      <if test="param.dataType != null">
        AND data_type = #{param.dataType,jdbcType=TINYINT}
      </if>
      <if test="param.bizTypes != null and param.bizTypes.size() > 0">
        AND biz_type IN
        <foreach item="bizType" collection="param.bizTypes" open="(" separator="," close=")">
          #{bizType}
        </foreach>
      </if>
    </where>
    GROUP BY
      stat_date, biz_type
  </select>
  <select id="statisticalQualityCheckData" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.LabelingAndCheckCountVO">
    SELECT
      COUNT(DISTINCT statistics.quality_checker_mis) AS peopleNum,
      COALESCE(SUM(statistics.quality_check_count), 0) AS quantity
    FROM
      quality_check_statistics AS statistics
    <where>
      <if test="query.bizTypes != null">
        statistics.biz_type IN
        <foreach collection="query.bizTypes" item="bizType" open="(" separator="," close=")">
          #{bizType}
        </foreach>
      </if>
      <if test="query.taskType != null">
        AND statistics.task_type = #{query.taskType}
      </if>
      <if test="query.dataType != null">
        AND statistics.data_type = #{query.dataType}
      </if>
      <if test="query.startDate != null">
        AND statistics.stat_date &gt;= #{query.startDate}
      </if>
      <if test="query.endDate != null">
        AND statistics.stat_date &lt;= #{query.endDate}
      </if>
    </where>
  </select>
  <select id="queryPersonnelDetail" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.PersonnelDetailVO" parameterType="com.meituan.aigc.aida.labeling.pojo.dto.dashboard.PersonnelDetailQuery">
    SELECT
      COUNT(DISTINCT stat_date) AS workingDays,
      quality_checker_name as name,
      quality_checker_mis as userMis,
      biz_type AS bizType,
      COALESCE(SUM(quality_check_count), 0) as totalCount,
      CASE
        WHEN COALESCE(SUM(quality_check_count), 0) > 0
          THEN 100.0 * COALESCE(SUM(quality_check_correct_count), 0) / SUM(quality_check_count)
        ELSE 0
      END AS qualityCheckRate
    FROM
      quality_check_statistics
    <where>
      <if test="query.name != null">
        quality_checker_name = #{query.name}
      </if>
      <if test="query.userMis != null">
        quality_checker_mis = #{query.userMis}
      </if>
      <if test="query.userMisList != null and query.userMisList.size() > 0">
        quality_checker_mis IN
        <foreach item="item" collection="query.userMisList" open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="query.startDate != null">
        AND stat_date &gt;= #{query.startDate,jdbcType=DATE}
      </if>
      <if test="query.endDate != null">
        AND stat_date &lt;= #{query.endDate,jdbcType=DATE}
      </if>
      <if test="query.taskType != null">
        AND task_type = #{query.taskType,jdbcType=TINYINT}
      </if>
      <if test="query.dataType != null">
        AND data_type = #{query.dataType,jdbcType=TINYINT}
      </if>
      <if test="query.bizTypes != null and query.bizTypes.size() > 0">
        AND biz_type IN
        <foreach item="bizType" collection="query.bizTypes" open="(" separator="," close=")">
          #{bizType}
        </foreach>
      </if>
    </where>
    GROUP BY
      quality_checker_name, quality_checker_mis, biz_type
    <if test="query.sortBy != null and query.sortOrder != null">
      ORDER BY
      <choose>
        <when test="query.sortBy == 'totalCount'">totalCount</when>
        <when test="query.sortBy == 'workingDays'">workingDays</when>
        <when test="query.sortBy == 'qualityCheckRate'">qualityCheckRate</when>
        <!-- 其他允许的排序列 -->
        <otherwise>
          total_count <!-- 默认排序列 -->
        </otherwise>
      </choose>
      <choose>
        <when test="query.sortOrder == 'ASC'">ASC</when>
        <when test="query.sortOrder == 'DESC'">DESC</when>
        <otherwise>DESC</otherwise> <!-- 默认排序方向 -->
      </choose>
    </if>
  </select>
  <select id="querySingleBizQualityCheckRateTrends" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.BizQualityCheckQueryVO" parameterType="com.meituan.aigc.aida.labeling.pojo.dto.dashboard.BaseStatisticsDTO">
    SELECT
      DATE_FORMAT(stat_date, '%m/%d') as date,
      COALESCE(SUM(quality_check_correct_count), 0) as totalCorrectCount,
      COALESCE(SUM(quality_check_count), 0) as totalCount
    FROM
      quality_check_statistics
    <where>
      <if test="query.startDate != null">
        AND stat_date &gt;= #{query.startDate,jdbcType=DATE}
      </if>
      <if test="query.endDate != null">
        AND stat_date &lt;= #{query.endDate,jdbcType=DATE}
      </if>
      <if test="query.taskType != null">
        AND task_type = #{query.taskType,jdbcType=TINYINT}
      </if>
      <if test="query.dataType != null">
        AND data_type = #{query.dataType,jdbcType=TINYINT}
      </if>
      <if test="query.bizTypes != null">
        AND biz_type IN
        <foreach item="bizType" collection="query.bizTypes" open="(" separator="," close=")">
          #{bizType}
        </foreach>
      </if>
    </where>
    GROUP BY
      stat_date, biz_type
    ORDER BY
      stat_date
  </select>
  <select id="queryBizQualityCheckRate" resultType="com.meituan.aigc.aida.labeling.pojo.vo.dashboard.BizQualityCheckQueryVO" parameterType="com.meituan.aigc.aida.labeling.pojo.dto.dashboard.BaseStatisticsDTO">
    SELECT
      biz_type as bizType,
      COALESCE(SUM(quality_check_correct_count), 0) as totalCorrectCount,
      COALESCE(SUM(quality_check_count), 0) as totalCount
    FROM
      quality_check_statistics
    <where>
      <if test="query.startDate != null">
        AND stat_date &gt;= #{query.startDate,jdbcType=DATE}
      </if>
      <if test="query.endDate != null">
        AND stat_date &lt;= #{query.endDate,jdbcType=DATE}
      </if>
      <if test="query.taskType != null">
        AND task_type = #{query.taskType,jdbcType=TINYINT}
      </if>
      <if test="query.dataType != null">
        AND data_type = #{query.dataType,jdbcType=TINYINT}
      </if>
      <if test="query.bizTypes != null and query.bizTypes.size() > 0">
        AND biz_type IN
        <foreach item="bizType" collection="query.bizTypes" open="(" separator="," close=")">
          #{bizType}
        </foreach>
      </if>
    </where>
    GROUP BY
      biz_type
  </select>
  <insert id="batchInsert">
    INSERT INTO quality_check_statistics
    (
    stat_date,
    biz_type,
    task_type,
    data_type,
    quality_checker_name,
    quality_checker_mis,
    quality_check_count,
    quality_check_correct_count,
    create_time,
    update_time
    )
    VALUES
    <foreach collection="qualityCheckStatisticsList" item="item" separator=",">
      (
      #{item.statDate},
      #{item.bizType},
      #{item.taskType},
      #{item.dataType},
      #{item.qualityCheckerName},
      #{item.qualityCheckerMis},
      #{item.qualityCheckCount},
      #{item.qualityCheckCorrectCount},
      #{item.createTime},
      #{item.updateTime}
      )
    </foreach>
  </insert>

</mapper>