package com.meituan.aigc.aida.labeling.dao.model;

import lombok.Data;

import java.util.Date;

/**
 * 操作日志表实体类
 */
@Data
public class OperationLogs {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 操作类型(1:新增, 2:修改, 3:删除)
     */
    private Integer operateType;
    
    /**
     * 操作数据ID
     */
    private Long operateDataId;
    
    /**
     * 业务类型(1:标注, 2:转交, 3:质检, 4:编辑数据集数据)
     */
    private Integer bizType;
    
    /**
     * 操作前数据
     */
    private String beforeData;
    
    /**
     * 操作后数据
     */
    private String afterData;
    
    /**
     * 租户id
     */
    private String tenantId;
    
    /**
     * 扩展字段
     */
    private String extraInfo;
    
    /**
     * 操作时间
     */
    private Date createTime;
} 