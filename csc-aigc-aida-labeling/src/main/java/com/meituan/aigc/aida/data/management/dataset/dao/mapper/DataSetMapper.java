package com.meituan.aigc.aida.data.management.dataset.dao.mapper;

import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSet;
import com.meituan.aigc.aida.data.management.dataset.dao.model.DataSetExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DataSetMapper {
    long countByExample(DataSetExample example);

    int deleteByExample(DataSetExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DataSet record);

    int insertSelective(DataSet record);

    List<DataSet> selectByExample(DataSetExample example);

    DataSet selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DataSet record, @Param("example") DataSetExample example);

    int updateByExample(@Param("record") DataSet record, @Param("example") DataSetExample example);

    int updateByPrimaryKeySelective(DataSet record);

    int updateByPrimaryKey(DataSet record);
}