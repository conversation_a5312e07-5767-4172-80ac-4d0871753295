package com.meituan.aigc.aida.data.management.common.constant;

public class LionConstant {

    /**
     * 数据集锁过期时间
     */
    public static final String DATASET_LOCK_EXPIRE = "dataset.lock.expire";


    public static final String LION_COMMON_FIELDS_KEY = "aida.data.common.fields";

    /**
     * 数据集管理页url
     */
    public static final String DATASET_MANAGE_URL = "dataset.manage.url";

    /**
     * 数据处理任务管理页url
     */
    public static final String DATA_PROCESS_TASK_MANAGE_URL = "data.process.task.manage.url";

    /**
     * 非自定义打标机器人配置
     */
    public static final String NON_CUSTOM_ROBOT_CONFIG = "non.custom.robot.config";

    /**
     * es最大字符串长度
     */
    public static final String MAX_STRING_LENGTH = "max.string.length";

    /**
     * 数据获取任务每批最大处理数量
     */
    public static final String DATA_FETCH_TASK_BATCH_SIZE = "data.fetch.task.batch.size";
}
