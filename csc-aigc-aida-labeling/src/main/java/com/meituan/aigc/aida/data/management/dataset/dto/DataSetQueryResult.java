package com.meituan.aigc.aida.data.management.dataset.dto;

import com.meituan.aigc.aida.data.management.dataset.param.DataFieldInfo;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetRecord;
import com.meituan.aigc.aida.labeling.param.PageData;
import lombok.Data;

import java.io.Serializable;

/**
 * 数据集查询结果
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集管理 - 查询结果
 */
@Data
public class DataSetQueryResult implements Serializable {
    /**
     * 滚动ID
     */
    private String scrollId;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 分页数据
     */
    private PageDataWithHead<DataFieldInfo, DataSetRecord> pageData;
    
    /**
     * 带表头的分页数据
     *
     * @param <H> 表头类型
     * @param <T> 数据类型
     */
    @Data
    public static class PageDataWithHead<H, T> implements Serializable {
        /**
         * 总数
         */
        private Integer totalCount;
        
        /**
         * 表头列表
         */
        private java.util.List<H> headList;
        
        /**
         * 数据列表
         */
        private java.util.List<T> data;
    }
} 