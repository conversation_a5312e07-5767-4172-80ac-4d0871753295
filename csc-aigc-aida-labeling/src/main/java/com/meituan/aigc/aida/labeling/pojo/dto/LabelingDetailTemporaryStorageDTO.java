package com.meituan.aigc.aida.labeling.pojo.dto;

import com.meituan.aigc.aida.labeling.pojo.vo.LabelResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/6/24 15:03
 * @Version: 1.0
 */
@Data
public class LabelingDetailTemporaryStorageDTO implements Serializable {

    /**
     * 详情数据ID
     */
    private Long id;
    /**
     * 大模型消息ID
     */
    private String messageId;
    /**
     * 三方消息ID
     */
    private String thirdMessageId;
    /**
     * 原始数据内容映射内容JSON
     */
    private String rawDataMappedContent;
    /**
     * labelingItems
     */
    private List<List<LabelResult>> labelingItems;

}
