package com.meituan.aigc.aida.data.management.fetch.mq.producer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.csccratos.aida.common.core.exception.biz.AidaMqProducerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * 信号埋点日志-生产者
 *
 * <AUTHOR>
 * @date 2025/04/06
 */
@Component
@Slf4j
public class SignalLogsToMafkaProducer {

    @Autowired
    @Qualifier("signalLogToMafkaProducer")
    private IProducerProcessor producer;

    /**
     * 同步信号埋点日志到Mafka
     *
     * @param message 消息体
     * <AUTHOR>
     * @date 2024/11/07
     */
    public void send(String message) {
        log.info("SignalLogToMafkaProducer.send.start, message:{}", message);
        ProducerResult result = null;
        try {
            result = producer.sendMessage(message);
            if (ProducerStatus.SEND_FAILURE.equals(result.getProducerStatus())) {
                throw new AidaMqProducerException();
            }
        } catch (Exception e) {
            log.error("SignalLogToMafkaProducerService.send.error, message:{}, result:{}", message, result, e);
            Cat.logErrorWithCategory("SignalLogToMafkaProducerService.send.error", e);
        }
    }
}
