<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.aigc.aida.labeling.dao.mapper.LabelingDetailMapper">
  <resultMap id="BaseResultMap" type="com.meituan.aigc.aida.labeling.dao.model.LabelingDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sub_task_id" jdbcType="BIGINT" property="subTaskId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="session_time" jdbcType="TIMESTAMP" property="sessionTime" />
    <result column="raw_data_id" jdbcType="BIGINT" property="rawDataId" />
    <result column="modified_raw_data_mapped_content" jdbcType="CHAR" property="modifiedRawDataMappedContent" />
    <result column="compar_items_consistent" jdbcType="TINYINT" property="comparItemsConsistent" />
    <result column="labeling_items_result" jdbcType="CHAR" property="labelingItemsResult" />
    <result column="labeler_mis" jdbcType="VARCHAR" property="labelerMis" />
    <result column="labeler_name" jdbcType="VARCHAR" property="labelerName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="sample_status" jdbcType="TINYINT" property="sampleStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="first_labeling_time" jdbcType="TIMESTAMP" property="firstLabelingTime" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="view_status" jdbcType="TINYINT" property="viewStatus" />
    <result column="extra_info" jdbcType="CHAR" property="extraInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sub_task_id, session_id, session_time, raw_data_id, modified_raw_data_mapped_content,
    compar_items_consistent, labeling_items_result, labeler_mis, labeler_name, status, 
    sample_status, create_time, update_time, first_labeling_time, message_id, view_status, extra_info
  </sql>
  <select id="selectByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from labeling_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from labeling_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from labeling_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingDetailExample">
    delete from labeling_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingDetail">
    insert into labeling_detail (id, sub_task_id, session_id,
      session_time, raw_data_id, modified_raw_data_mapped_content,
      compar_items_consistent, labeling_items_result,
      labeler_mis, labeler_name, status,
      sample_status, create_time, update_time,
      first_labeling_time, message_id, view_status, extra_info
      )
    values (#{id,jdbcType=BIGINT}, #{subTaskId,jdbcType=BIGINT}, #{sessionId,jdbcType=VARCHAR},
      #{sessionTime,jdbcType=TIMESTAMP}, #{rawDataId,jdbcType=BIGINT}, #{modifiedRawDataMappedContent,jdbcType=CHAR},
      #{comparItemsConsistent,jdbcType=TINYINT}, #{labelingItemsResult,jdbcType=CHAR},
      #{labelerMis,jdbcType=VARCHAR}, #{labelerName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{sampleStatus,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{firstLabelingTime,jdbcType=TIMESTAMP}, #{messageId,jdbcType=VARCHAR}, #{viewStatus,jdbcType=TINYINT}, #{extraInfo,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingDetail">
    insert into labeling_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="subTaskId != null">
        sub_task_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="sessionTime != null">
        session_time,
      </if>
      <if test="rawDataId != null">
        raw_data_id,
      </if>
      <if test="modifiedRawDataMappedContent != null">
        modified_raw_data_mapped_content,
      </if>
      <if test="comparItemsConsistent != null">
        compar_items_consistent,
      </if>
      <if test="labelingItemsResult != null">
        labeling_items_result,
      </if>
      <if test="labelerMis != null">
        labeler_mis,
      </if>
      <if test="labelerName != null">
        labeler_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="sampleStatus != null">
        sample_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="firstLabelingTime != null">
        first_labeling_time,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="viewStatus != null">
        view_status,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="subTaskId != null">
        #{subTaskId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionTime != null">
        #{sessionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawDataId != null">
        #{rawDataId,jdbcType=BIGINT},
      </if>
      <if test="modifiedRawDataMappedContent != null">
        #{modifiedRawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="comparItemsConsistent != null">
        #{comparItemsConsistent,jdbcType=TINYINT},
      </if>
      <if test="labelingItemsResult != null">
        #{labelingItemsResult,jdbcType=CHAR},
      </if>
      <if test="labelerMis != null">
        #{labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="labelerName != null">
        #{labelerName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="sampleStatus != null">
        #{sampleStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstLabelingTime != null">
        #{firstLabelingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="viewStatus != null">
        #{viewStatus,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingDetailExample" resultType="java.lang.Long">
    select count(*) from labeling_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="pageExportDataBySubTaskIdAndStatus" resultType="com.meituan.aigc.aida.labeling.pojo.vo.LabelTaskDataExportVO">
      SELECT
      detail.session_id AS sessionId, detail.session_time AS sessionTime,raw.message_id AS messageId, raw.raw_data_content AS rawDataContent,
      raw.raw_data_headers As rawDataHeaders, raw.raw_data_mapped_content AS rawDataMappedContent,
      raw.extra_info AS extraInfo, detail.modified_raw_data_mapped_content AS
      modifiedRawDataMappedContent,detail.labeler_mis AS labelMis, detail.labeler_name AS labelName,detail.first_labeling_time
      AS labelTime, detail.labeling_items_result AS labelingItemsResult,
      quality.quality_check_mis AS qualityCheckMis,quality.quality_check_name AS qualityCheckName, quality.first_check_time
      AS qualityCheckTime, quality.quality_check_result AS qualityCheckResult,
      quality.quality_check_items_result AS qualityCheckItemsResult, quality.modified_labeling_items_result AS qualityCheckModifiedLabelingItems,
      quality.modified_raw_data_mapped_content AS qualityCheckModifiedRawDataMappedContent, detail.sub_task_id AS subTaskId
      FROM labeling_detail AS detail
      LEFT JOIN labeling_task_raw_data AS raw ON detail.raw_data_id = raw.id
      LEFT JOIN labeling_quality_check_item AS quality ON detail.id = quality.labeling_data_id
      <where>
          <if test="subTaskIds != null and subTaskIds.size() > 0">
              detail.sub_task_id IN
              <foreach collection="subTaskIds" item="subTaskId" open="(" close=")" separator=",">
                  #{subTaskId}
              </foreach>
          </if>
          <if test="status != null">
              AND detail.status = #{status}
          </if>
      </where>
      <if test="pageSize == 1">
          ORDER BY
          CASE WHEN quality.quality_check_items_result IS NOT NULL THEN 0 ELSE 1 END
      </if>
      <if test="pageSize != 1">
          ORDER BY detail.id ASC
      </if>
      LIMIT #{offset}, #{pageSize}
  </select>
  <select id="pageQueryBySubTaskId" resultType="com.meituan.aigc.aida.labeling.pojo.dto.DetailDataItemDTO">
    SELECT
    d.id,
    d.session_id as sessionId,
    d.session_time as sessionTime,
    d.message_id as thirdMessageId,
    r.extra_info as extraInfo,
    d.raw_data_id as dataId,
    d.labeler_mis as labelerMis,
    d.labeler_name as labelerName,
    d.status,
    DATE_FORMAT(d.update_time, '%Y-%m-%d %H:%i:%s') as updateTime,
    d.labeling_items_result as labelingItemsResult,
    r.raw_data_content as rawDataContent,
    r.raw_data_headers as rawDataHeaders,
    <!-- 如果详情表中的modified_raw_data_mapped_content不为空，则使用它，否则使用原数据表中的raw_data_mapped_content -->
    CASE
    WHEN d.modified_raw_data_mapped_content IS NOT NULL
    THEN d.modified_raw_data_mapped_content
    ELSE r.raw_data_mapped_content
    END AS rawDataMappedContent
    FROM
    labeling_detail d
    LEFT JOIN
    labeling_task_raw_data r ON d.raw_data_id = r.id
    WHERE
    d.sub_task_id = #{param.subTaskId}
    <if test="param.status != null">
      <choose>
        <when test="param.status == 1 || param.status == 3">
          AND d.status IN (1, 3)
        </when>
        <otherwise>
          AND d.status IN (2, 4)
        </otherwise>
      </choose>
    </if>
    <if test="param.labelerMis != null">
      AND (d.labeler_mis = #{param.labelerMis} or d.labeler_name = #{param.labelerMis})
    </if>
    <if test="param.sessionId != null">
      AND r.session_id = #{param.sessionId}
    </if>
    <if test="param.startTime != null and param.endTime != null">
      AND d.update_time between #{param.startTime} and #{param.endTime}
    </if>
    ORDER BY
    d.update_time DESC
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update labeling_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.subTaskId != null">
        sub_task_id = #{record.subTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.sessionId != null">
        session_id = #{record.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="record.sessionTime != null">
        session_time = #{record.sessionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rawDataId != null">
        raw_data_id = #{record.rawDataId,jdbcType=BIGINT},
      </if>
      <if test="record.modifiedRawDataMappedContent != null">
        modified_raw_data_mapped_content = #{record.modifiedRawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="record.comparItemsConsistent != null">
        compar_items_consistent = #{record.comparItemsConsistent,jdbcType=TINYINT},
      </if>
      <if test="record.labelingItemsResult != null">
        labeling_items_result = #{record.labelingItemsResult,jdbcType=CHAR},
      </if>
      <if test="record.labelerMis != null">
        labeler_mis = #{record.labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="record.labelerName != null">
        labeler_name = #{record.labelerName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.sampleStatus != null">
        sample_status = #{record.sampleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstLabelingTime != null">
        first_labeling_time = #{record.firstLabelingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.messageId != null">
        message_id = #{record.messageId,jdbcType=VARCHAR},
      </if>
      <if test="record.viewStatus != null">
        view_status = #{record.viewStatus,jdbcType=TINYINT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=CHAR}
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update labeling_detail
    set id = #{record.id,jdbcType=BIGINT},
      sub_task_id = #{record.subTaskId,jdbcType=BIGINT},
      session_id = #{record.sessionId,jdbcType=VARCHAR},
      session_time = #{record.sessionTime,jdbcType=TIMESTAMP},
      raw_data_id = #{record.rawDataId,jdbcType=BIGINT},
      modified_raw_data_mapped_content = #{record.modifiedRawDataMappedContent,jdbcType=CHAR},
      compar_items_consistent = #{record.comparItemsConsistent,jdbcType=TINYINT},
      labeling_items_result = #{record.labelingItemsResult,jdbcType=CHAR},
      labeler_mis = #{record.labelerMis,jdbcType=VARCHAR},
      labeler_name = #{record.labelerName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      sample_status = #{record.sampleStatus,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      first_labeling_time = #{record.firstLabelingTime,jdbcType=TIMESTAMP},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      view_status = #{record.viewStatus,jdbcType=TINYINT},
      extra_info = #{record.extraInfo,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingDetail">
    update labeling_detail
    <set>
      <if test="subTaskId != null">
        sub_task_id = #{subTaskId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionTime != null">
        session_time = #{sessionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rawDataId != null">
        raw_data_id = #{rawDataId,jdbcType=BIGINT},
      </if>
      <if test="modifiedRawDataMappedContent != null">
        modified_raw_data_mapped_content = #{modifiedRawDataMappedContent,jdbcType=CHAR},
      </if>
      <if test="comparItemsConsistent != null">
        compar_items_consistent = #{comparItemsConsistent,jdbcType=TINYINT},
      </if>
      <if test="labelingItemsResult != null">
        labeling_items_result = #{labelingItemsResult,jdbcType=CHAR},
      </if>
      <if test="labelerMis != null">
        labeler_mis = #{labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="labelerName != null">
        labeler_name = #{labelerName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="sampleStatus != null">
        sample_status = #{sampleStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstLabelingTime != null">
        first_labeling_time = #{firstLabelingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="viewStatus != null">
        view_status = #{viewStatus,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=CHAR}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.meituan.aigc.aida.labeling.dao.model.LabelingDetail">
    update labeling_detail
    set sub_task_id = #{subTaskId,jdbcType=BIGINT},
      session_id = #{sessionId,jdbcType=VARCHAR},
      session_time = #{sessionTime,jdbcType=TIMESTAMP},
      raw_data_id = #{rawDataId,jdbcType=BIGINT},
      modified_raw_data_mapped_content = #{modifiedRawDataMappedContent,jdbcType=CHAR},
      compar_items_consistent = #{comparItemsConsistent,jdbcType=TINYINT},
      labeling_items_result = #{labelingItemsResult,jdbcType=CHAR},
      labeler_mis = #{labelerMis,jdbcType=VARCHAR},
      labeler_name = #{labelerName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      sample_status = #{sampleStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      first_labeling_time = #{firstLabelingTime,jdbcType=TIMESTAMP},
      message_id = #{messageId,jdbcType=VARCHAR},
      view_status = #{viewStatus,jdbcType=TINYINT},
      extra_info = #{extraInfo,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listBySubTaskIds" parameterType="list" resultType="long">
    SELECT
      id
    FROM
      labeling_detail
    WHERE
      sub_task_id IN
      <foreach collection="subTaskIds" item="subTaskId" open="(" separator="," close=")">
        #{subTaskId}
      </foreach>
  </select>
  <select id="listByIds" parameterType="java.util.List" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
    FROM labeling_detail
    WHERE
      id IN
      <foreach collection="detailDataIds" item="id" separator="," open="(" close=")" >
        #{id}
      </foreach>
  </select>

  <!-- 批量更新标注详情 -->
  <update id="batchUpdateLabelingDetail" parameterType="java.util.List">
    <foreach collection="updateLabelingDetails" item="item" separator=";" open="" close="">
      UPDATE labeling_detail
      <set>
        <if test="item.subTaskId != null">
          sub_task_id = #{item.subTaskId},
        </if>
        <if test="item.sessionId != null">
          session_id = #{item.sessionId},
        </if>
        <if test="item.sessionTime != null">
          session_time = #{item.sessionTime},
        </if>
        <if test="item.rawDataId != null">
          raw_data_id = #{item.rawDataId},
        </if>
        <if test="item.modifiedRawDataMappedContent != null">
          modified_raw_data_mapped_content = #{item.modifiedRawDataMappedContent, jdbcType=VARCHAR},
        </if>
        <if test="item.comparItemsConsistent != null">
          compar_items_consistent = #{item.comparItemsConsistent},
        </if>
        <if test="item.labelingItemsResult != null">
          labeling_items_result = #{item.labelingItemsResult, jdbcType=VARCHAR},
        </if>
        <if test="item.labelerMis != null">
          labeler_mis = #{item.labelerMis},
        </if>
        <if test="item.labelerName != null">
          labeler_name = #{item.labelerName},
        </if>
        <if test="item.status != null">
          status = #{item.status},
        </if>
        <if test="item.sampleStatus != null">
          sample_status = #{item.sampleStatus},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime},
        </if>
        <if test="item.firstLabelingTime != null">
          first_labeling_time = #{item.firstLabelingTime},
        </if>
        <if test="item.extraInfo != null">
          extra_info = #{item.extraInfo}
        </if>
      </set>
      WHERE id = #{item.id}
    </foreach>
  </update>
  <select id="listBySubTaskIdRawDataIdAndStatus" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
    FROM labeling_detail
    <where>
      <if test="subTaskIds != null">
        sub_task_id IN
        <foreach collection="subTaskIds" item="subTaskId" open="(" separator="," close=")">
          #{subTaskId}
        </foreach>
      </if>
      <if test="rawDataId != null">
          AND raw_data_id = #{rawDataId}
      </if>
      <if test="labelingDetailStatus != null">
          AND status = #{labelingDetailStatus}
      </if>
    </where>
  </select>
  <select id="listBySubTaskId" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskUnLabelPO">
    SELECT
      sub_task_id AS id,
      SUM(CASE WHEN status IN (1,3) THEN 1 ELSE 0 END) AS unLabelCount
    FROM
      labeling_detail
    <where>
      sub_task_id IN
      <foreach collection="subTaskIds" item="subTaskId" open="(" separator="," close=")">
        #{subTaskId}
      </foreach>
    </where>
    GROUP BY
    sub_task_id
  </select>
  <select id="listSessionCountBySubTaskId" resultType="com.meituan.aigc.aida.labeling.dao.po.LabelingSubTaskUnLabelPO">
    SELECT
    sub_task_id AS id,
    COUNT(DISTINCT CASE WHEN status IN (1,3) THEN session_id ELSE NULL END) AS unLabelCount
    FROM
    labeling_detail
    <where>
      sub_task_id IN
      <foreach collection="subTaskIds" item="subTaskId" open="(" separator="," close=")">
        #{subTaskId}
      </foreach>
    </where>
    GROUP BY
    sub_task_id
  </select>
  <insert id="batchInsertDetail" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO labeling_detail (
    sub_task_id,
    session_id,
    message_id,
    session_time,
    raw_data_id,
    modified_raw_data_mapped_content,
    compar_items_consistent,
    labeling_items_result,
    labeler_mis,
    labeler_name,
    sample_status,
    status,
    extra_info
    ) VALUES
    <foreach collection="details" item="item" separator=",">
      (
      #{item.subTaskId},
      #{item.sessionId},
      #{item.messageId},
      #{item.sessionTime},
      #{item.rawDataId},
      #{item.modifiedRawDataMappedContent, jdbcType=VARCHAR},
      #{item.comparItemsConsistent},
      #{item.labelingItemsResult, jdbcType=VARCHAR},
      #{item.labelerMis},
      #{item.labelerName},
      #{item.sampleStatus},
      #{item.status},
      #{item.extraInfo}
      )
    </foreach>
  </insert>
  <select id="countNumBySubTask" resultType="com.meituan.aigc.aida.labeling.pojo.vo.LabelDetailNumCountVO">
    SELECT
      COUNT(id) AS totalNum,
      COALESCE(SUM(status IN (1, 3)), 0) AS waitLabelingNum,
      COALESCE(SUM(status IN (2, 4)), 0) AS doneLabelingNum
    FROM labeling_detail
    <where>
      <if test="subTaskId != null">
        sub_task_id = #{subTaskId}
      </if>
    </where>
  </select>
  <select id="pageSessionBySubtaskAndSessionIdAndStatus" resultType="java.lang.String">
    SELECT DISTINCT session_id
    FROM labeling_detail
    <where>
      <if test="subTaskId != null">
        sub_task_id = #{subTaskId}
      </if>
      <if test="sessionId != null">
        AND session_id = #{sessionId}
      </if>
      <if test="labelStatus != null">
        <choose>
          <when test="labelStatus == 1 || labelStatus == 3">
            AND status IN (1, 3)
          </when>
          <otherwise>
            AND status IN (2, 4)
          </otherwise>
        </choose>
      </if>
    </where>
    ORDER BY id ASC
  </select>
  <select id="countConsistencyRateBySubTaskId" resultType="java.lang.Integer">
    SELECT CEIL(
      CASE
      WHEN COUNT(id) = 0 THEN 0
      ELSE COALESCE(SUM(compar_items_consistent = 1), 0) * 100.0 / COUNT(id)
      END
    ) AS consistency_percentage
    FROM labeling_detail
    WHERE sub_task_id = #{subTaskId}
  </select>
  <select id="listDetailBySubTaskIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    labeling_detail
    WHERE
    sub_task_id IN
    <foreach collection="subTaskIds" item="subTaskId" open="(" separator="," close=")">
      #{subTaskId}
    </foreach>
  </select>
  <update id="batchUpdateLabelerBySubTaskIdAndStatus">
    update labeling_detail
    <set>
      <if test="labelerMis != null">
        labeler_mis = #{labelerMis,jdbcType=VARCHAR},
      </if>
      <if test="labelerName != null">
        labeler_name = #{labelerName,jdbcType=VARCHAR},
      </if>
    </set>
    <where>
      <if test="subTaskId != null">
        sub_task_id = #{subTaskId}
      </if>
      <if test="labelingStatus != null">
        AND status = #{labelingStatus}
      </if>
    </where>
  </update>
  <select id="listSessionAssignLabelingDetail" resultType="java.lang.String">
      SELECT session_id
      FROM labeling_detail
      WHERE sub_task_id = #{subTaskId} and sample_status = 2
      group by session_id
  </select>
  <update id="updateViewStatusByIdList">
    <foreach collection="idList" item="item" separator=";">
        UPDATE labeling_detail
        SET view_status = 2
      WHERE id = #{item}
    </foreach>
  </update>
  <select id="countSessionBySubTaskIdAndStatus" resultType="java.lang.Integer">
    SELECT count(distinct session_id)
    FROM labeling_detail
    <where>
      <if test="subTaskId != null">
        sub_task_id = #{subTaskId}
      </if>
      <if test="status != null">
        AND status = #{status}
      </if>
    </where>
  </select>
  <select id="countQueryBySubTaskIdAndStatus" resultType="java.lang.Integer">
    SELECT count(id)
    FROM labeling_detail
    <where>
      <if test="subTaskId != null">
        sub_task_id = #{subTaskId}
      </if>
      <if test="status != null">
        AND status = #{status}
      </if>
    </where>
  </select>
  <select id="statisticsPersonLabelingData" resultType="com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingStatisticsPO">
    SELECT
      detail.labeler_mis AS labelerMis,
      detail.labeler_name AS labelerName,
      COALESCE(SUM(detail.status IN (2, 4) ) ,0) AS labelingCount,
      COALESCE(SUM(quality.status = 2) ,0) AS qcInspectedCount,
      COALESCE(SUM(quality.status = 2 AND quality.quality_check_result = 1), 0) AS qcInspectedCorrectCount,
      task.biz_type AS bizType,
      task.data_type AS dataType,
      task.task_type AS taskType
    FROM labeling_detail AS detail
    LEFT JOIN labeling_sub_task AS sub ON sub.id = detail.sub_task_id
    LEFT JOIN labeling_task AS task ON sub.task_id = task.id
    LEFT JOIN (
      SELECT
        status, quality_check_result, labeling_data_id
      FROM
        labeling_quality_check_item
      WHERE
        first_check_time &gt;= #{startTime}
        AND first_check_time &lt;= #{endTime}
    ) AS quality ON detail.id = quality.labeling_data_id
    WHERE
     detail.labeler_mis IS NOT NULL
     AND detail.first_labeling_time &gt;= #{startTime}
     AND detail.first_labeling_time &lt;= #{endTime}
     AND sub.status != 8
    GROUP BY detail.labeler_mis, detail.labeler_name, task.biz_type, task.data_type, task.task_type
  </select>
  <select id="listByStatusAndDetailId" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
    FROM labeling_detail
    <where>
      <if test="subTaskId != null">
        sub_task_id = #{subTaskId}
      </if>
      <if test="labelStatus != null">
        <choose>
          <when test="labelStatus == 1 || labelStatus == 3">
            AND status IN (1, 3)
          </when>
          <otherwise>
            AND status IN (2, 4)
          </otherwise>
        </choose>
      </if>
      <if test="detailId != null">
        AND id = #{detailId}
      </if>
    </where>
    ORDER BY id ASC, session_id ASC
  </select>
  <select id="pageDetailBySubTaskId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM labeling_detail
    WHERE
    sub_task_id = #{subTaskId}
    ORDER BY id ASC
    LIMIT #{offset}, #{pageSize}
  </select>
  <select id="listPersonLabelingCount" resultType="com.meituan.aigc.aida.labeling.job.dashboard.po.PersonLabelingNumCountPO">
    SELECT
      COUNT(detail.first_labeling_time) AS labelingCount,
      COALESCE(SUM(quality.status = 2) ,0) AS qcInspectedCount,
      COALESCE(SUM(quality.status = 2 AND quality.quality_check_result = 1), 0) AS qcInspectedCorrectCount,
      detail.labeler_mis AS labelerMis
    FROM labeling_detail AS detail
    LEFT JOIN labeling_sub_task AS sub ON sub.id = detail.sub_task_id
    LEFT JOIN labeling_task AS task ON sub.task_id = task.id
    LEFT JOIN labeling_quality_check_item AS quality ON detail.id = quality.labeling_data_id
    WHERE
    task.id = #{taskId}
    AND detail.first_labeling_time &gt;= #{startTime}
    AND detail.first_labeling_time &lt;= #{endTime}
    AND JSON_EXTRACT(detail.extra_info, '$.isReLabeling') = true
    GROUP BY detail.labeler_mis
  </select>
  <select id="listLabelingDetailByIdsAndDate" resultMap="BaseResultMap">
    SELECT
      id, sub_task_id, labeler_mis, first_labeling_time
    FROM labeling_detail
    WHERE id IN
    <foreach collection="ids" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND first_labeling_time &lt; #{startTime}
  </select>
</mapper>