package com.meituan.aigc.aida.data.management.dataset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-06-04 17:49
 * @description 文件类型枚举
 */
@Getter
@AllArgsConstructor
public enum FileTypeEnum {

    /**
     * 1-Excel 2-JSON 3-JSONL
     */
    EXCEL(1, "Excel"),
    JSON(2, "JSON"),
    JSONL(3, "JSONL");

    private final int code;
    private final String desc;

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FileTypeEnum fileTypeEnum : FileTypeEnum.values()) {
            if (fileTypeEnum.getCode() == code) {
                return fileTypeEnum.getDesc();
            }
        }
        return null;
    }
}
