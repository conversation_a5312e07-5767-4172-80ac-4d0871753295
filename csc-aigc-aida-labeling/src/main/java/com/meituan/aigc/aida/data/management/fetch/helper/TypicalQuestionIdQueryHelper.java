package com.meituan.aigc.aida.data.management.fetch.helper;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.csc.analtics.ctx.dto.SessionTypicalDTO;
import com.dianping.csc.analtics.ctx.service.TypicalQuestionStatisticsService;
import com.meituan.aigc.aida.data.management.common.config.DataManagementLionConfig;
import com.meituan.aigc.aida.data.management.proxy.HiveRequestServiceProxy;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.model.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 标准问ID查询Helper
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class TypicalQuestionIdQueryHelper {

    private static final String LOG_PRE = "[标准问ID列表查询]";

    /**
     * 标准问ID
     */
    private static final String TYPICAL_QUESTION_ID = "typicalquestionid";

    @Autowired
    private TypicalQuestionStatisticsService typicalQuestionStatisticsService;

    @Resource
    private DataManagementLionConfig dataManagementLionConfig;

    @Resource
    private HiveRequestServiceProxy hiveRequestServiceProxy;

    /**
     * 查询标问ID列表
     * 
     * 先尝试实时获取标问列表，如果失败再查询Hive
     *
     * @param sessionId 会话ID
     * @return 标问ID列表
     */
    public List<String> getTypicalQuestionIds(String sessionId) {
        if (StringUtils.isBlank(sessionId)) {
            log.warn(LOG_PRE + "sessionId为空，无法查询标问ID列表");
            return Collections.emptyList();
        }

        // 实时获取标问列表
        List<String> realTimeTypicalIds = getRealTimeTypicalIds(sessionId);
        if (CollectionUtils.isNotEmpty(realTimeTypicalIds)) {
            log.info(LOG_PRE + "实时获取标问ID列表成功，sessionId:{}, 列表:{}", sessionId, JSON.toJSONString(realTimeTypicalIds));
            return realTimeTypicalIds;
        }

        return Collections.emptyList();
    }

    /**
     * 实时获取标问ID列表
     *
     * @param sessionId 会话ID
     * @return 标问ID列表
     */
    private List<String> getRealTimeTypicalIds(String sessionId) {
        try {
            List<SessionTypicalDTO> typicalList = typicalQuestionStatisticsService
                    .getTypicalQuestionBySessionMultiRoundState(sessionId);
            if (CollectionUtils.isEmpty(typicalList)) {
                log.warn(LOG_PRE + "实时查询标问ID列表为空, sessionId:{}", sessionId);
                return Collections.emptyList();
            }
            return typicalList.stream()
                    .map(dto -> String.valueOf(dto.getTypicalQuestionId()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(LOG_PRE + "实时查询标问ID列表异常, sessionId:{}", sessionId, e);
            Cat.logError(e);
            return Collections.emptyList();
        }
    }

    /**
     * 通过查询hive获取标准问Id
     * 
     * @param sessionId sessionId
     * @return 标准问Id
     */
    private List<String> getTypicalQuestionIdByHive(String sessionId) {
        AsyncTalosClient client = null;
        try {
            client = hiveRequestServiceProxy.getClient();
            String sqlTemplate = "SELECT %s FROM origindb.cscanaltics_cscplatform__csc_robotinvokelog WHERE sessionid = '%s'";
            String sql = String.format(sqlTemplate, TYPICAL_QUESTION_ID, sessionId);
            QueryResult queryResult = hiveRequestServiceProxy.invoke(client, sql,
                    dataManagementLionConfig.getTalosWaitingTime() * 60);
            log.info(LOG_PRE + "查询hive获取标问Id结果：{}", JSON.toJSONString(queryResult));
            if (Objects.isNull(queryResult) || queryResult.getResultSize() <= 0
                    || CollectionUtils.isEmpty(queryResult.fetchOne())) {
                log.warn(LOG_PRE + "查询hive库获取标问Id结果为空");
                return Collections.emptyList();
            }
            return queryResult.fetchOne().stream().map(String::valueOf).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error(LOG_PRE + "查询hive库获取标问Id异常：", e);
            return Collections.emptyList();
        } finally {
            hiveRequestServiceProxy.closeClient(client);
        }
    }
}