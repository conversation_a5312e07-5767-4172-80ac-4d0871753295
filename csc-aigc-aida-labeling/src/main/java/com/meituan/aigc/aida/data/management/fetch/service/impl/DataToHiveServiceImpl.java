package com.meituan.aigc.aida.data.management.fetch.service.impl;

import com.meituan.aigc.aida.data.management.fetch.dto.hive.signal.SignalLoggingToHiveDTO;
import com.meituan.aigc.aida.data.management.fetch.mq.producer.SignalLogsToMafkaProducer;
import com.meituan.aigc.aida.data.management.fetch.service.DataToHiveService;
import com.sankuai.csccratos.aida.common.utils.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>数据同步到Hive
 * <p>mafka -> kafka-> hive
 *
 * <AUTHOR>
 * @date 2025/04/09
 */
@Service
@Slf4j
public class DataToHiveServiceImpl implements DataToHiveService {

    @Resource
    private SignalLogsToMafkaProducer signalLogsToMafkaProducer;

    @Override
    public void pushSignalLoggingData(SignalLoggingToHiveDTO signalLogging2HiveDTO) {
        signalLogsToMafkaProducer.send(GsonUtil.toJsonStr(signalLogging2HiveDTO));
    }
}