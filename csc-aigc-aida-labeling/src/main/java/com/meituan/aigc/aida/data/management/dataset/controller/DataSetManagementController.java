package com.meituan.aigc.aida.data.management.dataset.controller;


import com.meituan.aigc.aida.data.management.dataset.dto.*;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetCreateResponseParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataSetParseHeadParam;
import com.meituan.aigc.aida.data.management.dataset.param.UpdateFieldParam;
import com.meituan.aigc.aida.data.management.dataset.service.DataSetManagementService;
import com.meituan.aigc.aida.labeling.param.PageData;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.meituan.aigc.aida.labeling.remote.caseanalysis.param.DataFieldParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Slf4j
@RestController
@RequestMapping("/api/v1/DataSet")
public class DataSetManagementController {

    @Autowired
    private DataSetManagementService dataSetManagementService;

    /**
     * 解析文件表头
     *
     * @param param 获取表头参数
     * @return 字段列表
     */
    @PostMapping("/dataset/parseHeader")
    public Result<List<DataFieldParam>> parseFileHeader(@RequestBody DataSetParseHeadParam param) {
        return Result.ok(dataSetManagementService.parseFileHeader(param));
    }

    /**
     * 创建数据集
     *
     * @param param 创建数据集参数
     * @return 数据集ID和锁ID
     */
    @PostMapping("/dataset/create")
    public Result<DataSetCreateResponseParam> createDataSet(@RequestBody DataSetCreateParam param) {
        dataSetManagementService.createDataSetAsync(param);
        return Result.create();
    }

    /**
     * 根据任务ID获取数据任务数据
     * @param taskId 任务ID
     * @return 数据任务数据
     */
    @GetMapping("/data/fetch")
    public Result<DataFetchContent> getDataFetchByTaskId(@RequestParam("taskId") Long taskId) {
        return Result.ok(dataSetManagementService.getDataFetchByTaskId(taskId));
    }

    /**
     * 分页查询数据集列表
     * @param name 数据集名称
     * @param pageNum 页码
     * @param pageSize 页数
     * @return 数据集列表
     */
    @GetMapping("/page")
    public Result<PageData<DataSetDTO>> pageListDataset(@RequestParam(value = "name", required = false) String name,
                                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return Result.ok(dataSetManagementService.pageDataset(name, pageNum, pageSize));
    }

    /**
     * 分页查询数据集详情
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @param pageNum 页码
     * @param pageSize 页数
     * @return 数据集详情
     */
    @GetMapping("/pageDetail")
    public Result<PageData<DataSetRecordDTO>> pageListDatasetRecord(@RequestParam(value = "dataSetId") Long dataSetId,
                                                                    @RequestParam(value = "versionId",required = false) Long versionId,
                                                                    @RequestParam(value = "pageNum", defaultValue = "0") Integer pageNum,
                                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return Result.ok(dataSetManagementService.pageDataSetDetail(dataSetId, versionId, pageNum, pageSize));
    }

    /**
     * 获取字段枚举
     * @return 字段枚举列表
     */
    @GetMapping("/field")
    public Result<List<FieldEnumDTO>> listFieldEnum() {
        return Result.ok(dataSetManagementService.listFieldEnum());
    }

    /**
     * 获取字段列表
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @return 字段列表
     */
    @GetMapping("/field/list")
    public Result<List<DataSourceField>> listField(@RequestParam("dataSetId") Long dataSetId, @RequestParam("versionId") Long versionId) {
        return Result.ok(dataSetManagementService.listField(dataSetId, versionId));
    }

    /**
     * 更新字段类型
     * @param param 更新字段参数
     * @return 更新结果
     */
    @PostMapping("/field/update")
    public Result<?> updateField(@RequestBody UpdateFieldParam param) {
        dataSetManagementService.updateField(param);
        return Result.create();
    }

    /**
     * 数据集导出
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @param fileType 导出文件类型
     * @return 导出结果
     */
    @GetMapping("/export")
    public Result<?> exportDataSet(@RequestParam("dataSetId") Long dataSetId,
                                   @RequestParam("versionId") Long versionId,
                                   @RequestParam("fileType") Integer fileType) {
        dataSetManagementService.exportDataSet(dataSetId, versionId, fileType);
        return Result.create();
    }

    /**
     * 删除数据集
     * @param dataSetId 数据集ID
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public Result<?> deleteDataSet(@RequestParam("dataSetId") Long dataSetId) {
        dataSetManagementService.deleteDataSet(dataSetId);
        return Result.create();
    }
    /**
     * 发布数据集
     *
     * @param dataSetId 数据集ID
     * @param versionId 版本ID
     * @return 操作结果
     */
    @GetMapping("/publish")
    public Result<?> publishDataSet(@RequestParam("dataSetId") Long dataSetId,
                                       @RequestParam("versionId") Long versionId) {
        log.info("发布数据集版本, dataSetId: {}, versionId: {}", dataSetId, versionId);
        dataSetManagementService.publishDataSet(dataSetId, versionId);
        return Result.create();
    }
}
