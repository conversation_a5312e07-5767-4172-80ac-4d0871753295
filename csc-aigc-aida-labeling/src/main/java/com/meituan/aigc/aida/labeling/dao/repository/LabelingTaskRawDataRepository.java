package com.meituan.aigc.aida.labeling.dao.repository;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskRawData;
import com.meituan.aigc.aida.labeling.dao.po.LabelingTaskRawDataPO;
import com.meituan.aigc.aida.labeling.pojo.dto.task.LabelingTaskCountDTO;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/2/28 11:11
 * @Version: 1.0
 */
public interface LabelingTaskRawDataRepository {

    /**
     * 批量插入
     *
     * @param data 原数据
     */
    void batchInsert(List<LabelingTaskRawData> data);

    /**
     * 根据主键更新
     *
     * @param data 更新数据
     */
    void updateByPrimaryKeySelective(LabelingTaskRawData data);

    /**
     * 根据主键批量查询
     *
     * @param rawDataIds 主键集合
     * @return 结果
     */
    List<LabelingTaskRawData> listByIds(List<Long> rawDataIds);

    /**
     * 查询任务原始数据量
     *
     * @param taskIds 任务id集合
     * @return 原数据数据数量
     */
    List<LabelingTaskRawDataPO> getCountByTaskIds(List<Long> taskIds);

    /**
     * 根据任务id查询原始数据
     *
     * @param taskId 任务ID
     * @return 原数数据列表
     */
    List<LabelingTaskRawData> getRawDataByTaskId(Long taskId);

    /**
     * 根据任务id删除原始数据
     *
     * @param id 任务id
     */
    void deleteByTaskId(Long id);

    /**
     * 根据任务id查询原始数据数量
     *
     * @param taskId 任务ID
     * @return 数量
     */
    long countByTaskId(Long taskId);

    /**
     * 根据任务id分页查询原始数据
     *
     * @param taskId 任务ID
     * @param offset 偏移量
     * @param limit  展示数量
     * @return 结果
     */
    List<LabelingTaskRawData> pageQueryByTaskId(Long taskId, Integer offset, Integer limit);


    /**
     * 根据任务id分页查询会话数据
     *
     * @param taskId 任务ID
     * @param offset 偏移量
     * @param limit  展示数量
     * @return 查询结果
     */
    List<String> pageSessionByTaskId(Long taskId, Integer offset, Integer limit);

    /**
     * 根据主键查询详情
     *
     * @param rawDataId 主键
     * @return 详情
     */
    LabelingTaskRawData getById(Long rawDataId);

    /**
     * 查询任务统计数据
     */
    LabelingTaskCountDTO listCountByTaskId(Long taskId);

    /**
     * 根据任务id和会话id列表查询数据
     *
     * @param taskId        任务id
     * @param sessionIdList 会话id列表
     * @return 数据列表
     */
    List<LabelingTaskRawData> listByTaskIdAndSessionIdList(Long taskId, List<String> sessionIdList);
}
