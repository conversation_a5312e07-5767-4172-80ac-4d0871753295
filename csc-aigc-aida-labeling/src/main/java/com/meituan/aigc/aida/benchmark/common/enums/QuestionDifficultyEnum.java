package com.meituan.aigc.aida.benchmark.common.enums;

import com.meituan.aigc.aida.common.enums.base.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考题难易程度枚举
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Getter
@AllArgsConstructor
public enum QuestionDifficultyEnum implements BaseEnum<Integer> {

    EASY(0, "简单"),
    MEDIUM(1, "中等"),
    HARD(2, "困难");

    /**
     * 难度代码
     */
    private final Integer code;

    /**
     * 难度描述
     */
    private final String value;

    /**
     * 根据code获取value
     *
     * @param code 难度代码
     * @return 对应的难度描述，如果未找到则返回EASY
     */
    public static String getValueByCode(Integer code) {
        for (QuestionDifficultyEnum difficultyEnum : QuestionDifficultyEnum.values()) {
            if (difficultyEnum.getCode().equals(code)) {
                return difficultyEnum.getValue();
            }
        }
        return EASY.value;
    }

    /**
     * 根据code获取枚举对象
     *
     * @param code 难度代码
     * @return 对应的枚举对象，如果未找到则返回EASY
     */
    public static QuestionDifficultyEnum getByCode(Integer code) {
        for (QuestionDifficultyEnum difficultyEnum : QuestionDifficultyEnum.values()) {
            if (difficultyEnum.getCode().equals(code)) {
                return difficultyEnum;
            }
        }
        return EASY;
    }
} 