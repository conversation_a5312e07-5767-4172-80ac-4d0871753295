package com.meituan.aigc.aida.labeling.mq.producer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.csccratos.aida.common.core.exception.biz.AidaMqProducerException;

import lombok.extern.slf4j.Slf4j;

/**
 * 人工标注结果-生产者
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
@Component
@Slf4j
public class ManualLabelingResultsProducer {

    @Autowired
    @Qualifier("manualLabelingMessageProducer")
    private IProducerProcessor producer;

    public void send(String message) {
        log.info("manualLabelingMessageProducer.send.start, message:{}", message);
        ProducerResult result = null;
        try {
            result = producer.sendMessage(message);
            if (ProducerStatus.SEND_FAILURE.equals(result.getProducerStatus())) {
                throw new AidaMqProducerException();
            }
        } catch (Exception e) {
            log.error("manualLabelingMessageProducer.send.error, message:{}, result:{}", message, result, e);
        }
    }
}
