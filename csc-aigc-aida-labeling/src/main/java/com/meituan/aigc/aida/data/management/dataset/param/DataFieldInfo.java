package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据字段信息
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集管理 - 数据字段信息参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataFieldInfo implements Serializable {
    /**
     * 字段名称
     */
    private String columnName;
    
    /**
     * 数据类型
     * 1-文本 2-JSON 3-处理后信号 4-标签 5-标注项 6-质检项 7-枚举
     */
    private Integer fieldType;
} 