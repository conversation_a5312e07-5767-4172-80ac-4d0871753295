package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-23 16:00
 * @description
 */
@Data
public class DataSetVersionDTO implements Serializable {
    /**
     * 数据集名称
     */
    private String dataSetName;
    /**
     * 版本id
     */
    private Long versionId;
    /**
     * 版本名称
     */
    private String versionName;
    /**
     * 版本状态
     */
    private Integer versionStatus;
    /**
     * 数据用途
     */
    private String dataPurpose;
    /**
     * 数据格式
     */
    private String dataFormat;
    /**
     * 数据量
     */
    private Integer dataCount;
    /**
     * 字符数
     */
    private String characterCount;
    /**
     * 创建人mis
     */
    private String creatorMis;
    /**
     * 创建人名字
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 标注进度
     */
    private String labelProgress;

}
