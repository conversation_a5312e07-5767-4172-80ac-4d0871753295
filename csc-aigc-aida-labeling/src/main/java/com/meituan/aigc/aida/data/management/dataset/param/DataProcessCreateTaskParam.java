package com.meituan.aigc.aida.data.management.dataset.param;

import lombok.Data;

/**
 * 创建清洗任务参数
 */
@Data
public class DataProcessCreateTaskParam {
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据集版本
     */
    private Long versionId;
    
    /**
     * 清洗后的数据集版本名称
     */
    private String processDatasetVersionName;
} 