package com.meituan.aigc.aida.labeling.dao.mapper;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroup;
import com.meituan.aigc.aida.labeling.dao.model.LabelingTaskGroupExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LabelingTaskGroupMapper {
    long countByExample(LabelingTaskGroupExample example);

    int deleteByExample(LabelingTaskGroupExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LabelingTaskGroup record);

    int insertSelective(LabelingTaskGroup record);

    List<LabelingTaskGroup> selectByExample(LabelingTaskGroupExample example);

    LabelingTaskGroup selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LabelingTaskGroup record, @Param("example") LabelingTaskGroupExample example);

    int updateByExample(@Param("record") LabelingTaskGroup record, @Param("example") LabelingTaskGroupExample example);

    int updateByPrimaryKeySelective(LabelingTaskGroup record);

    int updateByPrimaryKey(LabelingTaskGroup record);

    List<Long> listByTaskId(@Param("taskId") Long taskId);

    Long insertGroup(LabelingTaskGroup group);
}