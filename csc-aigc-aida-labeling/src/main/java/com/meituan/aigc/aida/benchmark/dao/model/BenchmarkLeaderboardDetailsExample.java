package com.meituan.aigc.aida.benchmark.dao.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BenchmarkLeaderboardDetailsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public BenchmarkLeaderboardDetailsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andVersionIdIsNull() {
            addCriterion("version_id is null");
            return (Criteria) this;
        }

        public Criteria andVersionIdIsNotNull() {
            addCriterion("version_id is not null");
            return (Criteria) this;
        }

        public Criteria andVersionIdEqualTo(Long value) {
            addCriterion("version_id =", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotEqualTo(Long value) {
            addCriterion("version_id <>", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdGreaterThan(Long value) {
            addCriterion("version_id >", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("version_id >=", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdLessThan(Long value) {
            addCriterion("version_id <", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdLessThanOrEqualTo(Long value) {
            addCriterion("version_id <=", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdIn(List<Long> values) {
            addCriterion("version_id in", values, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotIn(List<Long> values) {
            addCriterion("version_id not in", values, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdBetween(Long value1, Long value2) {
            addCriterion("version_id between", value1, value2, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotBetween(Long value1, Long value2) {
            addCriterion("version_id not between", value1, value2, "versionId");
            return (Criteria) this;
        }

        public Criteria andModelNameIsNull() {
            addCriterion("model_name is null");
            return (Criteria) this;
        }

        public Criteria andModelNameIsNotNull() {
            addCriterion("model_name is not null");
            return (Criteria) this;
        }

        public Criteria andModelNameEqualTo(String value) {
            addCriterion("model_name =", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotEqualTo(String value) {
            addCriterion("model_name <>", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameGreaterThan(String value) {
            addCriterion("model_name >", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("model_name >=", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLessThan(String value) {
            addCriterion("model_name <", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLessThanOrEqualTo(String value) {
            addCriterion("model_name <=", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameLike(String value) {
            addCriterion("model_name like", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotLike(String value) {
            addCriterion("model_name not like", value, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameIn(List<String> values) {
            addCriterion("model_name in", values, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotIn(List<String> values) {
            addCriterion("model_name not in", values, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameBetween(String value1, String value2) {
            addCriterion("model_name between", value1, value2, "modelName");
            return (Criteria) this;
        }

        public Criteria andModelNameNotBetween(String value1, String value2) {
            addCriterion("model_name not between", value1, value2, "modelName");
            return (Criteria) this;
        }

        public Criteria andMetricValueIsNull() {
            addCriterion("metric_value is null");
            return (Criteria) this;
        }

        public Criteria andMetricValueIsNotNull() {
            addCriterion("metric_value is not null");
            return (Criteria) this;
        }

        public Criteria andMetricValueEqualTo(BigDecimal value) {
            addCriterion("metric_value =", value, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueNotEqualTo(BigDecimal value) {
            addCriterion("metric_value <>", value, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueGreaterThan(BigDecimal value) {
            addCriterion("metric_value >", value, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("metric_value >=", value, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueLessThan(BigDecimal value) {
            addCriterion("metric_value <", value, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("metric_value <=", value, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueIn(List<BigDecimal> values) {
            addCriterion("metric_value in", values, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueNotIn(List<BigDecimal> values) {
            addCriterion("metric_value not in", values, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("metric_value between", value1, value2, "metricValue");
            return (Criteria) this;
        }

        public Criteria andMetricValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("metric_value not between", value1, value2, "metricValue");
            return (Criteria) this;
        }

        public Criteria andSumIsNull() {
            addCriterion("sum is null");
            return (Criteria) this;
        }

        public Criteria andSumIsNotNull() {
            addCriterion("sum is not null");
            return (Criteria) this;
        }

        public Criteria andSumEqualTo(BigDecimal value) {
            addCriterion("sum =", value, "sum");
            return (Criteria) this;
        }

        public Criteria andSumNotEqualTo(BigDecimal value) {
            addCriterion("sum <>", value, "sum");
            return (Criteria) this;
        }

        public Criteria andSumGreaterThan(BigDecimal value) {
            addCriterion("sum >", value, "sum");
            return (Criteria) this;
        }

        public Criteria andSumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sum >=", value, "sum");
            return (Criteria) this;
        }

        public Criteria andSumLessThan(BigDecimal value) {
            addCriterion("sum <", value, "sum");
            return (Criteria) this;
        }

        public Criteria andSumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sum <=", value, "sum");
            return (Criteria) this;
        }

        public Criteria andSumIn(List<BigDecimal> values) {
            addCriterion("sum in", values, "sum");
            return (Criteria) this;
        }

        public Criteria andSumNotIn(List<BigDecimal> values) {
            addCriterion("sum not in", values, "sum");
            return (Criteria) this;
        }

        public Criteria andSumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sum between", value1, value2, "sum");
            return (Criteria) this;
        }

        public Criteria andSumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sum not between", value1, value2, "sum");
            return (Criteria) this;
        }

        public Criteria andAvgIsNull() {
            addCriterion("avg is null");
            return (Criteria) this;
        }

        public Criteria andAvgIsNotNull() {
            addCriterion("avg is not null");
            return (Criteria) this;
        }

        public Criteria andAvgEqualTo(BigDecimal value) {
            addCriterion("avg =", value, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgNotEqualTo(BigDecimal value) {
            addCriterion("avg <>", value, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgGreaterThan(BigDecimal value) {
            addCriterion("avg >", value, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("avg >=", value, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgLessThan(BigDecimal value) {
            addCriterion("avg <", value, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgLessThanOrEqualTo(BigDecimal value) {
            addCriterion("avg <=", value, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgIn(List<BigDecimal> values) {
            addCriterion("avg in", values, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgNotIn(List<BigDecimal> values) {
            addCriterion("avg not in", values, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("avg between", value1, value2, "avg");
            return (Criteria) this;
        }

        public Criteria andAvgNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("avg not between", value1, value2, "avg");
            return (Criteria) this;
        }

        public Criteria andStdIsNull() {
            addCriterion("std is null");
            return (Criteria) this;
        }

        public Criteria andStdIsNotNull() {
            addCriterion("std is not null");
            return (Criteria) this;
        }

        public Criteria andStdEqualTo(BigDecimal value) {
            addCriterion("std =", value, "std");
            return (Criteria) this;
        }

        public Criteria andStdNotEqualTo(BigDecimal value) {
            addCriterion("std <>", value, "std");
            return (Criteria) this;
        }

        public Criteria andStdGreaterThan(BigDecimal value) {
            addCriterion("std >", value, "std");
            return (Criteria) this;
        }

        public Criteria andStdGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("std >=", value, "std");
            return (Criteria) this;
        }

        public Criteria andStdLessThan(BigDecimal value) {
            addCriterion("std <", value, "std");
            return (Criteria) this;
        }

        public Criteria andStdLessThanOrEqualTo(BigDecimal value) {
            addCriterion("std <=", value, "std");
            return (Criteria) this;
        }

        public Criteria andStdIn(List<BigDecimal> values) {
            addCriterion("std in", values, "std");
            return (Criteria) this;
        }

        public Criteria andStdNotIn(List<BigDecimal> values) {
            addCriterion("std not in", values, "std");
            return (Criteria) this;
        }

        public Criteria andStdBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("std between", value1, value2, "std");
            return (Criteria) this;
        }

        public Criteria andStdNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("std not between", value1, value2, "std");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andMetricNameIsNull() {
            addCriterion("metric_name is null");
            return (Criteria) this;
        }

        public Criteria andMetricNameIsNotNull() {
            addCriterion("metric_name is not null");
            return (Criteria) this;
        }

        public Criteria andMetricNameEqualTo(String value) {
            addCriterion("metric_name =", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotEqualTo(String value) {
            addCriterion("metric_name <>", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameGreaterThan(String value) {
            addCriterion("metric_name >", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameGreaterThanOrEqualTo(String value) {
            addCriterion("metric_name >=", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLessThan(String value) {
            addCriterion("metric_name <", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLessThanOrEqualTo(String value) {
            addCriterion("metric_name <=", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLike(String value) {
            addCriterion("metric_name like", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotLike(String value) {
            addCriterion("metric_name not like", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameIn(List<String> values) {
            addCriterion("metric_name in", values, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotIn(List<String> values) {
            addCriterion("metric_name not in", values, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameBetween(String value1, String value2) {
            addCriterion("metric_name between", value1, value2, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotBetween(String value1, String value2) {
            addCriterion("metric_name not between", value1, value2, "metricName");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreIsNull() {
            addCriterion("subjective_score is null");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreIsNotNull() {
            addCriterion("subjective_score is not null");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreEqualTo(BigDecimal value) {
            addCriterion("subjective_score =", value, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreNotEqualTo(BigDecimal value) {
            addCriterion("subjective_score <>", value, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreGreaterThan(BigDecimal value) {
            addCriterion("subjective_score >", value, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("subjective_score >=", value, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreLessThan(BigDecimal value) {
            addCriterion("subjective_score <", value, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreLessThanOrEqualTo(BigDecimal value) {
            addCriterion("subjective_score <=", value, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreIn(List<BigDecimal> values) {
            addCriterion("subjective_score in", values, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreNotIn(List<BigDecimal> values) {
            addCriterion("subjective_score not in", values, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("subjective_score between", value1, value2, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andSubjectiveScoreNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("subjective_score not between", value1, value2, "subjectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreIsNull() {
            addCriterion("objective_score is null");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreIsNotNull() {
            addCriterion("objective_score is not null");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreEqualTo(BigDecimal value) {
            addCriterion("objective_score =", value, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreNotEqualTo(BigDecimal value) {
            addCriterion("objective_score <>", value, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreGreaterThan(BigDecimal value) {
            addCriterion("objective_score >", value, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("objective_score >=", value, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreLessThan(BigDecimal value) {
            addCriterion("objective_score <", value, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreLessThanOrEqualTo(BigDecimal value) {
            addCriterion("objective_score <=", value, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreIn(List<BigDecimal> values) {
            addCriterion("objective_score in", values, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreNotIn(List<BigDecimal> values) {
            addCriterion("objective_score not in", values, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("objective_score between", value1, value2, "objectiveScore");
            return (Criteria) this;
        }

        public Criteria andObjectiveScoreNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("objective_score not between", value1, value2, "objectiveScore");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}