package com.meituan.aigc.aida.data.management.fetch.mq.producer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.csccratos.aida.common.core.exception.biz.AidaMqProducerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * 数据埋点延迟执行机器人生产者
 * @Author: guowenhui
 * @Create: 2025/5/7 15:54
 * @Version: 1.0
 */
@Slf4j
@Component
public class DelayTriggerRobotToMafkaProducer {

    @Autowired
    @Qualifier("delayTriggerRobotProducer")
    private IProducerProcessor producer;

    public void sendDelayMessage(String message, Long delayTime) {
        log.info("DelayTriggerRobotToMafkaProducer.sendDelayMessage.start, message:{}, delayTime:{}", message, delayTime);
        ProducerResult result = null;
        try {
            result = producer.sendDelayMessage(message, delayTime);
            if (ProducerStatus.SEND_FAILURE.equals(result.getProducerStatus())) {
                throw new AidaMqProducerException();
            }
        } catch (Exception e) {
            log.error("DelayTriggerRobotToMafkaProducer.sendDelayMessage.error, message:{}, result:{}", message, result, e);
            Cat.logErrorWithCategory("DelayTriggerRobotToMafkaProducer.sendDelayMessage.error", e);
        }
    }

}
