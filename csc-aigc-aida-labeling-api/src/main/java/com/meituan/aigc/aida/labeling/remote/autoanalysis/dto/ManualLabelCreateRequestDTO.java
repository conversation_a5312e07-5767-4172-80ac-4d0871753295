package com.meituan.aigc.aida.labeling.remote.autoanalysis.dto;

import com.meituan.aigc.aida.labeling.remote.common.dto.ManualLabelResultDTO;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 人工标注任务创建请求
 *
 * <AUTHOR>
 * @date 2025/05/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ManualLabelCreateRequestDTO implements Serializable {

    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 上传文件S3 URL
     */
    private String fileS3Url;

    /**
     * 上传文件原始名称
     */
    private String fileOriginalName;

    /**
     * 标注负责人mis
     */
    private String responsiblePerson;

    /**
     * 标注负责人名称
     */
    private String responsiblePersonName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 自定义标注项
     */
    private List<List<ManualLabelResultDTO>> labelResultList;
}
