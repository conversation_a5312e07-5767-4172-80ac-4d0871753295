package com.meituan.aigc.aida.labeling.convert;

import com.meituan.aigc.aida.labeling.dao.model.LabelingTask;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * @Author: guowenhui
 * @Create: 2025/6/10 15:11
 * @Version: 1.0
 */
@Mapper
public interface LabelingTaskConverter {

    LabelingTaskConverter INSTANCE = Mappers.getMapper(LabelingTaskConverter.class);

    /**
     * 复标任务复制
     * @param labelingTask 要复制的对象
     * @return 复制结果
     */
    @Mappings(value ={
        @Mapping(target = "id", ignore = true),
        @Mapping(target = "qualityCheckStatus", ignore = true),
        @Mapping(target = "checkFinishTime", ignore = true),
        @Mapping(target = "labelingFinishTime", ignore = true),
        @Mapping(target = "labelingStatus", expression = "java(com.meituan.aigc.aida.labeling.common.enums.LabelingTaskStatus.RE_LABELING.getCode())"),
        @Mapping(target = "updateTime", expression = "java(com.meituan.aigc.aida.labeling.util.DateUtil.getCurrentTimePlus30SecondsAsDate())"),
        @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    })
    LabelingTask converterToLabelingTask(LabelingTask labelingTask);

}
