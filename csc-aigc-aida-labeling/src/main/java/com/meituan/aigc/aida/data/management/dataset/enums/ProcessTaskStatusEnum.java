package com.meituan.aigc.aida.data.management.dataset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据处理任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum ProcessTaskStatusEnum {
    /**
     * 任务状态: 1-创建中 2-待处理 3-打标中 4-打标完成 5-打标失败
     */
    CREATING(1, "创建中"),
    WAITING(2, "待处理"),
    LABELING(3, "打标中"),
    LABELED(4, "打标完成"),
    FAILED(5, "打标失败"),
    ;

    private final Integer code;
    private final String desc;
} 