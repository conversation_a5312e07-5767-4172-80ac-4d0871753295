package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 数据集字段列表查询响应
 *
 * <AUTHOR>
 * @date 2025-06-04
 * @description 数据集字段列表查询响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetFieldListResponseDTO implements Serializable {

    /**
     * 字段列表
     */
    private List<String> fieldList;
} 