package com.meituan.aigc.aida.config;

import com.meituan.aigc.aida.common.constants.TrainingCommonConstants;
import com.meituan.aigc.aida.data.management.es.DatasetEsBaseIndexService;
import com.meituan.aigc.aida.data.management.es.DatasetSegBaseIndexService;
import com.sankuai.meituan.poros.client.PorosHighLevelClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 数据集管理ES客户端配置
 *
 * <AUTHOR>
 * @date 2025/05/24
 */
@Configuration
public class EsClientConfiguration {

    /**
     * ES-Lion配置
     */
    @Resource
    private EsLionConfig esLionConfig;

    @Bean
    public RestHighLevelClient aidaDatasetEsHighLevelClient() {
        return PorosHighLevelClientBuilder.builder()
                .clusterName(esLionConfig.getEsClusterName())
                .appKey(TrainingCommonConstants.TRAIN_APPKEY)
                .timeoutMillis(esLionConfig.getTimeoutMillis())
                .build();
    }

    @Bean
    public DatasetEsBaseIndexService aidaDatasetEsBaseIndexService(@Qualifier("aidaDatasetEsHighLevelClient") RestHighLevelClient client) {
        return new DatasetEsBaseIndexService(client);
    }

    @Bean
    public DatasetSegBaseIndexService aidaDatasetSegBaseIndexService(@Qualifier("aidaDatasetEsHighLevelClient") RestHighLevelClient client) {
        return new DatasetSegBaseIndexService(client);
    }
}