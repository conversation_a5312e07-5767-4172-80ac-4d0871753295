package com.meituan.aigc.aida.labeling.dao.po;

import lombok.Data;

/**
 * 质检任务PO
 *
 * <AUTHOR>
 */
@Data
public class QualityCheckTaskPO {

    /**
     * 质检任务ID
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据来源
     */
    private Integer dataSetSource;

    /**
     * 质检人Mis
     */
    private String qualityCheckMis;

    /**
     * 质检人Mis名称
     */
    private String qualityCheckName;

    /**
     * 样本数量
     */
    private Integer sampleCount;

    /**
     * 质检进度
     */
    private Integer qualityCheckProgress;

    /**
     * 质检状态
     */
    private Integer qualityCheckStatus;

    /**
     * 标注任务类型 1:评测集标注 2:训练集标注 3:线上会话标注
     *
     * @see com.meituan.aigc.aida.labeling.common.enums.LabelingTaskType
     */
    private Integer taskType;
    /**
     * 未完成数量
     */
    private Integer unFinishedCount;

    /**
     * 未完成会话数量
     */
    private Integer unFinishedSessionCount;

    /**
     * 样本会话数量
     */
    private Integer sampleSessionCount;
}
