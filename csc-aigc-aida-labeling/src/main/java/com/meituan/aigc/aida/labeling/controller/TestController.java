package com.meituan.aigc.aida.labeling.controller;

import com.meituan.aigc.aida.labeling.common.constant.CommonConstants;
import com.meituan.aigc.aida.labeling.common.s3.S3Service;
import com.meituan.aigc.aida.labeling.config.LionConfig;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/api/aigc/aida/test")
@Slf4j
public class TestController {

    @Resource
    private S3Service s3Service;

    @Resource
    private LionConfig lionConfig;

    @GetMapping
    public String sayHello() {
        return "hello world";
    }

    /**
     * 上传导入模版
     *
     * @param file             文件内容
     * @param templateFilePath 路径
     * @return 上传后路径
     */
    @PostMapping("/upLoadTemplate")
    public Result<String> upLoadTemplate(@RequestParam MultipartFile file,
                                         @RequestParam String templateFilePath) {
        log.info("上传模板文件请求，文件名: {}, 模板路径: {}", file.getOriginalFilename(), templateFilePath);
        String mis = Optional.ofNullable(UserUtils.getUser()).map(User::getLogin).orElse("unknwon");
        // 权限校验
        if ("unknwon".equals(mis) || !lionConfig.getAdministratorsList().contains(mis)) {
            log.warn("用户无权限上传模板文件，MIS: {}", mis);
            return Result.ok("无操作权限");
        }
        if (Objects.isNull(file)) {
            log.warn("上传的文件为空");
            return Result.ok("请上传文件");
        }
        if (StringUtils.isNotBlank(templateFilePath)) {
            templateFilePath = String.format("%s-%s-%s", UUID.randomUUID().toString().replace("-", ""),
                    new SimpleDateFormat(CommonConstants.DatePattern.COMPACT_DATE).format(new Date()),
                    templateFilePath);
        }
        String uploadPath = s3Service.upload(file, templateFilePath);
        log.info("模板文件上传成功，路径: {}", uploadPath);
        return Result.ok(uploadPath);
    }

}
