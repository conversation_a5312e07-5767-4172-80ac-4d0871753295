package com.meituan.aigc.aida.data.management.es;

import com.meituan.aigc.aida.common.enums.EsPropertyTypeEnum;
import com.meituan.aigc.aida.data.management.es.query.*;
import com.meituan.aigc.aida.labeling.Application;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DatasetEsIndexService 集成测试
 * 需要真实的ES环境才能运行
 *
 * <AUTHOR>
 * @date 2025/5/24
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
class DatasetEsIndexServiceIntegrationTest {

    @Resource
    private DatasetEsIndexService datasetEsIndexService;

    @Resource
    private DatasetEsBaseIndexService datasetEsBaseIndexService;

    private List<String> testDatasetIds = new ArrayList<>();

    @BeforeEach
    void setUp() {
        // 清理测试数据
        testDatasetIds.clear();

        // 检查依赖注入是否成功
        assertNotNull(datasetEsIndexService, "DatasetEsIndexService should be injected");
        assertNotNull(datasetEsBaseIndexService, "DatasetEsBaseIndexService should be injected");

        System.out.println("DatasetEsIndexService: " + datasetEsIndexService.getClass().getName());
        System.out.println("DatasetEsBaseIndexService: " + datasetEsBaseIndexService.getClass().getName());
    }

    /**
     * 创建测试用的DatasetEsIndex对象
     */
    private DatasetEsIndex createTestDataset(String datasetId, String dataSource) {
        DatasetEsIndex dataset = new DatasetEsIndex();
        dataset.setDocumentId(UUID.randomUUID().toString());
        dataset.setDatasetId(datasetId);
        dataset.setVersionId("v1.0.0");
        dataset.setSessionId("hahahaha");
        dataset.setDataSource(dataSource);
        dataset.setFieldMapping("integration-test-mapping");

        // 设置复杂字段
        Map<String, String> textData = new HashMap<>();
        textData.put("title", "集成测试标题-" + datasetId);
        textData.put("content", "集成测试内容-" + datasetId);
        textData.put("description", "这是一个集成测试数据集");

        Map<String, Object> flattenedData = new HashMap<>();

        // JSON对象1：用户信息
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", "user_" + System.currentTimeMillis());
        userInfo.put("userName", "测试用户");
        userInfo.put("department", "AI研发部");
        userInfo.put("role", "数据标注员");
        userInfo.put("permissions", Arrays.asList("read", "write", "label"));
        flattenedData.put("userInfo", userInfo);

        // JSON对象2：任务配置
        Map<String, Object> taskConfig = new HashMap<>();
        taskConfig.put("taskId", "task_" + datasetId);
        taskConfig.put("taskType", "text_classification");
        taskConfig.put("priority", 1);
        taskConfig.put("deadline", "2025-06-30");

        Map<String, Object> labelConfig = new HashMap<>();
        labelConfig.put("categories", Arrays.asList("正面", "负面", "中性"));
        labelConfig.put("multiLabel", false);
        labelConfig.put("confidence", 0.85);
        taskConfig.put("labelConfig", labelConfig);

        flattenedData.put("taskConfig", taskConfig);

        // 其他简单字段
        flattenedData.put("category", "IntegrationTest");
        flattenedData.put("environment", "test");

        dataset.setFlattenedData(flattenedData);

        Map<String, Date> dateData = new HashMap<>();
        dateData.put("testTime", new Date());
        dateData.put("createTime", new Date());
        dataset.setDateData(dateData);

        testDatasetIds.add(datasetId);
        return dataset;
    }

    @Test
    void testServiceInjection() {
        // 测试服务是否正确注入
        assertNotNull(datasetEsIndexService, "DatasetEsIndexService should be injected");
        assertNotNull(datasetEsBaseIndexService, "DatasetEsBaseIndexService should be injected");

        System.out.println("✅ 服务注入测试通过");
    }

    @Test
    void testInsertDataset_RealES() {
        // Given
        String datasetId = "integration-test-dataset-" + System.currentTimeMillis();
        DatasetEsIndex dataset = createTestDataset(datasetId, "case");

        System.out.println("🚀 开始测试插入数据集: " + datasetId);

        // When
        boolean result = datasetEsIndexService.insertDataset(dataset, "case");
    }

    @Test
    void testBatchInsertDatasets_RealES() {
        // Given
        long timestamp = System.currentTimeMillis();
        List<DatasetEsIndex> datasetList = Arrays.asList(
                createTestDataset("batch-test-1-" + timestamp, "case"),
                createTestDataset("batch-test-2-" + timestamp, "case"),
                createTestDataset("batch-test-3-" + timestamp, "case")
        );

        // When
        BatchResult result = datasetEsIndexService.batchInsertDatasets(datasetList, "case", "123");

        // Then
        //assertTrue(result, "批量插入数据集应该成功");

        // 验证所有数据集都设置了时间
        datasetList.forEach(dataset -> {
            assertNotNull(dataset.getCreateTime(), "创建时间应该被设置");
            assertNotNull(dataset.getUpdateTime(), "更新时间应该被设置");
        });
    }


    @Test
    void testInsertDataset_DifferentDataSources() {
        // Given
        long timestamp = System.currentTimeMillis();
        DatasetEsIndex caseDataset = createTestDataset("case-dataset-" + timestamp, "case");
        DatasetEsIndex onlineDataset = createTestDataset("online-dataset-" + timestamp, "online");
        DatasetEsIndex uploadDataset = createTestDataset("upload-dataset-" + timestamp, "upload");

        // When
        boolean caseResult = datasetEsIndexService.insertDataset(caseDataset, "case");
        boolean onlineResult = datasetEsIndexService.insertDataset(onlineDataset, "online");
        boolean uploadResult = datasetEsIndexService.insertDataset(uploadDataset, "upload");

        // Then
        assertTrue(caseResult, "case数据源插入应该成功");
        assertTrue(onlineResult, "online数据源插入应该成功");
        assertTrue(uploadResult, "upload数据源插入应该成功");
    }

    @Test
    void testInsertDataset_LargeData() {
        // Given
        String datasetId = "large-data-test-" + System.currentTimeMillis();
        DatasetEsIndex dataset = createTestDataset(datasetId, "case");

        // 创建大量平铺数据
        Map<String, Object> largeFlattenedData = new HashMap<>();
        for (int i = 0; i < 50; i++) {
            largeFlattenedData.put("meta_" + i, "metadata_value_" + i);
        }
        dataset.setFlattenedData(largeFlattenedData);

        // When
        boolean result = datasetEsIndexService.insertDataset(dataset, "case");

        // Then
        assertTrue(result, "大数据量插入应该成功");
    }

    @Test
    void testBatchInsertDatasets_LargeBatch() {
        // Given
        long timestamp = System.currentTimeMillis();
        List<DatasetEsIndex> largeDatasetList = new ArrayList<>();

        for (int i = 0; i < 10; i++) {
            DatasetEsIndex dataset = createTestDataset("large-batch-" + i + "-" + timestamp, "case");
            largeDatasetList.add(dataset);
        }

        // When
        BatchResult result = datasetEsIndexService.batchInsertDatasets(largeDatasetList, "case", "123");
    }

    @Test
    void testInsertDataset_SpecialCharacters() {
        // Given
        String datasetId = "special-chars-测试-" + System.currentTimeMillis();
        DatasetEsIndex dataset = createTestDataset(datasetId, "case");

        Map<String, Object> textData = new HashMap<>();
        textData.put("title", "包含特殊字符的标题：!@#$%^&*()_+-=[]{}|;':\",./<>?");
        textData.put("content", "包含中文、English、数字123、符号！@#和emoji😀🎉🚀的内容");
        textData.put("json_like", "{\"key\": \"value\", \"number\": 123, \"boolean\": true}");
//        dataset.setTextData(textData);

        // When
        boolean result = datasetEsIndexService.insertDataset(dataset, "case");

        // Then
        assertTrue(result, "包含特殊字符的数据插入应该成功");
    }

    @Test
    void testInsertDataset_NullAndEmptyFields() {
        // Given
        String datasetId = "null-empty-test-" + System.currentTimeMillis();
        DatasetEsIndex dataset = createTestDataset(datasetId, "case");

        // 设置一些null和空值
        Map<String, Object> textData = new HashMap<>();
        textData.put("title", "正常标题");
        textData.put("empty_field", "");
        textData.put("null_field", null);
        textData.put("whitespace_field", "   ");
//        dataset.setTextData(textData);

        // When
        boolean result = datasetEsIndexService.insertDataset(dataset, "case");

        // Then
        assertTrue(result, "包含null和空字段的数据插入应该成功");
    }

    @Test
    void testInsertDataset_PreserveExistingTimestamps() {
        // Given
        String datasetId = "preserve-time-test-" + System.currentTimeMillis();
        DatasetEsIndex dataset = createTestDataset(datasetId, "case");

        // 设置已存在的时间戳
        Date existingCreateTime = new Date(System.currentTimeMillis() - 86400000); // 1天前
        Date existingUpdateTime = new Date(System.currentTimeMillis() - 3600000);  // 1小时前
        dataset.setCreateTime(existingCreateTime);
        dataset.setUpdateTime(existingUpdateTime);

        // When
        boolean result = datasetEsIndexService.insertDataset(dataset, "case");

        // Then
        assertTrue(result, "插入应该成功");
        assertEquals(existingCreateTime, dataset.getCreateTime(), "应该保留原有的创建时间");
        assertEquals(existingUpdateTime, dataset.getUpdateTime(), "应该保留原有的更新时间");
    }

    /**
     * 性能测试：测试批量插入的性能
     */
    @Test
    void testBatchInsertDatasets_Performance() {
        // Given
        long timestamp = System.currentTimeMillis();
        List<DatasetEsIndex> datasetList = new ArrayList<>();

        // 创建1000个数据集
        for (int i = 0; i < 1000; i++) {
            DatasetEsIndex dataset = createTestDataset("perf-test-" + i + "-" + timestamp, "case");
            datasetList.add(dataset);
        }

        // When
        long startTime = System.currentTimeMillis();
        BatchResult result = datasetEsIndexService.batchInsertDatasets(datasetList, "case", "123");
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // Then
        System.out.println("批量插入1000条数据耗时: " + duration + "ms");

        // 性能断言：1000条数据插入应该在30秒内完成
        assertTrue(duration < 30000, "批量插入1000条数据应该在30秒内完成，实际耗时: " + duration + "ms");
    }

    @Test
    void testUpdateDataset_RealES() {
        // Given
        String datasetId = "update-test-dataset-" + System.currentTimeMillis();
        DatasetEsIndex dataset = createTestDataset(datasetId, "case");

        // 先插入数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试更新数据集: " + datasetId);

        // When
        boolean updateResult = datasetEsIndexService.updateDataset(dataset, "case", "train_dataset_case_2025-05");

        // Then
        assertTrue(updateResult, "数据更新应该成功");
    }

    @Test
    void testBatchUpdateDatasets_RealES() {
        // Given
        long timestamp = System.currentTimeMillis();
        List<DatasetEsIndex> datasetList = Arrays.asList(
                createTestDataset("batch-update-1-" + timestamp, "case"),
                createTestDataset("batch-update-2-" + timestamp, "case"),
                createTestDataset("batch-update-3-" + timestamp, "case")
        );

        // 先批量插入数据
        BatchResult insertResult = datasetEsIndexService.batchInsertDatasets(datasetList, "case", "123");
        assertNotNull(insertResult, "批量插入结果不应为空");
        assertTrue(insertResult.isAllSuccess(), "批量插入应该全部成功");

        System.out.println("🚀 开始测试批量更新数据集");

        // When
        BatchResult updateResult = datasetEsIndexService.batchUpdateDatasets(datasetList, "case", insertResult.getIndex());

        // Then
        assertNotNull(updateResult, "批量更新结果不应为空");
        assertTrue(updateResult.isAllSuccess(), "批量更新应该全部成功");
        assertEquals(datasetList.size(), updateResult.getSuccessCount(), "成功更新数量应该等于数据集数量");
    }

    @Test
    void testDeleteByDocId_RealES() {
        // Given
        String datasetId = "delete-test-dataset-" + System.currentTimeMillis();
        DatasetEsIndex dataset = createTestDataset(datasetId, "case");

        // 先插入数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试删除数据集: " + datasetId);
        System.out.println("文档ID: " + dataset.getDocumentId());

        // When
        boolean deleteResult = datasetEsIndexService.deleteByDocId(
            dataset.getDocumentId(),
            "case",
            "train_dataset_case_2025-05"
        );

        // Then
        assertTrue(deleteResult, "数据删除应该成功");
    }

    @Test
    void testBatchDeleteDatasets_RealES() {
        // Given
        long timestamp = System.currentTimeMillis();
        List<DatasetEsIndex> datasetList = Arrays.asList(
                createTestDataset("batch-delete-1-" + timestamp, "case"),
                createTestDataset("batch-delete-2-" + timestamp, "case"),
                createTestDataset("batch-delete-3-" + timestamp, "case")
        );

        // 先批量插入数据
        BatchResult insertResult = datasetEsIndexService.batchInsertDatasets(datasetList, "case", "123");
        assertNotNull(insertResult, "批量插入结果不应为空");
        assertTrue(insertResult.isAllSuccess(), "批量插入应该全部成功");

        System.out.println("🚀 开始测试批量删除数据集");
        datasetList.forEach(dataset ->
            System.out.println("文档ID: " + dataset.getDocumentId())
        );

        // When
        BatchResult deleteResult = datasetEsIndexService.batchDeleteDatasets(
            datasetList,
            "case",
            insertResult.getIndex()
        );

        // Then
        assertNotNull(deleteResult, "批量删除结果不应为空");
        assertTrue(deleteResult.isAllSuccess(), "批量删除应该全部成功");
        assertEquals(datasetList.size(), deleteResult.getSuccessCount(), "成功删除数量应该等于数据集数量");
    }

    @Test
    void testDeleteField_SimpleField_RealES() {
        // Given
//        String datasetId = "field-delete-test-" + System.currentTimeMillis();
//        DatasetEsIndex dataset = createTestDataset(datasetId, "case");
//
//        // 先插入数据
//        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
//        assertTrue(insertResult, "数据插入应该成功");
//
//        System.out.println("🚀 开始测试删除字段: " + datasetId);

        // When - 删除textData中的title字段
        boolean deleteResult = datasetEsIndexService.deleteField(
            "flattenedData.environment",
            "train_dataset_case_2025-05",
            "field-delete-test-1748347650938",
            "v1.0.0"
        );

        // Then
        assertTrue(deleteResult, "字段删除应该成功");
    }

    @Test
    void testDeleteField_NestedField_RealES() {
        // Given
        String datasetId = "nested-field-delete-" + System.currentTimeMillis();
        DatasetEsIndex dataset = createTestDataset(datasetId, "case");

        // 添加嵌套字段数据
        Map<String, Object> nestedData = new HashMap<>();
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("name", "测试用户");
        userInfo.put("age", 25);
        userInfo.put("email", "<EMAIL>");
        nestedData.put("userInfo", userInfo);
        dataset.setNestedData(nestedData);

        // 先插入数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试删除嵌套字段");

        // When - 删除嵌套字段中的email
        boolean deleteResult = datasetEsIndexService.deleteField(
            "nestedData.userInfo.email",
            "train_dataset_case_2025-05",
            dataset.getDatasetId(),
            dataset.getVersionId()
        );

        // Then
        assertTrue(deleteResult, "嵌套字段删除应该成功");
    }

    @Test
    void testDeleteField_MultipleDocuments_RealES() {
        // Given
        long timestamp = System.currentTimeMillis();
        List<DatasetEsIndex> datasetList = Arrays.asList(
                createTestDataset("multi-field-delete-1-" + timestamp, "case"),
                createTestDataset("multi-field-delete-2-" + timestamp, "case"),
                createTestDataset("multi-field-delete-3-" + timestamp, "case")
        );

        // 为所有数据集添加相同的测试字段
        datasetList.forEach(dataset -> {
            Map<String, Object> flattenedData = dataset.getFlattenedData();
            flattenedData.put("test_field", "要删除的值");
            flattenedData.put("keep_field", "保留的值");
            dataset.setFlattenedData(flattenedData);
        });

        // 批量插入数据
        BatchResult insertResult = datasetEsIndexService.batchInsertDatasets(datasetList, "case", "123");
        assertNotNull(insertResult, "批量插入结果不应为空");
        assertTrue(insertResult.isAllSuccess(), "批量插入应该全部成功");

        System.out.println("🚀 开始测试删除多个文档中的字段");

        // When - 删除所有文档中的test_field字段
        boolean deleteResult = datasetEsIndexService.deleteField(
            "flattenedData.test_field",
            insertResult.getIndex(),
            datasetList.get(0).getDatasetId(),
            datasetList.get(0).getVersionId()
        );

        // Then
        assertTrue(deleteResult, "多文档字段删除应该成功");
    }

    @Test
    void testPageQuery_BasicPagination_RealES() {
        // Given - 准备测试数据
        System.out.println("🚀 开始测试分页查询");

        // When - 执行分页查询
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .pageNum(0)  // 第一页
                .pageSize(10) // 每页10条
                .sortField("dateData.createTime")  // 按创建时间排序
                .sortOrder(SortOrder.DESC)  // 降序排序
                .build();

        // 添加数据集ID查询条件
        EsSearchProperty esSearchProperty = new EsSearchProperty();
        // nested下钻
//        esSearchProperty.setNested(true);
//        esSearchProperty.setPath("textData.title");
//        esSearchProperty.setFieldName("title");
//        esSearchProperty.setFieldType(EsPropertyTypeEnum.NESTED);
//        esSearchProperty.setNestedPath("textData");
        // flattened的前缀查找
        esSearchProperty.setNested(false);
        esSearchProperty.setFieldName("taskId");
        esSearchProperty.setFieldType(EsPropertyTypeEnum.KEYWORD);
        esSearchProperty.setPath("flattenedData.taskConfig.taskId");

        //condition.setMatchPhraseCondition(new HashMap<>());
        //condition.getMatchPhraseCondition().put(esSearchProperty, "集成测试标题-large-batch-2");
        //condition.addMatchCondition("title", "textData.title", "集成测试标题-large-batch-2");
        condition.setPrefixCondition(new HashMap<>());
        condition.getPrefixCondition().put(esSearchProperty, "task_large-batch-2");

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);

        // Then - 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertTrue(result.getTotal() >= 0, "总记录数应该大于等于0");
        assertEquals(0, result.getPageNum(), "当前页码应该是0");
        assertEquals(10, result.getPageSize(), "每页大小应该是10");

        System.out.println("查询结果总数: " + result.getTotal());
        System.out.println("返回记录数: " + result.getRecords().size());
    }

//    @Test
//    void testPageQuery_WithConditions_RealES() {
//        // Given - 准备测试数据
//        long timestamp = System.currentTimeMillis();
//        String testDatasetId = "condition-query-" + timestamp;
//        DatasetEsIndex dataset = createTestDataset(testDatasetId, "case");
//
//        // 设置特定的测试数据
//        Map<String, String> textData = new HashMap<>();
//        textData.put("title", "特殊标题用于测试");
//        textData.put("content", "这是一个用于测试条件查询的内容");
//        dataset.setTextData(textData);
//
//        Map<String, Object> flattenedData = new HashMap<>();
//        flattenedData.put("category", "测试分类");
//        flattenedData.put("tags", Arrays.asList("测试", "查询", "集成测试"));
//        dataset.setFlattenedData(flattenedData);
//
//        // 插入测试数据
//        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
//        assertTrue(insertResult, "数据插入应该成功");
//
//        System.out.println("🚀 开始测试条件查询");
//
//        // When - 执行条件查询
//        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
//                .dataSource("case")
//                .includeHighlight(true)    // 启用高亮
//                .highlightFields(Arrays.asList("textData.title", "textData.content"))  // 设置高亮字段
//                .pageNum(0)
//                .pageSize(10)
//                .build();
//
//        // 添加数据集ID查询条件
//        condition.addTermCondition("datasetId", "datasetId", testDatasetId);
//
//        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);
//
//        // Then - 验证结果
//        assertNotNull(result, "查询结果不应为空");
//        assertTrue(result.getTotal() > 0, "应该至少找到一条记录");
//
//        // 验证返回的数据
//        DatasetEsIndex foundDataset = result.getRecords().get(0);
//        assertEquals(testDatasetId, foundDataset.getDatasetId(), "应该找到指定的数据集");
//
//        // 验证高亮结果
//        assertNotNull(result.getHighlights(), "应该有高亮结果");
//        assertTrue(result.getHighlights().containsKey(foundDataset.getDocumentId()),
//                "应该包含文档的高亮信息");
//    }

    @Test
    void testPageQuery_MultipleDataSources_RealES() {
        // Given - 准备测试数据
        long timestamp = System.currentTimeMillis();

        // 创建并插入不同数据源的测试数据
        DatasetEsIndex caseDataset = createTestDataset("multi-source-case-" + timestamp, "case");
        DatasetEsIndex onlineDataset = createTestDataset("multi-source-online-" + timestamp, "online");
        DatasetEsIndex uploadDataset = createTestDataset("multi-source-upload-" + timestamp, "upload");

        datasetEsIndexService.insertDataset(caseDataset, "case");
        datasetEsIndexService.insertDataset(onlineDataset, "online");
        datasetEsIndexService.insertDataset(uploadDataset, "upload");

        System.out.println("🚀 开始测试多数据源查询");

        // When - 执行多数据源查询（分别查询每个数据源）
        List<String> dataSources = Arrays.asList("case", "online", "upload");
        List<DatasetEsIndex> allResults = new ArrayList<>();

        for (String dataSource : dataSources) {
            DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                    .dataSource(dataSource)  // 单个数据源查询
                    .pageNum(0)
                    .pageSize(20)
                    .sortField("createTime")
                    .sortOrder(SortOrder.DESC)
                    .build();

            DatasetPageResult result = datasetEsIndexService.pageQuery(condition);
            if (result != null && result.getRecords() != null) {
                allResults.addAll(result.getRecords());
            }
        }

        // Then - 验证结果
        assertTrue(allResults.size() >= 3, "应该至少找到3条记录");

        // 验证返回的数据包含不同数据源的记录
        List<String> foundDataSources = allResults.stream()
                .map(DatasetEsIndex::getDataSource)
                .distinct()
                .collect(Collectors.toList());
        assertTrue(foundDataSources.size() > 1, "应该包含多个数据源的记录");
    }

    @Test
    void testPageQuery_WithTextSearch_RealES() {
        // Given - 准备测试数据
        long timestamp = System.currentTimeMillis();
        String testDatasetId = "text-search-" + timestamp;
        DatasetEsIndex dataset = createTestDataset(testDatasetId, "case");


        // 插入测试数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试文本搜索");

        // When - 执行文本搜索查询
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .includeHighlight(true)
                .highlightFields(Arrays.asList("textData.title", "textData.content"))
                .pageNum(0)
                .pageSize(10)
                .build();

        // 添加文本搜索条件
        condition.addMatchCondition("tittle", "textData.title", "Elasticsearch");
        condition.addMatchCondition("content", "textData.content", "机器学习");

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);

        // Then - 验证结果
        assertNotNull(result, "查询结果不应为空");

        if (result.getTotal() > 0) {
            DatasetEsIndex foundDataset = result.getRecords().get(0);
            assertEquals(testDatasetId, foundDataset.getDatasetId(), "应该找到指定的数据集");

            // 验证高亮结果
            if (result.getHighlights() != null && result.getHighlights().containsKey(foundDataset.getDocumentId())) {
                System.out.println("高亮结果: " + result.getHighlights().get(foundDataset.getDocumentId()));
            }
        }
    }

    @Test
    void testPageQuery_WithRangeQuery_RealES() {
        // Given - 准备测试数据
        long timestamp = System.currentTimeMillis();
        String testDatasetId = "range-query-" + timestamp;
        DatasetEsIndex dataset = createTestDataset(testDatasetId, "case");

        // 插入测试数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试范围查询");

        // When - 执行范围查询
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .pageNum(0)
                .pageSize(10)
                .sortField("createTime")
                .sortOrder(SortOrder.DESC)
                .build();

        // 添加时间范围查询条件
        Date now = new Date();
        Date oneHourAgo = new Date(now.getTime() - 3600000); // 1小时前
        condition.addRangeCondition("createTime", "createTime",
            DatasetEsQueryCondition.RangeCondition.between(oneHourAgo, now));

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);

        // Then - 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertTrue(result.getTotal() >= 0, "总记录数应该大于等于0");

        System.out.println("范围查询结果总数: " + result.getTotal());
    }

    @Test
    void testPageQuery_WithWildcardQuery_RealES() {
        // Given - 准备测试数据
        long timestamp = System.currentTimeMillis();
        String testDatasetId = "wildcard-test-" + timestamp;
        DatasetEsIndex dataset = createTestDataset(testDatasetId, "case");

        // 设置特定的数据
        Map<String, Object> flattenedData = new HashMap<>();
        flattenedData.put("category", "test-category-123");
        flattenedData.put("environment", "production");
        dataset.setFlattenedData(flattenedData);

        // 插入测试数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试通配符查询");

        // When - 执行通配符查询
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .pageNum(0)
                .pageSize(10)
                .build();

        // 添加通配符查询条件
        condition.addWildcardCondition("category", "flattenedData.category", "test-*");

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);

        // Then - 验证结果
        assertNotNull(result, "查询结果不应为空");
        System.out.println("通配符查询结果总数: " + result.getTotal());
    }

    @Test
    void testPageQuery_WithPrefixQuery_RealES() {
        // Given - 准备测试数据
        long timestamp = System.currentTimeMillis();
        String testDatasetId = "prefix-test-" + timestamp;
        DatasetEsIndex dataset = createTestDataset(testDatasetId, "case");

        // 设置特定的数据
        Map<String, Object> flattenedData = new HashMap<>();
        flattenedData.put("category", "integration-test-category");
        dataset.setFlattenedData(flattenedData);

        // 插入测试数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试前缀查询");

        // When - 执行前缀查询
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .pageNum(0)
                .pageSize(10)
                .build();

        // 添加前缀查询条件
        condition.addPrefixCondition("category", "flattenedData.category", "integration");

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);

        // Then - 验证结果
        assertNotNull(result, "查询结果不应为空");
        System.out.println("前缀查询结果总数: " + result.getTotal());
    }

    @Test
    void testPageQuery_WithExistsQuery_RealES() {
        // Given - 准备测试数据
        long timestamp = System.currentTimeMillis();
        String testDatasetId = "exists-test-" + timestamp;
        DatasetEsIndex dataset = createTestDataset(testDatasetId, "case");

        // 插入测试数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试存在性查询");

        // When - 执行存在性查询
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .pageNum(0)
                .pageSize(10)
                .build();

        // 添加存在性查询条件
        condition.addExistsCondition("title", "textData.title", true);  // 查询存在title字段的文档
        condition.addExistsCondition("nonExistentField", "nonExistentField", false);  // 查询不存在某字段的文档

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);

        // Then - 验证结果
        assertNotNull(result, "查询结果不应为空");
        System.out.println("存在性查询结果总数: " + result.getTotal());
    }




    @Test
    void testPageQuery_WithMultipleConditions_RealES() {
        // Given - 准备测试数据
        long timestamp = System.currentTimeMillis();
        String testDatasetId = "multi-condition-test-" + timestamp;
        DatasetEsIndex dataset = createTestDataset(testDatasetId, "case");

        // 设置复杂的测试数据
        Map<String, Object> textData = new HashMap<>();
        textData.put("title", "Multi-condition Test Document");
        textData.put("content", "This is a comprehensive test for multiple query conditions");
        dataset.setTextData(textData);

        Map<String, Object> flattenedData = new HashMap<>();
        flattenedData.put("category", "test-category");
        flattenedData.put("priority", "high");
        flattenedData.put("status", "active");
        dataset.setFlattenedData(flattenedData);

        // 插入测试数据
        boolean insertResult = datasetEsIndexService.insertDataset(dataset, "case");
        assertTrue(insertResult, "数据插入应该成功");

        System.out.println("🚀 开始测试多条件组合查询");

        // When - 执行多条件查询
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .pageNum(0)
                .pageSize(10)
                .sortField("createTime")
                .sortOrder(SortOrder.DESC)
                .includeHighlight(true)
                .highlightFields(Arrays.asList("textData.title", "textData.content"))
                .build();

        // 添加多种查询条件
        condition.addTermCondition("datasetId", "datasetId", testDatasetId);  // 精确匹配
        condition.addMatchCondition("content", "textData.content", "comprehensive");  // 全文搜索
        condition.addPrefixCondition("category", "flattenedData.category", "test");  // 前缀查询
        condition.addExistsCondition("priority", "flattenedData.priority", true);  // 存在性查询

        // 添加时间范围查询
        Date now = new Date();
        Date oneHourAgo = new Date(now.getTime() - 3600000);
        condition.addRangeCondition("createTime", "createTime",
            DatasetEsQueryCondition.RangeCondition.between(oneHourAgo, now));

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);

        // Then - 验证结果
        assertNotNull(result, "查询结果不应为空");
        System.out.println("多条件查询结果总数: " + result.getTotal());

        if (result.getTotal() > 0) {
            DatasetEsIndex foundDataset = result.getRecords().get(0);
            assertEquals(testDatasetId, foundDataset.getDatasetId(), "应该找到指定的数据集");

            // 验证高亮结果
            if (result.getHighlights() != null && result.getHighlights().containsKey(foundDataset.getDocumentId())) {
                System.out.println("高亮结果: " + result.getHighlights().get(foundDataset.getDocumentId()));
            }
        }
    }

    @Test
    void testPageQuery_WithTermsQuery_RealES() {
        // Given - 准备测试数据
        long timestamp = System.currentTimeMillis();
        List<String> testDatasetIds = Arrays.asList(
            "terms-test-1-" + timestamp,
            "terms-test-2-" + timestamp,
            "terms-test-3-" + timestamp
        );

        List<DatasetEsIndex> datasets = new ArrayList<>();
        for (String datasetId : testDatasetIds) {
            DatasetEsIndex dataset = createTestDataset(datasetId, "case");
            datasets.add(dataset);
        }

        // 批量插入测试数据
        BatchResult insertResult = datasetEsIndexService.batchInsertDatasets(datasets, "case", "123");
        assertNotNull(insertResult, "批量插入结果不应为空");

        System.out.println("🚀 开始测试多值查询");

        // When - 执行多值查询
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .pageNum(0)
                .pageSize(10)
                .build();

        // 添加多值查询条件
        condition.addTermsCondition("datasetId", "datasetId", testDatasetIds);

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);

        // Then - 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertTrue(result.getTotal() >= testDatasetIds.size(), "应该找到所有插入的数据集");
        System.out.println("多值查询结果总数: " + result.getTotal());

        // 验证返回的数据集ID都在查询列表中
        List<String> foundDatasetIds = result.getRecords().stream()
                .map(DatasetEsIndex::getDatasetId)
                .collect(Collectors.toList());

        for (String expectedId : testDatasetIds) {
            assertTrue(foundDatasetIds.contains(expectedId),
                "应该找到数据集ID: " + expectedId);
        }
    }

    @Test
    void testPageQuery_WithTermsAgg_RealES() {
        DatasetAggCondition aggCondition = new DatasetAggCondition();
        aggCondition.setAggName("");
        aggCondition.setIsNestedAgg(true);
        aggCondition.setNestedPath("textData");
        aggCondition.setIndexName("train_dataset_case_2025-05");
        aggCondition.setFieldName("segment_1");
        aggCondition.setFullPath("textData.segment_1.keyword");

    }

    @Test
    void testPageQuery_WithTermsAgg1_RealES() {
        DatasetEsQueryCondition condition = DatasetEsQueryCondition.builder()
                .dataSource("case")
                .pageSize(0)
                .indexName("train_dataset_case_2025-05")
                .build();
        EsSearchProperty esSearchProperty = new EsSearchProperty();
        esSearchProperty.setPath("textData.segment_1.keyword");
        esSearchProperty.setNestedPath("textData");
        esSearchProperty.setNested(true);
        esSearchProperty.setFieldName("segment_1");
        esSearchProperty.setFieldType(EsPropertyTypeEnum.NESTED);
        condition.getTermsAggConditions().put(esSearchProperty, DatasetEsQueryCondition.AggregationCondition.builder()
                .type(DatasetEsQueryCondition.AggregationType.TERMS)
                .build());

        DatasetPageResult result = datasetEsIndexService.pageQuery(condition);
    }

}