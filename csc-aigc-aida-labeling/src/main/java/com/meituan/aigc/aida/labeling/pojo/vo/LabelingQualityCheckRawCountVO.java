package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: guowenhui
 * @Create: 2025/3/31 11:31
 * @Version: 1.0
 * 质检原数据维度统计
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelingQualityCheckRawCountVO {

    /**
     * 质检详情总数量
     */
    private Integer qualityCheckedTotalCount;
    /**
     * 已质检数量
     */
    private Integer qualityCheckedCount;
    /**
     * 未质检数量
     */
    private Integer noQualityCheckedCount;
    /**
     * 不可检数量（未标注数量）
     */
    private Integer unLabelCount;

}
