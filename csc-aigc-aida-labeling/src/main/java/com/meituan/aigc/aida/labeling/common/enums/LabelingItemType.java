package com.meituan.aigc.aida.labeling.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-11 14:15
 * @description
 */
@Getter
public enum LabelingItemType {

    LABEL(1, "标注"),
    QUALITY(2, "质检");

    private final int code;
    private final String value;

    LabelingItemType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    // 根据code获取枚举值
    public static LabelingItemType getByCode(int code) {
        for (LabelingItemType type : LabelingItemType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
