package com.meituan.aigc.aida.labeling.remote.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页数据
 *
 * <AUTHOR>
 */
@Data
public class PageData<T> implements Serializable {

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 数据结果
     */
    private List<T> data;

    public PageData() {

    }

    public PageData(List<T> data) {
        this.data = data;
    }
}
