package com.meituan.aigc.aida.data.management.fetch.service.impl;

import static com.meituan.aigc.aida.data.management.fetch.common.constans.DolphinChatConstant.ALLOCATE_RESULT_NOTICE_EVENT;
import static com.meituan.aigc.aida.data.management.fetch.common.constans.DolphinChatConstant.STAFF_MESSAGE_SEND_EVENT;
import static com.meituan.aigc.aida.data.management.fetch.common.constans.DolphinChatConstant.USER_MESSAGE_SEND_EVENT;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.csc.haitun.kefu.dto.StaffInfoFacadeDTO;
import com.meituan.aigc.aida.common.util.ObjectConverter;
import com.meituan.aigc.aida.data.management.common.constant.CommonConstants;
import com.meituan.aigc.aida.data.management.fetch.common.constans.DolphinChatConstant;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.ChatMessageFromEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.EventTypeEnum;
import com.meituan.aigc.aida.data.management.fetch.common.enums.TrackingRulesChannelEnum;
import com.meituan.aigc.aida.data.management.fetch.dto.BaseSingalTrackingBusParamDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.DolphinChatMafkaMessageDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.mq.SignalMockTriggerMessageDTO;
import com.meituan.aigc.aida.data.management.fetch.dto.pacific.PacificButlerContextParamDTO;
import com.meituan.aigc.aida.data.management.fetch.param.RobotListQuery;
import com.sankuai.csc.workbench.bff.api.workbench.dto.InitButlerResDTO;
import com.sankuai.mdp.csc.pacific.context.api.enums.ChannelTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 在线信号模拟触发服务实现类
 * <p>
 * 该服务负责处理在线客服系统中的信号模拟触发，主要功能包括:
 * 1. 处理海豚聊天消息
 * 2. 解析在线会话事件
 * 3. 构建在线渠道的业务参数
 * 4. 触发在线渠道的AI搭埋点机器人
 *
 * <AUTHOR>
 * @date 2025/06/06
 */
@Service("onlineSignalLoggingTriggerService")
@Slf4j
public class OnlineSignalLoggingTriggerServiceImpl extends AbstractSignalLoggingTriggerService {

    private static final String LOG_PREFIX = "[OnlineSignalLoggingTriggerService]";

    /**
     * 通过海豚消息处理AI搭埋点机器人应用触发
     * <p>
     * 将消息字符串解析为消息DTO并进行处理
     *
     * @param messageString 消息字符串，包含事件和会话信息
     */
    @Override
    public void processSignalLoggingTriggerByMessageString(String messageString) {
        if (StringUtils.isBlank(messageString)) {
            return;
        }
        // 1. 解析消息
        DolphinChatMafkaMessageDTO dolphinChatMafkaMessageDTO = buildFromMessageJsonString(messageString);
        if (isInvalidEvent(dolphinChatMafkaMessageDTO)) {
            return;
        }
        // 2. 初始化事件数据
        initEventDataStaffInfo(dolphinChatMafkaMessageDTO);
        SignalMockTriggerMessageDTO signalMockTriggerMessageDTO = buildSignalMockTriggerMessage(
                dolphinChatMafkaMessageDTO);
        // 3. 处理触发
        processSignalLoggingTrigger(signalMockTriggerMessageDTO);
    }

    /**
     * 检查事件是否无效
     * <p>
     * 事件在以下情况下被认为无效:
     * 1. 事件DTO为空
     * 2. 事件数据为空
     * 3. 客服员ID无效
     * 4. 消息来源不是客服
     *
     * @param eventDTO 事件DTO
     * @return true如果事件无效，false如果事件有效
     */
    private boolean isInvalidEvent(DolphinChatMafkaMessageDTO eventDTO) {
        if (Objects.isNull(eventDTO)) {
            return true;
        }
        DolphinChatMafkaMessageDTO.EventData eventObject = eventDTO.getEventObject();
        if (Objects.isNull(eventObject)) {
            return true;
        }
        if (BooleanUtils.isTrue(eventObject.getIsSystem())) {
            return true;
        }
        DolphinChatMafkaMessageDTO.BusinessData businessData = eventDTO.getBusinessData();
        if (Objects.isNull(businessData)) {
            return true;
        }
        Long chatStaffId = businessData.getChatStaffId();
        if (null == chatStaffId || chatStaffId <= 0) {
            log.warn("{}, 客服ID无效: {}", LOG_PREFIX, chatStaffId);
            return true;
        }
        ChatMessageFromEnum chatMessageFromEnum = businessData.getChatMessageFromEnum();
        if (Objects.isNull(chatMessageFromEnum)) {
            log.warn("{}, 消息来源为空", LOG_PREFIX);
            return true;
        }
        /**
         * 只处理客服发送的消息
         */
        boolean isNotFromStaff = ChatMessageFromEnum.STAFF != chatMessageFromEnum;
        if (isNotFromStaff) {
            log.warn("{}, 消息不是来自客服, 来源: {}", LOG_PREFIX, chatMessageFromEnum);
        }
        return isNotFromStaff;
    }

    /**
     * 构建信号Mock触发消息
     * <p>
     * 将事件数据转换为触发消息，并初始化管家信息
     *
     * @param eventDTO 事件数据，包含会话和客服信息
     * @return 触发消息DTO，包含会话、客服和管家信息
     */
    private SignalMockTriggerMessageDTO buildSignalMockTriggerMessage(
            DolphinChatMafkaMessageDTO dolphinChatMafkaMessageDTO) {
        DolphinChatMafkaMessageDTO.EventData eventData = dolphinChatMafkaMessageDTO.getEventObject();
        DolphinChatMafkaMessageDTO.BusinessData businessData = dolphinChatMafkaMessageDTO.getBusinessData();
        if (Objects.isNull(eventData) || Objects.isNull(businessData)) {
            return null;
        }
        SignalMockTriggerMessageDTO messageDTO = new SignalMockTriggerMessageDTO();
        messageDTO.setContactId(String.valueOf(eventData.getServiceId()));
        messageDTO.setMessageOccurredTime(String.valueOf(eventData.getOccurredOn()));
        messageDTO.setContactType(ChannelEnum.ONLINE.getCode());
        messageDTO.setChatMessageFromType(
                Objects.nonNull(businessData.getChatMessageFromEnum()) ? businessData.getChatMessageFromEnum().getCode()
                        : CommonConstants.EMPTY_STRING);
        messageDTO.setMessageId(String.valueOf(eventData.getMessageId()));
        messageDTO.setStaffMis(businessData.getPacificStaffMis());
        messageDTO.setUserMsgContent(businessData.getCustomerMessageContent());
        messageDTO.setStaffMessageContent(businessData.getStaffMessageContent());

        // 初始化管家信息
        try {
            Pair<InitButlerResDTO, Map<String, String>> contextInfo = contextUtils
                    .getContextInfo(String.valueOf(eventData.getServiceId()), ChannelTypeEnum.ON_LINE);
            if (null != contextInfo) {
                PacificButlerContextParamDTO pacificButlerContextParamDTO = new PacificButlerContextParamDTO();
                pacificButlerContextParamDTO.setButlerRes(contextInfo.getLeft());
                pacificButlerContextParamDTO.setButlerParamMap(contextInfo.getRight());
                messageDTO.setContextParam(pacificButlerContextParamDTO);
            }
        } catch (Exception e) {
            log.error("{}, 初始化管家信息异常, serviceId:{}", LOG_PREFIX, eventData.getServiceId(), e);
        }
        return messageDTO;
    }

    /**
     * 从消息JSON字符串构建消息DTO
     * <p>
     * 解析消息字符串并设置事件类型和消息来源
     * 支持的事件类型:
     * - 分配结果通知: 会话开始
     * - 客服/用户消息发送: 会话进行中
     *
     * @param messageJsonString 消息JSON字符串
     * @return 消息DTO，如果解析失败或事件类型不匹配则返回null
     */
    public DolphinChatMafkaMessageDTO buildFromMessageJsonString(String messageJsonString) {
        if (StringUtils.isBlank(messageJsonString)) {
            return null;
        }
        final String eventTypeKye = "eventType";
        String eventType = JSONObject.parseObject(messageJsonString).getString(eventTypeKye);
        // 提前过滤非 客服发送的消息 事件
        if (!Objects.equals(eventType, DolphinChatConstant.STAFF_MESSAGE_SEND_EVENT)) {
            return null;
        }
        DolphinChatMafkaMessageDTO dolphinChatMafkaMessageDTO = ObjectConverter.convert(messageJsonString,
                DolphinChatMafkaMessageDTO.class);
        if (null == dolphinChatMafkaMessageDTO) {
            log.error("{}, 解析消息JSON字符串失败, messageJsonString:{}", LOG_PREFIX, messageJsonString);
            return null;
        }
        DolphinChatMafkaMessageDTO.EventData eventData = ObjectConverter.convert(dolphinChatMafkaMessageDTO.getEvent(),
                DolphinChatMafkaMessageDTO.EventData.class);
        if (null == eventData) {
            // 此处失败可能是因为 staffMessageSendEvent 的 event 格式也不对，需要记录错误
            log.error("{}, 解析 staffMessageSendEvent 事件数据失败, event:{}", LOG_PREFIX,
                    dolphinChatMafkaMessageDTO.getEvent());
            return null;
        }
        dolphinChatMafkaMessageDTO.setEventObject(eventData);

        // 创建业务数据对象
        DolphinChatMafkaMessageDTO.BusinessData businessData = buildBusinessData(dolphinChatMafkaMessageDTO);
        dolphinChatMafkaMessageDTO.setBusinessData(businessData);
        return dolphinChatMafkaMessageDTO;
    }

    /**
     * 构建业务数据
     *
     * @param messageDTO 海豚聊天消息DTO
     * @return 业务数据对象
     */
    private static DolphinChatMafkaMessageDTO.BusinessData buildBusinessData(
            DolphinChatMafkaMessageDTO messageDTO) {
        // 创建业务数据对象
        DolphinChatMafkaMessageDTO.BusinessData businessData = new DolphinChatMafkaMessageDTO.BusinessData();

        // 根据事件类型设置会话状态
        if (Objects.equals(messageDTO.getEventType(), ALLOCATE_RESULT_NOTICE_EVENT)) {
            // 分配结果通知事件,设置为会话开始状态
            businessData.setEventTypeEnum(EventTypeEnum.CHAT_SESSION_START);
        } else {
            // 其他事件设置为会话进行中状态
            businessData.setEventTypeEnum(EventTypeEnum.CHAT_SESSION_IN_PROCESS);
        }

        // 获取事件数据对象
        DolphinChatMafkaMessageDTO.EventData eventData = messageDTO.getEventObject();

        // 处理消息发送事件
        if (Objects.equals(messageDTO.getEventType(), STAFF_MESSAGE_SEND_EVENT)
                || Objects.equals(messageDTO.getEventType(), USER_MESSAGE_SEND_EVENT)) {
            // 获取客户和客服的消息内容
            String customerMessageContent = eventData.getContent();
            String staffMessageContent = eventData.getMsgContent();
            businessData.setCustomerMessageContent(customerMessageContent);
            businessData.setStaffMessageContent(staffMessageContent);
            // 设置消息来源(客服/客户)
            businessData.setChatMessageFromEnum(
                    Objects.equals(messageDTO.getEventType(), STAFF_MESSAGE_SEND_EVENT)
                            ? ChatMessageFromEnum.STAFF
                            : ChatMessageFromEnum.CUSTOMER);
        }
        // 设置客服ID
        businessData.setChatStaffId(eventData.getStaffId());
        return businessData;
    }

    /**
     * 初始化事件数据中的客服信息
     * <p>
     * 通过客服ID获取Pacific客服ID和MIS号
     *
     * @param messageDTO 事件数据
     */
    private void initEventDataStaffInfo(DolphinChatMafkaMessageDTO messageDTO) {
        if (!ObjectUtils.allNotNull(messageDTO, messageDTO.getEventObject(), messageDTO.getBusinessData())) {
            log.error("{}, 事件数据不完整: {}", LOG_PREFIX, messageDTO);
            return;
        }
        DolphinChatMafkaMessageDTO.BusinessData businessData = messageDTO.getBusinessData();
        StaffInfoFacadeDTO staffInfoFacadeDTO = getPacificStaffIdByChatStaffId(messageDTO.getEventObject().getStaffId());
        if (Objects.isNull(staffInfoFacadeDTO)) {
            log.info("{}, 初始化事件数据中的客服信息失败, 客服信息为空, chatStaffId = [{}]", LOG_PREFIX,
                    messageDTO.getEventObject().getStaffId());
            return;
        }
        businessData.setPacificStaffId(Long.parseLong(staffInfoFacadeDTO.getBizId()));
        businessData.setPacificStaffMis(staffInfoFacadeDTO.getMisNo());
    }

    /**
     * 构建机器人列表查询参数
     * 设置为在线渠道
     *
     * @param busParamDTO 业务参数DTO
     * @return 机器人列表查询参数
     */
    @Override
    protected RobotListQuery buildRobotListQuery(BaseSingalTrackingBusParamDTO busParamDTO) {
        RobotListQuery robotListQuery = new RobotListQuery();
        // 逗号分隔的标问列表
        robotListQuery.setTypicalQuestionIds(String.join(CommonConstants.FieldName.ENGLISH_COMMA,
                Optional.ofNullable(busParamDTO.getTypicalQuestionIds())
                        .orElse(Collections.emptyList())));
        // 客服mis
        robotListQuery.setMisList(busParamDTO.getStaffMis());

        // 设置渠道为在线
        robotListQuery.setChannel(TrackingRulesChannelEnum.ONLINE.getCode());
        return robotListQuery;
    }

    /**
     * 获取渠道类型
     * 返回在线渠道类型
     *
     * @return 在线渠道类型枚举
     */
    @Override
    protected TrackingRulesChannelEnum getChannelType() {
        return TrackingRulesChannelEnum.ONLINE;
    }
} 