package com.meituan.aigc.aida.config;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 类描述.
 * @date 2025/5/27
 */
@Data
@Component
public class EsLionConfig {

    /**
     * ES集群名称
     */
    @MdpConfig("es.clusterName:csp_traindataset_default")
    private String esClusterName;

    /**
     * ES的超时时间
     */
    @MdpConfig("es.timeout.mills:10000")
    private Integer timeoutMillis;

    /**
     * 数据集-ES索引前缀-case分析平台来源数据
     */
    @MdpConfig("dataset.es.index.case:train_dataset_case")
    private String datasetEsIndexFromCase;

    /**
     * 数据集-ES索引前缀-线上拉取数据
     */
    @MdpConfig("dataset.es.index.online:train_dataset_online")
    private String datasetEsIndexFromOnline;

    /**
     * 数据集-ES索引前缀-上传数据
     */
    @MdpConfig("dataset.es.index.upload:train_dataset_upload")
    private String datasetEsIndexFromUpload;

    /**
     * 数据集-字段映射索引
     */
    @MdpConfig("dataset.field.mapping.index:train_dataset_field_mapping")
    private String datasetFieldMappingIndex;

    /**
     * 批量插入最大重试次数
     */
    @MdpConfig("dataset.es.batch.insert.max.retries:3")
    private int maxRetries;
}
