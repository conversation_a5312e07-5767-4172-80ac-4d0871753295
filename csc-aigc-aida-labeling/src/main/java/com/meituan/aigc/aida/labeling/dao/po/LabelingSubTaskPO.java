package com.meituan.aigc.aida.labeling.dao.po;

import lombok.Data;

import java.util.Date;

/**
 * 标注子任务PO
 *
 * <AUTHOR>
 */
@Data
public class LabelingSubTaskPO {

    /**
     * 子任务ID
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据来源
     */
    private Integer dataSetSource;

    /**
     * 数据量
     */
    private Integer dataTotalCount;

    /**
     * 标注状态
     */
    private Integer labelingStatus;

    /**
     * 标注进度
     */
    private Integer labelingProgress;

    /**
     * 未标注数量
     */
    private Integer unLabelCount;

    /**
     * 标注人mis
     */
    private String labelerMis;

    /**
     * 标注人名字
     */
    private String labelerName;

    /**
     * 标注任务类型 1:评测集标注 2:训练集标注 3:线上会话标注
     *
     * @see com.meituan.aigc.aida.labeling.common.enums.LabelingTaskType
     */
    private Integer taskType;

    /**
     * 数据类型 1:模型评测 2:Agent评测 3:有监督微调(SFT) 4:偏好对齐(DPO) 5:Session维度 6:Query维度
     */
    private Integer dataType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
