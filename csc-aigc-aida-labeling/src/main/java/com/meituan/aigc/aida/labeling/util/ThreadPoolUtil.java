package com.meituan.aigc.aida.labeling.util;

import com.dianping.cat.thread.pool.CatExecutorServiceTraceWrapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;

import java.util.concurrent.*;

public class ThreadPoolUtil {

    public static ExecutorService createPoolWithTraceAndCat(int coreThreadSize, int maxThreadSize, int queueSize, String threadNameFormat) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat(threadNameFormat)
                .setDaemon(true)
                .build();

        final BlockingQueue<Runnable> queue = new ArrayBlockingQueue<>(queueSize);
        ExecutorService originExecutorService = new ThreadPoolExecutor(coreThreadSize, maxThreadSize,
                60L, TimeUnit.SECONDS,
                queue, threadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
        ExecutorServiceTraceWrapper mtraceWrapper = new ExecutorServiceTraceWrapper(originExecutorService);
        return new CatExecutorServiceTraceWrapper(mtraceWrapper);
    }
}
