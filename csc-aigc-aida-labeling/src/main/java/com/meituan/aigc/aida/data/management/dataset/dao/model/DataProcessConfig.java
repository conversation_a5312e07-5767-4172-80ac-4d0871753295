package com.meituan.aigc.aida.data.management.dataset.dao.model;

import java.util.Date;

public class DataProcessConfig {
    private Long id;

    private Long taskId;

    private Byte processType;

    private Byte status;

    private String creatorMis;

    private String creatorName;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Byte getProcessType() {
        return processType;
    }

    public void setProcessType(Byte processType) {
        this.processType = processType;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getCreatorMis() {
        return creatorMis;
    }

    public void setCreatorMis(String creatorMis) {
        this.creatorMis = creatorMis == null ? null : creatorMis.trim();
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}