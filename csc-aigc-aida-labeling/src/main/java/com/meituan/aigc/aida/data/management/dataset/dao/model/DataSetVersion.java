package com.meituan.aigc.aida.data.management.dataset.dao.model;

import lombok.Data;

import java.util.Date;

@Data
public class DataSetVersion {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 数据集Id
     */
    private Long dataSetId;
    /**
     * 版本名称
     */
    private String versionName;
    /**
     * 版本描述
     */
    private String description;
    /**
     * 表头配置
     */
    private String headConfig;
    /**
     * 版本类型
     */
    private Integer versionType;
    /**
     * 版本来源
     */
    private String versionSource;
    /**
     * 数据条数
     */
    private Integer dataCount;
    /**
     * 版本状态
     */
    private Integer versionStatus;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * es索引名称
     */
    private String esIndexName;
}