package com.meituan.aigc.aida.labeling.pojo.dto.dashboard;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
public class PersonnelDetailQuery extends BaseStatisticsDTO{
    /**
     * 排序字段
     */
    private String sortBy;
    /**
     * 排序方式
     */
    private String sortOrder;
    /**
     * 人员名称
     */
    private String name;
    /**
     * 人员mis
     */
    private String userMis;
    /**
     * 查询类型
     */
    private Integer queryType;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页大小
     */
    private Integer pageSize;
    /**
     * 人员列表
     */
    private List<String> userMisList;
}
