package com.meituan.aigc.aida.labeling.param;

import com.meituan.aigc.aida.labeling.pojo.dto.QualityCheckResultSaveDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/3/6 10:42
 * @Version: 1.0】
 * 质检结果保存参数
 */
@Data
public class QualityCheckResultSaveParam implements Serializable {

    /**
     * 质检结果
     */
    private List<QualityCheckResultSaveDTO> checkItems;

}
