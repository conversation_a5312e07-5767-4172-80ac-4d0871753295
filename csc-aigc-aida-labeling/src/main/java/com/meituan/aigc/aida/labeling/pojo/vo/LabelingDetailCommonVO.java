package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: guowenhui
 * @Create: 2025/3/10 11:42
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelingDetailCommonVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 大模型消息ID
     */
    private String messageId;

    /**
     * 大模型消息内容
     */
    private String messageContent;

    /**
     * 三方消息ID
     */
    private String thirdMessageId;

    /**
     * 标注项
     */
    private List<List<LabelResult>> labelingItems;

    /**
     * 质检结果
     */
    private Integer qualityCheckResult;

    /**
     *  质检信息
     */
    private String qualityCheckRemark;
}
