package com.meituan.aigc.aida.benchmark.controller;

import com.meituan.aigc.aida.benchmark.param.BenchmarkDatasetListParam;
import com.meituan.aigc.aida.benchmark.param.IndicatorComputeTaskParam;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkDatasetListVO;
import com.meituan.aigc.aida.benchmark.pojo.vo.BenchmarkVersionListVO;
import com.meituan.aigc.aida.benchmark.service.BenchmarkManagementService;
import com.meituan.aigc.aida.labeling.pojo.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Benchmark管理Controller
 *
 * <AUTHOR>
 * @description Benchmark管理相关接口
 * @date 2025/6/24
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1/benchmark/management")
public class BenchmarkManagementController {

    @Resource
    private BenchmarkManagementService benchmarkManagementService;

    /**
     * 获取所有Benchmark版本列表
     *
     * @return Benchmark版本列表
     */
    @GetMapping("/version/list")
    public Result<BenchmarkVersionListVO> listBenchmarkVersions() {
        log.info("接收到查询Benchmark版本列表请求");
        BenchmarkVersionListVO result = benchmarkManagementService.listAllBenchmarkVersions();
        return Result.ok(result);
    }

    /**
     * 分页查询Benchmark数据集列表
     *
     * @param versionId benchmark版本ID
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @return Benchmark数据集列表
     */
    @GetMapping("/dataset/list")
    public Result<BenchmarkDatasetListVO> listBenchmarkDatasets(
            @RequestParam("versionId") Long versionId,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        // 构建查询参数
        BenchmarkDatasetListParam param = new BenchmarkDatasetListParam();
        param.setVersionId(versionId);
        param.setPageNum(pageNum);
        param.setPageSize(pageSize);

        BenchmarkDatasetListVO result = benchmarkManagementService.listBenchmarkDatasets(param);
        return Result.ok(result);
    }

    /**
     * 提交指标计算任务
     *
     * @param param 指标计算任务参数
     * @return 计算结果
     */
    @PostMapping("/computing/task")
    public Result<?> submitComputingTask(@RequestBody @Valid IndicatorComputeTaskParam param) {
        benchmarkManagementService.indicatorComputing(param);
        return Result.create();
    }
}
