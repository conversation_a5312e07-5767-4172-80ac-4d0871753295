package com.meituan.aigc.aida.data.management.dataset.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-30 17:30
 * @description
 */
@Getter
public enum TrainingTypeEnum {
    SFT(1,"SFT", "有监督微调"),
    DPO(2, "DPO", "偏好对齐"),
    SESSION(3,"SESSION","session维度"),
    QUERY(4,"QUERY","query维度");

    private Integer code;
    private String type;
    private String desc;

    TrainingTypeEnum(Integer code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }
   public static String getDescByCode(Integer code) {
        for (TrainingTypeEnum dataUsageTypeEnum : TrainingTypeEnum.values()) {
            if (dataUsageTypeEnum.getCode().equals(code)) {
                return dataUsageTypeEnum.getDesc();
            }
        }
        return null;
    }
}
