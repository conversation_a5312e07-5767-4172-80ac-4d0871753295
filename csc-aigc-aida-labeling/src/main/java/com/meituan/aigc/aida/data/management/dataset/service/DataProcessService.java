package com.meituan.aigc.aida.data.management.dataset.service;

import com.meituan.aigc.aida.data.management.dataset.dto.DataProcessTaskDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.LabelRobotInfoDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.NonCustomLabelInfoDTO;
import com.meituan.aigc.aida.data.management.dataset.dto.NonCustomRobotDTO;
import com.meituan.aigc.aida.data.management.dataset.param.DataMarkParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataProcessCreateTaskParam;
import com.meituan.aigc.aida.data.management.dataset.param.DataProcessTaskPageParam;
import com.meituan.aigc.aida.data.management.dataset.param.LabelRobotParam;
import com.meituan.aigc.aida.data.management.dataset.param.NonCustomRobotQueryParam;
import com.meituan.aigc.aida.labeling.param.PageData;

import java.util.List;

/**
 * 数据处理服务接口
 */
public interface DataProcessService {
    
    /**
     * 创建数据处理任务
     *
     * @param param 创建任务参数
     */
    void createProcessTask(DataProcessCreateTaskParam param);
    
    /**
     * 分页查询数据处理任务
     *
     * @param param 查询参数
     * @return 任务列表
     */
    PageData<DataProcessTaskDTO> listProcessTask(DataProcessTaskPageParam param);
    
    /**
     * 获取打标机器人信息
     *
     * @param param 机器人参数
     * @return 机器人信息
     */
    LabelRobotInfoDTO getLabelRobotInfo(LabelRobotParam param);
    
    /**
     * 获取非自定义标签信息
     *
     * @return 非自定义标签信息
     */
    NonCustomLabelInfoDTO getNonCustomLabelInfo();
    
    /**
     * 查询非自定义打标机器人
     *
     * @param param 查询参数
     * @return 打标机器人列表
     */
    List<NonCustomRobotDTO> queryNonCustomRobot(NonCustomRobotQueryParam param);
    
    /**
     * 数据打标
     *
     * @param param 打标参数
     */
    void markData(DataMarkParam param);
} 