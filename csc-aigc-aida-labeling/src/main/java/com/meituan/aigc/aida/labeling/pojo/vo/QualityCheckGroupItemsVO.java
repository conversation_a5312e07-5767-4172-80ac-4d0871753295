package com.meituan.aigc.aida.labeling.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 质检分组详情
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QualityCheckGroupItemsVO {

    /**
     * 质检任务ID
     */
    private Long qualityCheckTaskId;

    /**
     * 质检员Mis
     */
    private String qualityChecker;

    /**
     * 质检员名字
     */
    private String qualityCheckerName;

    /**
     * 质检完成数量
     */
    private Integer finishCount;

    /**
     * 质检总数
     */
    private Integer dataTotal;

    /**
     * 质检错误数量
     */
    private Integer errorCount;
    /**
     * 未标注数量
     */
    private Integer waitingLabelCount;
    /**
     * 标注员Mis
     */
    private String labelerMis;
    /**
     * 标注员名字
     */
    private String labelerName;
    /**
     * 质检任务创建时间
     */
    private String qualityCheckTime;



}
