package com.meituan.aigc.aida.data.management.dataset.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据集创建响应
 *
 * <AUTHOR>
 * @date 2025-01-07
 * @description 数据集管理 - 创建数据集响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSetCreateResponse implements Serializable {
    /**
     * 数据集ID
     */
    private Long dataSetId;
    
    /**
     * 锁ID
     */
    private String lockId;
} 