//package com.meituan.aigc.aida.data.management.es;
//
//import com.meituan.aigc.aida.common.enums.EsOperationEnum;
//import com.meituan.aigc.aida.data.management.es.query.BatchResult;
//import com.meituan.aigc.aida.labeling.remote.common.enums.DataSourceEnum;
//import org.elasticsearch.action.bulk.BulkResponse;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.*;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
///**
// * DatasetEsIndexService 单元测试
// * 重点测试ES插入相关功能
// *
// * <AUTHOR>
// * @date 2025/5/241
// */
////@ExtendWith(MockitoExtension.class)
//class DatasetEsIndexServiceTest {
//
//    @Mock
//    private DatasetEsBaseIndexService datasetEsBaseIndexService;
//
//    @InjectMocks
//    private DatasetEsIndexService datasetEsIndexService;
//
//    // 测试用的配置值
//    private static final String CASE_INDEX_PREFIX = "train_dataset_case";
//    private static final String ONLINE_INDEX_PREFIX = "train_dataset_online";
//    private static final String UPLOAD_INDEX_PREFIX = "train_dataset_upload";
//
//    @BeforeEach
//    void setUp() {
//        // 设置配置值
//        ReflectionTestUtils.setField(datasetEsIndexService, "datasetEsIndexFromCase", CASE_INDEX_PREFIX);
//        ReflectionTestUtils.setField(datasetEsIndexService, "datasetEsIndexFromOnline", ONLINE_INDEX_PREFIX);
//        ReflectionTestUtils.setField(datasetEsIndexService, "datasetEsIndexFromUpload", UPLOAD_INDEX_PREFIX);
//    }
//
//    /**
//     * 创建测试用的DatasetEsIndex对象
//     */
//    private DatasetEsIndex createTestDataset(String datasetId, String dataSource) {
//        DatasetEsIndex dataset = new DatasetEsIndex();
//        dataset.setDatasetId(datasetId);
//        dataset.setVersionId("v1.0.0");
//        dataset.setSessionId("session-001");
//        dataset.setDataSource(dataSource);
//        dataset.setFieldMapping("test-mapping");
//
//        // 设置复杂字段
//        Map<String, Object> textData = new HashMap<>();
//        textData.put("title", "测试标题");
//        textData.put("content", "测试内容");
////        dataset.setTextData(textData);
//
//        Map<String, Object> flattenedData = new HashMap<>();
//
//        // JSON对象1：模型配置
//        Map<String, Object> modelConfig = new HashMap<>();
//        modelConfig.put("modelName", "bert-base-chinese");
//        modelConfig.put("version", "v2.1.0");
//        modelConfig.put("parameters", Arrays.asList("learning_rate=0.001", "batch_size=32"));
//        Map<String, Object> hyperParams = new HashMap<>();
//        hyperParams.put("epochs", 10);
//        hyperParams.put("dropout", 0.1);
//        hyperParams.put("warmup_steps", 1000);
//        modelConfig.put("hyperParams", hyperParams);
//        flattenedData.put("modelConfig", modelConfig);
//
//        // JSON对象2：数据统计
//        Map<String, Object> dataStats = new HashMap<>();
//        dataStats.put("totalSamples", 10000);
//        dataStats.put("labelDistribution", Arrays.asList(
////            Map.of("label", "正面", "count", 3500),
////            Map.of("label", "负面", "count", 3000),
////            Map.of("label", "中性", "count", 3500)
//        ));
//        dataStats.put("avgLength", 128.5);
//        dataStats.put("maxLength", 512);
//        flattenedData.put("dataStats", dataStats);
//
//        // 其他简单字段
//        flattenedData.put("category", "AI");
//        flattenedData.put("priority", 1);
//
//        dataset.setFlattenedData(flattenedData);
//
//        Map<String, Date> dateData = new HashMap<>();
//        dateData.put("processTime", new Date());
//        dataset.setDateData(dateData);
//
//        return dataset;
//    }
//
//    /**
//     * 创建成功的BulkResponse mock
//     */
//    private BulkResponse createSuccessfulBulkResponse() {
//        BulkResponse response = mock(BulkResponse.class);
//        when(response.hasFailures()).thenReturn(false);
//        return response;
//    }
//
//    /**
//     * 创建失败的BulkResponse mock
//     */
//    private BulkResponse createFailedBulkResponse() {
//        BulkResponse response = mock(BulkResponse.class);
//        when(response.hasFailures()).thenReturn(true);
//        when(response.buildFailureMessage()).thenReturn("Mock failure message");
//        return response;
//    }
//
//    // ==================== 单条数据插入测试 ====================
//
//    @Test
//    void testInsertDataset_Success() {
//        // Given
//        DatasetEsIndex dataset = createTestDataset("dataset-001", "case");
//        String dataSource = "case";
//
//        doNothing().when(datasetEsBaseIndexService).insert(any(DatasetEsIndex.class), anyString());
//
//        // When
//        boolean result = datasetEsIndexService.insertDataset(dataset, dataSource);
//
//        // Then
//        assertTrue(result);
//        assertNotNull(dataset.getCreateTime());
//        assertNotNull(dataset.getUpdateTime());
//
//        // 验证调用了正确的方法
//        String expectedIndex = CASE_INDEX_PREFIX + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
//        verify(datasetEsBaseIndexService).insert(dataset, expectedIndex);
//    }
//
//    @Test
//    void testInsertDataset_NullDataset() {
//        // Given
//        DatasetEsIndex dataset = null;
//        String dataSource = "case";
//
//        // When
//        boolean result = datasetEsIndexService.insertDataset(dataset, dataSource);
//
//        // Then
//        assertFalse(result);
//        verify(datasetEsBaseIndexService, never()).insert(any(), anyString());
//    }
//
//    @Test
//    void testInsertDataset_BlankDataSource() {
//        // Given
//        DatasetEsIndex dataset = createTestDataset("dataset-001", "case");
//        String dataSource = "";
//
//        // When
//        boolean result = datasetEsIndexService.insertDataset(dataset, dataSource);
//
//        // Then
//        assertFalse(result);
//        verify(datasetEsBaseIndexService, never()).insert(any(), anyString());
//    }
//
//    @Test
//    void testInsertDataset_Exception() {
//        // Given
//        DatasetEsIndex dataset = createTestDataset("dataset-001", "case");
//        String dataSource = "case";
//
//        doThrow(new RuntimeException("ES connection failed"))
//                .when(datasetEsBaseIndexService).insert(any(DatasetEsIndex.class), anyString());
//
//        // When
//        boolean result = datasetEsIndexService.insertDataset(dataset, dataSource);
//
//        // Then
//        assertFalse(result);
//    }
//
//    @Test
//    void testInsertDataset_PreserveExistingTime() {
//        // Given
//        DatasetEsIndex dataset = createTestDataset("dataset-001", "case");
//        Date existingCreateTime = new Date(System.currentTimeMillis() - 86400000); // 1天前
//        Date existingUpdateTime = new Date(System.currentTimeMillis() - 3600000);  // 1小时前
//        dataset.setCreateTime(existingCreateTime);
//        dataset.setUpdateTime(existingUpdateTime);
//        String dataSource = "case";
//
//        doNothing().when(datasetEsBaseIndexService).insert(any(DatasetEsIndex.class), anyString());
//
//        // When
//        boolean result = datasetEsIndexService.insertDataset(dataset, dataSource);
//
//        // Then
//        assertTrue(result);
//        assertEquals(existingCreateTime, dataset.getCreateTime());
//        assertEquals(existingUpdateTime, dataset.getUpdateTime());
//    }
//
//    // ==================== 批量插入测试 ====================
//
//    @Test
//    void testBatchInsertDatasets_Success() {
//        // Given
//        List<DatasetEsIndex> datasetList = Arrays.asList(
//                createTestDataset("dataset-001", "case"),
//                createTestDataset("dataset-002", "case"),
//                createTestDataset("dataset-003", "case")
//        );
//        String dataSource = "case";
//
//        BulkResponse successResponse = createSuccessfulBulkResponse();
//        when(datasetEsBaseIndexService.batchInsert(anyList(), anyString())).thenReturn(successResponse);
//
//        // When
//        BatchResult result = datasetEsIndexService.batchInsertDatasets(datasetList, dataSource);
//
//
//        // 验证所有数据集都设置了时间
//        datasetList.forEach(dataset -> {
//            assertNotNull(dataset.getCreateTime());
//            assertNotNull(dataset.getUpdateTime());
//        });
//
//        // 验证调用了正确的方法
//        String expectedIndex = CASE_INDEX_PREFIX + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
//        verify(datasetEsBaseIndexService).batchInsert(datasetList, expectedIndex);
//    }
//
//    @Test
//    void testBatchInsertDatasets_EmptyList() {
//        // Given
//        List<DatasetEsIndex> datasetList = new ArrayList<>();
//        String dataSource = "case";
//
//        // When
//        BatchResult result = datasetEsIndexService.batchInsertDatasets(datasetList, dataSource);
//
//        // Then
//        verify(datasetEsBaseIndexService, never()).batchInsert(anyList(), anyString());
//    }
//
//    @Test
//    void testBatchInsertDatasets_NullList() {
//        // Given
//        List<DatasetEsIndex> datasetList = null;
//        String dataSource = "case";
//
//        // When
//        BatchResult result = datasetEsIndexService.batchInsertDatasets(datasetList, dataSource);
//
//        // Then
//        // (result);
//        verify(datasetEsBaseIndexService, never()).batchInsert(anyList(), anyString());
//    }
//
//    @Test
//    void testBatchInsertDatasets_BlankDataSource() {
//        // Given
//        List<DatasetEsIndex> datasetList = Arrays.asList(createTestDataset("dataset-001", "case"));
//        String dataSource = "";
//
//        // When & Then
//        assertThrows(IllegalArgumentException.class, () -> {
//            datasetEsIndexService.batchInsertDatasets(datasetList, dataSource);
//        });
//
//        verify(datasetEsBaseIndexService, never()).batchInsert(anyList(), anyString());
//    }
//
//    @Test
//    void testBatchInsertDatasets_HasFailures() {
//        // Given
//        List<DatasetEsIndex> datasetList = Arrays.asList(createTestDataset("dataset-001", "case"));
//        String dataSource = "case";
//
//        BulkResponse failedResponse = createFailedBulkResponse();
//        when(datasetEsBaseIndexService.batchInsert(anyList(), anyString())).thenReturn(failedResponse);
//
//        // When
//        BatchResult result = datasetEsIndexService.batchInsertDatasets(datasetList, dataSource);
//
//        // Then
//        assertEquals(result, BatchResult.empty(EsOperationEnum.INSERT));
//    }
//
//    @Test
//    void testBatchInsertDatasets_Exception() {
//        // Given
//        List<DatasetEsIndex> datasetList = Arrays.asList(createTestDataset("dataset-001", "case"));
//        String dataSource = "case";
//
//        when(datasetEsBaseIndexService.batchInsert(anyList(), anyString()))
//                .thenThrow(new RuntimeException("ES connection failed"));
//
//        // When
//        BatchResult result = datasetEsIndexService.batchInsertDatasets(datasetList, dataSource);
//
//    }
//
//    @Test
//    void testBatchInsertDatasets_DifferentDataSources() {
//        // Given
//        List<DatasetEsIndex> onlineDatasets = Arrays.asList(createTestDataset("dataset-001", "online"));
//        List<DatasetEsIndex> uploadDatasets = Arrays.asList(createTestDataset("dataset-002", "upload"));
//
//        BulkResponse successResponse = createSuccessfulBulkResponse();
//        when(datasetEsBaseIndexService.batchInsert(anyList(), anyString())).thenReturn(successResponse);
//
//        // When
//        BatchResult onlineResult = datasetEsIndexService.batchInsertDatasets(onlineDatasets, "online");
//        BatchResult uploadResult = datasetEsIndexService.batchInsertDatasets(uploadDatasets, "upload");
//
//        // 验证调用了不同的索引
//        String expectedOnlineIndex = ONLINE_INDEX_PREFIX + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
//        String expectedUploadIndex = UPLOAD_INDEX_PREFIX + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
//
//        verify(datasetEsBaseIndexService).batchInsert(onlineDatasets, expectedOnlineIndex);
//        verify(datasetEsBaseIndexService).batchInsert(uploadDatasets, expectedUploadIndex);
//    }
//
//    // ==================== 索引名称生成测试 ====================
//
//    @Test
//    void testGetIndexNameByDataSource_Case() {
//        // Given
//        String dataSource = "case";
//
//        // When
//        String indexName = (String) ReflectionTestUtils.invokeMethod(
//                datasetEsIndexService, "getIndexNameByDataSource", dataSource);
//
//        // Then
//        String expectedSuffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
//        assertEquals(CASE_INDEX_PREFIX + "-" + expectedSuffix, indexName);
//    }
//
//    @Test
//    void testGetIndexNameByDataSource_Online() {
//        // Given
//        String dataSource = "online";
//
//        // When
//        String indexName = (String) ReflectionTestUtils.invokeMethod(
//                datasetEsIndexService, "getIndexNameByDataSource", dataSource);
//
//        // Then
//        String expectedSuffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
//        assertEquals(ONLINE_INDEX_PREFIX + "-" + expectedSuffix, indexName);
//    }
//
//    @Test
//    void testGetIndexNameByDataSource_Upload() {
//        // Given
//        String dataSource = "upload";
//
//        // When
//        String indexName = (String) ReflectionTestUtils.invokeMethod(
//                datasetEsIndexService, "getIndexNameByDataSource", dataSource);
//
//        // Then
//        String expectedSuffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
//        assertEquals(UPLOAD_INDEX_PREFIX + "-" + expectedSuffix, indexName);
//    }
//
//    @Test
//    void testGetIndexNameByDataSource_UnsupportedDataSource() {
//        // Given
//        String dataSource = "unsupported";
//
//        // When & Then
//        assertThrows(IllegalArgumentException.class, () -> {
//            ReflectionTestUtils.invokeMethod(datasetEsIndexService, "getIndexNameByDataSource", dataSource);
//        });
//    }
//
//    // ==================== 数据来源分组测试 ====================
//
//    @Test
//    void testGroupDatasetsBySource() {
//        // Given
//        List<DatasetEsIndex> datasetList = Arrays.asList(
//                createTestDataset("dataset-001", "case"),
//                createTestDataset("dataset-002", "online"),
//                createTestDataset("dataset-003", "case"),
//                createTestDataset("dataset-004", "upload")
//        );
//
//        // When
//        @SuppressWarnings("unchecked")
//        Map<String, List<DatasetEsIndex>> result = (Map<String, List<DatasetEsIndex>>)
//                ReflectionTestUtils.invokeMethod(datasetEsIndexService, "groupDatasetsBySource", datasetList);
//
//        // Then
//        assertEquals(3, result.size());
//        assertEquals(2, result.get("case").size());
//        assertEquals(1, result.get("online").size());
//        assertEquals(1, result.get("upload").size());
//
//        // 验证分组正确性
//        assertTrue(result.get("case").stream().allMatch(d -> "case".equals(d.getDataSource())));
//        assertTrue(result.get("online").stream().allMatch(d -> "online".equals(d.getDataSource())));
//        assertTrue(result.get("upload").stream().allMatch(d -> "upload".equals(d.getDataSource())));
//    }
//
//    @Test
//    void testGroupDatasetsBySource_WithNullDataSource() {
//        // Given
//        DatasetEsIndex datasetWithNullSource = createTestDataset("dataset-001", null);
//        datasetWithNullSource.setDataSource(null);
//
//        DatasetEsIndex datasetWithEmptySource = createTestDataset("dataset-002", "");
//        datasetWithEmptySource.setDataSource("");
//
//        List<DatasetEsIndex> datasetList = Arrays.asList(datasetWithNullSource, datasetWithEmptySource);
//
//        // When
//        @SuppressWarnings("unchecked")
//        Map<String, List<DatasetEsIndex>> result = (Map<String, List<DatasetEsIndex>>)
//                ReflectionTestUtils.invokeMethod(datasetEsIndexService, "groupDatasetsBySource", datasetList);
//
//        // Then
//        assertEquals(1, result.size());
//        assertTrue(result.containsKey(DataSourceEnum.UPLOAD.getValue()));
//        assertEquals(2, result.get(DataSourceEnum.UPLOAD.getValue()).size());
//    }
//
//    // ==================== 边界条件和异常测试 ====================
//
//    @Test
//    void testInsertDataset_LargeDataset() {
//        // Given
//        DatasetEsIndex dataset = createTestDataset("dataset-001", "case");
//
//        // 创建大量数据
//        Map<String, Object> largeTextData = new HashMap<>();
//        for (int i = 0; i < 1000; i++) {
//            largeTextData.put("field" + i, "value" + i);
//        }
////        dataset.setTextData(largeTextData);
//
//        String dataSource = "case";
//        doNothing().when(datasetEsBaseIndexService).insert(any(DatasetEsIndex.class), anyString());
//
//        // When
//        boolean result = datasetEsIndexService.insertDataset(dataset, dataSource);
//
//        // Then
//        assertTrue(result);
//        verify(datasetEsBaseIndexService).insert(dataset, anyString());
//    }
//
//    @Test
//    void testBatchInsertDatasets_LargeBatch() {
//        // Given
//        List<DatasetEsIndex> largeDatasetList = new ArrayList<>();
//        for (int i = 0; i < 10000; i++) {
//            largeDatasetList.add(createTestDataset("dataset-" + i, "case"));
//        }
//        String dataSource = "case";
//
//        BulkResponse successResponse = createSuccessfulBulkResponse();
//        when(datasetEsBaseIndexService.batchInsert(anyList(), anyString())).thenReturn(successResponse);
//
//        // When
//        BatchResult result = datasetEsIndexService.batchInsertDatasets(largeDatasetList, dataSource);
//
//        // Then
//        assertEquals(result, BatchResult.empty(EsOperationEnum.INSERT));
//        verify(datasetEsBaseIndexService).batchInsert(largeDatasetList, anyString());
//    }
//
//    @Test
//    void testInsertDataset_SpecialCharacters() {
//        // Given
//        DatasetEsIndex dataset = createTestDataset("dataset-特殊字符-001", "case");
//        Map<String, Object> textData = new HashMap<>();
//        textData.put("title", "包含特殊字符的标题：!@#$%^&*()");
//        textData.put("content", "包含emoji的内容 😀🎉🚀");
////        dataset.setTextData(textData);
//
//        String dataSource = "case";
//        doNothing().when(datasetEsBaseIndexService).insert(any(DatasetEsIndex.class), anyString());
//
//        // When
//        boolean result = datasetEsIndexService.insertDataset(dataset, dataSource);
//
//        // Then
//        assertTrue(result);
//        verify(datasetEsBaseIndexService).insert(dataset, anyString());
//    }
//}