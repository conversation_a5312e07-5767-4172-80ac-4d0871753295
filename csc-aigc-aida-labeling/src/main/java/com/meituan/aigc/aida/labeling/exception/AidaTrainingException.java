package com.meituan.aigc.aida.labeling.exception;

/**
 * 训练系统自定义父类异常
 */
public class AidaTrainingException extends RuntimeException {

    public AidaTrainingException() {
        super();
    }

    public AidaTrainingException(String message) {
        super(message);
    }

    public AidaTrainingException(Throwable t) {
        super(t);
    }

    /**
     * 带有错误消息和原因的构造函数
     * 
     * @param message 错误消息
     * @param cause   原因异常
     */
    public AidaTrainingException(String message, Throwable cause) {
        super(message, cause);
    }
}
