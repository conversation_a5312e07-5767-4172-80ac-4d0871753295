package com.meituan.aigc.aida.data.management.es;

import com.meituan.aigc.aida.data.management.es.base.DatasetBaseEsIndex;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * 数据集维度-ES
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasetEsIndex extends DatasetBaseEsIndex {

    /**
     * 原始数据
     * 支持动态写入
     * ES映射类型: nested-text
     * 用于存储纯文本数据
     */
    private Map<String, Object> textData;

    /**
     * 平铺数据
     * 用于存储JSON文本数据
     * 使用Flattened类型存储JSON数据，平铺存储只会生成一层索引，防止动态增长引起映射爆炸
     * 仅支持精确检索和前缀检索
     * ES映射类型: flattened
     * 限制-属性值小于10000
     * 用于存储JSON文本数据
     */
    private Map<String, Object> flattenedData;

    /**
     * 日期数据
     * 用于存储日期类型数据
     * ES映射类型: nested-date
     */
    private Map<String, Date> dateData;

    /**
     * 嵌套数据
     * 用于存储复合数据，存在嵌套层级关系的JSON数据，支持嵌套存储
     * depth_limit: 5
     */
    private Map<String, Object> nestedData;
}
