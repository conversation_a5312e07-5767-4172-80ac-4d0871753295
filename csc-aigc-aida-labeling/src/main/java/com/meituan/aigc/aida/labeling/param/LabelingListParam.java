package com.meituan.aigc.aida.labeling.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 数据标注列表param
 * <AUTHOR>
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelingListParam implements Serializable {

    /**
     * 主任务id
     */
    private Long taskId;

    /**
     * 标注人mis
     */
    private String labelerMis;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 分配类型 1-session 2-query
     */
    private Integer assignType;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;

    /**
     * 是否是管理员
     */
    private boolean isAdmin;

}
